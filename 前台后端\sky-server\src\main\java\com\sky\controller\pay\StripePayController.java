package com.sky.controller.pay;

import com.sky.dto.PaymentRecordDTO;
import com.sky.entity.PaymentRecord;
import com.sky.result.Result;
import com.sky.service.PaymentRecordService;
import com.stripe.Stripe;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;
import com.stripe.model.Event;
import com.stripe.model.PaymentIntent;
import com.stripe.net.Webhook;
import com.stripe.param.PaymentIntentCreateParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/pay")
@Api(tags = "Stripe银行卡支付接口")
@Slf4j
public class StripePayController {

    private final PaymentRecordService paymentRecordService;

    @Value("${stripe.secret-key}")
    private String stripeSecretKey;

    @Value("${stripe.publishable-key}")
    private String stripePublishableKey;

    public StripePayController(PaymentRecordService paymentRecordService) {
        this.paymentRecordService = paymentRecordService;
    }

    @PostConstruct
    public void init() {
        Stripe.apiKey = stripeSecretKey;
    }

    @PostMapping("/stripePay")
    @ApiOperation("Stripe银行卡支付下单")
    public Result<Map<String, Object>> stripePay(@RequestBody PaymentRecordDTO dto) {
        try {
            // Stripe 金额单位为最小货币单位（如分）
            long amount = dto.getAmount().multiply(new BigDecimal(100)).longValue();
            PaymentIntentCreateParams params = PaymentIntentCreateParams.builder()
                    .setAmount(amount)
                    .setCurrency("usd") // 可根据需要调整币种
                    .setDescription("订单号:" + dto.getOrderId())
                    .putMetadata("orderId", String.valueOf(dto.getOrderId()))
                    .setAutomaticPaymentMethods(
                            PaymentIntentCreateParams.AutomaticPaymentMethods.builder().setEnabled(true).build()
                    )
                    .build();
            PaymentIntent intent = PaymentIntent.create(params);

            // 保存支付记录
            PaymentRecord record = PaymentRecord.builder()
                    .orderId(dto.getOrderId())
                    .paymentMethod(dto.getPaymentMethod())
                    .amount(dto.getAmount())
                    .transactionStatus("PENDING")
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            paymentRecordService.save(record);

            Map<String, Object> result = new HashMap<>();
            result.put("clientSecret", intent.getClientSecret());
            result.put("paymentIntentId", intent.getId());
            result.put("publishableKey", stripePublishableKey); // 返回前端用的 pk_test_xxx
            return Result.success(result);
        } catch (StripeException e) {
            log.error("Stripe下单异常", e);
            return Result.error("Stripe下单失败: " + e.getMessage());
        }
    }

    @PostMapping("/stripeWebhook")
    @ApiOperation("Stripe支付回调")
    public String stripeWebhook(HttpServletRequest request) throws IOException {
        String payload = null;
        try {
            // 直接从HttpServletRequest读取原始请求体，避免Spring的自动反序列化
            try (BufferedReader reader = request.getReader()) {
                payload = reader.lines().collect(Collectors.joining(System.lineSeparator()));
            }

            log.info("收到Stripe webhook: {}", payload);

            // 手动解析JSON为Event对象，这样apiVersion会被正确设置
            Event event = Event.GSON.fromJson(payload, Event.class);

            log.info("事件类型: {}, API版本: {}", event.getType(), event.getApiVersion());

            // 处理支付成功事件
            if ("payment_intent.succeeded".equals(event.getType())) {
                handlePaymentSuccess(event);
            }
            // 处理支付失败事件
            else if ("payment_intent.payment_failed".equals(event.getType())) {
                handlePaymentFailed(event);
            }
            // 处理支付创建事件（通常不需要特殊处理，只记录日志）
            else if ("payment_intent.created".equals(event.getType())) {
                log.info("支付意图已创建，事件ID: {}", event.getId());
            }
            // 其他事件类型
            else {
                log.info("忽略的事件类型: {}", event.getType());
            }

            return "success";
        } catch (Exception e) {
            log.error("处理Stripe webhook异常，payload: {}", payload, e);
            // 返回200状态码，避免Stripe重复发送webhook
            return "error";
        }
    }

    /**
     * 处理支付成功事件
     */
    private void handlePaymentSuccess(Event event) {
        try {
            PaymentIntent intent = (PaymentIntent) event.getDataObjectDeserializer().getObject().orElse(null);
            if (intent != null) {
                String orderId = intent.getMetadata().get("orderId");
                if (orderId != null) {
                    // 从PaymentIntent中提取完整的支付信息
                    Long amount = intent.getAmount(); // Stripe金额单位是分
                    BigDecimal amountDecimal = new BigDecimal(amount).divide(new BigDecimal(100)); // 转换为元

                    // 构建完整的支付记录，填充所有必需字段
                    PaymentRecord record = PaymentRecord.builder()
                            .orderId(Long.valueOf(orderId))
                            .paymentMethod("STRIPE") // Stripe支付方式
                            .amount(amountDecimal)
                            .transactionStatus("SUCCESS")
                            .transactionTime(LocalDateTime.now())
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build();

                    paymentRecordService.save(record);
                    log.info("订单{}支付成功，金额：{}元", orderId, amountDecimal);
                } else {
                    log.warn("PaymentIntent中未找到orderId");
                }
            } else {
                log.warn("无法解析PaymentIntent对象");
            }
        } catch (Exception e) {
            log.error("处理支付成功事件异常", e);
        }
    }

    /**
     * 处理支付失败事件
     */
    private void handlePaymentFailed(Event event) {
        try {
            PaymentIntent intent = (PaymentIntent) event.getDataObjectDeserializer().getObject().orElse(null);
            if (intent != null) {
                String orderId = intent.getMetadata().get("orderId");
                if (orderId != null) {
                    // 从PaymentIntent中提取完整的支付信息
                    Long amount = intent.getAmount(); // Stripe金额单位是分
                    BigDecimal amountDecimal = new BigDecimal(amount).divide(new BigDecimal(100)); // 转换为元

                    // 构建完整的支付记录，填充所有必需字段
                    PaymentRecord record = PaymentRecord.builder()
                            .orderId(Long.valueOf(orderId))
                            .paymentMethod("STRIPE") // Stripe支付方式
                            .amount(amountDecimal)
                            .transactionStatus("FAILED")
                            .transactionTime(LocalDateTime.now())
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build();

                    paymentRecordService.save(record);
                    log.info("订单{}支付失败，金额：{}元", orderId, amountDecimal);
                } else {
                    log.warn("PaymentIntent中未找到orderId");
                }
            } else {
                log.warn("无法解析PaymentIntent对象");
            }
        } catch (Exception e) {
            log.error("处理支付失败事件异常", e);
        }
    }
} 