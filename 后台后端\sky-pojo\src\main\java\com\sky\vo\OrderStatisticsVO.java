package com.sky.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单统计视图对象
 */
@Data
public class OrderStatisticsVO implements Serializable {

    // 原有字段（兼容性）
    private Integer toBeConfirmed;      // 待接单数量
    private Integer confirmed;          // 待派送数量
    private Integer deliveryInProgress; // 派送中数量

    // 新增字段 - 各状态订单数量
    private Integer pendingPayment;     // 待付款
    private Integer paid;               // 已付款
    private Integer processing;         // 处理中
    private Integer shipped;            // 已发货
    private Integer completed;          // 已完成
    private Integer cancelled;          // 已取消
    private Integer refunded;           // 已退款

    // 总订单数
    private Integer totalOrders;

    // 销售额统计
    private BigDecimal totalSales;      // 总销售额
    private BigDecimal todaySales;      // 今日销售额
    private BigDecimal yesterdaySales;  // 昨日销售额
    private BigDecimal weekSales;       // 本周销售额
    private BigDecimal lastWeekSales;   // 上周销售额
    private BigDecimal monthSales;      // 本月销售额
    private BigDecimal lastMonthSales;  // 上月销售额
    private BigDecimal yearSales;       // 本年销售额
    private BigDecimal avgOrderAmount;  // 平均订单金额

    // 订单数量统计
    private Integer todayOrders;        // 今日订单数
    private Integer yesterdayOrders;    // 昨日订单数
    private Integer weekOrders;         // 本周订单数
    private Integer lastWeekOrders;     // 上周订单数
    private Integer monthOrders;        // 本月订单数
    private Integer lastMonthOrders;    // 上月订单数
    private Integer yearOrders;         // 本年订单数

    // 转化率统计
    private Double paymentRate;         // 支付转化率（已支付/总订单）
    private Double completionRate;      // 完成率（已完成/已支付）
    private Double cancellationRate;    // 取消率（已取消/总订单）

    // 状态分布
    private List<Map<String, Object>> statusDistribution;

    // 时间段统计
    private List<Map<String, Object>> dailyStatistics;    // 每日统计
    private List<Map<String, Object>> weeklyStatistics;   // 每周统计
    private List<Map<String, Object>> monthlyStatistics;  // 每月统计
    private List<Map<String, Object>> hourlyStatistics;   // 每小时统计

    // 支付方式统计
    private List<Map<String, Object>> paymentMethodStats; // 支付方式统计

    // 商品维度统计
    private List<Map<String, Object>> topProducts;        // 热销商品排行
    private List<Map<String, Object>> categoryStats;      // 商品类别统计

    // 用户维度统计
    private Integer newUserOrders;      // 新用户订单数
    private Integer oldUserOrders;      // 老用户订单数
    private List<Map<String, Object>> userOrderFrequency; // 用户购买频次分布

    // 地域统计
    private List<Map<String, Object>> regionStats;        // 地域订单分布

    // 同比环比数据
    private Map<String, Object> comparisonData;           // 同比环比数据

    // 实时统计
    private Map<String, Object> realtimeStats;            // 实时统计数据
}
