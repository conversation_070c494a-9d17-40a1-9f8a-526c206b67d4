-- ========================================
-- 退款业务相关数据库表设计
-- ========================================

-- 0. 为orders表添加退款相关字段（如果不存在）
ALTER TABLE orders ADD COLUMN IF NOT EXISTS refund_status TINYINT DEFAULT 0 COMMENT '退款状态：0-无退款，1-退款申请中，2-部分退款，3-全额退款';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS refund_time DATETIME DEFAULT NULL COMMENT '退款时间';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_orders_refund_status ON orders(refund_status);

-- 1. 退款申请表
CREATE TABLE refund_application (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',

    -- 基本信息
    refund_no VARCHAR(64) NOT NULL UNIQUE COMMENT '退款申请单号',
    order_id BIGINT NOT NULL COMMENT '关联的订单ID',
    order_number VARCHAR(50) NOT NULL COMMENT '关联的订单号',
    buyer_id BIGINT NOT NULL COMMENT '申请人ID',

    -- 退款信息
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '申请退款金额',
    refund_reason VARCHAR(500) NOT NULL COMMENT '退款理由',
    refund_type TINYINT NOT NULL DEFAULT 1 COMMENT '退款类型：1-仅退款，2-退货退款',

    -- 申请状态
    application_status TINYINT NOT NULL DEFAULT 1 COMMENT '申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败',

    -- 审核信息
    need_approval TINYINT NOT NULL DEFAULT 0 COMMENT '是否需要审核：0-不需要，1-需要',
    approval_status TINYINT DEFAULT NULL COMMENT '审核状态：1-待审核，2-审核通过，3-审核拒绝',
    approver_id BIGINT DEFAULT NULL COMMENT '审核人ID',
    approval_time DATETIME DEFAULT NULL COMMENT '审核时间',
    approval_remark VARCHAR(500) DEFAULT NULL COMMENT '审核备注',

    -- 退款处理信息
    refund_method TINYINT DEFAULT NULL COMMENT '退款方式：1-原路退回，2-余额退款',
    actual_refund_amount DECIMAL(10,2) DEFAULT NULL COMMENT '实际退款金额',
    refund_time DATETIME DEFAULT NULL COMMENT '退款完成时间',

    -- 时间信息
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_order_id (order_id),
    INDEX idx_buyer_id (buyer_id),
    INDEX idx_refund_no (refund_no),
    INDEX idx_application_status (application_status),
    INDEX idx_create_time (create_time)
) COMMENT '退款申请表';

-- 2. 退款审核记录表
CREATE TABLE refund_approval_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',

    -- 关联信息
    refund_application_id BIGINT NOT NULL COMMENT '退款申请ID',
    refund_no VARCHAR(64) NOT NULL COMMENT '退款申请单号',

    -- 审核信息
    approver_id BIGINT NOT NULL COMMENT '审核人ID',
    approver_name VARCHAR(50) NOT NULL COMMENT '审核人姓名',
    approval_result TINYINT NOT NULL COMMENT '审核结果：1-通过，2-拒绝',
    approval_remark VARCHAR(500) DEFAULT NULL COMMENT '审核备注',

    -- 时间信息
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',

    -- 索引
    INDEX idx_refund_application_id (refund_application_id),
    INDEX idx_refund_no (refund_no),
    INDEX idx_approver_id (approver_id),

    -- 外键约束
    FOREIGN KEY (refund_application_id) REFERENCES refund_application(id) ON DELETE CASCADE
) COMMENT '退款审核记录表';

-- 3. 微信退款记录表（扩展现有表）
CREATE TABLE wechat_refund_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',

    -- 关联信息
    refund_application_id BIGINT NOT NULL COMMENT '退款申请ID',
    order_id BIGINT COMMENT '关联的订单ID',
    order_number VARCHAR(50) COMMENT '关联的订单号',

    -- 微信退款信息
    wechat_refund_id VARCHAR(32) COMMENT '微信支付退款订单号',
    out_refund_no VARCHAR(64) NOT NULL COMMENT '商户退款单号',
    transaction_id VARCHAR(32) COMMENT '微信支付交易订单号',
    out_trade_no VARCHAR(32) COMMENT '商户原交易订单号',

    -- 退款金额信息
    refund_amount INT NOT NULL COMMENT '退款金额（单位：分）',
    total_amount INT NOT NULL COMMENT '原订单金额（单位：分）',
    currency VARCHAR(16) DEFAULT 'USD' COMMENT '货币类型',
    payer_refund_amount INT COMMENT '用户退款金额（单位：分）',
    settlement_refund_amount INT COMMENT '结算币种退款金额（单位：分）',
    settlement_currency VARCHAR(16) COMMENT '结算币种',
    
    -- 退款状态和渠道
    refund_status VARCHAR(20) NOT NULL COMMENT '退款状态：SUCCESS/REFUNDCLOSE/PROCESSING/ABNORMAL',
    refund_channel VARCHAR(20) COMMENT '退款渠道：ORIGINAL/BALANCE/OTHER_BALANCE/OTHER_BANKCARD',
    recv_account VARCHAR(64) COMMENT '退款入账账户',
    fund_source VARCHAR(30) COMMENT '退款资金来源',
    
    -- 退款原因和备注
    refund_reason VARCHAR(80) COMMENT '退款原因',
    refund_remark TEXT COMMENT '退款备注',
    
    -- 时间信息
    refund_create_time DATETIME COMMENT '退款创建时间',
    refund_success_time DATETIME COMMENT '退款成功时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    
    -- 通知信息
    notify_url VARCHAR(256) COMMENT '退款通知地址',
    notify_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '通知状态：PENDING/SUCCESS/FAILED',
    notify_time DATETIME COMMENT '通知时间',
    
    -- 索引
    UNIQUE KEY uk_out_refund_no (out_refund_no),
    KEY idx_order_id (order_id),
    KEY idx_order_number (order_number),
    KEY idx_wechat_refund_id (wechat_refund_id),
    KEY idx_transaction_id (transaction_id),
    KEY idx_out_trade_no (out_trade_no),
    KEY idx_refund_status (refund_status),
    KEY idx_create_time (create_time)
) COMMENT '微信退款记录表';

-- 4. 为orders表添加退款相关字段（如果不存在）
ALTER TABLE orders ADD COLUMN IF NOT EXISTS refund_status TINYINT DEFAULT 0 COMMENT '退款状态：0-无退款，1-退款申请中，2-部分退款，3-全额退款';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS refund_time DATETIME DEFAULT NULL COMMENT '退款时间';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_orders_refund_status ON orders(refund_status);

-- 如果需要与现有订单表关联，可以添加外键约束
ALTER TABLE wechat_refund_record ADD CONSTRAINT fk_refund_order
FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL;

ALTER TABLE refund_application ADD CONSTRAINT fk_refund_app_order
FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;

-- ========================================
-- 常用查询SQL示例
-- ========================================

-- 1. 查询用户的退款申请列表
-- SELECT ra.*, o.number as order_number, o.amount as order_amount
-- FROM refund_application ra
-- LEFT JOIN orders o ON ra.order_id = o.id
-- WHERE ra.buyer_id = ?
-- ORDER BY ra.create_time DESC;

-- 2. 查询待审核的退款申请
-- SELECT ra.*, o.number as order_number, o.amount as order_amount, b.account_name as buyer_name
-- FROM refund_application ra
-- LEFT JOIN orders o ON ra.order_id = o.id
-- LEFT JOIN buyer b ON ra.buyer_id = b.id
-- WHERE ra.need_approval = 1 AND ra.approval_status = 1
-- ORDER BY ra.create_time ASC;

-- 3. 查询退款申请详情（包含审核记录）
-- SELECT ra.*, rar.approver_name, rar.approval_result, rar.approval_remark, rar.create_time as approval_time
-- FROM refund_application ra
-- LEFT JOIN refund_approval_record rar ON ra.id = rar.refund_application_id
-- WHERE ra.id = ?;

-- 4. 查询微信退款记录
-- SELECT wrr.*, ra.refund_reason, ra.buyer_id
-- FROM wechat_refund_record wrr
-- LEFT JOIN refund_application ra ON wrr.refund_application_id = ra.id
-- WHERE wrr.out_refund_no = ?;

-- 5. 统计退款数据
-- SELECT
--     DATE(create_time) as refund_date,
--     COUNT(*) as refund_count,
--     SUM(refund_amount) as total_refund_amount,
--     application_status
-- FROM refund_application
-- WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
-- GROUP BY DATE(create_time), application_status
-- ORDER BY refund_date DESC;

-- 6. 查询订单的退款历史
-- SELECT ra.*, wrr.refund_status as wechat_status, wrr.refund_success_time
-- FROM refund_application ra
-- LEFT JOIN wechat_refund_record wrr ON ra.id = wrr.refund_application_id
-- WHERE ra.order_id = ?
-- ORDER BY ra.create_time DESC;

-- 7. 查询需要处理的退款申请（按优先级排序）
-- SELECT ra.*, o.number as order_number, o.amount as order_amount,
--        CASE
--            WHEN o.status = 4 THEN 1  -- 已发货，优先级高
--            WHEN o.status = 5 THEN 2  -- 已完成，优先级中
--            ELSE 3                    -- 其他状态，优先级低
--        END as priority
-- FROM refund_application ra
-- LEFT JOIN orders o ON ra.order_id = o.id
-- WHERE ra.application_status = 1  -- 待处理
-- ORDER BY priority ASC, ra.create_time ASC;
