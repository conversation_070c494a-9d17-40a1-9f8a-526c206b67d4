package com.sky.dto.track17;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 17TRACK注册响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17RegisterResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<AcceptedItem> accepted;    // 成功注册的单号
    private List<RejectedItem> rejected;    // 拒绝注册的单号

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AcceptedItem implements Serializable {
        private Integer origin;         // 运输商识别方式
        private String number;          // 物流单号
        private Integer carrier;        // 运输商代码
        private String email;           // 邮箱
        private String tag;             // 标签
        private String lang;            // 语言
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RejectedItem implements Serializable {
        private String number;          // 物流单号
        private String tag;             // 标签
        private ErrorInfo error;        // 错误信息
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorInfo implements Serializable {
        private Integer code;           // 错误代码
        private String message;         // 错误信息
    }
}
