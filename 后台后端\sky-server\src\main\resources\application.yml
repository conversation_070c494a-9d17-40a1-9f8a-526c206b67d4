server:
  port: 8888
  ssl:
    enabled: false
    key-store: classpath:ssl.p12
    key-store-password: chr123456  # 密钥库密码
    key-store-type: PKCS12
    key-password: chr123456  # 如果密钥文件有独立的密码，确保它也匹配
  tomcat:
    threads:
      min-spare: 20    # 最小空闲线程
      max: 200         # 最大线程数
    accept-count: 100  # 队列容量
    max-connections: 10000

spring:
  servlet:
    multipart:
      enabled: true              # 是否启用文件上传支持（默认 true）
      max-file-size: 10MB        # 单个文件的最大大小
      max-request-size: 50MB    # 整个请求的最大大小
      file-size-threshold: 1MB   # 文件写入磁盘的阈值
      location: /tmp             # 临时文件存储路径
  mvc:
    static-path-pattern: /**
  web:
    resources:
      static-locations: classpath:/static/
  profiles:
    active: dev
  main:
    allow-circular-references: true
  datasource:
    druid:
      driver-class-name: ${sky.datasource.driver-class-name}
      url: jdbc:mysql://${sky.datasource.host}:${sky.datasource.port}/${sky.datasource.database}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true
      username: ${sky.datasource.username}
      password: ${sky.datasource.password}
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      time-between-eviction-runs-millis: 60000  # 检查闲置连接的间隔（毫秒）
      min-evictable-idle-time-millis: 300000    # 连接最小空闲时间（5分钟）
      max-evictable-idle-time-millis: 600000    # 连接最大空闲时间（10分钟）

      # 连接超时设置
      connect-timeout: 3000     # 连接建立超时（毫秒）
      socket-timeout: 60000     # 网络读写超时（毫秒）

      # 其他优化
      initial-size: 5           # 初始连接数
      max-active: 20            # 最大活跃连接数
      max-wait: 5000           # 获取连接的最大等待时间
  redis:
    host: ${sky.redis.host}
    port: ${sky.redis.port}

    database: ${sky.redis.database}
  config:
    import: "classpath:/kaptcha.properties"
  #mapper配置文件
mybatis:
  #mapper配置文件
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.sky.entity
  configuration:
    #开启驼峰命名
    map-underscore-to-camel-case: true
mybatis-plus:
  type-aliases-package: com.sky.entity
  mapper-locations: classpath:mapper/*.xml
  global-config:
    db-config:
      id-type: auto
      update-strategy: not_null
      column-format: "`%s`" # 自动给列名加反引号

logging:
  level:
    com:
      sky:
        mapper: debug
        service: debug
        controller: debug
  org.apache.ibatis: DEBUG


sky:
  jwt:
    # 设置jwt签名加密时使用的秘钥
    admin-secret-key: itcast
    # 设置jwt过期时间
    admin-ttl: 720000000000
    # 设置前端传递过来的令牌名称
    admin-token-name: token

# 17TRACK物流跟踪配置
track17:
  api:
    token: FD96F3D144B11DACB426071ADC5F21B3  # 17TRACK API密钥
    base-url: https://api.17track.net/track/v2.2
    connect-timeout: 10000
    read-timeout: 30000
    max-retry-count: 3
    rate-limit: 3
  webhook:
    url: https://your-domain.com/api/tracking/webhook
    secret: your-webhook-secret
    enabled: true
    max-retry-count: 3
  business:
    auto-register: true
    default-lang: zh-hans
    enable-translation: false
    enable-email-notification: false
    default-carrier-code: 3011
    auto-detection: true
    sync-interval: 6

# 微信支付相关配置
wechat:
  pay:
    # 商户号
    mchid: 777463044
    # 商户API证书序列号 - 使用与前台后端一致的序列号
    mchSerialNo: 7B90DEE3ABB5B5BCF3048BC24F72C0A767BCE451
    # 商户私钥文件路径
    privateKeyPath: apiclient_key.pem
    # APIv3密钥
    apiV3Key: abcdefghigklmnopqrstuvwxyzabzdef
    # APPID
    appid: wx2bd197bcb4b986a0
    # 微信支付回调通知地址
    notifyUrl: https://www.sharewharf.com/admin/pay/notify
    # 跨境支付相关配置
    crossBorder:
      # 是否启用跨境支付
      enabled: true
      # 商户分类代码
      merchantCategoryCode: 4111
      # 交易类型
      tradeType: NATIVE
      # 货币类型
      currency: USD
      # 查询订单API基础URL
      queryApiBaseUrl: https://apihk.mch.weixin.qq.com/v3/global/transactions
