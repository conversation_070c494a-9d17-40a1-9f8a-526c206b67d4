package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 退款审核记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundApprovalRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审核结果常量
     */
    public static final Integer RESULT_APPROVED = 1;  // 通过
    public static final Integer RESULT_REJECTED = 2;  // 拒绝

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 退款申请ID
     */
    private Long refundApplicationId;

    /**
     * 退款申请单号
     */
    private String refundNo;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 审核结果：1-通过，2-拒绝
     */
    private Integer approvalResult;

    /**
     * 审核备注
     */
    private String approvalRemark;

    /**
     * 审核时间
     */
    private LocalDateTime createTime;

    /**
     * 获取审核结果描述
     */
    public String getApprovalResultDesc() {
        switch (approvalResult) {
            case 1: return "审核通过";
            case 2: return "审核拒绝";
            default: return "未知结果";
        }
    }

    /**
     * 判断是否审核通过
     */
    public boolean isApproved() {
        return approvalResult != null && approvalResult.equals(RESULT_APPROVED);
    }

    /**
     * 判断是否审核拒绝
     */
    public boolean isRejected() {
        return approvalResult != null && approvalResult.equals(RESULT_REJECTED);
    }
}
