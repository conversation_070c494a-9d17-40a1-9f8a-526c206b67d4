package com.sky.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MerchantOrder {
    private Long id;
    private String orderNo;
    private String transactionNo;
    private String productInfo;
    private BigDecimal shippingFee;
    private BigDecimal totalAmount;
    private LocalDateTime paymentTime;
    private String trackingNo;
    private Integer status;
    private String orderSource;
    private String deliveryMethod;
    private String shippingInfo;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}