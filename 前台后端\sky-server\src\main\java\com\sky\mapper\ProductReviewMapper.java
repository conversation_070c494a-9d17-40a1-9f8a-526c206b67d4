package com.sky.mapper;

import com.sky.entity.ProductReview;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ProductReviewMapper {

    // 插入评论
    @Insert("INSERT INTO product_review (buyer_id, product_id, buyer_name, store_name, product_name, rating, comment, is_recommended, comment_time, status) " +
            "VALUES (#{buyerId}, #{productId}, #{buyerName}, #{storeName}, #{productName}, #{rating}, #{comment}, #{isRecommended}, #{commentTime}, #{status})")
    void insert(ProductReview productReview);

    // 查询所有评论
    @Select("SELECT * FROM product_review WHERE product_id = #{productId} AND status = 0")
    List<ProductReview> findByProductId(Long productId);

    // 根据评论 ID 删除评论
    @Delete("DELETE FROM product_review WHERE id = #{id}")
    void deleteById(Long id);
}
