package com.sky.entity;

import io.swagger.models.auth.In;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class SellerSubAccount {
    private Long id;
    private String accountName;
    private String password;
    private String gender;
    private String phone;
    private String email;
    private Integer accountStatus;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private LocalDateTime lastLoginTime;
    private String certificationInfo;
    private String photoUrl;
    private String userRole;
    private Long  createdBy;
    private Integer IsSubAccount;
    private List<String> permissions;
    private String remark;
}
