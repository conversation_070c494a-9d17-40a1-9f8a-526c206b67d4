package com.sky.mapper;



import com.github.pagehelper.Page;
import com.sky.dto.AdminAccountDTO;
import com.sky.dto.AdminAccountPageQueryDTO;
import com.sky.dto.AdminAccountUpdateDTO;
import com.sky.dto.AdminLogQueryDTO;
import com.sky.entity.AdminAccount;
import com.sky.vo.AdminAccountCreateVO;
import com.sky.vo.AdminAccountUpdateVO;
import com.sky.vo.AdminAccountVO;
import com.sky.vo.AdminLogVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AdminAccountMapper{

    /**
     * 分页查询管理员账号
     */
    List<AdminAccountVO> selectAdminAccounts(@Param("query") AdminAccountPageQueryDTO query);

    /**
     * 查询管理员账号总数
     */
    Long countAdminAccounts(@Param("query") AdminAccountPageQueryDTO query);
    void insertAdminAccount(AdminAccount account);

    @Select("select * from seller where email = #{email}")
    AdminAccount selectByEmail(String email);

    @Select("select * from seller where id = #{newAdminId}")
    AdminAccountCreateVO getCreateInfoById(Long newAdminId);

    void updateAdminAccountById(AdminAccountUpdateDTO adminAccountUpdateDTO);

    @Select("select * from seller where id = #{id}")
    AdminAccount selectById(Long id);

    @Select("select * from seller where id = #{id}")
    AdminAccountUpdateVO getUpdateInfoById(Long id);

    @Delete("delete from seller where id = #{id}")
    void deleteAdminAccountById(Long id);

    void updateAdminStatus(@Param("id")Long id,@Param("status") Integer status);

    Page<AdminLogVO> getAdminLogs( @Param("adminId") Long adminId,
                                   @Param("startTime") String startTime,
                                   @Param("endTime") String endTime,
                                   @Param("actionType") String actionType);

    @Update("update seller set is_sub_account = 1 where account_name = #{accountName}")
    void setSubAccount(String accountName);

    @Insert("insert into seller_permission(seller_id, permission_code) values(#{id}, #{permission})")
    void insertPermission(Long id, String permission);

    @Select("select permission_code from seller_permission where seller_id = #{id}")
    List<String> selectPermissionsByAdminId(Long id);

    @Delete("delete from seller_permission where seller_id = #{id}")
    void deleteAllPermissions(Long id);

    void deletePermissions(Long id, List<String> permissionsToDelete);
}
