<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SellerMapper">

    <update id="update" parameterType="com.sky.entity.Seller">
        UPDATE ry_mall.seller
        <set>
            <if test="password != null">password = #{password},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="accountStatus != null">account_status = #{accountStatus},</if>
            <if test="verificationCode != null">verification_code = #{verificationCode},</if>
            <if test="photoUrl != null">photo_url = #{photoUrl},</if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>
