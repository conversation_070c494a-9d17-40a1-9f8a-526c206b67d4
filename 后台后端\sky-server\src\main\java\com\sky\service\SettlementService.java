package com.sky.service;

import com.github.pagehelper.PageInfo;
import com.sky.dto.SettlementCompleteDTO;
import com.sky.dto.SettlementQueryDTO;
import com.sky.entity.OrderSettlementInfo;
import com.sky.vo.SettlementInfoVO;
import com.sky.vo.SettlementSummaryVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 回款服务接口
 */
public interface SettlementService {

    /**
     * 为订单创建回款信息
     * @param orderId 订单ID
     */
    void createSettlementInfo(Long orderId);

    /**
     * 分页查询回款信息
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageInfo<SettlementInfoVO> pageQuery(SettlementQueryDTO queryDTO);

    /**
     * 查询商家回款汇总信息
     * @param sellerId 商家ID
     * @return 汇总信息
     */
    SettlementSummaryVO getSummaryBySellerId(Long sellerId);

    /**
     * 查询所有商家回款汇总信息
     * @return 汇总信息列表
     */
    List<SettlementSummaryVO> getAllSummary();

    /**
     * 查询待回款订单
     * @return 待回款订单列表
     */
    List<SettlementInfoVO> getPendingSettlement();

    /**
     * 标记回款完成
     * @param completeDTO 完成信息
     */
    void completeSettlement(SettlementCompleteDTO completeDTO);

    /**
     * 更新回款状态（定时任务调用）
     */
    void updateSettlementStatus();

    /**
     * 计算回款日期
     * @param billingDate 账单日期
     * @return 回款日期
     */
    LocalDate calculateSettlementDate(LocalDate billingDate);

    /**
     * 计算账单周期
     * @param billingDate 账单日期
     * @return 账单周期（格式：2025-01）
     */
    String calculateBillingCycle(LocalDate billingDate);

    /**
     * 判断是否为工作日
     * @param date 日期
     * @return 是否为工作日
     */
    boolean isWorkingDay(LocalDate date);
}
