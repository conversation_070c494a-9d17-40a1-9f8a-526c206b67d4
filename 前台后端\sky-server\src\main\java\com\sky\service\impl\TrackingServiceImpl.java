package com.sky.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sky.dto.RegisterTrackingDTO;
import com.sky.dto.TrackingQueryDTO;
import com.sky.entity.Logistics;
import com.sky.mapper.LogisticsMapper;
import com.sky.service.Track17Service;
import com.sky.service.TrackingService;
import com.sky.vo.LogisticsVO;
import com.sky.vo.RegisterTrackingResponseVO;
import com.sky.vo.TrackingListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物流跟踪服务实现
 */
@Service
@Slf4j
public class TrackingServiceImpl implements TrackingService {

    @Autowired
    private LogisticsMapper logisticsMapper;

    @Autowired
    private Track17Service track17Service;

    @Override
    @Transactional
    public Logistics createLogistics(Logistics logistics) {
        log.info("创建物流记录: {}", logistics);

        try {
            logistics.setCreateTime(LocalDateTime.now());
            logisticsMapper.insert(logistics);
            return logistics;

        } catch (Exception e) {
            log.error("创建物流记录失败", e);
            throw new RuntimeException("创建物流记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RegisterTrackingResponseVO registerAndSaveTracking(RegisterTrackingDTO registerDTO) {
        log.info("注册并保存物流跟踪: {}", registerDTO);

        try {
            // 1. 注册到17TRACK
            List<RegisterTrackingDTO> trackingList = Arrays.asList(registerDTO);
            RegisterTrackingResponseVO response = track17Service.registerTracking(trackingList);

            // 2. 如果注册成功，保存到本地数据库
            if (response.getCode() == 0 && response.getSuccessCount() > 0) {
                Logistics logistics = Logistics.builder()
                        .logisticsNumber(registerDTO.getTrackingNumber())
                        .orderId(registerDTO.getOrderId())
                        .logisticsCompany(getCarrierName(registerDTO.getCarrierCode()))
                        .logisticsStatus("已注册")
                        .createTime(LocalDateTime.now())
                        .shippingDate(LocalDateTime.now())
                        .build();

                logisticsMapper.insert(logistics);
                log.info("物流记录保存成功: {}", logistics.getId());
            }

            return response;

        } catch (Exception e) {
            log.error("注册并保存物流跟踪失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("注册失败: " + e.getMessage())
                    .successCount(0)
                    .failureCount(1)
                    .build();
        }
    }

    @Override
    @Transactional
    public RegisterTrackingResponseVO batchRegisterTracking(List<RegisterTrackingDTO> registerDTOs) {
        log.info("批量注册物流跟踪: {}", registerDTOs.size());

        try {
            // 1. 批量注册到17TRACK
            RegisterTrackingResponseVO response = track17Service.registerTracking(registerDTOs);

            // 2. 保存成功的记录到本地数据库
            if (response.getSuccessCount() > 0) {
                List<Logistics> logisticsList = registerDTOs.stream()
                        .map(dto -> Logistics.builder()
                                .logisticsNumber(dto.getTrackingNumber())
                                .orderId(dto.getOrderId())
                                .logisticsCompany(getCarrierName(dto.getCarrierCode()))
                                .logisticsStatus("已注册")
                                .createTime(LocalDateTime.now())
                                .shippingDate(LocalDateTime.now())
                                .build())
                        .collect(Collectors.toList());

                // 批量插入
                for (Logistics logistics : logisticsList) {
                    logisticsMapper.insert(logistics);
                }

                log.info("批量保存物流记录成功: {}", logisticsList.size());
            }

            return response;

        } catch (Exception e) {
            log.error("批量注册物流跟踪失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("批量注册失败: " + e.getMessage())
                    .successCount(0)
                    .failureCount(registerDTOs.size())
                    .build();
        }
    }

    @Override
    public LogisticsVO getLogisticsByTrackingNumber(String trackingNumber) {
        log.info("根据物流单号查询物流信息: {}", trackingNumber);

        try {
            LambdaQueryWrapper<Logistics> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Logistics::getLogisticsNumber, trackingNumber);
            
            Logistics logistics = logisticsMapper.selectOne(queryWrapper);
            
            if (logistics != null) {
                LogisticsVO vo = new LogisticsVO();
                BeanUtils.copyProperties(logistics, vo);
                
                // 设置物流状态描述
                vo.setLogisticsStatusDesc(getStatusDescription(logistics.getLogisticsStatus()));
                
                return vo;
            }

            return null;

        } catch (Exception e) {
            log.error("查询物流信息失败", e);
            throw new RuntimeException("查询物流信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<LogisticsVO> getLogisticsByOrderId(Long orderId) {
        log.info("根据订单ID查询物流信息: {}", orderId);

        try {
            LambdaQueryWrapper<Logistics> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Logistics::getOrderId, orderId);
            queryWrapper.orderByDesc(Logistics::getCreateTime);
            
            List<Logistics> logisticsList = logisticsMapper.selectList(queryWrapper);
            
            return logisticsList.stream()
                    .map(logistics -> {
                        LogisticsVO vo = new LogisticsVO();
                        BeanUtils.copyProperties(logistics, vo);
                        vo.setLogisticsStatusDesc(getStatusDescription(logistics.getLogisticsStatus()));
                        return vo;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("根据订单ID查询物流信息失败", e);
            throw new RuntimeException("查询物流信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateLogisticsStatus(String trackingNumber, String status) {
        log.info("更新物流状态: {} -> {}", trackingNumber, status);

        try {
            LambdaUpdateWrapper<Logistics> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Logistics::getLogisticsNumber, trackingNumber);
            updateWrapper.set(Logistics::getLogisticsStatus, status);
            
            int result = logisticsMapper.update(null, updateWrapper);
            return result > 0;

        } catch (Exception e) {
            log.error("更新物流状态失败", e);
            return false;
        }
    }

    @Override
    public boolean syncTrackingStatus(String trackingNumber) {
        log.info("同步物流状态: {}", trackingNumber);

        try {
            // 从17TRACK获取最新状态
            TrackingQueryDTO queryDTO = TrackingQueryDTO.builder()
                    .trackingNumber(trackingNumber)
                    .page(1)
                    .pageSize(1)
                    .build();

            TrackingListVO trackingList = track17Service.getTrackingList(queryDTO);
            
            if (trackingList != null && trackingList.getTrackingList() != null && !trackingList.getTrackingList().isEmpty()) {
                TrackingListVO.TrackingDetailVO detail = trackingList.getTrackingList().get(0);
                String newStatus = detail.getStatusDescription();
                
                // 更新本地状态
                return updateLogisticsStatus(trackingNumber, newStatus);
            }

            return false;

        } catch (Exception e) {
            log.error("同步物流状态失败", e);
            return false;
        }
    }

    @Override
    public boolean batchSyncTrackingStatus(List<String> trackingNumbers) {
        log.info("批量同步物流状态: {}", trackingNumbers.size());

        try {
            int successCount = 0;
            for (String trackingNumber : trackingNumbers) {
                if (syncTrackingStatus(trackingNumber)) {
                    successCount++;
                }
            }

            log.info("批量同步完成，成功: {}, 总数: {}", successCount, trackingNumbers.size());
            return successCount > 0;

        } catch (Exception e) {
            log.error("批量同步物流状态失败", e);
            return false;
        }
    }

    @Override
    public RegisterTrackingResponseVO stopTracking(String trackingNumber, Integer carrierCode) {
        log.info("停止物流跟踪: {} {}", trackingNumber, carrierCode);

        try {
            RegisterTrackingResponseVO response = track17Service.stopTracking(trackingNumber, carrierCode);
            
            // 如果停止成功，更新本地状态
            if (response.getCode() == 0) {
                updateLogisticsStatus(trackingNumber, "已停止跟踪");
            }

            return response;

        } catch (Exception e) {
            log.error("停止物流跟踪失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("停止跟踪失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RegisterTrackingResponseVO restartTracking(String trackingNumber, Integer carrierCode) {
        log.info("重启物流跟踪: {} {}", trackingNumber, carrierCode);

        try {
            RegisterTrackingResponseVO response = track17Service.retrack(trackingNumber, carrierCode);
            
            // 如果重启成功，更新本地状态
            if (response.getCode() == 0) {
                updateLogisticsStatus(trackingNumber, "跟踪中");
            }

            return response;

        } catch (Exception e) {
            log.error("重启物流跟踪失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("重启跟踪失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RegisterTrackingResponseVO deleteTracking(String trackingNumber, Integer carrierCode) {
        log.info("删除物流跟踪: {} {}", trackingNumber, carrierCode);

        try {
            RegisterTrackingResponseVO response = track17Service.deleteTracking(trackingNumber, carrierCode);
            
            // 如果删除成功，删除本地记录
            if (response.getCode() == 0) {
                LambdaQueryWrapper<Logistics> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Logistics::getLogisticsNumber, trackingNumber);
                logisticsMapper.delete(queryWrapper);
            }

            return response;

        } catch (Exception e) {
            log.error("删除物流跟踪失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("删除跟踪失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public Object getTrackingStatistics() {
        log.info("获取物流状态统计");

        try {
            // 这里可以实现各种统计功能
            // 比如各状态的物流单数量、今日新增物流单等
            
            return "统计功能开发中";

        } catch (Exception e) {
            log.error("获取物流状态统计失败", e);
            return null;
        }
    }

    @Override
    public List<LogisticsVO> getLogisticsByUserId(Long userId, Integer page, Integer pageSize) {
        log.info("根据用户ID获取物流信息: {} {} {}", userId, page, pageSize);

        try {
            // 这里需要关联订单表来查询用户的物流信息
            // 由于没有用户和订单的关联，这里先返回空列表
            
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("根据用户ID获取物流信息失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据运输商代码获取运输商名称
     */
    private String getCarrierName(Integer carrierCode) {
        // 这里可以维护一个运输商代码到名称的映射
        switch (carrierCode) {
            case 21051: return "USPS";
            case 3002: return "FedEx";
            case 3001: return "UPS";
            case 3003: return "DHL";
            default: return "未知运输商";
        }
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String status) {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case "已注册": return "物流信息已注册";
            case "跟踪中": return "正在跟踪中";
            case "已停止跟踪": return "已停止跟踪";
            case "运输中": return "包裹运输中";
            case "已签收": return "包裹已签收";
            case "异常": return "物流异常";
            default: return status;
        }
    }
}
