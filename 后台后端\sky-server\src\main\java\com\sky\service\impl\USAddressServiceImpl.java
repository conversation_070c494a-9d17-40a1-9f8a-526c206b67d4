package com.sky.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sky.entity.BusinessException;
import com.sky.entity.USAddress;
import com.sky.exception.AccountIsExitException;
import com.sky.mapper.USAddressMapper;
import com.sky.service.USAddressService;
import kotlin.jvm.internal.Lambda;
import net.bytebuddy.implementation.bytecode.Throw;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.stream.Collectors;

@Service
public class USAddressServiceImpl extends ServiceImpl<USAddressMapper, USAddress> implements USAddressService {

    @Autowired
    private USAddressMapper addressMapper;

    @Override
    public void validateUSAddress(USAddress address) {
        if (address.getCity() == null || address.getState() == null) {
            throw new BusinessException("City and state cannot be empty");
        }
        if (!isValidUSCity(address.getCity(), address.getState())) {
            throw new BusinessException("City does not belong to the United States");
        }
    }

    @Override
    public void defaultSet(Long id) {
        USAddress usAddress = new USAddress();
        USAddress one = lambdaQuery().eq(USAddress::getId, id).one();
        Long userId = one.getUserId();
        List<USAddress> list = lambdaQuery().eq(USAddress::getUserId, userId).list();
        List<USAddress> collect = list.stream().filter(o -> !o.getId().equals(id)).collect(Collectors.toList());
        List<USAddress> collect1 = collect.stream().filter(o -> o.getDefault() == 1).collect(Collectors.toList());
        if(collect1.size()>0){
            throw new AccountIsExitException("最多只能设置一个默认地址");
        }
        usAddress.setDefault(Long.valueOf(1));
        lambdaUpdate().set(USAddress::getDefault,usAddress.getDefault()).eq(USAddress::getId,id).update(usAddress);
    }

    private boolean isValidUSCity(String city, String state) {
        return addressMapper.isValidUSCity(city, state) > 0;
    }

    @Override
    public void addAddress(USAddress address) {
        // validateUSAddress(address);
        if (address.getUserId() == null) {
            throw new BusinessException("User ID is required");
        }
        if (address.getName() == null || address.getName().isEmpty()) {
            throw new BusinessException("Recipient name is required");
        }
        if (address.getPhoneNumber() == null || address.getPhoneNumber().isEmpty()) {
            throw new BusinessException("Phone number is required");
        }
        if (address.getStreet() == null || address.getStreet().isEmpty()) {
            throw new BusinessException("Street is required");
        }
        if (address.getCity() == null || address.getCity().isEmpty()) {
            throw new BusinessException("City is required");
        }
        if (address.getState() == null || address.getState().isEmpty()) {
            throw new BusinessException("State is required");
        }
        if (address.getZipCode() == null || address.getZipCode().isEmpty()) {
            throw new BusinessException("Zip code is required");
        }
        address.setDefault(Long.valueOf(0));
        addressMapper.insert(address);
    }

    @Override
    public List<USAddress> getAllAddresses(Long userId) {
        if (userId == null) {
            throw new BusinessException("用户未登录，无法获取地址列表");
        }
        List<USAddress> list = lambdaQuery().eq(USAddress::getUserId, userId).list();
        return list ;
    }

    @Override
    public void updateAddress(USAddress address) {
        if(address.getUserId()==null) throw new BusinessException("请携带用户id");
        if ((address.getName() == null || address.getName().isEmpty()) ||
                (address.getPhoneNumber() == null || address.getPhoneNumber().isEmpty()) ||
                (address.getStreet() == null || address.getStreet().isEmpty()) ||
                (address.getCity() == null || address.getCity().isEmpty()) ||
                (address.getState() == null || address.getState().isEmpty()) ||
                (address.getZipCode() == null || address.getZipCode().isEmpty())) {
            throw new BusinessException("所有字段更新后都不能为空");
        }
        lambdaUpdate().eq(USAddress::getId,address.getId()).update(address);
    }

    @Override
    public void deleteAddress(Long id) {
        addressMapper.deleteById(id);
    }
}