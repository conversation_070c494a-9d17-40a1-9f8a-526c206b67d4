<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.LogisticsMapper">

        <insert id="insertLogistics" parameterType="com.sky.entity.LogisticsInfo" useGeneratedKeys="true" keyProperty="id">
            insert into logistics(logistics_number,order_id,logistics_company,logistics_status,create_time,shipping_date)
            values (#{logisticsNumber},#{orderId},#{logisticsCompany},#{logisticsStatus},#{createTime},#{shippingDate})
        </insert>
</mapper>
