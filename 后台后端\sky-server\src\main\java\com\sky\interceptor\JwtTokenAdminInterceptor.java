package com.sky.interceptor;

import com.sky.constant.JwtClaimsConstant;
import com.sky.context.BaseContext;
import com.sky.properties.JwtProperties;
import com.sky.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * jwt令牌校验的拦截器
 */
@Component
@Slf4j
public class JwtTokenAdminInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtProperties jwtProperties;

    /**
     * 校验jwt
     *
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 放行 OPTIONS 请求
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }

        // 使用 Ant 风格路径匹配
        AntPathMatcher pathMatcher = new AntPathMatcher();
        String url = request.getRequestURI();

        // 放行登录、注册和非 Controller 方法
        if (!(handler instanceof HandlerMethod)
                || pathMatcher.match("/buyer/login", url)
                || pathMatcher.match("/buyer/register", url)) {
            return true;
        }

        // 从请求头获取令牌
        String tokenHeader = jwtProperties.getAdminTokenName(); // 如 "Authorization"
        String token = request.getHeader("Authorization");
        log.info("Received token: {}", token);
        // 明确处理令牌缺失的情况
        if (token == null || token.isEmpty()) {
            log.error("令牌缺失，请求头未携带: {}", tokenHeader);
            response.setStatus(401);
            response.getWriter().write("Token is required");
            return false;
        }

        // 校验令牌
        try {
            Claims claims = JwtUtil.parseJWT(jwtProperties.getAdminSecretKey(), token);
            Long empId = Long.valueOf(claims.get(JwtClaimsConstant.EMP_ID).toString());
            BaseContext.setCurrentId(empId);
            return true;
        } catch (Exception ex) {
            log.error("JWT 校验失败", ex);
            response.setStatus(401);
            response.getWriter().write("Invalid token");
            return false;
        }
//        return true;
//    }

    }
}
