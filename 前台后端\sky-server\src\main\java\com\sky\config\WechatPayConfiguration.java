package com.sky.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.sky.properties.WechatPayProperties;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 微信支付全局配置类
 */
@Configuration
@Slf4j
public class WechatPayConfiguration {

    @Autowired
    private WechatPayProperties wechatPayProperties;

    /**
     * 创建微信支付配置
     */
    @Bean
    public Config wechatPayConfig() {
        try {
            // 记录配置信息
            log.info("初始化微信支付配置...");
            
            // 检查必要参数
            String mchid = wechatPayProperties.getMchid();
            String mchSerialNo = wechatPayProperties.getMchSerialNo();
            String apiV3Key = wechatPayProperties.getApiV3Key();
            
            log.info("商户号: {}", mchid);
            log.info("证书序列号: {}", mchSerialNo);
            log.info("APIv3密钥: {}", apiV3Key != null ? "已设置" : "未设置");
            log.info("私钥路径: {}", wechatPayProperties.getPrivateKeyPath());
            
            // 检查跨境支付配置
            if (wechatPayProperties.getCrossBorder().isEnabled()) {
                log.info("跨境支付已启用");
                log.info("商户分类代码: {}", wechatPayProperties.getCrossBorder().getMerchantCategoryCode());
                log.info("交易类型: {}", wechatPayProperties.getCrossBorder().getTradeType());
                log.info("货币类型: {}", wechatPayProperties.getCrossBorder().getCurrency());
                log.info("查询订单API基础URL: {}", wechatPayProperties.getCrossBorder().getQueryApiBaseUrl());
            } else {
                log.info("跨境支付未启用");
            }
            
            // 参数校验
            if (!StringUtils.hasText(mchid)) {
                throw new IllegalArgumentException("商户号(mchid)不能为空");
            }
            if (!StringUtils.hasText(mchSerialNo)) {
                throw new IllegalArgumentException("证书序列号(mch-serial-no)不能为空");
            }
            if (!StringUtils.hasText(apiV3Key)) {
                throw new IllegalArgumentException("APIv3密钥(apiV3-key)不能为空");
            }
            
            // 从classpath加载私钥内容
            String privateKey;
            try {
                String privateKeyPath = "cert/" + wechatPayProperties.getPrivateKeyPath();
                log.info("加载私钥文件: {}", privateKeyPath);
                ClassPathResource resource = new ClassPathResource(privateKeyPath);
                privateKey = FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));
                log.info("成功加载私钥文件，内容长度: {}", privateKey.length());
                
                // 检查私钥内容是否有效
                if (!privateKey.contains("BEGIN PRIVATE KEY") || !privateKey.contains("END PRIVATE KEY")) {
                    log.warn("私钥文件内容可能不正确，没有找到标准的PEM头部和尾部");
                }
            } catch (Exception e) {
                log.error("无法加载私钥文件: {}", e.getMessage(), e);
                throw new RuntimeException("无法加载私钥文件: " + e.getMessage(), e);
            }

            // 初始化商户配置
            log.info("开始构建RSAAutoCertificateConfig...");
            RSAAutoCertificateConfig.Builder builder = new RSAAutoCertificateConfig.Builder()
                    .merchantId(mchid)
                    .privateKey(privateKey)
                    .merchantSerialNumber(mchSerialNo)
                    .apiV3Key(apiV3Key);
            
            log.info("开始构建Config对象...");
            Config config = builder.build();
            log.info("微信支付配置初始化成功");
            return config;
        } catch (Exception e) {
            log.error("微信支付配置初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("微信支付配置初始化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建NativePayService
     */
    @Bean
    public NativePayService nativePayService(Config wechatPayConfig) {
        try {
            NativePayService service = new NativePayService.Builder().config(wechatPayConfig).build();
            log.info("NativePayService初始化成功");
            return service;
        } catch (Exception e) {
            log.error("NativePayService初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("NativePayService初始化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建用于处理微信支付响应的ObjectMapper
     * 配置下划线转驼峰命名策略
     */
    @Bean("wechatPayObjectMapper")
    public ObjectMapper wechatPayObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        return objectMapper;
    }
    
    /**
     * 获取商户号
     */
    @Bean("wechatPayMerchantId")
    public String wechatPayMerchantId() {
        return wechatPayProperties.getMchid();
    }
} 