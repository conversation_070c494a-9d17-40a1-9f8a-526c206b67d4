<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.51tracking.maven.example</groupId>
  <artifactId>51tracking-maven-example</artifactId>
  <version>1.0-SNAPSHOT</version>

  <name>51tracking-maven-example</name>

  <!-- FIXME change it to the project's website -->
  <url>https://github.com/51tracking/51tracking-sdk-java</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.github.51tracking</groupId>
      <artifactId>51tracking-sdk-java</artifactId>
      <version>1.0.1</version>
    </dependency>
  </dependencies>

</project>
