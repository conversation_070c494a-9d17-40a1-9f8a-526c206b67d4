package com.sky.controller.admin;

import com.github.pagehelper.PageInfo;
import com.sky.dto.OrderBatchOperationDTO;
import com.sky.dto.OrderPageQueryDTO;
import com.sky.dto.ShipOrderDTO;
import com.sky.result.Result;
import com.sky.service.OrderBatchService;
import com.sky.service.OrderStatusService;
import com.sky.service.OrdersService;
import com.sky.service.TrackingService;

import com.sky.vo.OrderBatchOperationResultVO;
import com.sky.vo.OrderVO;
import com.sky.tracking.model.courier.Courier;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/order")
@Api(tags = "后台订单管理接口")
public class OrdersController {
    @Autowired
    private OrdersService ordersService;

    @Autowired
    private OrderBatchService orderBatchService;

    @Autowired
    private OrderStatusService orderStatusService;

    @Autowired
    private TrackingService trackingService;

    /**
     * 订单分页查询
     */
    @GetMapping("/conditionSearch")
    @ApiOperation("订单分页查询")
    public Result<PageInfo<OrderVO>> conditionSearch(OrderPageQueryDTO orderPageQueryDTO) {
        PageInfo<OrderVO> pageInfo = ordersService.pageQuery(orderPageQueryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/details/{id}")
    @ApiOperation("获取订单详情")
    public Result<OrderVO> getOrderById(@PathVariable Long id) {
        OrderVO orderVO = ordersService.getOrderById(id);
        return Result.success(orderVO);
    }

    /**
     * 根据订单号查询订单
     */
    @GetMapping("/number/{number}")
    @ApiOperation("根据订单号查询订单")
    public Result<OrderVO> getOrderByNumber(@PathVariable String number) {
        OrderVO orderVO = ordersService.getOrderByNumber(number);
        return Result.success(orderVO);
    }

    /**
     * 发货
     */
    @PutMapping("/delivery")
    @ApiOperation("发货")
    public Result<Void> shipOrder(@RequestBody @Valid ShipOrderDTO shipOrderDTO) {
        ordersService.shipOrder(shipOrderDTO);
        return Result.success();
    }

    /**
     * 获取支持的快递公司列表
     */
    @GetMapping("/couriers")
    @ApiOperation("获取支持的快递公司列表")
    public Result<List<Courier>> getAllCouriers() {
        List<Courier> couriers = trackingService.getAllCouriers();
        return Result.success(couriers);
    }

    /**
     * 完成订单
     */
    @PutMapping("/complete/{id}")
    @ApiOperation("完成订单")
    public Result<Void> completeOrder(@PathVariable Long id) {
        ordersService.completeOrder(id);
        return Result.success();
    }

    /**
     * 取消订单
     */
    @PutMapping("/cancel")
    @ApiOperation("取消订单")
    public Result<Void> cancelOrder(@RequestBody Map<String, Object> params) {
        Long id = Long.valueOf(params.get("id").toString());
        String cancelReason = params.get("cancelReason").toString();
        ordersService.cancelOrder(id, cancelReason);
        return Result.success();
    }

    /**
     * 订单统计
     */
    @GetMapping("/statistics")
    @ApiOperation("订单统计")
    public Result<Map<String, Object>> getOrderStatistics() {
        Map<String, Object> statistics = ordersService.getOrderStatistics();
        return Result.success(statistics);
    }

    /**
     * 根据买家ID查询订单列表
     */
    @GetMapping("/buyer/{buyerId}")
    @ApiOperation("根据买家ID查询订单列表")
    public Result<List<OrderVO>> getOrdersByBuyerId(@PathVariable Long buyerId) {
        List<OrderVO> orders = ordersService.getOrdersByBuyerId(buyerId);
        return Result.success(orders);
    }

    /**
     * 查询历史订单
     */
    @GetMapping("/history")
    @ApiOperation("查询历史订单")
    public Result<List<OrderVO>> getOrderHistory(@RequestParam Long buyerId) {
        List<OrderVO> history = ordersService.getOrderHistory(buyerId);
        return Result.success(history);
    }

    /**
     * 批量发货
     */
    @PutMapping("/batch/ship")
    @ApiOperation("批量发货")
    public Result<OrderBatchOperationResultVO> batchShip(@RequestBody OrderBatchOperationDTO batchOperationDTO) {
        OrderBatchOperationResultVO result = orderBatchService.batchShip(batchOperationDTO);
        return Result.success(result);
    }

    /**
     * 批量取消
     */
    @PutMapping("/batch/cancel")
    @ApiOperation("批量取消")
    public Result<OrderBatchOperationResultVO> batchCancel(@RequestBody OrderBatchOperationDTO batchOperationDTO) {
        OrderBatchOperationResultVO result = orderBatchService.batchCancel(batchOperationDTO);
        return Result.success(result);
    }

    /**
     * 批量完成
     */
    @PutMapping("/batch/complete")
    @ApiOperation("批量完成")
    public Result<OrderBatchOperationResultVO> batchComplete(@RequestBody OrderBatchOperationDTO batchOperationDTO) {
        OrderBatchOperationResultVO result = orderBatchService.batchComplete(batchOperationDTO);
        return Result.success(result);
    }

    /**
     * 批量导出
     */
    @PostMapping("/batch/export")
    @ApiOperation("批量导出")
    public Result<OrderBatchOperationResultVO> batchExport(@RequestBody OrderBatchOperationDTO batchOperationDTO) {
        OrderBatchOperationResultVO result = orderBatchService.batchExport(batchOperationDTO);
        return Result.success(result);
    }

    /**
     * 编辑订单
     */
    @PutMapping("/edit/{id}")
    @ApiOperation("编辑订单")
    public Result<Void> editOrder(@PathVariable Long id,
                                  @RequestParam String addressInfo,
                                  @RequestParam String remark,
                                  @RequestParam String editReason) {
        ordersService.editOrder(id, addressInfo, remark, editReason);
        return Result.success();
    }

    /**
     * 退款处理
     */
    @PutMapping("/refund/{id}")
    @ApiOperation("退款处理")
    public Result<Void> refundOrder(@PathVariable Long id,
                                   @RequestParam String refundReason,
                                   @RequestParam String refundAmount) {
        ordersService.refundOrder(id, refundReason, refundAmount);
        return Result.success();
    }

    /**
     * 获取订单操作日志
     */
    @GetMapping("/logs/{id}")
    @ApiOperation("获取订单操作日志")
    public Result<List<Object>> getOrderLogs(@PathVariable Long id) {
        List<Object> logs = ordersService.getOrderLogs(id);
        return Result.success(logs);
    }

    /**
     * 获取订单可执行操作
     */
    @GetMapping("/actions/{id}")
    @ApiOperation("获取订单可执行操作")
    public Result<String[]> getAvailableActions(@PathVariable Long id) {
        OrderVO order = ordersService.getOrderById(id);
        String[] actions = orderStatusService.getAvailableActions(order.getStatus());
        return Result.success(actions);
    }



    /**
     * 获取订单概览
     */
    @GetMapping("/overview")
    @ApiOperation("获取订单概览")
    public Result<Map<String, Object>> getOrderOverview() {
        Map<String, Object> overview = ordersService.getOrderOverview();
        return Result.success(overview);
    }

    /**
     * 导出订单
     */
    @PostMapping("/export")
    @ApiOperation("导出订单")
    public Result<String> exportOrders(@RequestParam List<Long> orderIds,
                                      @RequestParam(defaultValue = "excel") String format) {
        String downloadUrl = ordersService.exportOrders(orderIds, format);
        return Result.success(downloadUrl);
    }
}
