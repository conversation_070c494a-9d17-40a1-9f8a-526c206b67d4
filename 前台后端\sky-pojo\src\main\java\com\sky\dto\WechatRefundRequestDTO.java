package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 微信退款申请请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatRefundRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信支付订单号（与商户订单号二选一）
     */
    private String transactionId;

    /**
     * 商户订单号（与微信支付订单号二选一）
     */
    private String outTradeNo;

    /**
     * 商户退款单号（必填）
     */
    private String outRefundNo;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 退款金额（单位：分）
     */
    private Integer refundAmount;

    /**
     * 原订单金额（单位：分）
     */
    private Integer totalAmount;

    /**
     * 退款资金来源
     * REFUND_SOURCE_UNSETTLED_FUNDS：未结算资金退款（默认）
     * REFUND_SOURCE_RECHARGE_FUNDS：可用余额退款
     */
    private String source;

    /**
     * 退款通知地址
     */
    private String notifyUrl;
}
