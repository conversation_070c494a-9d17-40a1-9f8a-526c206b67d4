package com.sky.task;

import com.sky.entity.Orders;
import com.sky.mapper.OrdersMapper;
import com.sky.service.OrderLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单状态自动流转定时任务
 */
@Component
@Slf4j
public class OrderStatusTask {

    @Autowired
    private OrdersMapper ordersMapper;

    @Autowired
    private OrderLogService orderLogService;

    /**
     * 自动取消超时未支付订单
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void cancelTimeoutOrders() {
        log.info("开始执行自动取消超时未支付订单任务");
        
        try {
            // 查找30分钟前创建但仍未支付的订单
            LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(30);
            List<Orders> timeoutOrders = ordersMapper.findTimeoutPendingPaymentOrders(timeoutTime);
            
            for (Orders order : timeoutOrders) {
                // 更新订单状态为已取消
                ordersMapper.updateStatus(order.getId(), Orders.STATUS_CANCELLED, LocalDateTime.now());
                
                // 设置取消原因
                LocalDateTime now = LocalDateTime.now();
                ordersMapper.updateCancelInfo(order.getId(), "超时未支付", now, now);
                
                // 记录日志
                orderLogService.recordLog(
                    order.getId(),
                    order.getNumber(),
                    "auto_cancel",
                    Orders.STATUS_PENDING_PAYMENT,
                    Orders.STATUS_CANCELLED,
                    "system",
                    null,
                    "系统",
                    "超时未支付自动取消"
                );
                
                log.info("订单 {} 因超时未支付被自动取消", order.getNumber());
            }
            
            if (!timeoutOrders.isEmpty()) {
                log.info("自动取消超时未支付订单任务完成，共处理 {} 个订单", timeoutOrders.size());
            }
            
        } catch (Exception e) {
            log.error("自动取消超时未支付订单任务执行异常", e);
        }
    }

    /**
     * 自动确认收货
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void autoConfirmDelivery() {
        log.info("开始执行自动确认收货任务");
        
        try {
            // 查找7天前发货但仍未确认收货的订单
            LocalDateTime autoConfirmTime = LocalDateTime.now().minusDays(7);
            List<Orders> shippedOrders = ordersMapper.findLongTimeShippedOrders(autoConfirmTime);
            
            for (Orders order : shippedOrders) {
                // 更新订单状态为已完成
                ordersMapper.updateStatus(order.getId(), Orders.STATUS_COMPLETED, LocalDateTime.now());
                
                // 设置完成时间
                LocalDateTime completeTime = LocalDateTime.now();
                ordersMapper.updateCompleteTime(order.getId(), completeTime, completeTime);
                
                // 记录日志
                orderLogService.recordLog(
                    order.getId(),
                    order.getNumber(),
                    "auto_confirm",
                    Orders.STATUS_SHIPPED,
                    Orders.STATUS_COMPLETED,
                    "system",
                    null,
                    "系统",
                    "发货7天后自动确认收货"
                );
                
                log.info("订单 {} 发货7天后自动确认收货", order.getNumber());
            }
            
            if (!shippedOrders.isEmpty()) {
                log.info("自动确认收货任务完成，共处理 {} 个订单", shippedOrders.size());
            }
            
        } catch (Exception e) {
            log.error("自动确认收货任务执行异常", e);
        }
    }

    /**
     * 检查退款超时处理
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkRefundTimeout() {
        log.info("开始执行退款超时检查任务");
        
        try {
            // 查找24小时前申请但仍在退款中的记录
            // LocalDateTime timeoutTime = LocalDateTime.now().minusHours(24);
            // 这里可以添加退款超时处理逻辑
            // 例如：将长时间处于退款中状态的申请标记为需要人工处理

            log.info("退款超时检查任务完成");
            
        } catch (Exception e) {
            log.error("退款超时检查任务执行异常", e);
        }
    }
}
