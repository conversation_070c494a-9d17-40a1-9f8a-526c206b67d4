<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信支付测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"], textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #1aad19;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #129611;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: none;
        }
        .qrcode {
            text-align: center;
            margin-top: 20px;
        }
        .qrcode img {
            max-width: 300px;
            border: 1px solid #ddd;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 4px 4px 0 0;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            color: black;
            margin-top: 0;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #1aad19;
            color: white;
        }
        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 4px 4px;
            background-color: white;
        }
    </style>
</head>
<body>
    <h1>微信支付测试</h1>
    <div class="container">
        <div class="tab">
            <button class="tablinks active" onclick="openTab(event, 'urlMode')">URL模式</button>
            <button class="tablinks" onclick="openTab(event, 'imageMode')">图片模式</button>
        </div>

        <div id="urlMode" class="tabcontent" style="display: block;">
            <h2>生成支付URL</h2>
            <div class="form-group">
                <label for="amount">支付金额（分）:</label>
                <input type="number" id="amount" value="1" min="1">
            </div>
            <div class="form-group">
                <label for="description">商品描述:</label>
                <input type="text" id="description" value="测试商品">
            </div>
            <div class="form-group">
                <label for="orderNo">订单号（可选）:</label>
                <input type="text" id="orderNo" placeholder="留空自动生成">
            </div>
            <button onclick="createPayment('url')">生成支付URL</button>
            
            <div id="urlResult" class="result">
                <h3>支付URL:</h3>
                <p id="payUrl"></p>
                <div class="qrcode">
                    <h3>支付二维码:</h3>
                    <div id="qrcode"></div>
                </div>
                <p>订单号: <span id="outTradeNo"></span></p>
            </div>
        </div>

        <div id="imageMode" class="tabcontent">
            <h2>直接生成支付二维码图片</h2>
            <div class="form-group">
                <label for="amountImg">支付金额（分）:</label>
                <input type="number" id="amountImg" value="1" min="1">
            </div>
            <div class="form-group">
                <label for="descriptionImg">商品描述:</label>
                <input type="text" id="descriptionImg" value="测试商品">
            </div>
            <div class="form-group">
                <label for="orderNoImg">订单号（可选）:</label>
                <input type="text" id="orderNoImg" placeholder="留空自动生成">
            </div>
            <button onclick="createPayment('image')">生成支付二维码</button>
            
            <div id="imageResult" class="result">
                <div class="qrcode">
                    <h3>支付二维码:</h3>
                    <img id="qrcodeImg" src="" alt="支付二维码">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode.js@1.0.0/qrcode.min.js"></script>
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }

        function createPayment(mode) {
            const amount = mode === 'url' ? document.getElementById('amount').value : document.getElementById('amountImg').value;
            const description = mode === 'url' ? document.getElementById('description').value : document.getElementById('descriptionImg').value;
            const orderNo = mode === 'url' ? document.getElementById('orderNo').value : document.getElementById('orderNoImg').value;
            
            const data = {
                total: parseInt(amount),
                description: description,
                outTradeNo: orderNo
            };
            
            if (mode === 'url') {
                // 使用URL模式
                fetch('/pay/wechat/native', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.code === 1) {
                        document.getElementById('payUrl').textContent = result.data.codeUrl;
                        document.getElementById('outTradeNo').textContent = result.data.outTradeNo;
                        
                        // 生成二维码
                        const qrcodeContainer = document.getElementById('qrcode');
                        qrcodeContainer.innerHTML = '';
                        new QRCode(qrcodeContainer, {
                            text: result.data.codeUrl,
                            width: 256,
                            height: 256
                        });
                        
                        document.getElementById('urlResult').style.display = 'block';
                    } else {
                        alert('创建支付订单失败: ' + result.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('请求失败: ' + error);
                });
            } else {
                // 使用图片模式，直接获取二维码图片
                document.getElementById('qrcodeImg').src = '/pay/wechat/native/qrcode?' + new Date().getTime();
                document.getElementById('imageResult').style.display = 'block';
                
                // 创建表单数据
                const formData = new FormData();
                formData.append('total', amount);
                formData.append('description', description);
                if (orderNo) {
                    formData.append('outTradeNo', orderNo);
                }
                
                // 发送请求获取二维码图片
                fetch('/pay/wechat/native/qrcode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取二维码失败');
                    }
                    return response.blob();
                })
                .then(blob => {
                    const imageUrl = URL.createObjectURL(blob);
                    document.getElementById('qrcodeImg').src = imageUrl;
                    document.getElementById('imageResult').style.display = 'block';
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取二维码失败: ' + error);
                });
            }
        }
    </script>
</body>
</html> 