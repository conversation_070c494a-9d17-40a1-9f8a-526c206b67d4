package com.sky.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SellerRegisterDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String password;

    private String accountName;

    private String gender;

    private String phone;

    private String email;

    private int accountStatus;

    private String verificationCode;

    private String shopName;


    private String companyName;


    private String businessLicense;


    private LocalDate licenseValidity;


    private String companyIntro;


    private String contactPerson;


    private String contactPhone;


    private String province;


    private String city;


    private String district;


    private String addressDetail;


    private LocalDateTime createdTime;


    private LocalDateTime lastUpdated;

    private Long IDCard;

}