sky:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    host: ************
    port: 3306
    database: ry_mall
    username: user
    password: sharewharf
  redis:
    host: localhost
    port: 6379
    database: 0
  # 微信支付相关配置
wechat:
  pay:
    # 商户号（替换为实际的商户号）
    mchid: 777463044
    # 商户API证书序列号（替换为实际的商户证书序列号）
    mch-serial-no: 7B90DEE3ABB5B5BCF3048BC24F72C0A767BCE451
    # 商户私钥文件路径（只提供文件名，实际位于classpath:cert/目录下）
    private-key-path: apiclient_key.pem
    # APIv3密钥（替换为实际的APIv3密钥）
    apiV3-key: abcdefghigklmnopqrstuvwxyzabzdef
    # APPID（替换为实际的APPID）
    appid: wx2bd197bcb4b986a0
    # 微信支付回调通知地址（替换为实际的回调地址，使用备案域名）
    notify-url: https://your-domain.com/api/pay/wechat/notify
    # 跨境支付相关配置
    crossBorder:
      # 是否启用跨境支付
      enabled: true
      # 商户分类代码
      merchantCategoryCode: 4111
      # 交易类型
      tradeType: NATIVE
      # 货币类型
      currency: USD
      # 查询订单API基础URL
      queryApiBaseUrl: https://apihk.mch.weixin.qq.com/v3/global/transactions
