package com.sky.mapper;

import com.sky.entity.Buyer;
import com.sky.entity.EVPI;
import com.sky.entity.Seller;
import com.sky.vo.EVPIVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface LoginMapper {

    @Select("select * from seller where account_name = #{accountName}")
    Seller getByName(String accountName);

    @Select("select * from seller where email = #{email}")
    Seller getByEmail(String email);


    void save(Seller seller);


    void update(Seller seller);

    /**
     * 根据营业执照查询商家是否存在
     * @param businessImgUrl
     * @return
     */
    @Select("select count(1) from shop_info where business_img_url = #{businessImgUrl}")
    boolean existsByBusinessImg(String businessImgUrl);


    void saveData(EVPI evpi);

    @Select("select * from shop_info where seller_id = #{id}")
    EVPI getData(Long id);

    @Select("select count(1) from shop_info where warehouse_img_url1 = #{warehouseImgUrl1}")
    boolean existsImg1(String warehouseImgUrl1);

    @Select("select count(1) from shop_info where warehouse_img_url2 = #{warehouseImgUrl2}")
    boolean existsImg2(String warehouseImgUrl2);

    @Select("select count(1) from shop_info where warehouse_img_url3 = #{warehouseImgUrl3}")
    boolean existsImg3(String warehouseImgUrl3);

    @Select("select count(1) from shop_info where seller_id = #{sellerId}")
    int selectBySellerId(Long sellerId);

    @Select("select count(1) from shop_info where id_card1 = #{idCard1}")
    boolean existsByIDCardImg1(String idCard1);

    @Select("select count(1) from shop_info where id_card2 = #{idCard2}")
    boolean existsByIDCardImg2(String idCard2);

    @Select("select * from ry_mall.seller where account_name = #{accountName}")
    Seller getByAccountName(String accountName);

    @Select("select * from ry_mall.seller where id = #{id}")
    Seller getByid(Long id);
}
