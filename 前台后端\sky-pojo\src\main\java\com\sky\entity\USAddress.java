package com.sky.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_address")
public class USAddress {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField("user_id")
    private Long userId;
    private String name;
    private String street;
    private String city;
    private String state;
    @TableField("zip_code")
    private String zipCode;

    @TableField("phone_number")
    private String phoneNumber;

    @TableField("addressDetail")
    private String addressDetail; // 详细地址

    @TableField("`default`")  // 或 @TableField("is_default")
    @Builder.Default
    private Long isDefault = 0L;   // 默认值为0（非默认地址），避免空指针异常
}