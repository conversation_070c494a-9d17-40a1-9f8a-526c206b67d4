package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.sky.entity.OrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单详情Mapper接口
 */
@Mapper
public interface OrderDetailMapper extends BaseMapper<OrderDetail> {

    /**
     * 批量插入订单详情
     * @param orderDetails 订单详情列表
     * @return 影响行数
     */
    int batchInsert(@Param("orderDetails") List<OrderDetail> orderDetails);
    
    /**
     * 根据订单ID查询订单详情
     * @param orderId 订单ID
     * @return 订单详情列表
     */
    List<OrderDetail> getByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 根据订单ID删除订单详情
     * @param orderId 订单ID
     * @return 影响行数
     */
    int deleteByOrderId(@Param("orderId") Long orderId);
} 