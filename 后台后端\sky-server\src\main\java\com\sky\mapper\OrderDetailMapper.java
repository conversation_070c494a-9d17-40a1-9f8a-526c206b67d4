package com.sky.mapper;

import com.sky.entity.OrderDetail;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface OrderDetailMapper {

    /**
     * 根据ID查询订单明细
     */
    @Select("SELECT * FROM order_detail WHERE id = #{id}")
    OrderDetail findById(Long id);

    /**
     * 根据订单ID查询订单明细列表
     */
    @Select("SELECT * FROM order_detail WHERE order_id = #{orderId}")
    List<OrderDetail> findByOrderId(@Param("orderId") Long orderId);

    /**
     * 插入订单明细
     */
    @Insert("INSERT INTO order_detail (order_id, product_id, product_name, product_image, product_spec, " +
            "quantity, unit_price, discount, subtotal, create_time, update_time) " +
            "VALUES (#{orderId}, #{productId}, #{productName}, #{productImage}, #{productSpec}, " +
            "#{quantity}, #{unitPrice}, #{discount}, #{subtotal}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(OrderDetail orderDetail);

    /**
     * 批量插入订单明细
     */
    void insertBatch(List<OrderDetail> orderDetails);

    /**
     * 更新订单明细
     */
    @Update("UPDATE order_detail SET product_id = #{productId}, product_name = #{productName}, " +
            "product_image = #{productImage}, product_spec = #{productSpec}, quantity = #{quantity}, " +
            "unit_price = #{unitPrice}, discount = #{discount}, subtotal = #{subtotal}, " +
            "update_time = #{updateTime} WHERE id = #{id}")
    void updateOrderDetail(OrderDetail orderDetail);

    /**
     * 根据订单ID删除订单明细
     */
    @Delete("DELETE FROM order_detail WHERE order_id = #{orderId}")
    void deleteByOrderId(Long orderId);
}
