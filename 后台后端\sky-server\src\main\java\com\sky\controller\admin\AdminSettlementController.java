package com.sky.controller.admin;

import com.github.pagehelper.PageInfo;
import com.sky.dto.SettlementCompleteDTO;
import com.sky.dto.SettlementQueryDTO;
import com.sky.result.Result;
import com.sky.service.SettlementService;
import com.sky.vo.SettlementInfoVO;
import com.sky.vo.SettlementSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台端回款管理接口
 */
@RestController
@RequestMapping("/admin/settlement")
@Api(tags = "平台端回款管理接口")
@CrossOrigin(origins = "*")
@Slf4j
public class AdminSettlementController {

    @Autowired
    private SettlementService settlementService;

    /**
     * 分页查询所有订单回款信息
     */
    @GetMapping("/orders")
    @ApiOperation("分页查询所有订单回款信息")
    public Result<PageInfo<SettlementInfoVO>> getAllSettlementOrders(SettlementQueryDTO queryDTO) {
        log.info("平台查询所有订单回款信息：{}", queryDTO);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 查询所有商家回款汇总信息
     */
    @GetMapping("/summary")
    @ApiOperation("查询所有商家回款汇总信息")
    public Result<List<SettlementSummaryVO>> getAllSettlementSummary() {
        log.info("查询所有商家回款汇总信息");
        
        List<SettlementSummaryVO> summaryList = settlementService.getAllSummary();
        return Result.success(summaryList);
    }

    /**
     * 查询待回款订单
     */
    @GetMapping("/pending")
    @ApiOperation("查询待回款订单")
    public Result<List<SettlementInfoVO>> getPendingSettlement() {
        log.info("查询待回款订单");
        
        List<SettlementInfoVO> pendingList = settlementService.getPendingSettlement();
        return Result.success(pendingList);
    }

    /**
     * 根据商家查询回款信息
     */
    @GetMapping("/seller/{sellerId}")
    @ApiOperation("根据商家查询回款信息")
    public Result<PageInfo<SettlementInfoVO>> getSettlementBySeller(
            @PathVariable Long sellerId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        log.info("根据商家查询回款信息，商家ID：{}", sellerId);
        
        SettlementQueryDTO queryDTO = new SettlementQueryDTO();
        queryDTO.setSellerId(sellerId);
        queryDTO.setPage(page);
        queryDTO.setPageSize(pageSize);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 根据商家查询回款汇总
     */
    @GetMapping("/seller/{sellerId}/summary")
    @ApiOperation("根据商家查询回款汇总")
    public Result<SettlementSummaryVO> getSettlementSummaryBySeller(@PathVariable Long sellerId) {
        log.info("根据商家查询回款汇总，商家ID：{}", sellerId);
        
        SettlementSummaryVO summary = settlementService.getSummaryBySellerId(sellerId);
        return Result.success(summary);
    }

    /**
     * 根据回款状态查询订单
     */
    @GetMapping("/orders/status/{status}")
    @ApiOperation("根据回款状态查询订单")
    public Result<PageInfo<SettlementInfoVO>> getOrdersByStatus(
            @PathVariable Integer status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        log.info("根据状态查询回款订单，状态：{}", status);
        
        SettlementQueryDTO queryDTO = new SettlementQueryDTO();
        queryDTO.setSettlementStatus(status);
        queryDTO.setPage(page);
        queryDTO.setPageSize(pageSize);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 标记回款完成
     */
    @PutMapping("/complete")
    @ApiOperation("标记回款完成")
    public Result<String> completeSettlement(@Valid @RequestBody SettlementCompleteDTO completeDTO) {
        log.info("标记回款完成：{}", completeDTO);
        
        settlementService.completeSettlement(completeDTO);
        return Result.success("回款完成标记成功");
    }

    /**
     * 手动更新回款状态
     */
    @PostMapping("/update-status")
    @ApiOperation("手动更新回款状态")
    public Result<String> updateSettlementStatus() {
        log.info("手动更新回款状态");
        
        settlementService.updateSettlementStatus();
        return Result.success("回款状态更新成功");
    }

    /**
     * 根据账单周期查询订单
     */
    @GetMapping("/orders/cycle/{billingCycle}")
    @ApiOperation("根据账单周期查询订单")
    public Result<PageInfo<SettlementInfoVO>> getOrdersByCycle(
            @PathVariable String billingCycle,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        log.info("根据账单周期查询回款订单，周期：{}", billingCycle);
        
        SettlementQueryDTO queryDTO = new SettlementQueryDTO();
        queryDTO.setBillingCycle(billingCycle);
        queryDTO.setPage(page);
        queryDTO.setPageSize(pageSize);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 为指定订单创建回款信息
     */
    @PostMapping("/create/{orderId}")
    @ApiOperation("为指定订单创建回款信息")
    public Result<String> createSettlementInfo(@PathVariable Long orderId) {
        log.info("为订单创建回款信息，订单ID：{}", orderId);
        
        settlementService.createSettlementInfo(orderId);
        return Result.success("回款信息创建成功");
    }
}
