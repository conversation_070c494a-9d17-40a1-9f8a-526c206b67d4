package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.PaymentAccountQueryDTO;
import com.sky.entity.SellerPaymentAccount;
import com.sky.vo.PaymentAccountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商家收账账户信息Mapper
 */
@Mapper
public interface SellerPaymentAccountMapper {

    /**
     * 插入收账账户信息
     */
    void insert(SellerPaymentAccount sellerPaymentAccount);

    /**
     * 根据ID查询收账账户信息
     */
    SellerPaymentAccount selectById(Long id);

    /**
     * 根据商家ID和账户ID查询收账账户信息
     */
    SellerPaymentAccount selectByIdAndSellerId(@Param("id") Long id, @Param("sellerId") Long sellerId);

    /**
     * 更新收账账户信息
     */
    void update(SellerPaymentAccount sellerPaymentAccount);

    /**
     * 根据ID删除收账账户信息
     */
    void deleteById(Long id);

    /**
     * 根据商家ID和账户ID删除收账账户信息
     */
    void deleteByIdAndSellerId(@Param("id") Long id, @Param("sellerId") Long sellerId);

    /**
     * 分页查询收账账户信息
     */
    Page<PaymentAccountVO> selectPage(PaymentAccountQueryDTO queryDTO);

    /**
     * 查询商家的所有收账账户
     */
    List<PaymentAccountVO> selectBySellerId(@Param("sellerId") Long sellerId);

    /**
     * 查询商家的默认收账账户
     */
    PaymentAccountVO selectDefaultBySellerId(@Param("sellerId") Long sellerId);

    /**
     * 清除商家的所有默认账户标记
     */
    void clearDefaultBySellerId(@Param("sellerId") Long sellerId);

    /**
     * 设置默认账户
     */
    void setDefault(@Param("id") Long id, @Param("sellerId") Long sellerId);

    /**
     * 更新账户状态
     */
    void updateStatus(@Param("id") Long id, @Param("sellerId") Long sellerId, @Param("accountStatus") Integer accountStatus);

    /**
     * 更新验证状态
     */
    void updateVerificationStatus(@Param("id") Long id, @Param("sellerId") Long sellerId, @Param("verificationStatus") Integer verificationStatus);

    /**
     * 检查账户号码是否已存在
     */
    int countByAccountNumber(@Param("sellerId") Long sellerId, @Param("accountNumber") String accountNumber, @Param("accountType") Integer accountType, @Param("excludeId") Long excludeId);

    /**
     * 统计商家的账户数量
     */
    int countBySellerId(@Param("sellerId") Long sellerId);

    /**
     * 统计商家已验证的账户数量
     */
    int countVerifiedBySellerId(@Param("sellerId") Long sellerId);
}
