package com.sky.mapper;

import com.sky.entity.MerchantProduct;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface MerchantProductMapper {
    @Select("SELECT * FROM merchant_product")
    List<MerchantProduct> findAll();

    @Select("SELECT * FROM merchant_product WHERE product_code = #{productCode}")
    List<MerchantProduct> findByProductCode(String productCode);

    @Select("SELECT * FROM merchant_product WHERE product_title LIKE CONCAT('%', #{productTitle}, '%')")
    List<MerchantProduct> findByProductTitle(String productTitle);

    @Select("SELECT * FROM merchant_product WHERE status = #{status}")
    List<MerchantProduct> findByStatus(String status);

    @Insert("INSERT INTO merchant_product (product_code, product_title, product_category, main_image, price, product_description, status, nbs, created_at) " +
            "VALUES (#{productCode}, #{productTitle}, #{productCategory}, #{mainImage}, #{price}, #{productDescription}, 'active', #{nbs}, NOW())")
    void insert(MerchantProduct merchantProduct);

}