package com.sky.mapper;

import com.sky.annotation.AutoFill;
import com.sky.entity.Buyer;
import com.sky.entity.Employee;
import com.sky.enumeration.OperationType;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface BuyerMapper {

    /**
     * 根据用户名查询
     * @param accountName
     * @return
     */
    @Select("select * from buyer where account_name = #{accountName}")
    Buyer getByUsername(String accountName);

    /**
     * 根据id查询买家信息
     * @param id
     * @return
     */
    @Select("select * from buyer where id=#{id}")
    Buyer getByBuyerId(Long id);

    /**
     * 用户注册
     * @param buyer
     * @return
     */
    void save(Buyer buyer);

    /**
     * 更新用户信息
     * @param buyer
     */
    void update(Buyer buyer);

    @Select("select * from ry_mall.buyer where phone=#{phone}")
    Buyer getByBuyerPhone(String phone);
    @Select("select * from ry_mall.buyer where email=#{email}")
    Buyer getByBuyeremail(String email);
}
