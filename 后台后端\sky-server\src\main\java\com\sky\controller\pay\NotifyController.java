package com.sky.controller.pay;


import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sky.entity.PaymentRecord;
import com.sky.service.PaymentRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.io.InputStream;
import java.math.BigDecimal;
import java.security.KeyFactory;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.util.Map;
import java.security.PublicKey;
import java.security.Signature;
import java.util.Base64;


@RestController
@Api(tags = "微信支付回调相关接口")
@CrossOrigin(origins = "*")
@Slf4j
@RequestMapping("/pay")
public class NotifyController {
    @Autowired
    private PaymentRecordService paymentRecordService;

    /**
     * 微信支付回调接口
     * 微信支付平台在用户支付完成后会向该接口推送支付结果
     * @param body 微信推送的回调原始数据（JSON字符串）
     * @param signature 微信签名
     * @param timestamp 时间戳
     * @param nonce 随机串
     * @param serial 证书序列号
     * @return 微信平台要求的响应字符串（success/fail）
     */
    @PostMapping("/notify")
    public String wechatPayNotify(
            @RequestBody String body,
            @RequestHeader("Wechatpay-Signature") String signature,
            @RequestHeader("Wechatpay-Timestamp") String timestamp,
            @RequestHeader("Wechatpay-Nonce") String nonce,
            @RequestHeader("Wechatpay-Serial") String serial
    ) {
        log.info("收到微信支付回调: {}", body);
        // 1. 拼接待签名字符串
        String message = timestamp + "\n" + nonce + "\n" + body + "\n";
        // 2. 获取微信平台证书公钥（此处需替换为你实际的证书管理方式）
        PublicKey wechatPublicKey = getWechatPublicKey(serial);
        // 3. 验证签名
        boolean isValid = verifySignature(message, signature, wechatPublicKey);
        if (!isValid) {
            log.error("微信支付回调签名校验失败");
            return "fail";
        }
        // 4. 解析body为Map（实际可用更安全的方式）
        Map<String, Object> notifyData = parseJsonToMap(body);
        try {
            Long orderId = Long.valueOf(notifyData.getOrDefault("orderId", "0").toString());
            String paymentMethod = "1"; // 1代表微信支付
            BigDecimal amount = new BigDecimal(notifyData.getOrDefault("amount", "0").toString());
            String transactionStatus = notifyData.getOrDefault("trade_state", "SUCCESS").toString();
            LocalDateTime transactionTime = LocalDateTime.now();
            PaymentRecord paymentRecord = PaymentRecord.builder()
                    .orderId(orderId)
                    .paymentMethod(paymentMethod)
                    .amount(amount)
                    .transactionStatus(transactionStatus)
                    .transactionTime(transactionTime)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            paymentRecordService.save(paymentRecord);
            log.info("支付记录保存成功: {}", paymentRecord);
            return "success";
        } catch (Exception e) {
            log.error("微信支付回调处理异常", e);
            return "fail";
        }
    }

    /**
     * 验证微信支付签名
     */
    public boolean verifySignature(String message, String signature, PublicKey publicKey) {
        try {
            Signature sign = Signature.getInstance("com");
            sign.initVerify(publicKey);
            sign.update(message.getBytes("UTF-8"));
            return sign.verify(Base64.getDecoder().decode(signature));
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取微信平台证书公钥（此处为示例，实际应从本地或远程获取并缓存）
     */
    private PublicKey getWechatPublicKey(String serial) {
        // TODO: 平台公钥路径 ，实现证书下载与缓存，按serial号获取对应公钥
        // 假设所有公钥都放在resources目录下，文件名如 apiclient_key.pem 或 wechatpay_{serial}.pem
        String fileName = "平台公钥路径"; // 或 "wechatpay_" + serial + ".pem"
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(fileName)) {
            if (is == null) {
                throw new RuntimeException("未找到微信平台公钥文件: " + fileName);
            }
            StringBuilder sb = new StringBuilder();
            try (java.io.BufferedReader br = new java.io.BufferedReader(new java.io.InputStreamReader(is))) {
                String line;
                boolean inKey = false;
                while ((line = br.readLine()) != null) {
                    if (line.contains("BEGIN PUBLIC KEY")) {
                        inKey = true;
                        continue;
                    }
                    if (line.contains("END PUBLIC KEY")) {
                        break;
                    }
                    if (inKey) {
                        sb.append(line.trim());
                    }
                }
            }
            byte[] keyBytes = Base64.getDecoder().decode(sb.toString());
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("读取或解析微信平台公钥失败: " + fileName, e);
        }
    }

    /**
     * 简单JSON转Map（建议用更安全的方式）
     */
    private Map<String, Object> parseJsonToMap(String json) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(json, Map.class);
        } catch (Exception e) {
            throw new RuntimeException("微信回调JSON解析失败", e);
        }
    }
}
