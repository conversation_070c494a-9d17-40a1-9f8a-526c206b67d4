package com.sky.enumeration;

public enum RoleEnum {
    SUPER_ADMIN(1, "超级管理员", "系统最高权限管理员"),
    ADMIN(2, "管理员", "普通管理员角色"),
    STAFF(3, "普通员工", "普通员工权限");

    private final Integer roleId;
    private final String roleName;
    private final String description;

    RoleEnum(Integer roleId, String roleName, String description) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.description = description;
    }

    // Getters
    public Integer getRoleId() { return roleId; }
    public String getRoleName() { return roleName; }
    public String getDescription() { return description; }

    public static RoleEnum getByRoleId(Integer roleId) {
        for (RoleEnum value : values()) {
            if (value.getRoleId().equals(roleId)) {
                return value;
            }
        }
        return null;
    }
}
