package com.sky.service.impl;

import com.sky.dto.RefundApplicationDTO;
import com.sky.dto.RefundApprovalDTO;
import com.sky.dto.RefundQueryDTO;
import com.sky.dto.WechatRefundRequestDTO;
import com.sky.entity.Orders;
import com.sky.entity.RefundApplication;
import com.sky.entity.RefundApprovalRecord;
import com.sky.exception.OrderException;
import com.sky.exception.RefundException;
import com.sky.mapper.OrdersMapper;
import com.sky.mapper.RefundApplicationMapper;
import com.sky.mapper.RefundApprovalRecordMapper;
import com.sky.service.RefundApplicationService;
import com.sky.service.WechatPayService;
import com.sky.service.AdminAccountService;
import com.sky.entity.AdminAccount;
import com.sky.vo.RefundApplicationVO;
import com.sky.vo.RefundStatistics;
import com.sky.vo.WechatRefundResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 退款申请服务实现类
 */
@Service
@Slf4j
public class RefundApplicationServiceImpl implements RefundApplicationService {

    @Autowired
    private RefundApplicationMapper refundApplicationMapper;

    @Autowired
    private RefundApprovalRecordMapper refundApprovalRecordMapper;

    @Autowired
    private WechatPayService wechatPayService;

    @Autowired
    private OrdersMapper ordersMapper;

    @Autowired
    private AdminAccountService adminAccountService;

    /**
     * 申请退款
     */
    @Override
    @Transactional
    public RefundApplicationVO applyRefund(RefundApplicationDTO refundApplicationDTO, Long buyerId) {
        log.info("用户申请退款，buyerId: {}, dto: {}", buyerId, refundApplicationDTO);

        // 1. 检查退款条件
        RefundCheckResult checkResult = checkRefundEligibility(refundApplicationDTO.getOrderId(), buyerId);
        if (!checkResult.isCanRefund()) {
            throw new RefundException(checkResult.getReason());
        }

        // 2. 验证退款金额
        if (refundApplicationDTO.getRefundAmount().compareTo(checkResult.getMaxRefundAmount()) > 0) {
            throw new RefundException("退款金额不能超过订单金额");
        }

        // 3. 查询订单信息
        Orders order = ordersMapper.selectById(refundApplicationDTO.getOrderId());
        if (order == null) {
            throw new OrderException("订单不存在");
        }

        // 4. 创建退款申请
        RefundApplication refundApplication = RefundApplication.builder()
                .refundNo(generateRefundNo())
                .orderId(refundApplicationDTO.getOrderId())
                .orderNumber(order.getNumber())
                .buyerId(buyerId)
                .refundAmount(refundApplicationDTO.getRefundAmount())
                .refundReason(refundApplicationDTO.getRefundReason())
                .refundType(refundApplicationDTO.getRefundType())
                .applicationStatus(RefundApplication.STATUS_PENDING)
                .needApproval(checkResult.isNeedApproval() ? 1 : 0)
                .approvalStatus(checkResult.isNeedApproval() ? RefundApplication.APPROVAL_PENDING : null)
                .refundMethod(refundApplicationDTO.getRefundMethod())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        refundApplicationMapper.insert(refundApplication);

        // 5. 更新订单退款状态
        ordersMapper.updateRefundStatus(order.getId(), 1, null, null, LocalDateTime.now());

        // 6. 如果不需要审核，直接处理退款
        if (!checkResult.isNeedApproval()) {
            try {
                processRefund(refundApplication.getId());
            } catch (Exception e) {
                log.error("自动处理退款失败", e);
                // 更新申请状态为待处理，需要人工干预
                refundApplicationMapper.updateApplicationStatus(
                        refundApplication.getId(), 
                        RefundApplication.STATUS_PENDING, 
                        LocalDateTime.now()
                );
            }
        }

        // 7. 返回结果
        return getRefundApplicationById(refundApplication.getId());
    }

    /**
     * 取消退款申请
     */
    @Override
    @Transactional
    public boolean cancelRefundApplication(Long refundApplicationId, Long buyerId) {
        log.info("取消退款申请，refundApplicationId: {}, buyerId: {}", refundApplicationId, buyerId);

        RefundApplication refundApplication = refundApplicationMapper.selectById(refundApplicationId);
        if (refundApplication == null) {
            throw new RefundException("退款申请不存在");
        }

        if (!refundApplication.getBuyerId().equals(buyerId)) {
            throw new RefundException("无权限操作此退款申请");
        }

        if (!refundApplication.canCancel()) {
            throw new RefundException("当前状态不允许取消");
        }

        // 更新申请状态为已取消
        int result = refundApplicationMapper.updateApplicationStatus(
                refundApplicationId, 
                RefundApplication.STATUS_CANCELLED, 
                LocalDateTime.now()
        );

        // 更新订单退款状态
        if (result > 0) {
            ordersMapper.updateRefundStatus(refundApplication.getOrderId(), 0, null, null, LocalDateTime.now());
        }

        return result > 0;
    }

    /**
     * 根据ID查询退款申请详情
     */
    @Override
    public RefundApplicationVO getRefundApplicationById(Long refundApplicationId) {
        return refundApplicationMapper.selectDetailById(refundApplicationId);
    }

    /**
     * 根据退款申请单号查询详情
     */
    @Override
    public RefundApplicationVO getRefundApplicationByNo(String refundNo) {
        RefundApplication refundApplication = refundApplicationMapper.selectByRefundNo(refundNo);
        if (refundApplication == null) {
            return null;
        }
        return getRefundApplicationById(refundApplication.getId());
    }

    /**
     * 分页查询退款申请
     */
    @Override
    public List<RefundApplicationVO> pageQuery(RefundQueryDTO queryDTO) {
        int offset = (queryDTO.getPage() - 1) * queryDTO.getPageSize();
        return refundApplicationMapper.selectPageWithDetails(
                queryDTO.getBuyerId(),
                queryDTO.getApplicationStatus(),
                queryDTO.getNeedApproval(),
                queryDTO.getApprovalStatus(),
                queryDTO.getStartTime(),
                queryDTO.getEndTime(),
                offset,
                queryDTO.getPageSize()
        );
    }

    /**
     * 统计退款申请数量
     */
    @Override
    public Long countRefundApplications(RefundQueryDTO queryDTO) {
        return refundApplicationMapper.countRefundApplications(
                queryDTO.getBuyerId(),
                queryDTO.getApplicationStatus(),
                queryDTO.getNeedApproval(),
                queryDTO.getApprovalStatus(),
                queryDTO.getStartTime(),
                queryDTO.getEndTime()
        );
    }

    /**
     * 查询用户的退款申请列表
     */
    @Override
    public List<RefundApplicationVO> getUserRefundApplications(Long buyerId, Integer page, Integer pageSize) {
        RefundQueryDTO queryDTO = RefundQueryDTO.builder()
                .buyerId(buyerId)
                .page(page)
                .pageSize(pageSize)
                .build();
        return pageQuery(queryDTO);
    }

    /**
     * 查询待审核的退款申请
     */
    @Override
    public List<RefundApplicationVO> getPendingApprovalApplications(Integer page, Integer pageSize) {
        RefundQueryDTO queryDTO = RefundQueryDTO.builder()
                .needApproval(1)
                .approvalStatus(RefundApplication.APPROVAL_PENDING)
                .page(page)
                .pageSize(pageSize)
                .build();
        return pageQuery(queryDTO);
    }

    /**
     * 审核退款申请
     */
    @Override
    @Transactional
    public boolean approveRefundApplication(RefundApprovalDTO approvalDTO, Long approverId) {
        log.info("审核退款申请，dto: {}, approverId: {}", approvalDTO, approverId);

        RefundApplication refundApplication = refundApplicationMapper.selectById(approvalDTO.getRefundApplicationId());
        if (refundApplication == null) {
            throw new RefundException("退款申请不存在");
        }

        if (!refundApplication.isNeedApproval()) {
            throw new RefundException("此退款申请无需审核");
        }

        if (refundApplication.getApprovalStatus() != null &&
            !refundApplication.getApprovalStatus().equals(RefundApplication.APPROVAL_PENDING)) {
            throw new RefundException("退款申请已审核，无法重复审核");
        }

        LocalDateTime now = LocalDateTime.now();

        // 1. 获取审核人姓名
        String approverName = getApproverName(approvalDTO.getApproverName(), approverId);

        // 2. 如果是审核拒绝，直接处理
        if (approvalDTO.getApprovalResult().equals(RefundApprovalRecord.RESULT_REJECTED)) {
            return handleRefundRejection(approvalDTO, approverId, approverName, refundApplication, now);
        }

        // 3. 如果是审核通过，先调用微信退款API，成功后再更新数据库
        if (approvalDTO.getApprovalResult().equals(RefundApprovalRecord.RESULT_APPROVED)) {
            return callWechatRefundAPI(refundApplication, approvalDTO, approverId, approverName, now);
        }

        throw new RefundException("无效的审核结果");
    }



    /**
     * 处理退款审核拒绝
     */
    private boolean handleRefundRejection(RefundApprovalDTO approvalDTO, Long approverId, String approverName,
                                        RefundApplication refundApplication, LocalDateTime now) {
        log.info("处理退款审核拒绝，refundApplicationId: {}", approvalDTO.getRefundApplicationId());

        // 1. 创建审核记录
        RefundApprovalRecord approvalRecord = RefundApprovalRecord.builder()
                .refundApplicationId(approvalDTO.getRefundApplicationId())
                .refundNo(refundApplication.getRefundNo())
                .approverId(approverId)
                .approverName(approverName)
                .approvalResult(approvalDTO.getApprovalResult())
                .approvalRemark(approvalDTO.getApprovalRemark())
                .createTime(now)
                .build();

        refundApprovalRecordMapper.insert(approvalRecord);

        // 2. 更新退款申请的审核信息
        refundApplicationMapper.updateApprovalInfo(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.APPROVAL_REJECTED,
                approverId,
                now,
                approvalDTO.getApprovalRemark(),
                now
        );

        // 3. 更新申请状态为已拒绝
        refundApplicationMapper.updateApplicationStatus(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.STATUS_REJECTED,
                now
        );

        // 4. 更新订单退款状态为正常
        ordersMapper.updateRefundStatus(refundApplication.getOrderId(), 0, null, null, now);

        log.info("退款审核拒绝处理成功，refundApplicationId: {}", approvalDTO.getRefundApplicationId());
        return true;
    }

    /**
     * 处理退款（调用微信退款接口）
     */
    @Override
    @Transactional
    public boolean processRefund(Long refundApplicationId) {
        log.info("处理退款，refundApplicationId: {}", refundApplicationId);

        RefundApplication refundApplication = refundApplicationMapper.selectById(refundApplicationId);
        if (refundApplication == null) {
            throw new RefundException("退款申请不存在");
        }

        // 更新状态为退款中
        refundApplicationMapper.updateApplicationStatus(
                refundApplicationId,
                RefundApplication.STATUS_REFUNDING,
                LocalDateTime.now()
        );

        try {
            // 调用微信退款接口（简化版本，用于自动处理）
            boolean refundSuccess = callWechatRefundAPISimple(refundApplication);

            if (refundSuccess) {
                log.info("微信退款接口调用成功");

                // 更新状态为退款成功
                refundApplicationMapper.updateApplicationStatus(
                        refundApplicationId,
                        RefundApplication.STATUS_REFUND_SUCCESS,
                        LocalDateTime.now()
                );

                // 更新退款完成信息
                refundApplicationMapper.updateRefundCompleteInfo(
                        refundApplicationId,
                        refundApplication.getRefundAmount(),
                        LocalDateTime.now(),
                        LocalDateTime.now()
                );

                // 更新订单退款状态为全额退款
                ordersMapper.updateRefundStatus(refundApplication.getOrderId(), 3,
                        refundApplication.getRefundAmount(), LocalDateTime.now(), LocalDateTime.now());

                log.info("退款处理成功，refundApplicationId: {}", refundApplicationId);
                return true;
            } else {
                log.error("微信退款接口调用失败");
                // 更新状态为退款失败
                refundApplicationMapper.updateApplicationStatus(
                        refundApplicationId,
                        RefundApplication.STATUS_REFUND_FAILED,
                        LocalDateTime.now()
                );
                return false;
            }

        } catch (Exception e) {
            log.error("退款处理失败，refundApplicationId: {}", refundApplicationId, e);

            // 更新状态为退款失败
            refundApplicationMapper.updateApplicationStatus(
                    refundApplicationId,
                    RefundApplication.STATUS_REFUND_FAILED,
                    LocalDateTime.now()
            );

            throw new RefundException("退款处理失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新退款状态（用于微信回调）
     */
    @Override
    @Transactional
    public boolean updateRefundStatus(String refundNo, Integer refundStatus, BigDecimal actualRefundAmount) {
        log.info("更新退款状态，refundNo: {}, refundStatus: {}, actualRefundAmount: {}",
                refundNo, refundStatus, actualRefundAmount);

        RefundApplication refundApplication = refundApplicationMapper.selectByRefundNo(refundNo);
        if (refundApplication == null) {
            log.warn("退款申请不存在，refundNo: {}", refundNo);
            return false;
        }

        // 更新申请状态
        refundApplicationMapper.updateApplicationStatus(
                refundApplication.getId(),
                refundStatus,
                LocalDateTime.now()
        );

        // 如果退款成功，更新退款完成信息
        if (refundStatus.equals(RefundApplication.STATUS_REFUND_SUCCESS)) {
            refundApplicationMapper.updateRefundCompleteInfo(
                    refundApplication.getId(),
                    actualRefundAmount,
                    LocalDateTime.now(),
                    LocalDateTime.now()
            );

            // 更新订单退款状态
            ordersMapper.updateRefundStatus(refundApplication.getOrderId(), 3,
                    actualRefundAmount, LocalDateTime.now(), LocalDateTime.now());
        }

        return true;
    }

    /**
     * 检查订单是否可以申请退款
     */
    @Override
    public RefundCheckResult checkRefundEligibility(Long orderId, Long buyerId) {
        log.info("检查退款条件，orderId: {}, buyerId: {}", orderId, buyerId);

        // 1. 查询订单信息
        Orders order = ordersMapper.selectById(orderId);
        if (order == null) {
            return new RefundCheckResult(false, false, "订单不存在", BigDecimal.ZERO);
        }

        // 2. 检查订单所有者
        if (!order.getBuyerId().equals(buyerId)) {
            return new RefundCheckResult(false, false, "无权限操作此订单", BigDecimal.ZERO);
        }

        // 3. 检查订单状态
        if (order.getStatus().equals(Orders.STATUS_CANCELLED)) {
            return new RefundCheckResult(false, false, "订单已取消", BigDecimal.ZERO);
        }

        // 4. 检查是否已有退款申请
        if (order.getRefundStatus() != null && order.getRefundStatus() > 0) {
            return new RefundCheckResult(false, false, "订单已有退款申请", BigDecimal.ZERO);
        }

        // 5. 根据订单状态判断是否需要审核
        boolean needApproval = false;
        String reason = "可以申请退款";

        if (order.getStatus().equals(Orders.STATUS_SHIPPED)) {
            // 已发货，需要审核
            needApproval = true;
            reason = "订单已发货，需要审核";
        } else if (order.getStatus().equals(Orders.STATUS_COMPLETED)) {
            // 已完成，需要审核
            needApproval = true;
            reason = "订单已完成，需要审核";
        } else if (order.getStatus().equals(Orders.STATUS_PENDING_PAYMENT)) {
            // 待付款，不能退款
            return new RefundCheckResult(false, false, "订单未付款，无法退款", BigDecimal.ZERO);
        }

        return new RefundCheckResult(true, needApproval, reason, order.getAmount());
    }

    /**
     * 调用微信退款API并根据状态处理后续逻辑
     * @param refundApplication 退款申请
     * @param approvalDTO 审核DTO
     * @param approverId 审核人ID
     * @param approverName 审核人姓名
     * @param now 当前时间
     * @return 是否成功
     * @throws RefundException 退款异常
     */
    private boolean callWechatRefundAPI(RefundApplication refundApplication, RefundApprovalDTO approvalDTO,
                                      Long approverId, String approverName, LocalDateTime now) throws RefundException {
        try {
            log.info("调用微信退款API，退款单号：{}，退款金额：{}",
                    refundApplication.getRefundNo(), refundApplication.getRefundAmount());

            // 查询订单信息获取微信支付的商户订单号
            Orders order = ordersMapper.selectById(refundApplication.getOrderId());
            if (order == null) {
                throw new RefundException("订单不存在，无法退款");
            }

            // 检查订单是否有微信支付的商户订单号
            if (order.getPaymentTransactionId() == null || order.getPaymentTransactionId().trim().isEmpty()) {
                throw new RefundException("订单没有微信支付的商户订单号，无法退款");
            }

            // 构建微信退款请求
            WechatRefundRequestDTO refundRequest = WechatRefundRequestDTO.builder()
                    .outTradeNo(order.getPaymentTransactionId()) // 使用微信支付时的商户订单号
                    .outRefundNo(refundApplication.getRefundNo()) // 商户退款单号
                    .refundAmount(refundApplication.getRefundAmount().multiply(new BigDecimal(100)).intValue()) // 转换为分
                    .totalAmount(order.getAmount().multiply(new BigDecimal(100)).intValue()) // 订单总金额（分）
                    .currency("USD") // 使用美元
                    .reason(refundApplication.getRefundReason())
                    .build();

            // 调用微信退款服务
            WechatRefundResponseVO response = wechatPayService.applyRefund(refundRequest);

            if (response != null && isRefundSuccess(response)) {
                String refundId = response.getId() != null ? response.getId() : response.getRefundId();
                log.info("微信退款申请提交成功，退款单号：{}，微信退款单号：{}",
                        refundApplication.getRefundNo(), refundId);

                // 详细的退款信息日志
                logRefundDetails(response, refundApplication, order);

                // 查询退款状态进行确认
                String finalStatus = queryAndDetermineRefundStatus(response.getOutRefundNo());
                log.info("最终退款状态: {}", finalStatus);

                // 根据实际状态决定如何处理
                return handleRefundByStatus(finalStatus, refundApplication, order, approvalDTO, approverId, approverName, now);

            } else {
                String errorMsg = response != null ? response.toString() : "响应为空";
                throw new RefundException("微信退款API调用失败：" + errorMsg);
            }

        } catch (RefundException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("调用微信退款API异常", e);

            // 根据异常类型提供更具体的错误信息
            String errorMessage = "微信退款API调用失败";
            if (e.getMessage() != null) {
                if (e.getMessage().contains("私钥文件不存在")) {
                    errorMessage = "微信支付配置错误：私钥文件不存在";
                } else if (e.getMessage().contains("商户号未配置")) {
                    errorMessage = "微信支付配置错误：商户号未配置";
                } else if (e.getMessage().contains("网络")) {
                    errorMessage = "网络连接失败，请检查网络连接";
                } else {
                    errorMessage = "微信退款API调用失败：" + e.getMessage();
                }
            }

            throw new RefundException(errorMessage, e);
        }
    }

    /**
     * 调用微信退款API（简化版本，用于自动处理）
     * @param refundApplication 退款申请
     * @return 是否成功
     * @throws RefundException 退款异常
     */
    private boolean callWechatRefundAPISimple(RefundApplication refundApplication) throws RefundException {
        try {
            log.info("调用微信退款API（自动处理），退款单号：{}，退款金额：{}",
                    refundApplication.getRefundNo(), refundApplication.getRefundAmount());

            // 查询订单信息获取微信支付的商户订单号
            Orders order = ordersMapper.selectById(refundApplication.getOrderId());
            if (order == null) {
                throw new RefundException("订单不存在，无法退款");
            }

            // 检查订单是否有微信支付的商户订单号
            if (order.getPaymentTransactionId() == null || order.getPaymentTransactionId().trim().isEmpty()) {
                throw new RefundException("订单没有微信支付的商户订单号，无法退款");
            }

            // 构建微信退款请求
            WechatRefundRequestDTO refundRequest = WechatRefundRequestDTO.builder()
                    .outTradeNo(order.getPaymentTransactionId())
                    .outRefundNo(refundApplication.getRefundNo())
                    .refundAmount(refundApplication.getRefundAmount().multiply(new BigDecimal(100)).intValue())
                    .totalAmount(order.getAmount().multiply(new BigDecimal(100)).intValue())
                    .currency("USD")
                    .reason(refundApplication.getRefundReason())
                    .build();

            // 调用微信退款服务
            WechatRefundResponseVO response = wechatPayService.applyRefund(refundRequest);

            if (response != null && isRefundSuccess(response)) {
                String refundId = response.getId() != null ? response.getId() : response.getRefundId();
                log.info("微信退款申请成功（自动处理），退款单号：{}，微信退款单号：{}",
                        refundApplication.getRefundNo(), refundId);
                return true;
            } else {
                String errorMsg = response != null ? response.toString() : "响应为空";
                throw new RefundException("微信退款API调用失败：" + errorMsg);
            }

        } catch (RefundException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用微信退款API异常（自动处理）", e);
            throw new RefundException("微信退款API调用失败：" + e.getMessage(), e);
        }
    }

    /**
     * 判断退款是否成功
     */
    private boolean isRefundSuccess(WechatRefundResponseVO response) {
        if (response == null) {
            return false;
        }

        // 检查是否有退款ID（新版API返回id字段，旧版API返回refund_id字段）
        boolean hasRefundId = (response.getId() != null && !response.getId().trim().isEmpty()) ||
                             (response.getRefundId() != null && !response.getRefundId().trim().isEmpty());

        // 检查状态（如果有status字段）
        boolean statusSuccess = response.getStatus() == null ||
                               "SUCCESS".equals(response.getStatus()) ||
                               "PROCESSING".equals(response.getStatus());

        // 检查是否有商户退款单号
        boolean hasOutRefundNo = response.getOutRefundNo() != null && !response.getOutRefundNo().trim().isEmpty();

        // 只要有退款ID和商户退款单号，就认为退款申请成功
        boolean isSuccess = hasRefundId && hasOutRefundNo && statusSuccess;

        log.debug("退款成功判断: hasRefundId={}, hasOutRefundNo={}, statusSuccess={}, result={}",
                 hasRefundId, hasOutRefundNo, statusSuccess, isSuccess);

        return isSuccess;
    }

    /**
     * 查询并确定退款状态
     */
    private String queryAndDetermineRefundStatus(String outRefundNo) {
        try {
            // 等待2秒后查询状态
            Thread.sleep(2000);

            WechatRefundResponseVO queryResponse = wechatPayService.queryRefund(outRefundNo);
            if (queryResponse != null) {
                String status = queryResponse.getStatus();
                log.info("退款状态查询结果: {}", status);
                logRefundDetails(queryResponse, null, null);
                return status;
            }

            log.warn("查询退款状态返回空响应");
            return "UNKNOWN";

        } catch (Exception e) {
            log.error("查询退款状态失败: {}", e.getMessage(), e);
            return "QUERY_FAILED";
        }
    }

    /**
     * 根据退款状态处理后续逻辑
     */
    private boolean handleRefundByStatus(String status, RefundApplication refundApplication, Orders order,
                                       RefundApprovalDTO approvalDTO, Long approverId, String approverName, LocalDateTime now) {

        switch (status) {
            case "SUCCESS":
                // 退款成功，更新为退款成功状态
                log.info("退款已成功完成，更新状态为退款成功");
                updateRefundToSuccess(refundApplication, order, approvalDTO, approverId, approverName, now);
                return true;

            case "PROCESSING":
                // 退款处理中，更新为退款中状态
                log.info("退款正在处理中，更新状态为退款中");
                updateRefundToProcessing(refundApplication, order, approvalDTO, approverId, approverName, now);
                return true;

            case "CLOSED":
            case "ABNORMAL":
                // 退款失败，更新为退款失败状态
                log.error("退款失败，状态: {}", status);
                updateRefundToFailed(refundApplication, approvalDTO, approverId, approverName, now, "微信退款失败，状态: " + status);
                throw new RefundException("微信退款失败，状态: " + status);

            default:
                // 未知状态，更新为退款中状态，等待后续处理
                log.warn("未知退款状态: {}，暂时标记为退款中", status);
                updateRefundToProcessing(refundApplication, order, approvalDTO, approverId, approverName, now);
                return true;
        }
    }

    /**
     * 记录详细的退款信息
     */
    private void logRefundDetails(WechatRefundResponseVO response, RefundApplication refundApplication, Orders order) {
        log.info("=== 退款详细信息 ===");
        log.info("商户订单号: {}", order.getPaymentTransactionId());
        log.info("订单原始金额: {} USD", order.getAmount());
        log.info("申请退款金额: {} USD", refundApplication.getRefundAmount());
        log.info("微信退款单号: {}", response.getId());
        log.info("商户退款单号: {}", response.getOutRefundNo());
        log.info("退款创建时间: {}", response.getCreateTime());

        if (response.getAmount() != null) {
            log.info("=== 退款金额信息 ===");
            log.info("退款金额: {} 分 ({})", response.getAmount().getRefund(), response.getAmount().getCurrency());
            log.info("订单总金额: {} 分", response.getAmount().getTotal());
            log.info("用户退款金额: {} 分 ({})", response.getAmount().getPayerRefund(), response.getAmount().getPayerCurrency());
            log.info("结算退款金额: {} 分 ({})", response.getAmount().getSettlementRefund(), response.getAmount().getSettlementRefund());

            if (response.getAmount().getExchangeRate() != null) {
                log.info("=== 汇率信息 ===");
                log.info("汇率类型: {}", response.getAmount().getExchangeRate().getType());
                log.info("汇率: {}", response.getAmount().getExchangeRate().getRate());

                // 计算实际退款金额
                if (response.getAmount().getPayerRefund() != null) {
                    double actualRefundCNY = response.getAmount().getPayerRefund() / 100.0;
                    log.info("用户实际收到退款: {} CNY", actualRefundCNY);
                }
            }
        }

        log.info("退款状态: {}", response.getStatus() != null ? response.getStatus() : "未返回状态");
        log.info("=== 退款信息结束 ===");
    }

    /**
     * 更新退款为成功状态
     */
    private void updateRefundToSuccess(RefundApplication refundApplication, Orders order,
                                     RefundApprovalDTO approvalDTO, Long approverId, String approverName, LocalDateTime now) {
        // 创建审核记录
        RefundApprovalRecord approvalRecord = RefundApprovalRecord.builder()
                .refundApplicationId(approvalDTO.getRefundApplicationId())
                .refundNo(refundApplication.getRefundNo())
                .approverId(approverId)
                .approverName(approverName)
                .approvalResult(approvalDTO.getApprovalResult())
                .approvalRemark(approvalDTO.getApprovalRemark())
                .createTime(now)
                .build();

        refundApprovalRecordMapper.insert(approvalRecord);

        // 更新退款申请的审核信息
        refundApplicationMapper.updateApprovalInfo(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.APPROVAL_APPROVED,
                approverId,
                now,
                approvalDTO.getApprovalRemark(),
                now
        );

        // 更新申请状态为退款成功
        refundApplicationMapper.updateApplicationStatus(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.STATUS_REFUND_SUCCESS,
                now
        );

        // 更新订单退款状态
        ordersMapper.updateRefundStatus(
                refundApplication.getOrderId(),
                3, // 全额退款
                refundApplication.getRefundAmount(),
                now,
                now
        );
    }

    /**
     * 更新退款为处理中状态
     */
    private void updateRefundToProcessing(RefundApplication refundApplication, Orders order,
                                        RefundApprovalDTO approvalDTO, Long approverId, String approverName, LocalDateTime now) {
        // 创建审核记录
        RefundApprovalRecord approvalRecord = RefundApprovalRecord.builder()
                .refundApplicationId(approvalDTO.getRefundApplicationId())
                .refundNo(refundApplication.getRefundNo())
                .approverId(approverId)
                .approverName(approverName)
                .approvalResult(approvalDTO.getApprovalResult())
                .approvalRemark(approvalDTO.getApprovalRemark() + " (退款处理中)")
                .createTime(now)
                .build();

        refundApprovalRecordMapper.insert(approvalRecord);

        // 更新退款申请的审核信息
        refundApplicationMapper.updateApprovalInfo(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.APPROVAL_APPROVED,
                approverId,
                now,
                approvalDTO.getApprovalRemark() + " (退款处理中)",
                now
        );

        // 更新申请状态为退款中
        refundApplicationMapper.updateApplicationStatus(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.STATUS_REFUNDING,
                now
        );
    }

    /**
     * 更新退款为失败状态
     */
    private void updateRefundToFailed(RefundApplication refundApplication,
                                    RefundApprovalDTO approvalDTO, Long approverId, String approverName,
                                    LocalDateTime now, String failureReason) {
        // 创建审核记录
        RefundApprovalRecord approvalRecord = RefundApprovalRecord.builder()
                .refundApplicationId(approvalDTO.getRefundApplicationId())
                .refundNo(refundApplication.getRefundNo())
                .approverId(approverId)
                .approverName(approverName)
                .approvalResult(approvalDTO.getApprovalResult())
                .approvalRemark(failureReason)
                .createTime(now)
                .build();

        refundApprovalRecordMapper.insert(approvalRecord);

        // 更新退款申请的审核信息
        refundApplicationMapper.updateApprovalInfo(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.APPROVAL_APPROVED,
                approverId,
                now,
                failureReason,
                now
        );

        // 更新申请状态为退款失败
        refundApplicationMapper.updateApplicationStatus(
                approvalDTO.getRefundApplicationId(),
                RefundApplication.STATUS_REFUND_FAILED,
                now
        );
    }

    /**
     * 生成退款申请单号
     */
    @Override
    public String generateRefundNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%04d", ThreadLocalRandom.current().nextInt(10000));
        return "RF" + timestamp + random;
    }

    /**
     * 获取审核人姓名
     * @param dtoApproverName DTO中的审核人姓名
     * @param approverId 审核人ID
     * @return 审核人姓名
     */
    private String getApproverName(String dtoApproverName, Long approverId) {
        // 如果DTO中有审核人姓名，直接使用
        if (dtoApproverName != null && !dtoApproverName.trim().isEmpty()) {
            return dtoApproverName;
        }

        // 如果没有，根据审核人ID查询
        if (approverId != null) {
            try {
                AdminAccount adminAccount = adminAccountService.getAdminById(approverId);
                if (adminAccount != null && adminAccount.getAccountName() != null) {
                    return adminAccount.getAccountName();
                }
            } catch (Exception e) {
                log.warn("查询管理员信息失败，approverId: {}, error: {}", approverId, e.getMessage());
            }
        }

        // 如果都失败了，使用默认值
        return "系统管理员";
    }

    /**
     * 获取退款统计信息
     */
    @Override
    public RefundStatistics getRefundStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("获取退款统计信息，startTime: {}, endTime: {}", startTime, endTime);

        RefundStatistics.RefundStatisticsBuilder builder = RefundStatistics.builder();

        // 1. 总申请数
        Long totalApplications = refundApplicationMapper.countRefundApplications(
                null, null, null, null, startTime, endTime);
        builder.totalApplications(totalApplications);

        // 2. 待审核数（需要审核且审核状态为待审核）
        Long pendingApprovals = refundApplicationMapper.countRefundApplications(
                null, null, 1, RefundApplication.APPROVAL_PENDING, startTime, endTime);
        builder.pendingApprovals(pendingApprovals);

        // 3. 已通过数（审核状态为审核通过）
        Long approvedApplications = refundApplicationMapper.countRefundApplications(
                null, null, 1, RefundApplication.APPROVAL_APPROVED, startTime, endTime);
        builder.approvedApplications(approvedApplications);

        // 4. 已拒绝数（审核状态为审核拒绝）
        Long rejectedApplications = refundApplicationMapper.countRefundApplications(
                null, null, 1, RefundApplication.APPROVAL_REJECTED, startTime, endTime);
        builder.rejectedApplications(rejectedApplications);

        // 5. 已完成退款数（申请状态为退款成功）
        Long completedRefunds = refundApplicationMapper.countRefundApplications(
                null, RefundApplication.STATUS_REFUND_SUCCESS, null, null, startTime, endTime);
        builder.completedRefunds(completedRefunds);

        // 6. 退款中数量（申请状态为退款中）
        Long refundingCount = refundApplicationMapper.countRefundApplications(
                null, RefundApplication.STATUS_REFUNDING, null, null, startTime, endTime);
        builder.refundingCount(refundingCount);

        // 7. 退款失败数量（申请状态为退款失败）
        Long refundFailedCount = refundApplicationMapper.countRefundApplications(
                null, RefundApplication.STATUS_REFUND_FAILED, null, null, startTime, endTime);
        builder.refundFailedCount(refundFailedCount);

        // 8. 已取消数量（申请状态为已取消）
        Long cancelledCount = refundApplicationMapper.countRefundApplications(
                null, RefundApplication.STATUS_CANCELLED, null, null, startTime, endTime);
        builder.cancelledCount(cancelledCount);

        RefundStatistics statistics = builder.build();
        log.info("退款统计信息: {}", statistics);

        return statistics;
    }
}
