package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 分类创建参数
 */
@Data
@ApiModel("分类创建请求")
public class CategoryCreateDTO {

    @ApiModelProperty(value = "分类名称", required = true, example = "手机")

    private String name;

    private Long parentId;

    private Integer sortOrder;
}