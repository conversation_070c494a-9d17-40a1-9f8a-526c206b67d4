package com.sky.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

@Data
@TableName("admin_account")
public class AdminAccount {
    private Long id;
    @TableField("accountName")
    private String accountName;
    private String email;
    private String phone;
    private String password;
    private Integer status;
    @TableField("createTime")
    private LocalDateTime createTime;
    @TableField("updateTime")
    private LocalDateTime updateTime;
    @TableField("lastLoginTime")
    private LocalDateTime lastLoginTime;
    @TableField("loginCount")
    private Long loginCount;
    private List<String> permissions;
    @TableField("roleId")
    private Long roleId;
    @TableField("createdBy")
    private Long createdBy;
    private String remark;
}
