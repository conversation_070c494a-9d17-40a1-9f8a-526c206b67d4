package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 17TRACK Webhook日志实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookLog implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;                        // 主键ID
    private String eventType;               // 事件类型
    private String trackingNumber;          // 物流单号
    private Integer carrierCode;            // 运输商代码
    private String requestBody;             // 请求体内容
    private Integer responseStatus;         // 响应状态码
    private String responseBody;            // 响应内容
    private String processStatus;           // 处理状态：pending/success/failed
    private String errorMessage;            // 错误信息
    private Integer retryCount;             // 重试次数
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime processTime;      // 处理时间

    // 事件类型常量
    public static final String EVENT_TYPE_TRACKING_UPDATED = "TRACKING_UPDATED";

    // 处理状态常量
    public static final String PROCESS_STATUS_PENDING = "pending";
    public static final String PROCESS_STATUS_SUCCESS = "success";
    public static final String PROCESS_STATUS_FAILED = "failed";
}
