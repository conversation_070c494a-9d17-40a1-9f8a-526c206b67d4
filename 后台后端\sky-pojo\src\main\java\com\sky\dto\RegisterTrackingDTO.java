package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 注册物流单号DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterTrackingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "物流单号不能为空")
    private String trackingNumber;          // 物流单号

    @NotNull(message = "运输商代码不能为空")
    private Integer carrierCode;            // 运输商代码

    private Long orderId;                   // 关联订单ID
    private String orderNumber;             // 订单号
    private String orderTime;               // 订单时间
    private String lang;                    // 翻译语言代码
    private String email;                   // 邮箱
    private String param;                   // 附加跟踪参数
    private Integer finalCarrier;           // 尾程运输商代码
    private Boolean autoDetection;          // 是否开启运输商自动检测
    private String tag;                     // 自定义标签
    private String remark;                  // 备注信息
}
