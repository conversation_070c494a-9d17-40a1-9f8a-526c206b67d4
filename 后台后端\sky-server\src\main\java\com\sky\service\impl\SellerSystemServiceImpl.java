package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.context.BaseContext;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.dto.MessageNotificationsDTO;
import com.sky.dto.SystemProgramsDTO;
import com.sky.entity.Category;
import com.sky.entity.MessageNotifications;
import com.sky.entity.SystemPrograms;
import com.sky.mapper.SellerSystemMapper;
import com.sky.result.PageResult;
import com.sky.service.SellerSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SellerSystemServiceImpl implements SellerSystemService {
    @Autowired
    private SellerSystemMapper sellerSystemMapper;

    /**
     * 分类分页查询
     * @param messageNotificationsDTO
     * @return
     */
    @Override
    public PageResult pageQuery(MessageNotificationsDTO messageNotificationsDTO) {

        PageHelper.startPage(messageNotificationsDTO.getPage(),messageNotificationsDTO.getPageSize());
        //下一条sql进行分页，自动加入limit关键字分页
        Page<MessageNotifications> page = sellerSystemMapper.pageQuery(messageNotificationsDTO);
        return new PageResult(page.getTotal(), page.getResult());

    }

    /**
     * 消息状态查询
     * @param status
     * @return
     */
    @Override
    public MessageNotifications getByStatus(String status) {
        MessageNotifications messageNotifications = new MessageNotifications();
        Long id = BaseContext.getCurrentId();
        int SellerId=sellerSystemMapper.getById(id);
        messageNotifications= sellerSystemMapper.getByStatus(SellerId,status);

        return messageNotifications;
    }

    @Override
    public PageResult pageSystemQuery(SystemProgramsDTO systemProgramsDTO) {
        PageHelper.startPage(systemProgramsDTO.getPage(),systemProgramsDTO.getPageSize());
        //下一条sql进行分页，自动加入limit关键字分页
        Page<SystemPrograms> page = sellerSystemMapper.pageSystemQuery(systemProgramsDTO);
        return new PageResult(page.getTotal(), page.getResult());
    }


}
