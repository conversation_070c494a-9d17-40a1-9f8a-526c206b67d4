package com.sky.service.impl;

import com.sky.service.TrackingService;
import com.sky.service.Track17ApiService;
import com.sky.dto.track17.*;
import com.sky.vo.Track17Response;
import com.sky.exception.LogisticsException;
import com.sky.tracking.model.courier.Courier;
import com.sky.config.Track17Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

/**
 * 物流跟踪服务实现
 */
@Service
@Slf4j
public class TrackingServiceImpl implements TrackingService {

    @Autowired
    private Track17ApiService track17ApiService;

    @Autowired
    private Track17Config track17Config;

    @Override
    public boolean validateTrackingNumber(String trackingNumber, String courierCode) {
        log.info("验证物流单号：{}，快递公司代码：{}", trackingNumber, courierCode);

        try {
            // 1. 基础格式验证
            if (trackingNumber == null || trackingNumber.trim().isEmpty()) {
                log.warn("物流单号为空");
                throw new RuntimeException("物流单号不能为空");
            }

            if (courierCode == null || courierCode.trim().isEmpty()) {
                log.warn("快递公司代码为空");
                throw new RuntimeException("快递公司代码不能为空");
            }

            // 2. 物流单号长度验证（根据17TRACK要求：5-50位）
            String trimmedNumber = trackingNumber.trim();
            if (trimmedNumber.length() < 5 || trimmedNumber.length() > 50) {
                log.warn("物流单号长度不符合要求：{}，要求5-50位", trimmedNumber.length());
                throw new RuntimeException("物流单号长度不符合要求，应为5-50位字符");
            }

            // 3. 物流单号格式验证（根据17TRACK要求：字母、数字和中杠的组合）
            if (!trimmedNumber.matches("^[A-Za-z0-9\\-]+$")) {
                log.warn("物流单号格式不正确，只允许字母、数字和中杠：{}", trimmedNumber);
                throw new RuntimeException("物流单号格式不正确，只允许字母、数字和中杠");
            }

            // 4. 运输商代码验证
            Integer numericCarrierCode = getNumericCarrierCode(courierCode);
            if (numericCarrierCode == null) {
                log.warn("无效的运输商代码：{}", courierCode);
                throw new RuntimeException("无效的运输商代码：" + courierCode);
            }

            // 5. 基础格式严格验证（检查明显无效的物流单号）
            if (!validateBasicFormat(trimmedNumber, String.valueOf(numericCarrierCode))) {
                log.warn("物流单号基础格式验证失败：{}", trimmedNumber);
                throw new RuntimeException("物流单号格式不符合运输商要求");
            }

            // 6. 调用17TRACK API进行真实性验证
            return validateWithTrack17Api(trimmedNumber, numericCarrierCode);

        } catch (Exception e) {
            log.error("验证物流单号失败：{}", e.getMessage(), e);
            // 不再返回false，而是抛出异常，确保验证失败时阻止发货
            throw new RuntimeException("物流单号验证失败：" + e.getMessage(), e);
        }
    }

    /**
     * 使用17TRACK API验证物流单号真实性
     */
    private boolean validateWithTrack17Api(String trackingNumber, Integer carrierCode) {
        try {
            // 首先检查API配置
            checkApiConfiguration();

            log.info("调用17TRACK API验证物流单号：{}，运输商：{}", trackingNumber, carrierCode);

            // 构建注册请求（用于验证物流单号是否有效）
            Track17RegisterRequest request = Track17RegisterRequest.builder()
                    .number(trackingNumber)
                    .carrier(carrierCode)
                    .auto_detection(true)
                    .tag("验证物流单号")
                    .remark("发货前验证物流单号有效性")
                    .build();

            List<Track17RegisterRequest> requests = Arrays.asList(request);
            Track17Response<Track17RegisterResponse> response = track17ApiService.register(requests);

            if (response == null) {
                log.error("17TRACK API响应为空，物流单号验证失败：{}", trackingNumber);
                throw new RuntimeException("17TRACK API响应为空，无法验证物流单号");
            }

            // 检查API调用是否成功
            if (response.getCode() != 0) {
                log.error("17TRACK API调用失败，错误代码：{}，消息：{}，物流单号：{}",
                        response.getCode(), response.getMessage(), trackingNumber);
                throw new RuntimeException("17TRACK API调用失败：" + response.getMessage());
            }

            Track17RegisterResponse data = response.getData();
            if (data == null) {
                log.error("17TRACK API响应数据为空，物流单号验证失败：{}", trackingNumber);
                throw new RuntimeException("17TRACK API响应数据为空，无法验证物流单号");
            }

            // 检查是否有成功接受的物流单号
            if (data.getAccepted() != null && !data.getAccepted().isEmpty()) {
                log.info("物流单号验证成功：{}", trackingNumber);

                // 验证成功后，立即删除这个测试记录，避免占用配额
                try {
                    Track17DeleteTrackRequest deleteRequest = Track17DeleteTrackRequest.builder()
                            .number(trackingNumber)
                            .carrier(carrierCode)
                            .build();
                    track17ApiService.deleteTrack(Arrays.asList(deleteRequest));
                    log.debug("已删除验证用的临时物流记录：{}", trackingNumber);
                } catch (Exception e) {
                    log.warn("删除验证用的临时物流记录失败：{}", e.getMessage());
                    // 删除失败不影响验证结果
                }

                return true;
            }

            // 检查被拒绝的原因
            if (data.getRejected() != null && !data.getRejected().isEmpty()) {
                Track17RegisterResponse.RejectedItem rejected = data.getRejected().get(0);
                if (rejected.getError() != null) {
                    String errorMsg = String.format("物流单号验证失败：%s，错误代码：%s，错误信息：%s",
                            trackingNumber, rejected.getError().getCode(), rejected.getError().getMessage());
                    log.error(errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            }

            String errorMsg = "物流单号验证失败，未找到有效的响应：" + trackingNumber;
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);

        } catch (Exception e) {
            log.error("调用17TRACK API验证物流单号失败：{}", e.getMessage(), e);
            // 不再降级，直接抛出异常，确保验证失败时不能发货
            throw new RuntimeException("物流单号验证失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取数字类型的运输商代码
     */
    private Integer getNumericCarrierCode(String courierCode) {
        try {
            // 如果已经是数字，直接返回
            return Integer.parseInt(courierCode);
        } catch (NumberFormatException e) {
            // 如果是字符串代码，需要映射到数字代码
            return mapCarrierCodeToNumeric(courierCode.toLowerCase());
        }
    }

    /**
     * 将字符串运输商代码映射为17TRACK数字代码
     * 根据17TRACK官方文档的运输商代码
     */
    private Integer mapCarrierCodeToNumeric(String courierCode) {
        // 根据17TRACK官方文档的运输商代码映射
        switch (courierCode) {
            // 国际主流快递
            case "ups": return 3001;
            case "fedex": return 3002;
            case "dhl": return 3003;
            case "usps": return 21051;  // 修正：USPS的正确代码是21051
            case "tnt": return 3005;
            case "aramex": return 3006;
            case "dpd": return 3007;
            case "gls": return 3008;

            // 中国邮政和快递
            case "chinapost":
            case "china-post": return 3011;
            case "ems": return 3012;
            case "sf":
            case "sf-express": return 190766;  // 顺丰速运
            case "yto": return 190415;          // 圆通速递
            case "sto": return 190416;          // 申通快递
            case "zto": return 190417;          // 中通快递
            case "yt":
            case "yunda": return 190418;        // 韵达速递
            case "jt": return 190419;           // 极兔速递

            default:
                log.warn("未知的运输商代码：{}，请参考17TRACK官方文档", courierCode);
                return null;
        }
    }

    @Override
    public Object getTrackingInfo(String trackingNumber, String courierCode) {
        log.info("获取物流跟踪信息：{}，快递公司代码：{}", trackingNumber, courierCode);
        
        try {
            Track17RealtimeRequest request = new Track17RealtimeRequest();
            request.setTrackingNumber(trackingNumber);
            request.setCarrierCode(Integer.valueOf(courierCode));
            
            List<Track17RealtimeRequest> requests = Arrays.asList(request);
            return track17ApiService.realtime(requests);
            
        } catch (Exception e) {
            log.error("获取物流跟踪信息失败", e);
            return null;
        }
    }

    /**
     * 基础格式验证（严格验证，不允许明显无效的物流单号）
     */
    private boolean validateBasicFormat(String trackingNumber, String courierCode) {
        try {
            // 首先检查是否为明显无效的物流单号
            if (isObviouslyInvalidTrackingNumber(trackingNumber)) {
                log.warn("明显无效的物流单号：{}", trackingNumber);
                return false;
            }

            int carrier = Integer.parseInt(courierCode);

            // 根据不同快递公司的规则进行基础验证
            switch (carrier) {
                case 3001: // UPS
                    return validateUPSFormat(trackingNumber);
                case 3002: // FedEx
                    return validateFedExFormat(trackingNumber);
                case 3003: // DHL
                    return validateDHLFormat(trackingNumber);
                case 21051: // USPS (修正代码)
                    return validateUSPSFormat(trackingNumber);
                case 3011: // China Post
                    return validateChinaPostFormat(trackingNumber);
                case 190766: // 顺丰速运
                    return validateSFExpressFormat(trackingNumber);
                default:
                    // 对于未知的快递公司，使用通用验证
                    return validateGenericFormat(trackingNumber);
            }
        } catch (NumberFormatException e) {
            log.warn("快递公司代码格式错误：{}", courierCode);
            return false;
        }
    }

    /**
     * 检查是否为明显无效的物流单号
     */
    private boolean isObviouslyInvalidTrackingNumber(String trackingNumber) {
        if (trackingNumber == null || trackingNumber.trim().isEmpty()) {
            return true;
        }

        String number = trackingNumber.trim();

        // 检查是否为纯数字且重复（如：111111、123456789、000000等）
        if (number.matches("^\\d+$")) {
            // 检查是否为重复数字
            if (isRepeatingDigits(number)) {
                return true;
            }

            // 检查是否为连续数字
            if (isSequentialDigits(number)) {
                return true;
            }
        }

        // 检查是否为明显的测试数据
        String lowerNumber = number.toLowerCase();
        if (lowerNumber.contains("test") || lowerNumber.contains("fake") ||
            lowerNumber.contains("demo") || lowerNumber.contains("sample")) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否为重复数字（如：111111、222222等）
     */
    private boolean isRepeatingDigits(String number) {
        if (number.length() < 3) {
            return false;
        }

        char firstChar = number.charAt(0);
        for (int i = 1; i < number.length(); i++) {
            if (number.charAt(i) != firstChar) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查是否为连续数字（如：123456789、987654321等）
     */
    private boolean isSequentialDigits(String number) {
        if (number.length() < 5) {
            return false;
        }

        // 检查递增序列
        boolean isIncreasing = true;
        for (int i = 1; i < number.length(); i++) {
            if (Character.getNumericValue(number.charAt(i)) !=
                Character.getNumericValue(number.charAt(i-1)) + 1) {
                isIncreasing = false;
                break;
            }
        }

        // 检查递减序列
        boolean isDecreasing = true;
        for (int i = 1; i < number.length(); i++) {
            if (Character.getNumericValue(number.charAt(i)) !=
                Character.getNumericValue(number.charAt(i-1)) - 1) {
                isDecreasing = false;
                break;
            }
        }

        return isIncreasing || isDecreasing;
    }

    /**
     * 检查17TRACK API配置
     */
    private void checkApiConfiguration() {
        if (track17Config == null || track17Config.getApi() == null) {
            throw new RuntimeException("17TRACK API配置未找到，请检查配置文件");
        }

        String token = track17Config.getApi().getToken();
        if (token == null || token.trim().isEmpty() ||
            "your-17track-api-token".equals(token) ||
            "your-actual-17track-api-token".equals(token)) {
            throw new RuntimeException("17TRACK API密钥未配置或配置无效，请在application.yml中配置正确的API密钥");
        }

        String baseUrl = track17Config.getApi().getBaseUrl();
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new RuntimeException("17TRACK API基础URL未配置");
        }

        log.debug("17TRACK API配置检查通过，密钥长度：{}", token.length());
    }

    /**
     * UPS格式验证
     */
    private boolean validateUPSFormat(String trackingNumber) {
        // UPS单号通常是18位，以1Z开头
        return trackingNumber.matches("^1Z[A-Za-z0-9]{16}$");
    }

    /**
     * FedEx格式验证
     */
    private boolean validateFedExFormat(String trackingNumber) {
        // FedEx单号通常是12位或14位数字
        return trackingNumber.matches("^[0-9]{12}$|^[0-9]{14}$");
    }

    /**
     * DHL格式验证
     */
    private boolean validateDHLFormat(String trackingNumber) {
        // DHL单号通常是10位数字
        return trackingNumber.matches("^[0-9]{10}$");
    }

    /**
     * USPS格式验证
     */
    private boolean validateUSPSFormat(String trackingNumber) {
        // USPS单号格式验证，更严格的规则
        // 常见格式：
        // 1. Priority Mail Express: 9xxx xxxx xxxx xxxx xxxx xx (22位)
        // 2. Priority Mail: 9xxx xxxx xxxx xxxx xxxx xx (22位)
        // 3. Certified Mail: 9xxx xxxx xxxx xxxx xxxx xx (22位)
        // 4. Registered Mail: 9xxx xxxx xxxx xxxx xxxx xx (22位)

        if (trackingNumber.length() < 10 || trackingNumber.length() > 35) {
            return false;
        }

        // USPS单号通常以9开头，且为数字
        if (trackingNumber.matches("^9\\d{21}$")) {
            return true; // 22位，以9开头的数字
        }

        // 其他USPS格式：字母+数字组合
        if (trackingNumber.matches("^[A-Z]{2}\\d{9}[A-Z]{2}$")) {
            return true; // 13位，字母+数字+字母格式
        }

        // 更宽松的验证：至少包含一些字母或特定模式
        if (trackingNumber.matches("^[A-Z0-9]{10,35}$") &&
            (trackingNumber.contains("US") || trackingNumber.startsWith("9") ||
             trackingNumber.matches(".*[A-Z].*"))) {
            return true;
        }

        return false;
    }

    /**
     * 中国邮政格式验证
     */
    private boolean validateChinaPostFormat(String trackingNumber) {
        // 中国邮政单号通常是13位，以字母开头和结尾
        return trackingNumber.matches("^[A-Za-z]{2}[0-9]{9}[A-Za-z]{2}$");
    }

    /**
     * 顺丰速运格式验证
     */
    private boolean validateSFExpressFormat(String trackingNumber) {
        // 顺丰单号格式：通常以SF开头，后跟12位数字
        if (trackingNumber.matches("^SF\\d{12}$")) {
            return true;
        }
        // 其他顺丰格式：12-15位数字
        if (trackingNumber.matches("^\\d{12,15}$")) {
            return true;
        }
        return false;
    }

    /**
     * 通用格式验证
     */
    private boolean validateGenericFormat(String trackingNumber) {
        // 通用验证：6-50位字母数字组合
        return trackingNumber.matches("^[A-Za-z0-9]{6,50}$");
    }

    @Override
    public List<Courier> getAllCouriers() {
        List<Courier> couriers = new ArrayList<>();

        // 添加常用快递公司
        couriers.add(Courier.builder()
                .code("fedex")
                .courierCode("fedex")
                .name("FedEx")
                .courierName("FedEx")
                .country("US")
                .active(true)
                .build());

        couriers.add(Courier.builder()
                .code("ups")
                .courierCode("ups")
                .name("UPS")
                .courierName("UPS")
                .country("US")
                .active(true)
                .build());

        couriers.add(Courier.builder()
                .code("dhl")
                .courierCode("dhl")
                .name("DHL")
                .courierName("DHL")
                .country("DE")
                .active(true)
                .build());

        couriers.add(Courier.builder()
                .code("usps")
                .courierCode("usps")
                .name("USPS")
                .courierName("USPS")
                .country("US")
                .active(true)
                .build());

        return couriers;
    }
}
