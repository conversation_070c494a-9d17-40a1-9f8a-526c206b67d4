package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商家收账账户信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SellerPaymentAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户类型常量
     */
    public static final Integer ACCOUNT_TYPE_BANK_CARD = 1;     // 银行卡
    public static final Integer ACCOUNT_TYPE_ALIPAY = 2;        // 支付宝
    public static final Integer ACCOUNT_TYPE_WECHAT = 3;        // 微信
    public static final Integer ACCOUNT_TYPE_OTHER = 4;         // 其他

    /**
     * 账户状态常量
     */
    public static final Integer ACCOUNT_STATUS_DISABLED = 0;    // 禁用
    public static final Integer ACCOUNT_STATUS_ENABLED = 1;     // 启用

    /**
     * 验证状态常量
     */
    public static final Integer VERIFICATION_STATUS_UNVERIFIED = 0;  // 未验证
    public static final Integer VERIFICATION_STATUS_VERIFIED = 1;    // 已验证

    /**
     * 是否默认账户常量
     */
    public static final Integer IS_DEFAULT_NO = 0;             // 非默认
    public static final Integer IS_DEFAULT_YES = 1;            // 默认

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商家ID
     */
    private Long sellerId;

    /**
     * 账户类型(1-银行卡,2-支付宝,3-微信,4-其他)
     */
    private Integer accountType;

    /**
     * 账户名称/持卡人姓名
     */
    private String accountName;

    /**
     * 账户号码/卡号
     */
    private String accountNumber;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 开户支行
     */
    private String branchName;

    /**
     * 平台名称(支付宝/微信等)
     */
    private String platformName;

    /**
     * 平台账号(手机号/邮箱等)
     */
    private String platformAccount;

    /**
     * 是否默认账户(0-否,1-是)
     */
    private Integer isDefault;

    /**
     * 账户状态(0-禁用,1-启用)
     */
    private Integer accountStatus;

    /**
     * 验证状态(0-未验证,1-已验证)
     */
    private Integer verificationStatus;

    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 预留手机号
     */
    private String phone;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
