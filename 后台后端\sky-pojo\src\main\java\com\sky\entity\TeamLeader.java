package com.sky.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TeamLeader {
    private Long id;
    private String name;
    private String realName;
    private String avatar;
    private String phone;
    private String email;
    private String idNumber;
    private String bankInfo;
    private String address;
    private String createTime;
    private String level;
    private String status;
    private Integer teamSize;
    private BigDecimal totalSales;
    private BigDecimal commissionEarned;
    private BigDecimal todaySales;
    private List<TeamMember> teamMembers;
    private Performance performance;
} 