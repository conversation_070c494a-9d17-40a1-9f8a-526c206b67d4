package com.sky.mapper;

import com.sky.entity.OrderLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface OrderLogMapper {
    
    /**
     * 插入订单日志
     */
    @Insert("INSERT INTO order_log (order_id, order_number, operation, old_status, new_status, " +
            "operator_type, operator_id, operator_name, remark, details, create_time) " +
            "VALUES (#{orderId}, #{orderNumber}, #{operation}, #{oldStatus}, #{newStatus}, " +
            "#{operatorType}, #{operatorId}, #{operatorName}, #{remark}, #{details}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(OrderLog orderLog);
    
    /**
     * 根据订单ID查询日志
     */
    @Select("SELECT * FROM order_log WHERE order_id = #{orderId} ORDER BY create_time DESC")
    List<OrderLog> findByOrderId(Long orderId);
    
    /**
     * 根据订单号查询日志
     */
    @Select("SELECT * FROM order_log WHERE order_number = #{orderNumber} ORDER BY create_time DESC")
    List<OrderLog> findByOrderNumber(String orderNumber);
    
    /**
     * 分页查询订单日志
     */
    @Select("SELECT * FROM order_log ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<OrderLog> findByPage(@Param("offset") int offset, @Param("limit") int limit);
}
