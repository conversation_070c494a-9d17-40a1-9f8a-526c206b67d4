package com.sky.aspect;


import com.sky.annotation.PreAuthorize;
import com.sky.constant.MessageConstant;
import com.sky.entity.Permission;
import com.sky.exception.AccountIsExitException;
import com.sky.mapper.AdminMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Aspect
public class AuthorizeAspect {

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private HttpServletResponse response;

    //配置切入点
    @Pointcut("@annotation(com.sky.annotation.PreAuthorize)")
    public void authorizePointCut() {
    }

    /**
     * 后端接口资源鉴权
     * 1.查询用户所有接口资源权限列表
     * 2.获取当前接口资源的访问权限标识符
     * 3.比较
     * @return
     */
    @Around("authorizePointCut()")
    public Object handle(ProceedingJoinPoint joinPoint) throws Throwable {
        //1.查询用户所有接口资源权限列表
        String userId = request.getHeader("user_id");
        List<Permission> permissionList = adminMapper.findSellerBySellerId(Long.parseLong(userId));
        //2.获取当前接口资源的访问权限标识符
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        PreAuthorize annotation = method.getAnnotation(PreAuthorize.class);
        String MethodPermission = annotation.value();
        //3.比较
        if(permissionList == null || permissionList.size() == 0){
            throw new AccountIsExitException("该用户不存在");
        }
        Set<String> userPerms = permissionList.stream()
                .map(Permission::getPermCode)
                .collect(Collectors.toSet());
        if (!userPerms.contains(MethodPermission)) {
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(403);
            response.getWriter().write("亲,无权访问");
            return null;
        }
        return joinPoint.proceed();
    }


}
