package com.sky.controller.admin;

import com.sky.dto.ProductPageQueryDTO;
import com.sky.entity.PmsProduct;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.ProductService;
import com.sky.vo.ProductVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 产品管理
 */
@RestController
@RequestMapping("/products")
@Api(tags = "产品相关接口")
@Slf4j
public class ProductController {

    @Autowired
    private ProductService productService;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 产品分页查询
     *
     * @param productPageQueryDTO
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("产品分页查询")
    public Result<PageResult> page(ProductPageQueryDTO productPageQueryDTO) {
        log.info("产品分页查询:{}", productPageQueryDTO);
        PageResult pageResult = productService.pageQuery(productPageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据id查询产品
     *
     * @param id
     * @return
     */
    @GetMapping("/111/{id}")
    @ApiOperation("根据id查询产品")
    public Result<PmsProduct> getById(@PathVariable Long id) {
        log.info("根据id查询产品：{}", id);
        PmsProduct pmsProduct = productService.getByProductId(id);
        return Result.success(pmsProduct);
    }

    @ApiOperation("根据分类id查询产品")
    @GetMapping("/category/{id}")
    public Result<List<PmsProduct>> getCategoryId(@PathVariable Long id){
        log.info("根据分类id查询产品：{}", id);
        List<PmsProduct> pmsProduct = productService.getByCategoryId(id);
        return Result.success(pmsProduct);
    }
    /**
     * 清理缓存数据
     * @param pattern
     */
    private void cleanCache(String pattern){
        Set keys = redisTemplate.keys(pattern);
        redisTemplate.delete(keys);
    }
}
