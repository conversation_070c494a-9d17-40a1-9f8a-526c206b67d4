package com.sky.service.impl;

import com.sky.Utils.WechatPayService;
import com.sky.dto.WechatRefundRequestDTO;
import com.sky.dto.WechatRefundQueryDTO;
import com.sky.dto.WechatRefundListQueryDTO;
import com.sky.service.WechatRefundService;
import com.sky.vo.WechatRefundResponseVO;
import com.sky.vo.WechatRefundQueryVO;
import com.sky.vo.WechatRefundListQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 微信退款服务实现类
 */
@Slf4j
@Service
public class WechatRefundServiceImpl implements WechatRefundService {

    @Autowired
    private WechatPayService wechatPayService;

    /**
     * 申请退款
     * @param requestDTO 退款申请请求
     * @return 退款响应
     */
    @Override
    public WechatRefundResponseVO applyRefund(WechatRefundRequestDTO requestDTO) {
        log.info("申请退款，请求参数: {}", requestDTO);
        
        try {
            // 调用微信支付服务申请退款
            WechatRefundResponseVO response = wechatPayService.applyRefund(requestDTO);
            log.info("申请退款成功，响应: {}", response);
            return response;
        } catch (Exception e) {
            log.error("申请退款失败: {}", e.getMessage(), e);
            throw new RuntimeException("申请退款失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询退款结果
     * @param queryDTO 退款查询请求
     * @return 退款查询响应
     */
    @Override
    public WechatRefundQueryVO queryRefund(WechatRefundQueryDTO queryDTO) {
        log.info("查询退款结果，请求参数: {}", queryDTO);
        
        try {
            // 调用微信支付服务查询退款结果
            WechatRefundQueryVO response = wechatPayService.queryRefund(queryDTO);
            log.info("查询退款结果成功，响应: {}", response);
            return response;
        } catch (Exception e) {
            log.error("查询退款结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询退款结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询所有退款
     * @param queryDTO 查询所有退款请求
     * @return 查询所有退款响应
     */
    @Override
    public WechatRefundListQueryVO queryAllRefunds(WechatRefundListQueryDTO queryDTO) {
        log.info("查询所有退款，请求参数: {}", queryDTO);

        try {
            // 调用微信支付服务查询所有退款
            WechatRefundListQueryVO response = wechatPayService.queryAllRefunds(queryDTO);
            log.info("查询所有退款成功，响应: {}", response);
            return response;
        } catch (Exception e) {
            log.error("查询所有退款失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询所有退款失败: " + e.getMessage(), e);
        }
    }
}
