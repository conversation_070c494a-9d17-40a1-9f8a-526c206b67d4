package com.sky.service.impl;

import com.sky.entity.Orders;
import com.sky.service.OrderStatusService;
import org.springframework.stereotype.Service;


import java.util.HashMap;
import java.util.Map;

/**
 * 订单状态管理服务实现类
 */
@Service
public class OrderStatusServiceImpl implements OrderStatusService {
    
    // 状态描述映射
    private static final Map<Integer, String> STATUS_DESCRIPTIONS = new HashMap<>();
    
    static {
        STATUS_DESCRIPTIONS.put(Orders.STATUS_PENDING_PAYMENT, "待付款");
        STATUS_DESCRIPTIONS.put(Orders.STATUS_PAID, "已付款");
        STATUS_DESCRIPTIONS.put(Orders.STATUS_PROCESSING, "处理中");
        STATUS_DESCRIPTIONS.put(Orders.STATUS_SHIPPED, "已发货");
        STATUS_DESCRIPTIONS.put(Orders.STATUS_COMPLETED, "已完成");
        STATUS_DESCRIPTIONS.put(Orders.STATUS_CANCELLED, "已取消");
        STATUS_DESCRIPTIONS.put(Orders.STATUS_REFUNDED, "已退款");
    }
    
    @Override
    public boolean validateStatusTransition(Integer currentStatus, Integer targetStatus) {
        if (currentStatus == null || targetStatus == null) {
            return false;
        }
        
        // 相同状态不允许转换
        if (currentStatus.equals(targetStatus)) {
            return false;
        }
        
        // 已取消、已完成、已退款的订单不能再变更状态
        if (currentStatus.equals(Orders.STATUS_CANCELLED) ||
            currentStatus.equals(Orders.STATUS_COMPLETED) ||
            currentStatus.equals(Orders.STATUS_REFUNDED)) {
            return false;
        }

        // 定义合法的状态流转
        switch (currentStatus) {
            case 1: // 待付款
                return targetStatus.equals(Orders.STATUS_PAID) || targetStatus.equals(Orders.STATUS_CANCELLED);
            case 2: // 已付款
                return targetStatus.equals(Orders.STATUS_PROCESSING) ||
                       targetStatus.equals(Orders.STATUS_SHIPPED) ||
                       targetStatus.equals(Orders.STATUS_CANCELLED);
            case 3: // 处理中
                return targetStatus.equals(Orders.STATUS_SHIPPED) || targetStatus.equals(Orders.STATUS_CANCELLED);
            case 4: // 已发货
                return targetStatus.equals(Orders.STATUS_COMPLETED);
            case 5: // 已完成
                return targetStatus.equals(Orders.STATUS_REFUNDED);
            default:
                return false;
        }
    }
    
    @Override
    public String[] getAvailableActions(Integer status) {
        if (status == null) {
            return new String[0];
        }
        
        switch (status) {
            case 1: // 待付款
                return new String[]{"cancel", "pay"};
            case 2: // 已付款
                return new String[]{"process", "cancel"};
            case 3: // 处理中
                return new String[]{"ship", "cancel"};
            case 4: // 已发货
                return new String[]{"complete", "refund"};
            case 5: // 已完成
                return new String[]{"refund"};
            default:
                return new String[0];
        }
    }
    
    @Override
    public String getStatusDescription(Integer status) {
        return STATUS_DESCRIPTIONS.getOrDefault(status, "未知状态");
    }
    
    @Override
    public boolean canCancel(Orders order) {
        if (order == null || order.getStatus() == null) {
            return false;
        }
        
        // 已取消、已完成、已退款的订单不能取消
        if (order.getStatus().equals(Orders.STATUS_CANCELLED) ||
            order.getStatus().equals(Orders.STATUS_COMPLETED) ||
            order.getStatus().equals(Orders.STATUS_REFUNDED)) {
            return false;
        }
        
        // 已发货的订单不能取消，只能退款
        if (order.getStatus().equals(Orders.STATUS_SHIPPED)) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean canShip(Orders order) {
        if (order == null || order.getStatus() == null) {
            return false;
        }
        
        // 只有已支付的订单可以发货
        return order.getStatus().equals(Orders.STATUS_PAID);
    }
    
    @Override
    public boolean canComplete(Orders order) {
        if (order == null || order.getStatus() == null) {
            return false;
        }
        
        // 只有已发货的订单可以完成
        return order.getStatus().equals(Orders.STATUS_SHIPPED);
    }
    
    @Override
    public boolean canRefund(Orders order) {
        if (order == null || order.getStatus() == null) {
            return false;
        }
        
        // 已发货或已完成的订单可以退款
        return order.getStatus().equals(Orders.STATUS_SHIPPED) || 
               order.getStatus().equals(Orders.STATUS_COMPLETED);
    }
}
