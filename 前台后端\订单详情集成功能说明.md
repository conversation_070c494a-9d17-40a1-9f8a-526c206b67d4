# 前台订单详情集成功能说明

## 功能概述

本功能为前台用户提供完整的订单详情查看体验，集成了订单信息、物流跟踪、地址信息、支付详情等多个维度的数据，让用户能够全面了解订单状态和物流进展。

## 核心特性

### 1. 订单信息集成
- **基础订单信息**：订单号、状态、金额、下单时间等
- **商品详情**：订单包含的商品列表和详细信息
- **订单进度**：可视化的订单进度条和当前阶段说明
- **操作权限**：根据订单状态动态显示可执行的操作

### 2. 物流跟踪功能
- **实时物流状态**：显示当前物流状态和位置
- **完整物流轨迹**：按时间顺序展示物流轨迹详情
- **物流信息同步**：支持手动同步最新物流信息
- **预计送达时间**：显示预计和实际送达时间

### 3. 地址信息管理
- **收货地址**：完整的收货地址信息
- **发货地址**：商家发货地址信息
- **地址格式化**：自动格式化显示完整地址

### 4. 支付信息展示
- **支付方式**：显示使用的支付方式
- **支付状态**：实时支付状态更新
- **交易详情**：第三方交易号、支付时间等
- **退款信息**：退款金额、退款时间等

## 数据库设计

### 新增数据表

1. **order_logistics_tracking** - 订单物流跟踪表
2. **logistics_trace_detail** - 物流轨迹详情表
3. **order_address_info** - 订单地址信息表
4. **order_payment_detail** - 订单支付详情表
5. **order_status_log** - 订单状态变更日志表

### 表关系说明
```
orders (订单表)
├── order_logistics_tracking (物流跟踪)
│   └── logistics_trace_detail (物流轨迹)
├── order_address_info (地址信息)
├── order_payment_detail (支付详情)
└── order_status_log (状态日志)
```

## API接口说明

### 基础查询接口

#### 1. 获取订单完整详情
```
GET /user/order-detail/{orderId}
```
**功能**：获取指定订单的完整详情信息
**返回**：包含订单、物流、地址、支付等所有信息的集成对象

#### 2. 根据订单号查询
```
GET /user/order-detail/by-number/{orderNumber}
```
**功能**：通过订单号查询订单详情
**应用场景**：用户通过订单号快速查找订单

#### 3. 获取用户订单列表
```
GET /user/order-detail/list?status=1&pageNum=1&pageSize=10
```
**功能**：分页查询用户的订单列表
**参数**：
- `status`：订单状态筛选（可选）
- `pageNum`：页码
- `pageSize`：每页大小

### 物流相关接口

#### 4. 同步物流信息
```
POST /user/order-detail/{orderId}/sync-logistics
```
**功能**：手动同步订单的最新物流信息
**说明**：调用17TRACK API获取最新物流轨迹

#### 5. 获取实时物流信息
```
GET /user/order-detail/{orderId}/realtime-logistics
```
**功能**：获取实时物流轨迹信息
**说明**：直接从17TRACK API获取最新数据

### 订单操作接口

#### 6. 确认收货
```
POST /user/order-detail/{orderId}/confirm-receipt
```
**功能**：用户确认收货，订单状态变为已完成

#### 7. 申请退款
```
POST /user/order-detail/{orderId}/apply-refund?reason=商品有问题
```
**功能**：用户申请退款
**参数**：`reason` - 退款原因

#### 8. 取消订单
```
POST /user/order-detail/{orderId}/cancel?reason=不想要了
```
**功能**：取消订单
**参数**：`reason` - 取消原因（可选）

### 统计和辅助接口

#### 9. 获取订单统计
```
GET /user/order-detail/statistics
```
**功能**：获取用户的订单统计信息
**返回**：各状态订单数量、总金额等

#### 10. 获取最近物流更新
```
GET /user/order-detail/recent-logistics?limit=10
```
**功能**：获取用户最近的物流更新记录

#### 11. 检查操作权限
```
GET /user/order-detail/{orderId}/check-action/{action}
```
**功能**：检查用户是否可以对订单执行某个操作
**参数**：`action` - 操作类型（cancel/refund/confirm）

## 前端集成建议

### 1. 订单详情页面结构
```
订单详情页面
├── 订单基础信息区域
│   ├── 订单号、状态、金额
│   ├── 订单进度条
│   └── 操作按钮区域
├── 商品信息区域
│   └── 商品列表和详情
├── 物流信息区域
│   ├── 当前物流状态
│   ├── 物流轨迹时间线
│   └── 同步物流按钮
├── 地址信息区域
│   ├── 收货地址
│   └── 发货地址
└── 支付信息区域
    ├── 支付方式和状态
    └── 交易详情
```

### 2. 状态映射
```javascript
const ORDER_STATUS = {
  1: { name: '待付款', color: '#ff9500', progress: 10 },
  2: { name: '待发货', color: '#007aff', progress: 30 },
  3: { name: '待收货', color: '#34c759', progress: 70 },
  4: { name: '已完成', color: '#30d158', progress: 100 },
  5: { name: '已取消', color: '#ff3b30', progress: 0 },
  6: { name: '退款中', color: '#ff9500', progress: 80 },
  7: { name: '已退款', color: '#8e8e93', progress: 100 }
};
```

### 3. 物流状态映射
```javascript
const LOGISTICS_STATUS = {
  'NotFound': '查询不到',
  'InfoReceived': '已揽收',
  'InTransit': '运输中',
  'OutForDelivery': '派送中',
  'Delivered': '已签收',
  'Exception': '异常'
};
```

## 使用示例

### 1. 查询订单详情
```javascript
// 获取订单详情
const response = await fetch('/user/order-detail/123');
const orderDetail = await response.json();

// 显示订单信息
console.log('订单号:', orderDetail.data.orderNumber);
console.log('订单状态:', orderDetail.data.orderStatusName);
console.log('物流状态:', orderDetail.data.logisticsStatusDesc);
console.log('可否取消:', orderDetail.data.canCancel);
```

### 2. 同步物流信息
```javascript
// 同步物流信息
const syncResponse = await fetch('/user/order-detail/123/sync-logistics', {
  method: 'POST'
});

if (syncResponse.ok) {
  // 重新获取订单详情
  location.reload();
}
```

### 3. 确认收货
```javascript
// 确认收货
const confirmResponse = await fetch('/user/order-detail/123/confirm-receipt', {
  method: 'POST'
});

if (confirmResponse.ok) {
  alert('确认收货成功！');
  location.reload();
}
```

## 注意事项

### 1. 权限控制
- 所有接口都会验证订单归属，确保用户只能操作自己的订单
- 操作权限根据订单状态动态判断

### 2. 物流信息同步
- 物流信息同步会消耗17TRACK API配额
- 建议设置同步频率限制，避免频繁调用

### 3. 数据一致性
- 订单状态变更时会同步更新相关表
- 使用事务确保数据一致性

### 4. 性能优化
- 订单列表查询支持分页
- 物流轨迹按时间倒序排列
- 可考虑添加缓存机制

## 扩展功能

### 1. 消息推送
- 物流状态变化时推送消息给用户
- 订单状态变更通知

### 2. 评价功能
- 订单完成后支持商品评价
- 物流服务评价

### 3. 订单导出
- 支持订单信息导出
- 物流轨迹导出

### 4. 智能客服
- 集成智能客服系统
- 订单问题自动解答

## 测试建议

### 1. 功能测试
- 测试各种订单状态下的功能
- 验证物流信息同步准确性
- 测试订单操作权限控制

### 2. 性能测试
- 大量订单数据下的查询性能
- 并发物流同步测试
- 接口响应时间测试

### 3. 异常测试
- 网络异常情况处理
- 17TRACK API异常处理
- 数据异常情况处理
