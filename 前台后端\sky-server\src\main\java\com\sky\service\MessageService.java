package com.sky.service;

import com.sky.dto.BatchMessageDTO;
import com.sky.dto.MessageDTO;
import com.sky.dto.RecipientQueryDTO;
import com.sky.vo.MessageDetailVO;
import com.sky.vo.MessageListVO;
import com.sky.vo.RecipientVO;

import java.util.List;

public interface MessageService {
    /**
     * 获取消息接收者列表
     */
    List<RecipientVO> getRecipients(RecipientQueryDTO queryDTO);

    /**
     * 发送站内信
     */
    String sendMessage(MessageDTO messageDTO);

    /**
     * 发送系统公告
     */
    String sendAnnouncement(MessageDTO messageDTO);

    /**
     * 获取站内信列表
     */
    MessageListVO getMessageList(String type, Long id);

    /**
     * 获取消息详情
     */
    MessageDetailVO getMessageDetail(String msgID, String type, Long id);

    /**
     * 标记消息为已读
     */
    void markAsRead(String msgID, String type, Long id);

    /**
     * 批量标记消息为已读
     */
    void batchMarkAsRead(BatchMessageDTO batchDTO);

    /**
     * 标记全部消息为已读
     */
    void markAllAsRead(String type, Long id);

    /**
     * 获取未读消息数量
     */
    Integer getUnreadCount(String type, Long id);

    /**
     * 删除消息
     */
    void deleteMessage(String msgID, String type, Long id);

    /**
     * 批量删除消息
     */
    void batchDeleteMessage(BatchMessageDTO batchDTO);
} 