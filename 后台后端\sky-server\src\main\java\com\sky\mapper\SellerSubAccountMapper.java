package com.sky.mapper;

import com.sky.entity.SellerSubAccount;
import com.sky.vo.SellerSubAccountVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface SellerSubAccountMapper {

    @Select("select * from ry_mall.seller where email = #{email}")
    SellerSubAccount selectByEmail(String email);

    @Select("select * from ry_mall.seller where account_name = #{accountName}")
    SellerSubAccount selectByAccountName(String accountName);

    void save(SellerSubAccount sellerSubAccount);

    @Select("select id from ry_mall.seller where account_name = #{accountName}")
    Long selectIdByAccountName(String accountName);

    void savePermissions(Long id, List<String> permissions);

    List<SellerSubAccountVO> getSubAccountList(Long createdBy, Integer accountStatus);

    @Select("select * from ry_mall.seller where id = #{id} and created_by = #{createdBy}")
    SellerSubAccount getSubAccount(Long id,Long createdBy);

    @Select("select permission_code from ry_mall.seller_permission where seller_id = #{id}")
    List<String> getPermissionsBySellerId(Long id);

    void update(SellerSubAccount sellerSubAccount);

    @Select("select created_by from ry_mall.seller where id = #{id}")
     Long selectCreatedById(Long id);

    @Delete("delete from ry_mall.seller where id = #{id} and seller.created_by = #{createdBy}")
    void deleteSubAccount(Long id,Long createdBy);

    @Delete("delete from ry_mall.seller_permission where seller_id = #{id}")
    void deleteAllPermissions(Long id);

    @Update("update ry_mall.seller set account_status = #{status} where id = #{id}")
    void updateStatus(Long id, Integer status);

    @Update("update ry_mall.seller set password = #{password} where id = #{id}")
    void resetPassword(Long id, String password);

    @Select("select account_name from ry_mall.seller where id = #{id}")
    String selectByAccountNameById(Long id);

    @Select("select email from ry_mall.seller where id = #{id}")
    String selectEmailById(Long id);

    @Select("select * from ry_mall.seller where phone = #{phone}")
    SellerSubAccount selectByPhone(String phone);

    @Select("select phone from ry_mall.seller where id = #{id}")
    String selectPhoneById(Long id);

}
