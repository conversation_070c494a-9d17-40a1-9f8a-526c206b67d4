package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.dto.PaymentRecordPageQueryDTO;
import com.sky.entity.PaymentRecord;
import com.sky.exception.MessageNotFound;
import com.sky.mapper.PaymentRecordMapper;
import com.sky.result.PageResult;
import com.sky.service.PaymentRecordService;
import com.sky.vo.PaymentRecordVO;
import com.sky.vo.ProductVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PaymentRecordServiceImpl implements PaymentRecordService {

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    /**
     *根据ID查询支付记录
     * @param id
     * @return
     */
    @Override
    public PaymentRecordVO getPaymentRecord(Long id) {
        if (id == null){
            throw new MessageNotFound(MessageConstant.ID_NOT_FOUND);
        }
        PaymentRecord paymentRecord =paymentRecordMapper.getById(id);
        PaymentRecordVO paymentRecordVO = new PaymentRecordVO();
        BeanUtils.copyProperties(paymentRecord,paymentRecordVO);

        return paymentRecordVO;
    }

    /**
     *支付记录分页查询
     * @param paymentRecordPageQueryDTO
     * @return
     */
    @Override
    public PageResult pageQuery(PaymentRecordPageQueryDTO paymentRecordPageQueryDTO) {
        PageHelper.startPage(paymentRecordPageQueryDTO.getPage(), paymentRecordPageQueryDTO.getPageSize());
        Page<PaymentRecordVO> page = paymentRecordMapper.pageQuery(paymentRecordPageQueryDTO);
        return new PageResult(page.getTotal(), page.getResult());
    }

    @Override
    public void save(PaymentRecord paymentRecord) {
        paymentRecordMapper.insert(paymentRecord);
    }
}
