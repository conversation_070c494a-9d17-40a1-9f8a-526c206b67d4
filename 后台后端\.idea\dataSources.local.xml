<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-242.23726.103">
    <data-source name="@47.74.24.46" uuid="944ad715-28a5-4c75-a0c8-435a0e96fe1c">
      <database-info product="MySQL" version="5.7.43-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.43" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>user</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="ry_mall" />
            <name qname="ry_sql" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="8.218.65.151" uuid="5ee459fa-1511-4512-aa9d-f1607e7b8390">
      <database-info product="MySQL" version="5.7.44-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.44" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>user</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" negative="1" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="121.43.34.17" uuid="1a7e9401-8896-4eda-921c-bf6aba490f96">
      <database-info product="MySQL" version="5.7.40-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.40" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>hiram</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="hiram" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>