package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.dto.ProductPageQueryDTO;
import com.sky.entity.PmsProduct;
import com.sky.entity.Product;
import com.sky.exception.MessageNotFound;
import com.sky.mapper.ProductMapper;
import com.sky.result.PageResult;
import com.sky.service.ProductService;
import com.sky.vo.ProductVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductMapper productMapper;


    /**
     * 产品分页查询
     *
     * @param productPageQueryDTO
     * @return
     */
    public PageResult pageQuery(ProductPageQueryDTO productPageQueryDTO) {
        PageHelper.startPage(productPageQueryDTO.getPage(), productPageQueryDTO.getPageSize());
        Page<ProductVO> page = productMapper.pageQuery(productPageQueryDTO);
        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 根据id查询产品
     *
     * @param id
     * @return
     */
    public PmsProduct getByProductId(Long id) {
        if (id == null)
        {
            throw new MessageNotFound(MessageConstant.ID_NOT_FOUND);
        }
        //根据id查询产品数据
        PmsProduct pmsProduct = productMapper.getById(id);
        //将查询到的数据封装到VO
        PmsProduct pmsProduct1 = new PmsProduct();
        BeanUtils.copyProperties(pmsProduct, pmsProduct1);

        return pmsProduct1;
    }

    /**
     * 根据分类id查询菜品
     *
     * @param categoryId
     * @return
     */
    @Override
    public List<PmsProduct> getByCategoryId(Long categoryId) {
         if (categoryId == null)
        {
            throw new MessageNotFound(MessageConstant.ID_NOT_FOUND);
        }
         if(categoryId == 0){
             List<PmsProduct> product = productMapper.getByCategoryId1(categoryId);
             List<PmsProduct> productVO = new ArrayList<>();
             for (PmsProduct p : product) {
                 PmsProduct pmsProduct = new PmsProduct();
                 BeanUtils.copyProperties(p, pmsProduct);
                 productVO.add(pmsProduct);
             }
             return productVO;
         }
        List<PmsProduct> product = productMapper.getByCategoryId(categoryId);
        List<PmsProduct> productVO = new ArrayList<>();
        for (PmsProduct product1 : product) {
            PmsProduct pmsProduct = new PmsProduct();
            BeanUtils.copyProperties(product1, pmsProduct);
            productVO.add(pmsProduct);
        }
        return productVO;
    }

}
