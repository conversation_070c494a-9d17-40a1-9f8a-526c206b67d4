package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String logisticsNumber;
    private Integer orderId;
    private String logisticsCompany;
    private String logisticsStatus;
    private LocalDateTime createTime;
    private LocalDateTime shippingDate;
}
