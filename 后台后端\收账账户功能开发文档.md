# 收账账户功能开发文档

## 1. 功能概述

收账账户功能为商家提供了管理收款账户信息的完整解决方案，支持银行卡、支付宝、微信等多种收款方式。商家可以添加、管理多个收账账户，设置默认账户，管理员可以审核验证账户信息。

### 1.1 主要功能
- 多种账户类型支持（银行卡、支付宝、微信、其他）
- 账户信息管理（增删改查）
- 默认账户设置
- 账户状态管理（启用/禁用）
- 账户验证功能
- 数据脱敏保护
- 分页查询和条件筛选

### 1.2 用户角色
- **商家用户**：管理自己的收账账户
- **管理员**：审核验证所有商家的收账账户

## 2. 数据库设计

### 2.1 表结构

```sql
CREATE TABLE seller_payment_account (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    seller_id BIGINT NOT NULL COMMENT '商家ID，关联seller表',
    account_type TINYINT NOT NULL COMMENT '账户类型：1-银行卡，2-支付宝，3-微信，4-其他',
    account_name VARCHAR(100) NOT NULL COMMENT '账户名称（持卡人/账户持有人姓名）',
    account_number VARCHAR(100) NOT NULL COMMENT '账户号码（卡号/账号）',
    bank_name VARCHAR(100) COMMENT '银行名称（银行卡类型时必填）',
    bank_code VARCHAR(20) COMMENT '银行代码',
    branch_name VARCHAR(200) COMMENT '开户支行（银行卡类型时必填）',
    platform_name VARCHAR(50) COMMENT '平台名称（支付宝/微信等）',
    platform_account VARCHAR(100) COMMENT '平台账号（支付宝账号/微信号等）',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认账户：0-否，1-是',
    account_status TINYINT DEFAULT 1 COMMENT '账户状态：0-禁用，1-启用',
    verification_status TINYINT DEFAULT 0 COMMENT '验证状态：0-未验证，1-已验证',
    id_card_number VARCHAR(20) COMMENT '身份证号码',
    phone VARCHAR(20) COMMENT '手机号码',
    remark VARCHAR(500) COMMENT '备注信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_seller_id (seller_id),
    INDEX idx_account_type (account_type),
    INDEX idx_account_status (account_status),
    INDEX idx_verification_status (verification_status),
    INDEX idx_is_default (is_default),
    INDEX idx_create_time (create_time),
    UNIQUE KEY uk_seller_account (seller_id, account_type, account_number),
    
    FOREIGN KEY (seller_id) REFERENCES seller(id) ON DELETE CASCADE
) COMMENT='商家收账账户信息表';
```

### 2.2 字段说明

| 字段名 | 类型 | 说明 | 必填 |
|--------|------|------|------|
| id | BIGINT | 主键ID | 是 |
| seller_id | BIGINT | 商家ID | 是 |
| account_type | TINYINT | 账户类型：1-银行卡，2-支付宝，3-微信，4-其他 | 是 |
| account_name | VARCHAR(100) | 账户名称 | 是 |
| account_number | VARCHAR(100) | 账户号码 | 是 |
| bank_name | VARCHAR(100) | 银行名称（银行卡必填） | 条件必填 |
| bank_code | VARCHAR(20) | 银行代码 | 否 |
| branch_name | VARCHAR(200) | 开户支行（银行卡必填） | 条件必填 |
| platform_name | VARCHAR(50) | 平台名称 | 否 |
| platform_account | VARCHAR(100) | 平台账号 | 否 |
| is_default | TINYINT | 是否默认账户 | 否 |
| account_status | TINYINT | 账户状态 | 否 |
| verification_status | TINYINT | 验证状态 | 否 |
| id_card_number | VARCHAR(20) | 身份证号码 | 否 |
| phone | VARCHAR(20) | 手机号码 | 否 |
| remark | VARCHAR(500) | 备注信息 | 否 |

## 3. 实体类定义

### 3.1 SellerPaymentAccount 实体类

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "seller_payment_account")
public class SellerPaymentAccount implements Serializable {
    
    // 账户类型常量
    public static final Integer ACCOUNT_TYPE_BANK_CARD = 1;     // 银行卡
    public static final Integer ACCOUNT_TYPE_ALIPAY = 2;        // 支付宝
    public static final Integer ACCOUNT_TYPE_WECHAT = 3;        // 微信
    public static final Integer ACCOUNT_TYPE_OTHER = 4;         // 其他
    
    // 默认账户常量
    public static final Integer IS_DEFAULT_NO = 0;             // 非默认
    public static final Integer IS_DEFAULT_YES = 1;            // 默认
    
    // 账户状态常量
    public static final Integer ACCOUNT_STATUS_DISABLED = 0;   // 禁用
    public static final Integer ACCOUNT_STATUS_ENABLED = 1;    // 启用
    
    // 验证状态常量
    public static final Integer VERIFICATION_STATUS_UNVERIFIED = 0; // 未验证
    public static final Integer VERIFICATION_STATUS_VERIFIED = 1;   // 已验证
    
    @Id
    private Long id;
    private Long sellerId;
    private Integer accountType;
    private String accountName;
    private String accountNumber;
    private String bankName;
    private String bankCode;
    private String branchName;
    private String platformName;
    private String platformAccount;
    private Integer isDefault;
    private Integer accountStatus;
    private Integer verificationStatus;
    private String idCardNumber;
    private String phone;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
```

### 3.2 PaymentAccountDTO 数据传输对象

```java
@Data
public class PaymentAccountDTO implements Serializable {
    
    private Long id;
    
    @NotNull(message = "账户类型不能为空")
    private Integer accountType;
    
    @NotBlank(message = "账户名称不能为空")
    private String accountName;
    
    @NotBlank(message = "账户号码不能为空")
    private String accountNumber;
    
    private String bankName;
    private String bankCode;
    private String branchName;
    private String platformName;
    private String platformAccount;
    private Integer isDefault;
    private String idCardNumber;
    private String phone;
    private String remark;
}
```

### 3.3 PaymentAccountQueryDTO 查询条件对象

```java
@Data
public class PaymentAccountQueryDTO implements Serializable {
    
    private Integer page = 1;
    private Integer pageSize = 10;
    private Long sellerId;
    private Integer accountType;
    private String accountName;
    private Integer accountStatus;
    private Integer verificationStatus;
    private Integer isDefault;
}
```

### 3.4 PaymentAccountVO 视图对象

```java
@Data
public class PaymentAccountVO implements Serializable {
    
    private Long id;
    private Long sellerId;
    private String sellerName;
    private Integer accountType;
    private String accountTypeDesc;
    private String accountName;
    private String accountNumber;        // 脱敏后的账号
    private String fullAccountNumber;    // 完整账号（仅内部使用）
    private String bankName;
    private String bankCode;
    private String branchName;
    private String platformName;
    private String platformAccount;
    private Integer isDefault;
    private String isDefaultDesc;
    private Integer accountStatus;
    private String accountStatusDesc;
    private Integer verificationStatus;
    private String verificationStatusDesc;
    private String idCardNumber;         // 脱敏后的身份证
    private String phone;                // 脱敏后的手机号
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
```

## 4. 接口定义

### 4.1 商家端接口

#### 基础路径：`/merchant/payment-account`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| POST | `/` | 添加收账账户 | PaymentAccountDTO |
| PUT | `/` | 更新收账账户 | PaymentAccountDTO |
| DELETE | `/{id}` | 删除收账账户 | id |
| GET | `/{id}` | 查询收账账户详情 | id |
| GET | `/page` | 分页查询收账账户 | PaymentAccountQueryDTO |
| GET | `/list` | 查询所有收账账户 | - |
| GET | `/default` | 查询默认收账账户 | - |
| PUT | `/{id}/default` | 设置默认收账账户 | id |
| PUT | `/{id}/enable` | 启用收账账户 | id |
| PUT | `/{id}/disable` | 禁用收账账户 | id |

### 4.2 管理员端接口

#### 基础路径：`/admin/payment-account`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询所有收账账户 | PaymentAccountQueryDTO |
| GET | `/{id}` | 查询收账账户详情 | id |
| GET | `/seller/{sellerId}` | 查询指定商家的收账账户 | sellerId, PaymentAccountQueryDTO |
| PUT | `/{id}/verify` | 验证收账账户 | id |
| PUT | `/{id}/enable` | 启用收账账户 | id |
| PUT | `/{id}/disable` | 禁用收账账户 | id |
| GET | `/statistics/type` | 按账户类型统计 | - |
| GET | `/statistics/verification` | 按验证状态统计 | - |

## 5. 请求响应示例

### 5.1 添加银行卡账户

**请求：**
```json
POST /merchant/payment-account
{
    "accountType": 1,
    "accountName": "张三",
    "accountNumber": "6222021234567890123",
    "bankName": "中国工商银行",
    "bankCode": "ICBC",
    "branchName": "北京分行营业部",
    "idCardNumber": "110101199001011234",
    "phone": "***********",
    "isDefault": 1,
    "remark": "主要收款账户"
}
```

**响应：**
```json
{
    "code": 1,
    "msg": "收账账户添加成功",
    "data": null
}
```

### 5.2 查询收账账户列表

**请求：**
```
GET /merchant/payment-account/list
```

**响应：**
```json
{
    "code": 1,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "sellerId": 16,
            "sellerName": "测试商家",
            "accountType": 1,
            "accountTypeDesc": "银行卡",
            "accountName": "张三",
            "accountNumber": "6222****0123",
            "bankName": "中国工商银行",
            "branchName": "北京分行营业部",
            "isDefault": 1,
            "isDefaultDesc": "是",
            "accountStatus": 1,
            "accountStatusDesc": "启用",
            "verificationStatus": 1,
            "verificationStatusDesc": "已验证",
            "idCardNumber": "1101****1234",
            "phone": "138****8000",
            "remark": "主要收款账户",
            "createTime": "2025-01-29T10:00:00",
            "updateTime": "2025-01-29T10:00:00"
        }
    ]
}
```

## 6. 业务规则

### 6.1 数据验证规则
1. **账户类型验证**：必须是1-4之间的有效值
2. **银行卡特殊验证**：银行卡类型必须填写银行名称和开户支行
3. **账户号码唯一性**：同一商家的同类型账户号码不能重复
4. **默认账户唯一性**：每个商家只能有一个默认账户

### 6.2 状态管理规则
1. **新建账户**：默认为启用状态，未验证状态
2. **默认账户设置**：设置新默认账户时自动清除原默认账户
3. **账户删除**：只能删除自己的账户
4. **状态变更**：禁用状态的账户不能设为默认账户

### 6.3 数据脱敏规则
1. **账户号码**：显示前4位和后4位，中间用****代替
2. **身份证号码**：显示前4位和后4位，中间用****代替
3. **手机号码**：显示前3位和后4位，中间用****代替

## 7. 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 该账户号码已存在 | 账户号码重复 |
| 0 | 银行卡类型必须填写银行名称 | 银行卡验证失败 |
| 0 | 银行卡类型必须填写开户支行 | 银行卡验证失败 |
| 0 | 收账账户不存在 | 账户不存在或无权限 |

## 8. 部署说明

### 8.1 数据库初始化
1. 执行 `settlement_tables.sql` 文件创建数据表
2. 确保seller表已存在且有测试数据
3. 验证外键约束正常工作

### 8.2 代码部署
1. 确保所有依赖包已正确导入
2. 检查MyBatis XML映射文件路径配置
3. 验证JWT认证和权限控制正常工作

### 8.3 测试验证
1. 使用提供的API测试文档进行接口测试
2. 运行单元测试验证业务逻辑
3. 验证数据脱敏功能正常工作

## 9. 注意事项

### 9.1 安全考虑
1. 所有接口都需要JWT认证
2. 商家只能操作自己的账户
3. 敏感信息自动脱敏显示
4. 数据库层面的外键约束保证数据一致性

### 9.2 性能优化
1. 数据库索引优化查询性能
2. 分页查询避免大数据量问题
3. 缓存常用的账户类型描述信息

### 9.3 扩展性
1. 支持新增账户类型
2. 支持自定义验证规则
3. 支持多级审核流程

## 10. 文件清单

### 10.1 数据库文件
- `settlement_tables.sql` - 数据库表结构和初始化脚本

### 10.2 实体类文件
- `SellerPaymentAccount.java` - 收账账户实体类
- `PaymentAccountDTO.java` - 数据传输对象
- `PaymentAccountQueryDTO.java` - 查询条件对象
- `PaymentAccountVO.java` - 视图对象

### 10.3 业务逻辑文件
- `SellerPaymentAccountMapper.java` - 数据访问接口
- `SellerPaymentAccountMapper.xml` - MyBatis映射文件
- `PaymentAccountService.java` - 业务服务接口
- `PaymentAccountServiceImpl.java` - 业务服务实现

### 10.4 控制器文件
- `PaymentAccountController.java` - 商家端控制器
- `AdminPaymentAccountController.java` - 管理员端控制器

### 10.5 测试文件
- `PaymentAccountServiceTest.java` - 单元测试类
- `payment-account-api-test.md` - API测试文档

### 10.6 配置文件
- `MessageConstant.java` - 消息常量（已更新）

## 11. 联系方式

如有问题请联系开发团队进行技术支持。
