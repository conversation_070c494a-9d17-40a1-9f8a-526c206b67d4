package com.sky.service;


import com.sky.dto.AdminAccountDTO;
import com.sky.dto.AdminAccountPageQueryDTO;
import com.sky.dto.AdminAccountUpdateDTO;

import com.sky.dto.AdminLogQueryDTO;
import com.sky.entity.AdminAccount;
import com.sky.result.PageResult;
import com.sky.vo.*;

import java.util.List;

public interface AdminAccountService {

    AdminAccount getAdminById(Long id);

    AdminAccountCreateVO createAdminAccount(AdminAccountDTO dto);

    AdminAccountUpdateVO updateAdminAccount(AdminAccountUpdateDTO dto);

    void deleteAdminAccountById(Long id);

    BatchDeleteResultVO batchDelete(List<Long> ids);

    BatchUpdateStatusResultVO batchUpdateStatus(List<Long> ids, Integer status);

    void resetPassword(Long id, String password);

    PageResult<AdminLogVO> getAdminLogs(AdminLogQueryDTO dto);

    AdminPermissionUpdateVO updatePermissions(Long id, List<String> permisssions);

    PageResult<AdminAccountVO> getAdminAccountList(AdminAccountPageQueryDTO query);
}
