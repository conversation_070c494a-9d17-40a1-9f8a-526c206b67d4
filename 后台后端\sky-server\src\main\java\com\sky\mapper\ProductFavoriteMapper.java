package com.sky.mapper;

import com.sky.entity.ProductFavorite;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ProductFavoriteMapper {

    // 插入收藏记录
    @Insert("INSERT INTO product_favorite (buyer_id, storeName, product_id, product_name, collect_time) " +
            "VALUES (#{buyerId}, #{storeName}, #{productId}, #{productName}, #{collectTime})")
    void insert(ProductFavorite productFavorite);

    // 根据买家 ID 查询收藏列表
    @Select("SELECT * FROM product_favorite WHERE buyer_id = #{buyerId}")
    List<ProductFavorite> findByBuyerId(Long buyerId);

    // 根据收藏 ID 删除收藏记录
    @Delete("DELETE FROM product_favorite WHERE id = #{id}")
    void deleteById(Long id);
}
