package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 支付金额（单位：分）
     */
    private Integer total;

    /**
     * 订单标题
     */
    private String title;

    /**
     * 商户订单号
     */
    private String outTradeNo;
    
    /**
     * 商品描述
     */
    private String description;
}
