package com.sky.controller.admin;

import com.sky.result.Result;
import com.sky.service.TrackingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 物流单号验证测试控制器
 */
@RestController
@RequestMapping("/admin/tracking/validation")
@Api(tags = "物流单号验证测试接口")
@Slf4j
public class TrackingValidationController {

    @Autowired
    private TrackingService trackingService;

    /**
     * 测试物流单号验证
     */
    @PostMapping("/test")
    @ApiOperation("测试物流单号验证")
    public Result<String> testTrackingValidation(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam String courierCode) {
        
        log.info("测试物流单号验证：trackingNumber={}, courierCode={}", trackingNumber, courierCode);
        
        try {
            boolean isValid = trackingService.validateTrackingNumber(trackingNumber, courierCode);
            
            if (isValid) {
                return Result.success("物流单号验证成功！物流单号：" + trackingNumber + "，运输商：" + courierCode);
            } else {
                return Result.error("物流单号验证失败！请检查物流单号和运输商代码是否正确。");
            }
            
        } catch (Exception e) {
            log.error("物流单号验证异常", e);
            return Result.error("验证过程中发生异常：" + e.getMessage());
        }
    }

    /**
     * 获取支持的运输商代码列表
     */
    @GetMapping("/carriers")
    @ApiOperation("获取支持的运输商代码")
    public Result<String> getSupportedCarriers() {
        String carriers = "支持的运输商代码（已修正为17TRACK官方代码）：\n" +
                "• ups (UPS) - 代码: 3001\n" +
                "• fedex (FedEx) - 代码: 3002\n" +
                "• dhl (DHL) - 代码: 3003\n" +
                "• usps (USPS) - 代码: 21051 ⚠️已修正\n" +
                "• tnt (TNT) - 代码: 3005\n" +
                "• aramex (Aramex) - 代码: 3006\n" +
                "• dpd (DPD) - 代码: 3007\n" +
                "• gls (GLS) - 代码: 3008\n" +
                "• chinapost/china-post (中国邮政) - 代码: 3011\n" +
                "• ems (EMS) - 代码: 3012\n" +
                "• sf/sf-express (顺丰速运) - 代码: 190766\n" +
                "• yto (圆通速递) - 代码: 190415\n" +
                "• sto (申通快递) - 代码: 190416\n" +
                "• zto (中通快递) - 代码: 190417\n" +
                "• yt/yunda (韵达速递) - 代码: 190418\n" +
                "• jt (极兔速递) - 代码: 190419";
        
        return Result.success(carriers);
    }

    /**
     * 物流单号格式说明
     */
    @GetMapping("/format-guide")
    @ApiOperation("物流单号格式说明")
    public Result<String> getFormatGuide() {
        String guide = "物流单号格式要求：\n" +
                "1. 长度：5-50位字符\n" +
                "2. 字符类型：只允许字母、数字和中杠(-)\n" +
                "3. 常见格式示例：\n" +
                "   • UPS: 1Z999AA1234567890 (18位，以1Z开头)\n" +
                "   • FedEx: 123456789012 (12位数字) 或 12345678901234 (14位数字)\n" +
                "   • DHL: 1234567890 (10位数字)\n" +
                "   • 中国邮政: RR123456789CN (13位，字母+数字+字母)\n" +
                "   • 顺丰: SF1234567890123 (以SF开头)\n" +
                "4. 注意：物流单号必须在对应运输商系统中真实存在才能验证通过";
        
        return Result.success(guide);
    }
}
