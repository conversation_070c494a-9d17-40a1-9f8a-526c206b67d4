package com.sky.exception;

import com.sky.enumeration.CodeEnum;
import lombok.Getter;

@Getter
public class CannotDeleteSuperAdminException extends BaseException {
    private final int code;
    public CannotDeleteSuperAdminException(int code,String msg) {
        super(msg);
        this.code = code;
    }
    public CannotDeleteSuperAdminException(CodeEnum codeEnum) {
        super(codeEnum.getMessage());
        this.code = codeEnum.getCode();
    }
}
