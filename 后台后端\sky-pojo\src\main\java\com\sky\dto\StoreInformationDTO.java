package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;
import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreInformationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String SellerName;

    private String companyName;

    private String companyDescribe;

    //联系人
    private String liaison;

    private String phone;

    private String address;

    //营业执照
    private String storeImg;

    //发货仓库照片
    private String deliveryImg;
}
