package com.sky.Utils;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
public class QuickStartUtil {

    // 配置参数（测试环境，实际使用时需要替换）
    private static final String MERCHANT_ID = "777463044";
    private static final String MERCHANT_SERIAL_NUMBER = "5157F09EFDC096DE15EBE81A47057A72";
    private static final String API_V3_KEY = "abcdefghigklmnopqrstuvwxyzabzdef";
    private static final String APP_ID = "wx2bd197bcb4b986a0";
    private static final String NOTIFY_URL = "https://your-domain.com/api/payments/wechat/notify";
    private static final String CURRENCY = "CNY";
    private static final int MAX_OUT_TRADE_NO_LENGTH = 32;

    // 跨境支付专用参数
    private static final String TRADE_TYPE = "NATIVE";
    private static final String MERCHANT_CATEGORY_CODE = "5311"; // 一般商品零售

    private String loadPrivateKey() {
        try {
            // 直接从cert目录加载，使用固定路径
            ClassPathResource resource = new ClassPathResource("cert/apiclient_key.pem");
            String privateKey = FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));
            log.info("QuickStartUtil成功加载私钥文件");
            return privateKey;
        } catch (IOException e) {
            log.error("QuickStartUtil无法加载私钥文件: {}", e.getMessage(), e);
            throw new RuntimeException("无法加载私钥文件", e);
        }
    }

    private Config createWechatPayConfig() {
        try {
            return new RSAAutoCertificateConfig.Builder()
                    .merchantId(MERCHANT_ID)
                    .privateKey(loadPrivateKey())
                    .merchantSerialNumber(MERCHANT_SERIAL_NUMBER)
                    .apiV3Key(API_V3_KEY)
                    .build();
        } catch (Exception e) {
            log.error("创建微信支付配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建微信支付配置失败", e);
        }
    }

    private String generateOutTradeNo(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomStr = String.valueOf(ThreadLocalRandom.current().nextInt(10000));

        String baseNo = prefix + "_" + timestamp + "_" + randomStr;
        return baseNo.length() <= MAX_OUT_TRADE_NO_LENGTH
                ? baseNo
                : baseNo.substring(0, MAX_OUT_TRADE_NO_LENGTH);
    }

    /**
     * 创建跨境支付订单（100%兼容最新SDK版本）
     */
    public String createCrossBorderPayment(Integer total, String title, String clientIp) {
        try {
            NativePayService service = new NativePayService.Builder()
                    .config(createWechatPayConfig())
                    .build();

            String outTradeNo = generateOutTradeNo("GLOBAL");
            log.info("跨境支付请求 - 订单号: {}, 金额: {} {}", outTradeNo, total, CURRENCY);

            // 1. 构建基础请求
            PrepayRequest request = new PrepayRequest();

            // 2. 设置金额
            Amount amount = new Amount();
            amount.setTotal(total);
            amount.setCurrency(CURRENCY);
            request.setAmount(amount);

            // 3. 设置基础参数
            request.setAppid(APP_ID);
            request.setMchid(MERCHANT_ID);
            request.setDescription(title.length() > 128 ? title.substring(0, 128) : title);
            request.setNotifyUrl(NOTIFY_URL);
            request.setOutTradeNo(outTradeNo);

            // 4. 设置支付场景
            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setPayerClientIp(clientIp);
            sceneInfo.setDeviceId("WEB");
            request.setSceneInfo(sceneInfo);

            // 5. 通过反射设置跨境专用参数（关键修改点）
            try {
                // 设置交易类型
                java.lang.reflect.Field tradeTypeField = request.getClass().getDeclaredField("tradeType");
                tradeTypeField.setAccessible(true);
                tradeTypeField.set(request, TRADE_TYPE);

                // 设置商户分类代码
                java.lang.reflect.Field mccField = request.getClass().getDeclaredField("merchantCategoryCode");
                mccField.setAccessible(true);
                mccField.set(request, MERCHANT_CATEGORY_CODE);
            } catch (Exception e) {
                log.warn("设置跨境支付扩展参数失败（不影响主要功能）", e);
            }

            // 6. 调用支付接口
            PrepayResponse response = service.prepay(request);
            return response.getCodeUrl();
        } catch (Exception e) {
            log.error("跨境支付失败 - 请检查：1.商户跨境权限 2.参数完整性 3.证书有效性", e);
            throw new RuntimeException("跨境支付创建失败: " + e.getMessage());
        }
    }
}