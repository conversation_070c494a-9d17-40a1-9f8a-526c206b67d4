package com.sky.controller.Seller;

import com.sky.annotation.PreAuthorize;
import com.sky.entity.MerchantOrder;
import com.sky.result.Result;
import com.sky.service.MerchantOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/merchantOrder")
@Api(tags = "商家订单管理接口")
public class MerchantOrderController {
    @Autowired
    private MerchantOrderService merchantOrderService;

    @PreAuthorize("merchantOrder:all")
    @GetMapping("/all")
    @ApiOperation("获取所有订单")
    public Result<List<MerchantOrder>> findAll() {
        List<MerchantOrder> merchantOrders = merchantOrderService.findAll();
        return Result.success(merchantOrders);
    }

    @PreAuthorize("merchantOrder:byStatus")
    @GetMapping("/byStatus/{status}")
    @ApiOperation("根据状态获取订单")
    public Result<List<MerchantOrder>> findByStatus(@PathVariable Integer status) {
        List<MerchantOrder> merchantOrders = merchantOrderService.findByStatus(status);
        return Result.success(merchantOrders);
    }

    @PreAuthorize("merchantOrder:byOrderNo")
    @GetMapping("/byOrderNo/{orderNo}")
    @ApiOperation("根据订单号获取订单")
    public Result<List<MerchantOrder>> findByOrderNo(@PathVariable String orderNo) {
        List<MerchantOrder> merchantOrders = merchantOrderService.findByOrderNo(orderNo);
        return Result.success(merchantOrders);
    }

    @PreAuthorize("merchantOrder:byTransactionNo")
    @GetMapping("/byTransactionNo/{transactionNo}")
    @ApiOperation("根据流水号获取订单")
    public Result<List<MerchantOrder>> findByTransactionNo(@PathVariable String transactionNo) {
        List<MerchantOrder> merchantOrders = merchantOrderService.findByTransactionNo(transactionNo);
        return Result.success(merchantOrders);
    }

    @PreAuthorize("merchantOrder:byTrackingNo")
    @GetMapping("/byTrackingNo/{trackingNo}")
    @ApiOperation("根据运单号获取订单")
    public Result<List<MerchantOrder>> findByTrackingNo(@PathVariable String trackingNo) {
        List<MerchantOrder> merchantOrders = merchantOrderService.findByTrackingNo(trackingNo);
        return Result.success(merchantOrders);
    }
}