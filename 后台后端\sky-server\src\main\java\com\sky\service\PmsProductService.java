package com.sky.service;

import com.sky.entity.PmsProduct;

import java.util.List;

public interface PmsProductService {
    List<PmsProduct> getProductsBySellerId(Long sellerId);
    List<PmsProduct> getAllProducts();
    PmsProduct addProduct(Long sellerId, PmsProduct product);
    void deleteProduct(Long sellerId, Long productId);
    PmsProduct updateProduct(Long sellerId, Long productId, PmsProduct product);

    PmsProduct getProductsById(Long productId);

    void changePass(Long id);

    void deleteProductById(Long id);
}