<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SettlementConfigMapper">

    <resultMap id="BaseResultMap" type="com.sky.entity.SettlementConfig">
        <id column="id" property="id"/>
        <result column="config_key" property="configKey"/>
        <result column="config_value" property="configValue"/>
        <result column="config_desc" property="configDesc"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectByKey" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM settlement_config WHERE config_key = #{configKey}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM settlement_config ORDER BY create_time
    </select>

    <insert id="insert" parameterType="com.sky.entity.SettlementConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO settlement_config (config_key, config_value, config_desc, create_time, update_time)
        VALUES (#{configKey}, #{configValue}, #{configDesc}, #{createTime}, #{updateTime})
    </insert>

    <update id="update" parameterType="com.sky.entity.SettlementConfig">
        UPDATE settlement_config
        SET config_value = #{configValue},
            config_desc = #{configDesc},
            update_time = #{updateTime}
        WHERE config_key = #{configKey}
    </update>

    <delete id="deleteByKey" parameterType="string">
        DELETE FROM settlement_config WHERE config_key = #{configKey}
    </delete>

</mapper>
