# 订单详情集成功能部署配置说明

## 1. 数据库配置

### 执行数据库脚本
确保已执行以下数据库脚本创建相关表：

```sql
-- 1. 订单物流跟踪表
CREATE TABLE IF NOT EXISTS `order_logistics_tracking` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '订单号',
  `tracking_number` varchar(100) NOT NULL COMMENT '物流单号',
  `carrier_code` varchar(50) NOT NULL COMMENT '运输商代码',
  `carrier_name` varchar(100) NOT NULL COMMENT '运输商名称',
  `current_status` varchar(50) DEFAULT NULL COMMENT '当前物流状态',
  `current_status_desc` varchar(200) DEFAULT NULL COMMENT '当前状态描述',
  `latest_location` varchar(200) DEFAULT NULL COMMENT '最新位置',
  `estimated_delivery_time` datetime DEFAULT NULL COMMENT '预计送达时间',
  `actual_delivery_time` datetime DEFAULT NULL COMMENT '实际送达时间',
  `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_tracking` (`order_id`, `tracking_number`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_tracking_number` (`tracking_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单物流跟踪表';

-- 2. 物流轨迹详情表
CREATE TABLE IF NOT EXISTS `logistics_trace_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tracking_id` bigint NOT NULL COMMENT '物流跟踪ID',
  `tracking_number` varchar(100) NOT NULL COMMENT '物流单号',
  `trace_time` datetime NOT NULL COMMENT '轨迹时间',
  `trace_location` varchar(200) DEFAULT NULL COMMENT '轨迹位置',
  `trace_status` varchar(50) DEFAULT NULL COMMENT '轨迹状态',
  `trace_desc` varchar(500) NOT NULL COMMENT '轨迹描述',
  `operator` varchar(100) DEFAULT NULL COMMENT '操作员',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_tracking_id` (`tracking_id`),
  KEY `idx_tracking_number` (`tracking_number`),
  KEY `idx_trace_time` (`trace_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流轨迹详情表';

-- 3. 订单地址信息表
CREATE TABLE IF NOT EXISTS `order_address_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `address_type` tinyint NOT NULL COMMENT '地址类型：1-收货地址，2-发货地址',
  `consignee_name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `province_code` varchar(20) DEFAULT NULL COMMENT '省份编码',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `city_code` varchar(20) DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
  `district_code` varchar(20) DEFAULT NULL COMMENT '区县编码',
  `district_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
  `detail_address` varchar(200) NOT NULL COMMENT '详细地址',
  `postal_code` varchar(10) DEFAULT NULL COMMENT '邮政编码',
  `is_default` tinyint DEFAULT 0 COMMENT '是否默认地址：0-否，1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_address_type` (`address_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单地址信息表';

-- 4. 订单支付详情表
CREATE TABLE IF NOT EXISTS `order_payment_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '订单号',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式：wechat-微信，alipay-支付宝，bank-银行卡',
  `payment_channel` varchar(50) DEFAULT NULL COMMENT '支付渠道',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `payment_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '货币类型',
  `payment_status` varchar(20) NOT NULL COMMENT '支付状态：pending-待支付，success-成功，failed-失败，refunded-已退款',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `refund_amount` decimal(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `payment_desc` varchar(200) DEFAULT NULL COMMENT '支付描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_payment` (`order_id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单支付详情表';

-- 5. 订单状态变更日志表
CREATE TABLE IF NOT EXISTS `order_status_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '订单号',
  `old_status` int DEFAULT NULL COMMENT '原状态',
  `new_status` int NOT NULL COMMENT '新状态',
  `status_desc` varchar(100) NOT NULL COMMENT '状态描述',
  `operator_type` varchar(20) DEFAULT NULL COMMENT '操作者类型：user-用户，admin-管理员，system-系统',
  `operator_id` bigint DEFAULT NULL COMMENT '操作者ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作者姓名',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单状态变更日志表';
```

### 插入测试数据
```sql
-- 插入测试数据
INSERT INTO `order_logistics_tracking` (`order_id`, `order_number`, `tracking_number`, `carrier_code`, `carrier_name`, `current_status`, `current_status_desc`, `latest_location`, `ship_time`, `last_update_time`) VALUES
(1, '202412190001', 'SF1234567890123', 'sf', '顺丰速运', 'InTransit', '运输途中', '北京分拣中心', '2024-12-19 10:00:00', '2024-12-19 15:30:00'),
(2, '202412190002', '9434609208316579761831', 'usps', 'USPS', 'OutForDelivery', '派送途中', '纽约配送中心', '2024-12-18 14:00:00', '2024-12-19 09:20:00');

INSERT INTO `logistics_trace_detail` (`tracking_id`, `tracking_number`, `trace_time`, `trace_location`, `trace_status`, `trace_desc`, `operator`, `sort_order`) VALUES
(1, 'SF1234567890123', '2024-12-19 10:00:00', '深圳宝安区', 'PickedUp', '快件已从寄件网点发出', '张三', 1),
(1, 'SF1234567890123', '2024-12-19 12:30:00', '深圳转运中心', 'InTransit', '快件已到达深圳转运中心', '李四', 2),
(1, 'SF1234567890123', '2024-12-19 15:30:00', '北京分拣中心', 'InTransit', '快件已到达北京分拣中心，正在分拣', '王五', 3);

INSERT INTO `order_address_info` (`order_id`, `address_type`, `consignee_name`, `phone`, `province_name`, `city_name`, `district_name`, `detail_address`, `postal_code`) VALUES
(1, 1, '张三', '13800138000', '北京市', '北京市', '朝阳区', '三里屯街道1号院2号楼3单元401室', '100000'),
(1, 2, '发货仓库', '************', '广东省', '深圳市', '宝安区', '西乡街道物流园区A区1号仓库', '518000');

INSERT INTO `order_payment_detail` (`order_id`, `order_number`, `payment_method`, `payment_channel`, `transaction_id`, `payment_amount`, `payment_status`, `payment_time`, `payment_desc`) VALUES
(1, '202412190001', 'wechat', 'wxpay', 'wx20241219100001', 299.00, 'success', '2024-12-19 09:30:00', '微信支付'),
(2, '202412190002', 'alipay', 'alipay', 'ali20241218140001', 599.00, 'success', '2024-12-18 13:45:00', '支付宝支付');
```

## 2. 应用配置

### application.yml配置
在`application.yml`或`application-dev.yml`中添加17TRACK API配置：

```yaml
# 17TRACK物流API配置
track17:
  api:
    key: YOUR_17TRACK_API_KEY  # 替换为实际的API密钥
    url: https://api.17track.net/track/v2.2
    connect-timeout: 10000
    read-timeout: 30000
    max-retry-count: 3
    rate-limit: 3  # 每秒最大请求数
```

### 环境变量配置
也可以通过环境变量配置：

```bash
export TRACK17_API_KEY=your_api_key_here
export TRACK17_API_URL=https://api.17track.net/track/v2.2
```

## 3. 依赖检查

确保项目中包含以下依赖：

```xml
<!-- Spring Boot Web -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- MyBatis Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>

<!-- MySQL驱动 -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
</dependency>

<!-- Lombok -->
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
</dependency>

<!-- JSON处理 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
</dependency>
```

## 4. 编译和打包

```bash
# 清理并编译
mvn clean compile

# 运行测试
mvn test

# 打包
mvn clean package -DskipTests

# 运行应用
java -jar target/sky-server-1.0-SNAPSHOT.jar
```

## 5. 部署验证

### 健康检查
```bash
# 检查应用是否启动成功
curl http://localhost:8443/actuator/health

# 检查数据库连接
curl http://localhost:8443/actuator/health/db
```

### 功能验证
```bash
# 测试获取订单详情接口
curl -X GET "http://localhost:8443/user/order-detail/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 6. 监控配置

### 日志配置
在`logback-spring.xml`中添加订单详情相关的日志配置：

```xml
<!-- 订单详情集成服务日志 -->
<logger name="com.sky.service.impl.OrderDetailIntegrationServiceImpl" level="INFO"/>
<logger name="com.sky.controller.user.OrderDetailIntegrationController" level="INFO"/>
<logger name="com.sky.service.impl.Track17ApiServiceImpl" level="DEBUG"/>
```

### 性能监控
建议添加以下监控指标：
- API响应时间
- 17TRACK API调用成功率
- 数据库查询性能
- 订单操作成功率

## 7. 安全配置

### JWT配置
确保JWT配置正确：

```yaml
sky:
  jwt:
    admin-secret-key: your-secret-key
    admin-ttl: 7200000
    admin-token-name: token
```

### 接口权限
确保所有订单详情相关接口都在JWT拦截器的保护范围内。

## 8. 性能优化

### 数据库优化
```sql
-- 添加必要的索引
CREATE INDEX idx_orders_buyer_status ON orders(buyer_id, status);
CREATE INDEX idx_order_logistics_order_id ON order_logistics_tracking(order_id);
CREATE INDEX idx_logistics_trace_tracking_time ON logistics_trace_detail(tracking_id, trace_time DESC);
```

### 缓存配置
可以考虑为以下数据添加缓存：
- 订单详情信息（5分钟）
- 物流状态映射（1小时）
- 用户订单统计（10分钟）

## 9. 故障排查

### 常见问题

1. **编译错误**
   - 检查所有依赖是否正确导入
   - 确认实体类字段名与数据库字段匹配

2. **数据库连接问题**
   - 检查数据库配置
   - 确认数据库表是否创建成功

3. **17TRACK API调用失败**
   - 检查API密钥配置
   - 确认网络连接正常
   - 查看API调用频率是否超限

4. **权限验证失败**
   - 检查JWT配置
   - 确认token格式正确

### 日志查看
```bash
# 查看应用日志
tail -f logs/sky-server.log

# 查看错误日志
grep ERROR logs/sky-server.log

# 查看订单详情相关日志
grep "OrderDetailIntegration" logs/sky-server.log
```

## 10. 备份和恢复

### 数据备份
```bash
# 备份相关表数据
mysqldump -u username -p database_name \
  order_logistics_tracking \
  logistics_trace_detail \
  order_address_info \
  order_payment_detail \
  order_status_log > order_detail_backup.sql
```

### 数据恢复
```bash
# 恢复数据
mysql -u username -p database_name < order_detail_backup.sql
```

## 11. 版本升级

### 升级步骤
1. 备份当前数据
2. 停止应用服务
3. 部署新版本
4. 执行数据库迁移脚本（如有）
5. 启动应用服务
6. 验证功能正常

### 回滚计划
如果升级失败，可以：
1. 停止新版本应用
2. 恢复数据库备份
3. 启动旧版本应用
4. 验证功能恢复正常
