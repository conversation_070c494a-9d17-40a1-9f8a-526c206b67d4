package com.sky.service.impl;

import com.sky.constant.MessageConstant;
import com.sky.dto.PassWordDTO;
import com.sky.entity.Buyer;
import com.sky.exception.AccountNotFoundException;
import com.sky.mapper.ChangeMapper;
import com.sky.service.ChangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ChangeServiceImpl implements ChangeService {
    @Autowired
    private ChangeMapper changeMapper;

    @Override
    public void ChangePassWord(PassWordDTO passWordDTO) {
        String phone = passWordDTO.getPhone();
        Buyer buyer = changeMapper.getBuyerByphone(phone);
        if (buyer == null) {
            throw new AccountNotFoundException("手机号输入错误，未查询到此用户");
        }
        String oldPassWord = changeMapper.getpassword(phone);
        String OldPassword = passWordDTO.getOldPassword();
        if (!oldPassWord.equals(OldPassword)) {
            throw new AccountNotFoundException("旧密码输入错误，请重试");
        }
        String newPassword = passWordDTO.getNewPassword();
        changeMapper.UpdatePassWord(newPassword,phone);
    }
}
