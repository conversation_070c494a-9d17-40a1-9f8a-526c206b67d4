package com.sky.vo;

import com.sky.entity.OrderDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVO  {

    private Long id;                        // 订单ID
    private String number;                  // 订单号
    private Long buyerId;                   // 买家ID
    private Long addressId;                 // 收货地址ID
    private Long shippingMethodId;          // 配送方式ID
    private Integer status;                 // 订单状态
    private BigDecimal amount;              // 订单总金额
    private LocalDateTime orderTime;        // 下单时间
    private LocalDateTime payTime;          // 支付时间
    private LocalDateTime checkoutTime;     // 结算时间
    private LocalDateTime shipTime;         // 发货时间
    private LocalDateTime completeTime;     // 完成时间
    private LocalDateTime cancelTime;       // 取消时间
    private Integer payMethod;              // 支付方式
    private String paymentTransactionId;    // 支付交易ID
    private String orderRemark;             // 订单备注
    private String cancelReason;            // 取消原因
    private String rejectionReason;         // 拒绝原因
    private Integer cancelRequest;          // 是否申请取消
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间

    private List<OrderDetail> orderDetails; // 订单详情列表
}
