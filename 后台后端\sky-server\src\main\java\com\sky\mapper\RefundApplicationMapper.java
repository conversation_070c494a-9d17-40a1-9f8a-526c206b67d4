package com.sky.mapper;

import com.sky.entity.RefundApplication;
import com.sky.vo.RefundApplicationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款申请Mapper接口
 */
@Mapper
public interface RefundApplicationMapper {

    /**
     * 插入退款申请
     * @param refundApplication 退款申请
     * @return 影响行数
     */
    int insert(RefundApplication refundApplication);

    /**
     * 根据ID查询退款申请
     * @param id 退款申请ID
     * @return 退款申请
     */
    RefundApplication selectById(@Param("id") Long id);

    /**
     * 根据订单ID查询退款申请
     * @param orderId 订单ID
     * @return 退款申请列表
     */
    List<RefundApplication> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据买家ID查询退款申请
     * @param buyerId 买家ID
     * @return 退款申请列表
     */
    List<RefundApplication> selectByBuyerId(@Param("buyerId") Long buyerId);

    /**
     * 根据退款申请单号查询
     * @param refundNo 退款申请单号
     * @return 退款申请
     */
    RefundApplication selectByRefundNo(@Param("refundNo") String refundNo);

    /**
     * 查询待审核的退款申请
     * @return 待审核的退款申请列表
     */
    List<RefundApplication> selectPendingApproval();

    /**
     * 分页查询退款申请（带详细信息）
     * @param buyerId 买家ID（可选）
     * @param applicationStatus 申请状态（可选）
     * @param needApproval 是否需要审核（可选）
     * @param approvalStatus 审核状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 退款申请VO列表
     */
    List<RefundApplicationVO> selectPageWithDetails(
            @Param("buyerId") Long buyerId,
            @Param("applicationStatus") Integer applicationStatus,
            @Param("needApproval") Integer needApproval,
            @Param("approvalStatus") Integer approvalStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 统计退款申请数量
     * @param buyerId 买家ID（可选）
     * @param applicationStatus 申请状态（可选）
     * @param needApproval 是否需要审核（可选）
     * @param approvalStatus 审核状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计数量
     */
    Long countRefundApplications(
            @Param("buyerId") Long buyerId,
            @Param("applicationStatus") Integer applicationStatus,
            @Param("needApproval") Integer needApproval,
            @Param("approvalStatus") Integer approvalStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据ID查询退款申请详情（包含关联信息）
     * @param id 退款申请ID
     * @return 退款申请VO
     */
    RefundApplicationVO selectDetailById(@Param("id") Long id);

    /**
     * 更新申请状态
     * @param id 退款申请ID
     * @param applicationStatus 申请状态
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateApplicationStatus(@Param("id") Long id, 
                               @Param("applicationStatus") Integer applicationStatus,
                               @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新审核信息
     * @param id 退款申请ID
     * @param approvalStatus 审核状态
     * @param approverId 审核人ID
     * @param approvalTime 审核时间
     * @param approvalRemark 审核备注
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateApprovalInfo(@Param("id") Long id,
                          @Param("approvalStatus") Integer approvalStatus,
                          @Param("approverId") Long approverId,
                          @Param("approvalTime") LocalDateTime approvalTime,
                          @Param("approvalRemark") String approvalRemark,
                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新退款完成信息
     * @param id 退款申请ID
     * @param actualRefundAmount 实际退款金额
     * @param refundTime 退款完成时间
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateRefundCompleteInfo(@Param("id") Long id,
                                @Param("actualRefundAmount") java.math.BigDecimal actualRefundAmount,
                                @Param("refundTime") LocalDateTime refundTime,
                                @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据状态查询退款申请列表
     * @param applicationStatus 申请状态
     * @return 退款申请列表
     */
    List<RefundApplication> selectByStatus(@Param("applicationStatus") Integer applicationStatus);

    /**
     * 查找退款中超时的申请
     */
    @Select("SELECT * FROM refund_application WHERE application_status = 5 AND update_time < #{timeoutTime}")
    List<RefundApplication> findRefundingTimeout(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 查找待审核超时的申请
     */
    @Select("SELECT * FROM refund_application WHERE application_status = 2 AND create_time < #{timeoutTime}")
    List<RefundApplication> findPendingApprovalTimeout(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 查找可自动处理的退款申请
     */
    @Select("SELECT * FROM refund_application WHERE application_status = 1 AND need_approval = 0")
    List<RefundApplication> findAutoProcessableRefunds();

    /**
     * 更新备注信息
     */
    @Update("UPDATE refund_application SET remark = #{remark}, update_time = NOW() WHERE id = #{id}")
    void updateRemark(@Param("id") Long id, @Param("remark") String remark);

    /**
     * 更新拒绝信息
     */
    @Update("UPDATE refund_application SET reject_operator = #{rejectOperator}, reject_reason = #{rejectReason}, " +
            "reject_time = #{rejectTime}, update_time = NOW() WHERE id = #{id}")
    void updateRejectInfo(@Param("id") Long id,
                         @Param("rejectOperator") String rejectOperator,
                         @Param("rejectReason") String rejectReason,
                         @Param("rejectTime") LocalDateTime rejectTime);
}
