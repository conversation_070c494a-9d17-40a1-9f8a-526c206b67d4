package com.sky.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CommissionSettings {
    private Long id;
    private BigDecimal basicRate;
    private BigDecimal teamRate;
    private BigDecimal leaderRate;
    private BigDecimal minWithdraw;
    private String withdrawCycle;
    private List<String> withdrawDays;
    private String upgradeRules;
    private String rebatePolicy;
    private String createTime;
    private String updateTime;
} 