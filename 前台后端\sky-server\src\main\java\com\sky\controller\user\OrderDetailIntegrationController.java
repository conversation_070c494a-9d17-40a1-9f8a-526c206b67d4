package com.sky.controller.user;

import com.sky.context.BaseContext;
import com.sky.entity.LogisticsTraceDetail;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.OrderDetailIntegrationService;
import com.sky.vo.OrderDetailIntegrationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户端订单详情集成控制器
 */
@RestController
@RequestMapping("/user/order-detail")
@Api(tags = "用户端订单详情集成接口")
@Slf4j
public class OrderDetailIntegrationController {

    @Autowired
    private OrderDetailIntegrationService orderDetailIntegrationService;

    /**
     * 获取订单完整详情
     */
    @GetMapping("/{orderId}")
    @ApiOperation("获取订单完整详情")
    public Result<OrderDetailIntegrationVO> getOrderDetail(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId) {
        
        log.info("用户查询订单详情，订单ID：{}", orderId);
        
        Long userId = BaseContext.getCurrentId();
        OrderDetailIntegrationVO orderDetail = orderDetailIntegrationService
                .getOrderDetailIntegration(orderId, userId);
        
        return Result.success(orderDetail);
    }

    /**
     * 根据订单号获取订单详情
     */
    @GetMapping("/by-number/{orderNumber}")
    @ApiOperation("根据订单号获取订单详情")
    public Result<OrderDetailIntegrationVO> getOrderDetailByNumber(
            @ApiParam(value = "订单号", required = true) @PathVariable String orderNumber) {
        
        log.info("用户根据订单号查询订单详情，订单号：{}", orderNumber);
        
        Long userId = BaseContext.getCurrentId();
        OrderDetailIntegrationVO orderDetail = orderDetailIntegrationService
                .getOrderDetailIntegrationByNumber(orderNumber, userId);
        
        return Result.success(orderDetail);
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/list")
    @ApiOperation("获取用户订单列表")
    public Result<List<OrderDetailIntegrationVO>> getUserOrderList(
            @ApiParam(value = "订单状态", required = false) @RequestParam(required = false) Integer status,
            @ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", required = false) @RequestParam(defaultValue = "10") Integer pageSize) {
        
        log.info("用户查询订单列表，状态：{}，页码：{}，每页大小：{}", status, pageNum, pageSize);
        
        Long userId = BaseContext.getCurrentId();
        List<OrderDetailIntegrationVO> orderList = orderDetailIntegrationService
                .getUserOrderList(userId, status, pageNum, pageSize);
        
        return Result.success(orderList);
    }

    /**
     * 同步订单物流信息
     */
    @PostMapping("/{orderId}/sync-logistics")
    @ApiOperation("同步订单物流信息")
    public Result<String> syncLogistics(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId) {
        
        log.info("用户请求同步物流信息，订单ID：{}", orderId);
        
        Long userId = BaseContext.getCurrentId();
        
        // 验证订单归属
        OrderDetailIntegrationVO orderDetail = orderDetailIntegrationService
                .getOrderDetailIntegration(orderId, userId);
        
        if (orderDetail.getLogisticsTracking() == null) {
            return Result.error("该订单暂无物流信息");
        }
        
        boolean success = orderDetailIntegrationService.syncLogisticsTrace(orderId);
        
        if (success) {
            return Result.success("物流信息同步成功");
        } else {
            return Result.error("物流信息同步失败，请稍后重试");
        }
    }

    /**
     * 获取实时物流信息
     */
    @GetMapping("/{orderId}/realtime-logistics")
    @ApiOperation("获取实时物流信息")
    public Result<List<LogisticsTraceDetail>> getRealtimeLogistics(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId) {
        
        log.info("用户查询实时物流信息，订单ID：{}", orderId);
        
        Long userId = BaseContext.getCurrentId();
        OrderDetailIntegrationVO orderDetail = orderDetailIntegrationService
                .getOrderDetailIntegration(orderId, userId);
        
        if (orderDetail.getLogisticsTracking() == null) {
            return Result.error("该订单暂无物流信息");
        }
        
        List<LogisticsTraceDetail> realtimeTraces = orderDetailIntegrationService
                .getRealtimeLogisticsInfo(
                        orderDetail.getLogisticsTracking().getTrackingNumber(),
                        orderDetail.getLogisticsTracking().getCarrierCode()
                );
        
        return Result.success(realtimeTraces);
    }

    /**
     * 确认收货
     */
    @PostMapping("/{orderId}/confirm-receipt")
    @ApiOperation("确认收货")
    public Result<String> confirmReceipt(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId) {
        
        log.info("用户确认收货，订单ID：{}", orderId);
        
        Long userId = BaseContext.getCurrentId();
        orderDetailIntegrationService.confirmReceipt(orderId, userId);
        
        return Result.success("确认收货成功");
    }

    /**
     * 申请退款
     */
    @PostMapping("/{orderId}/apply-refund")
    @ApiOperation("申请退款")
    public Result<String> applyRefund(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId,
            @ApiParam(value = "退款原因", required = true) @RequestParam String reason) {
        
        log.info("用户申请退款，订单ID：{}，原因：{}", orderId, reason);
        
        Long userId = BaseContext.getCurrentId();
        orderDetailIntegrationService.applyRefund(orderId, userId, reason);
        
        return Result.success("退款申请提交成功，请等待处理");
    }

    /**
     * 取消订单
     */
    @PostMapping("/{orderId}/cancel")
    @ApiOperation("取消订单")
    public Result<String> cancelOrder(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId,
            @ApiParam(value = "取消原因", required = false) @RequestParam(required = false) String reason) {
        
        log.info("用户取消订单，订单ID：{}，原因：{}", orderId, reason);
        
        Long userId = BaseContext.getCurrentId();
        orderDetailIntegrationService.cancelOrder(orderId, userId, reason);
        
        return Result.success("订单取消成功");
    }

    /**
     * 获取用户订单统计
     */
    @GetMapping("/statistics")
    @ApiOperation("获取用户订单统计")
    public Result<Map<String, Object>> getUserOrderStatistics() {
        
        log.info("用户查询订单统计");
        
        Long userId = BaseContext.getCurrentId();
        Map<String, Object> statistics = orderDetailIntegrationService.getUserOrderStatistics(userId);
        
        return Result.success(statistics);
    }

    /**
     * 获取最近物流更新
     */
    @GetMapping("/recent-logistics")
    @ApiOperation("获取最近物流更新")
    public Result<List<LogisticsTraceDetail>> getRecentLogisticsUpdates(
            @ApiParam(value = "限制数量", required = false) @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("用户查询最近物流更新，限制：{}", limit);
        
        Long userId = BaseContext.getCurrentId();
        List<LogisticsTraceDetail> recentUpdates = orderDetailIntegrationService
                .getRecentLogisticsUpdates(userId, limit);
        
        return Result.success(recentUpdates);
    }

    /**
     * 检查订单操作权限
     */
    @GetMapping("/{orderId}/check-action/{action}")
    @ApiOperation("检查订单操作权限")
    public Result<Boolean> checkOrderAction(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId,
            @ApiParam(value = "操作类型", required = true) @PathVariable String action) {
        
        log.info("检查订单操作权限，订单ID：{}，操作：{}", orderId, action);
        
        Long userId = BaseContext.getCurrentId();
        boolean canPerform = orderDetailIntegrationService.canPerformAction(orderId, userId, action);
        
        return Result.success(canPerform);
    }

    /**
     * 获取物流状态说明
     */
    @GetMapping("/logistics-status-desc/{status}")
    @ApiOperation("获取物流状态说明")
    public Result<String> getLogisticsStatusDescription(
            @ApiParam(value = "物流状态", required = true) @PathVariable String status) {
        
        String description = orderDetailIntegrationService.getLogisticsStatusDescription(status);
        return Result.success(description);
    }
}
