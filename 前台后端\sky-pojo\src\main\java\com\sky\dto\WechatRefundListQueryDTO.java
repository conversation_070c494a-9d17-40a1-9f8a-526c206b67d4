package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 微信查询所有退款请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatRefundListQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信支付订单号（与商户订单号二选一）
     */
    private String transactionId;

    /**
     * 商户订单号（与微信支付订单号二选一）
     */
    private String outTradeNo;

    /**
     * 商户号（直连模式必填）
     */
    private String mchid;

    /**
     * 子商户号（机构模式必填）
     */
    private String subMchid;

    /**
     * 机构商户号（机构模式必填）
     */
    private String spMchid;

    /**
     * 记录起始位置（分页功能）
     */
    private Integer offset;

    /**
     * 每页笔数（分页功能，最大值限制为20）
     */
    private Integer count;
}
