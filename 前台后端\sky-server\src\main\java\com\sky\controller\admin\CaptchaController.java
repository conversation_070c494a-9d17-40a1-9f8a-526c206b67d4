package com.sky.controller.admin;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Controller
@CrossOrigin
@Slf4j
public class CaptchaController {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    static String uuid = UUID.randomUUID().toString().replace("-", "");

    @Autowired
    private DefaultKaptcha captchaProducer;

    @GetMapping("/captcha/{phone}")
    @ResponseBody
    public void generateCaptcha(HttpServletResponse response, @PathVariable("phone") String phone) throws IOException {
        // 生成验证码文本
        log.info("开始生成图片");
        String captchaText = captchaProducer.createText();
        String lowerCase = captchaText.toLowerCase();
        stringRedisTemplate.opsForValue().set(phone, lowerCase,1L, TimeUnit.MINUTES);
        // 生成验证码图片
        BufferedImage captchaImage = captchaProducer.createImage(captchaText);
        // 将验证码图片写入到 HTTP 响应
        response.setContentType("image/jpeg");
        try (ServletOutputStream out = response.getOutputStream()) {
            javax.imageio.ImageIO.write(captchaImage, "jpg", out);
        }
    }
}
