<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.MerChantMapper">
    <insert id="save">
        insert into ry_mall.merchant (shop_id, shop_name, mobile, contact_name, province, city, detail_address, status, create_time, update_time)
        values (#{shopId},#{shopName},#{mobile},#{contactName},#{province},#{city},#{detailAddress},#{status},#{createTime},#{updateTime})
    </insert>
    <update id="MerChantchange">
        update ry_mall.merchant
        set status = #{status}
        where shop_id = #{ShopId}
    </update>

</mapper>
