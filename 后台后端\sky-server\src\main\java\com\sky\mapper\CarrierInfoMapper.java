package com.sky.mapper;

import com.sky.entity.CarrierInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 运输商信息Mapper
 */
@Mapper
public interface CarrierInfoMapper {

    /**
     * 根据运输商代码查询
     */
    @Select("SELECT * FROM carrier_info WHERE carrier_code = #{carrierCode}")
    CarrierInfo findByCarrierCode(Integer carrierCode);

    /**
     * 查询所有启用的运输商
     */
    @Select("SELECT * FROM carrier_info WHERE is_active = true ORDER BY carrier_name")
    List<CarrierInfo> findAllActive();

    /**
     * 根据国家代码查询
     */
    @Select("SELECT * FROM carrier_info WHERE country_code = #{countryCode} AND is_active = true")
    List<CarrierInfo> findByCountryCode(String countryCode);

    /**
     * 插入运输商信息
     */
    @Insert("INSERT INTO carrier_info (carrier_code, carrier_name, carrier_name_en, carrier_type, " +
            "country_code, website, phone, is_active, param_required, param_type, param_example, " +
            "param_required_flag, create_time, update_time) " +
            "VALUES (#{carrierCode}, #{carrierName}, #{carrierNameEn}, #{carrierType}, " +
            "#{countryCode}, #{website}, #{phone}, #{isActive}, #{paramRequired}, #{paramType}, " +
            "#{paramExample}, #{paramRequiredFlag}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(CarrierInfo carrierInfo);

    /**
     * 更新运输商信息
     */
    @Update("UPDATE carrier_info SET carrier_name = #{carrierName}, carrier_name_en = #{carrierNameEn}, " +
            "carrier_type = #{carrierType}, country_code = #{countryCode}, website = #{website}, " +
            "phone = #{phone}, is_active = #{isActive}, param_required = #{paramRequired}, " +
            "param_type = #{paramType}, param_example = #{paramExample}, " +
            "param_required_flag = #{paramRequiredFlag}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    void update(CarrierInfo carrierInfo);

    /**
     * 批量插入运输商信息
     */
    void insertBatch(List<CarrierInfo> carrierInfos);

    /**
     * 检查运输商代码是否存在
     */
    @Select("SELECT COUNT(*) FROM carrier_info WHERE carrier_code = #{carrierCode}")
    int countByCarrierCode(Integer carrierCode);
}
