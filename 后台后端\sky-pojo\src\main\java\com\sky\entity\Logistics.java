package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 物流信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Logistics {
    
    private Long id;                        // 主键ID
    private String trackingNumber;          // 物流单号
    private String courierCode;             // 快递公司代码
    private String courierName;             // 快递公司名称
    private Long orderId;                   // 订单ID
    private String orderNumber;             // 订单号
    private String status;                  // 物流状态
    private String statusDesc;              // 状态描述
    private String originCountry;           // 发货国家
    private String destinationCountry;      // 目的国家
    private LocalDateTime shipTime;         // 发货时间
    private LocalDateTime deliveryTime;     // 送达时间
    private LocalDateTime estimatedDeliveryTime; // 预计送达时间
    private String latestEventInfo;         // 最新事件信息
    private LocalDateTime latestEventTime;  // 最新事件时间
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间
}
