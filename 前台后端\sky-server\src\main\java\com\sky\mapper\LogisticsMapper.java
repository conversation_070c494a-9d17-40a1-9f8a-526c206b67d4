package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.sky.entity.Logistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 物流信息Mapper接口
 */
@Mapper
public interface LogisticsMapper extends BaseMapper<Logistics> {
    
    /**
     * 根据ID查询物流信息
     * @param id 物流ID
     * @return 物流信息
     */
    Logistics getById(@Param("id") Long id);
    
    /**
     * 更新物流状态
     * @param id 物流ID
     * @param logisticsStatus 物流状态
     * @return 影响行数
     */
    int updateLogisticsStatus(@Param("id") Long id, @Param("logisticsStatus") String logisticsStatus);
}