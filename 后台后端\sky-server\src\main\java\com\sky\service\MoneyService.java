package com.sky.service;

import com.sky.dto.DataRecordsPageDTO;
import com.sky.dto.FundFlowPageDTO;
import com.sky.result.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;

public interface MoneyService {
    PageResult pageQuery(DataRecordsPageDTO dataRecordsPageDTO);

    BigDecimal getByAmount(Long id);

    PageResult fundFlowpageQuery(FundFlowPageDTO fundFlowPageDTO);

    void exportFlowData(LocalDate startDate, LocalDate endDate, HttpServletResponse response);
}
