package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 注册物流跟踪DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "注册物流跟踪DTO")
public class RegisterTrackingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物流单号", required = true)
    @NotBlank(message = "物流单号不能为空")
    private String trackingNumber;

    @ApiModelProperty(value = "运输商代码", required = true)
    @NotNull(message = "运输商代码不能为空")
    private Integer carrierCode;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "标签")
    private String tag;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否自动检测运输商")
    private Boolean autoDetection;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户邮箱")
    private String customerEmail;

    @ApiModelProperty(value = "目的地国家代码")
    private String destinationCountry;

    @ApiModelProperty(value = "原产地国家代码")
    private String originCountry;

    @ApiModelProperty(value = "商品标题")
    private String itemTitle;

    @ApiModelProperty(value = "商品价格")
    private String itemPrice;

    @ApiModelProperty(value = "商品重量")
    private String itemWeight;

    @ApiModelProperty(value = "商品数量")
    private Integer itemQuantity;

    @ApiModelProperty(value = "商品SKU")
    private String itemSku;

    @ApiModelProperty(value = "订单金额")
    private String orderAmount;

    @ApiModelProperty(value = "订单创建时间")
    private String orderCreateTime;

    @ApiModelProperty(value = "物流费用")
    private String shippingCost;

    @ApiModelProperty(value = "收件人姓名")
    private String recipientName;

    @ApiModelProperty(value = "收件人地址")
    private String recipientAddress;

    @ApiModelProperty(value = "收件人城市")
    private String recipientCity;

    @ApiModelProperty(value = "收件人州/省")
    private String recipientState;

    @ApiModelProperty(value = "收件人邮编")
    private String recipientPostalCode;

    @ApiModelProperty(value = "收件人电话")
    private String recipientPhone;

    @ApiModelProperty(value = "收件人邮箱")
    private String recipientEmail;
}
