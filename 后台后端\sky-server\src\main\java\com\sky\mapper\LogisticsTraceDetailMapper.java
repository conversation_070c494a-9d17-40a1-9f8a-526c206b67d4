package com.sky.mapper;

import com.sky.entity.LogisticsTraceDetail;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 物流轨迹详情Mapper
 */
@Mapper
public interface LogisticsTraceDetailMapper {

    /**
     * 插入物流轨迹详情
     */
    @Insert("INSERT INTO logistics_trace_detail (tracking_id, tracking_number, trace_time, trace_location, " +
            "trace_status, trace_desc, operator, phone, sort_order, create_time) VALUES " +
            "(#{trackingId}, #{trackingNumber}, #{traceTime}, #{traceLocation}, " +
            "#{traceStatus}, #{traceDesc}, #{operator}, #{phone}, #{sortOrder}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(LogisticsTraceDetail traceDetail);

    /**
     * 根据物流跟踪ID查询轨迹详情
     */
    @Select("SELECT * FROM logistics_trace_detail WHERE tracking_id = #{trackingId} ORDER BY sort_order DESC")
    List<LogisticsTraceDetail> getByTrackingId(Long trackingId);

    /**
     * 根据物流单号查询轨迹详情
     */
    @Select("SELECT * FROM logistics_trace_detail WHERE tracking_number = #{trackingNumber} ORDER BY sort_order DESC")
    List<LogisticsTraceDetail> getByTrackingNumber(String trackingNumber);

    /**
     * 批量插入物流轨迹详情
     */
    void batchInsert(@Param("traceDetails") List<LogisticsTraceDetail> traceDetails);
}
