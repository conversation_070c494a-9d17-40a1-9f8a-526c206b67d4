<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物流跟踪查询</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .search-section {
            padding: 40px;
        }

        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }

        .search-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .search-btn:hover {
            transform: translateY(-2px);
        }

        .result-section {
            margin-top: 30px;
            display: none;
        }

        .tracking-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .tracking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .tracking-number {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .tracking-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
        }

        .status-delivered {
            background: #28a745;
        }

        .status-in-transit {
            background: #007bff;
        }

        .status-shipped {
            background: #ffc107;
            color: #333;
        }

        .status-unknown {
            background: #6c757d;
        }

        .tracking-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .info-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .info-value {
            font-weight: bold;
            color: #333;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .example-section {
            background: #f8f9fa;
            padding: 20px;
            margin-top: 30px;
            border-radius: 10px;
        }

        .example-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .example-numbers {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .example-number {
            background: white;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-family: monospace;
        }

        .example-number:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚚 物流跟踪查询</h1>
            <p>输入您的物流单号，实时查询包裹状态</p>
        </div>

        <div class="search-section">
            <div class="search-form">
                <input type="text" class="search-input" id="trackingInput" 
                       placeholder="请输入物流单号，如：1234567890" maxlength="50">
                <button class="search-btn" onclick="searchTracking()">查询物流</button>
            </div>

            <div class="example-section">
                <div class="example-title">示例物流单号（点击快速查询）：</div>
                <div class="example-numbers">
                    <span class="example-number" onclick="quickSearch('RR123456789CN')">RR123456789CN</span>
                    <span class="example-number" onclick="quickSearch('1234567890')">1234567890</span>
                    <span class="example-number" onclick="quickSearch('CP123456789CN')">CP123456789CN</span>
                </div>
            </div>

            <div class="result-section" id="resultSection">
                <!-- 查询结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 快速搜索
        function quickSearch(trackingNumber) {
            document.getElementById('trackingInput').value = trackingNumber;
            searchTracking();
        }

        // 搜索物流信息
        async function searchTracking() {
            const trackingNumber = document.getElementById('trackingInput').value.trim();
            const resultSection = document.getElementById('resultSection');

            if (!trackingNumber) {
                alert('请输入物流单号');
                return;
            }

            // 显示加载状态
            resultSection.style.display = 'block';
            resultSection.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在查询物流信息，请稍候...</p>
                </div>
            `;

            try {
                // 调用API查询物流信息
                const response = await fetch(`/user/tracking/query/${encodeURIComponent(trackingNumber)}`);
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    displayTrackingResult(result.data);
                } else {
                    displayError(result.msg || '查询失败，请检查物流单号是否正确');
                }
            } catch (error) {
                console.error('查询失败:', error);
                displayError('网络错误，请稍后重试');
            }
        }

        // 显示查询结果
        function displayTrackingResult(data) {
            const resultSection = document.getElementById('resultSection');
            
            if (!data.trackingList || data.trackingList.length === 0) {
                displayError('未找到该物流单号的信息');
                return;
            }

            const tracking = data.trackingList[0];
            const statusClass = getStatusClass(tracking.status);

            resultSection.innerHTML = `
                <div class="tracking-card">
                    <div class="tracking-header">
                        <div class="tracking-number">${tracking.trackingNumber}</div>
                        <div class="tracking-status ${statusClass}">${tracking.statusDescription || tracking.status}</div>
                    </div>
                    
                    <div class="tracking-info">
                        <div class="info-item">
                            <div class="info-label">运输商</div>
                            <div class="info-value">${tracking.carrierName || '未知'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">创建时间</div>
                            <div class="info-value">${formatDate(tracking.createTime)}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最后更新</div>
                            <div class="info-value">${formatDate(tracking.updateTime)}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">目的地</div>
                            <div class="info-value">${tracking.destinationCountry || '未知'}</div>
                        </div>
                    </div>

                    ${tracking.trackingEvents && tracking.trackingEvents.length > 0 ? `
                        <div class="tracking-events">
                            <h4>物流轨迹</h4>
                            ${tracking.trackingEvents.map(event => `
                                <div class="event-item">
                                    <div class="event-time">${formatDate(event.eventTime)}</div>
                                    <div class="event-description">${event.eventDescription}</div>
                                    <div class="event-location">${event.eventLocation || ''}</div>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 显示错误信息
        function displayError(message) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <div class="error-message">
                    ❌ ${message}
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            if (!status) return 'status-unknown';
            
            const statusLower = status.toLowerCase();
            if (statusLower.includes('delivered') || statusLower.includes('已送达')) {
                return 'status-delivered';
            } else if (statusLower.includes('transit') || statusLower.includes('运输中')) {
                return 'status-in-transit';
            } else if (statusLower.includes('shipped') || statusLower.includes('已发货')) {
                return 'status-shipped';
            } else {
                return 'status-unknown';
            }
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            
            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            } catch (error) {
                return dateString;
            }
        }

        // 回车键搜索
        document.getElementById('trackingInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchTracking();
            }
        });
    </script>
</body>
</html>
