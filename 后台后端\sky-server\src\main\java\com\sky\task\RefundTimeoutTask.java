package com.sky.task;

import com.sky.entity.RefundApplication;
import com.sky.mapper.RefundApplicationMapper;
import com.sky.service.RefundApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款超时处理定时任务
 */
@Component
@Slf4j
public class RefundTimeoutTask {

    @Autowired
    private RefundApplicationMapper refundApplicationMapper;

    @Autowired
    private RefundApplicationService refundApplicationService;

    /**
     * 处理退款超时
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void handleRefundTimeout() {
        log.info("开始执行退款超时处理任务");
        
        try {
            // 1. 处理退款中超时的申请（24小时）
            handleRefundingTimeout();
            
            // 2. 处理待审核超时的申请（72小时）
            handlePendingApprovalTimeout();
            
            log.info("退款超时处理任务完成");
            
        } catch (Exception e) {
            log.error("退款超时处理任务执行异常", e);
        }
    }

    /**
     * 处理退款中超时的申请
     */
    private void handleRefundingTimeout() {
        LocalDateTime timeoutTime = LocalDateTime.now().minusHours(24);
        
        // 查找24小时前开始退款但仍在退款中的申请
        List<RefundApplication> timeoutRefunds = refundApplicationMapper.findRefundingTimeout(timeoutTime);
        
        for (RefundApplication refund : timeoutRefunds) {
            try {
                // 将状态改为退款失败，需要人工处理
                refundApplicationMapper.updateApplicationStatus(
                    refund.getId(),
                    RefundApplication.STATUS_REFUND_FAILED,
                    LocalDateTime.now()
                );
                
                // 添加备注
                refundApplicationMapper.updateRemark(
                    refund.getId(),
                    "退款超时，需要人工处理。原因：微信退款接口24小时未响应"
                );
                
                log.warn("退款申请 {} 超时，已标记为失败，需要人工处理", refund.getRefundNo());
                
            } catch (Exception e) {
                log.error("处理退款超时失败，refundNo: {}", refund.getRefundNo(), e);
            }
        }
        
        if (!timeoutRefunds.isEmpty()) {
            log.info("处理退款中超时申请 {} 个", timeoutRefunds.size());
        }
    }

    /**
     * 处理待审核超时的申请
     */
    private void handlePendingApprovalTimeout() {
        LocalDateTime timeoutTime = LocalDateTime.now().minusHours(72);
        
        // 查找72小时前提交但仍待审核的申请
        List<RefundApplication> timeoutApprovals = refundApplicationMapper.findPendingApprovalTimeout(timeoutTime);
        
        for (RefundApplication refund : timeoutApprovals) {
            try {
                // 自动拒绝超时的审核申请
                refundApplicationMapper.updateApplicationStatus(
                    refund.getId(),
                    RefundApplication.STATUS_REJECTED,
                    LocalDateTime.now()
                );
                
                // 添加拒绝原因
                refundApplicationMapper.updateRejectInfo(
                    refund.getId(),
                    "系统自动拒绝",
                    "申请超时未处理（72小时）",
                    LocalDateTime.now()
                );
                
                log.info("退款申请 {} 审核超时，已自动拒绝", refund.getRefundNo());
                
            } catch (Exception e) {
                log.error("处理审核超时失败，refundNo: {}", refund.getRefundNo(), e);
            }
        }
        
        if (!timeoutApprovals.isEmpty()) {
            log.info("处理待审核超时申请 {} 个", timeoutApprovals.size());
        }
    }

    /**
     * 自动处理符合条件的退款申请
     * 每30分钟执行一次
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void autoProcessRefunds() {
        log.info("开始执行自动退款处理任务");
        
        try {
            // 查找待处理且不需要审核的退款申请
            List<RefundApplication> autoProcessRefunds = refundApplicationMapper.findAutoProcessableRefunds();
            
            for (RefundApplication refund : autoProcessRefunds) {
                try {
                    // 调用退款处理服务
                    boolean success = refundApplicationService.processRefund(refund.getId());
                    
                    if (success) {
                        log.info("自动处理退款成功，refundNo: {}", refund.getRefundNo());
                    } else {
                        log.warn("自动处理退款失败，refundNo: {}", refund.getRefundNo());
                    }
                    
                } catch (Exception e) {
                    log.error("自动处理退款异常，refundNo: {}", refund.getRefundNo(), e);
                }
            }
            
            if (!autoProcessRefunds.isEmpty()) {
                log.info("自动退款处理任务完成，共处理 {} 个申请", autoProcessRefunds.size());
            }
            
        } catch (Exception e) {
            log.error("自动退款处理任务执行异常", e);
        }
    }
}
