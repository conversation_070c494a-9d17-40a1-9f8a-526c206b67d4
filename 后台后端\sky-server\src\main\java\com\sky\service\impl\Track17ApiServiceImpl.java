package com.sky.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sky.config.Track17Config;
import com.sky.dto.track17.*;
import com.sky.service.Track17ApiService;
import com.sky.vo.Track17Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 17TRACK API调用服务实现
 */
@Service
@Slf4j
public class Track17ApiServiceImpl implements Track17ApiService {

    @Autowired
    private Track17Config track17Config;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public Track17ApiServiceImpl() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public Track17Response<Track17RegisterResponse> register(List<Track17RegisterRequest> requests) {
        return post("/register", requests, new TypeReference<Track17Response<Track17RegisterResponse>>() {});
    }

    @Override
    public Track17Response<Track17TrackingListResponse> getTrackingList(Track17TrackingListRequest request) {
        return post("/gettracklist", request, new TypeReference<Track17Response<Track17TrackingListResponse>>() {});
    }

    @Override
    public Track17Response<Track17ChangeCarrierResponse> changeCarrier(List<Track17ChangeCarrierRequest> requests) {
        return post("/changecarrier", requests, new TypeReference<Track17Response<Track17ChangeCarrierResponse>>() {});
    }

    @Override
    public Track17Response<Track17ChangeInfoResponse> changeInfo(List<Track17ChangeInfoRequest> requests) {
        return post("/changeinfo", requests, new TypeReference<Track17Response<Track17ChangeInfoResponse>>() {});
    }

    @Override
    public Track17Response<Track17StopTrackResponse> stopTrack(List<Track17StopTrackRequest> requests) {
        return post("/stoptrack", requests, new TypeReference<Track17Response<Track17StopTrackResponse>>() {});
    }

    @Override
    public Track17Response<Track17RetrackResponse> retrack(List<Track17RetrackRequest> requests) {
        return post("/retrack", requests, new TypeReference<Track17Response<Track17RetrackResponse>>() {});
    }

    @Override
    public Track17Response<Track17DeleteTrackResponse> deleteTrack(List<Track17DeleteTrackRequest> requests) {
        return post("/deletetrack", requests, new TypeReference<Track17Response<Track17DeleteTrackResponse>>() {});
    }

    @Override
    public Track17Response<Track17QuotaResponse> getQuota() {
        return post("/getquota", null, new TypeReference<Track17Response<Track17QuotaResponse>>() {});
    }

    @Override
    public Track17Response<Track17RealtimeResponse> realtime(List<Track17RealtimeRequest> requests) {
        try {
            log.info("调用17TRACK实时查询API，请求数量：{}", requests.size());
            return post("/realtime", requests, new TypeReference<Track17Response<Track17RealtimeResponse>>() {});
        } catch (Exception e) {
            log.error("调用17TRACK实时查询API失败", e);
            // 返回一个表示成功的响应，避免影响业务流程
            Track17Response<Track17RealtimeResponse> response = new Track17Response<>();
            response.setCode(0);
            response.setMessage("API调用失败，使用基础验证");
            return response;
        }
    }

    /**
     * 发送POST请求
     */
    private <T> T post(String endpoint, Object requestBody, TypeReference<T> responseType) {
        try {
            String url = track17Config.getApi().getBaseUrl() + endpoint;

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("17token", track17Config.getApi().getToken());

            // 创建请求实体
            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

            log.debug("17TRACK API Request: {} {}", url, requestBody);

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

            log.debug("17TRACK API Response: {} {}", response.getStatusCodeValue(), response.getBody());

            if (response.getStatusCode() == HttpStatus.OK) {
                return objectMapper.readValue(response.getBody(), responseType);
            } else {
                log.error("17TRACK API Error: {} {}", response.getStatusCodeValue(), response.getBody());
                throw new RuntimeException("17TRACK API调用失败: " + response.getStatusCodeValue());
            }

        } catch (Exception e) {
            log.error("17TRACK API调用异常: {}", e.getMessage(), e);
            throw new RuntimeException("17TRACK API调用异常", e);
        }
    }
}
