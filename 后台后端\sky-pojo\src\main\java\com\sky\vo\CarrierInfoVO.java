package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 运输商信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarrierInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;                     // 主键ID
    private Integer carrierCode;            // 运输商代码
    private String carrierName;             // 运输商名称
    private String carrierNameEn;           // 运输商英文名称
    private String carrierType;             // 运输商类型
    private String countryCode;             // 国家代码
    private String website;                 // 官方网站
    private String phone;                   // 联系电话
    private Boolean isActive;               // 是否启用
    private Boolean paramRequired;          // 是否需要附加参数
    private String paramType;               // 参数类型
    private String paramExample;            // 参数示例
    private Boolean paramRequiredFlag;      // 参数是否必填
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间
}
