package com.sky.handler;

import com.sky.exception.BaseException;
import com.sky.exception.OrderNotFoundException;
import com.sky.exception.OrderStatusException;
import com.sky.exception.TrackingValidationException;
import com.sky.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器，处理项目中抛出的业务异常
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 捕获物流验证异常
     */
    @ExceptionHandler(TrackingValidationException.class)
    public Result<String> handleTrackingValidationException(TrackingValidationException ex){
        log.error("物流验证异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获订单状态异常
     */
    @ExceptionHandler(OrderStatusException.class)
    public Result<String> handleOrderStatusException(OrderStatusException ex){
        log.error("订单状态异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获订单不存在异常
     */
    @ExceptionHandler(OrderNotFoundException.class)
    public Result<String> handleOrderNotFoundException(OrderNotFoundException ex){
        log.error("订单不存在异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获业务异常
     */
    @ExceptionHandler(BaseException.class)
    public Result<String> handleBaseException(BaseException ex){
        log.error("业务异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<String> handleException(Exception ex){
        log.error("系统异常：{}", ex.getMessage(), ex);
        return Result.error("系统繁忙，请稍后重试");
    }

}
