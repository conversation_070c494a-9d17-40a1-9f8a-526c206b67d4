package com.sky.service;

import com.sky.dto.PaymentAccountDTO;
import com.sky.dto.PaymentAccountQueryDTO;
import com.sky.vo.PageResult;
import com.sky.vo.PaymentAccountVO;

import java.util.List;

/**
 * 收账账户信息服务接口
 */
public interface PaymentAccountService {

    /**
     * 添加收账账户
     * @param paymentAccountDTO 收账账户信息
     * @param sellerId 商家ID
     */
    void addPaymentAccount(PaymentAccountDTO paymentAccountDTO, Long sellerId);

    /**
     * 更新收账账户
     * @param paymentAccountDTO 收账账户信息
     * @param sellerId 商家ID
     */
    void updatePaymentAccount(PaymentAccountDTO paymentAccountDTO, Long sellerId);

    /**
     * 删除收账账户
     * @param id 账户ID
     * @param sellerId 商家ID
     */
    void deletePaymentAccount(Long id, Long sellerId);

    /**
     * 根据ID查询收账账户
     * @param id 账户ID
     * @param sellerId 商家ID
     * @return 收账账户信息
     */
    PaymentAccountVO getPaymentAccountById(Long id, Long sellerId);

    /**
     * 分页查询收账账户
     * @param queryDTO 查询条件
     * @param sellerId 商家ID（商家端查询时使用）
     * @return 分页结果
     */
    PageResult pageQuery(PaymentAccountQueryDTO queryDTO, Long sellerId);

    /**
     * 查询商家的所有收账账户
     * @param sellerId 商家ID
     * @return 收账账户列表
     */
    List<PaymentAccountVO> getPaymentAccountsBySellerId(Long sellerId);

    /**
     * 查询商家的默认收账账户
     * @param sellerId 商家ID
     * @return 默认收账账户
     */
    PaymentAccountVO getDefaultPaymentAccount(Long sellerId);

    /**
     * 设置默认收账账户
     * @param id 账户ID
     * @param sellerId 商家ID
     */
    void setDefaultPaymentAccount(Long id, Long sellerId);

    /**
     * 启用/禁用收账账户
     * @param id 账户ID
     * @param sellerId 商家ID
     * @param accountStatus 账户状态
     */
    void updateAccountStatus(Long id, Long sellerId, Integer accountStatus);

    /**
     * 验证收账账户
     * @param id 账户ID
     * @param sellerId 商家ID（管理员操作时可为null）
     */
    void verifyPaymentAccount(Long id, Long sellerId);

    /**
     * 管理员分页查询所有收账账户
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult adminPageQuery(PaymentAccountQueryDTO queryDTO);

    /**
     * 管理员根据ID查询收账账户
     * @param id 账户ID
     * @return 收账账户信息
     */
    PaymentAccountVO adminGetPaymentAccountById(Long id);

    /**
     * 管理员验证收账账户
     * @param id 账户ID
     */
    void adminVerifyPaymentAccount(Long id);

    /**
     * 管理员启用/禁用收账账户
     * @param id 账户ID
     * @param accountStatus 账户状态
     */
    void adminUpdateAccountStatus(Long id, Integer accountStatus);
}
