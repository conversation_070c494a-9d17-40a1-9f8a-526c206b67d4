package com.sky.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String msgType;         // 站内信类型(商家消息，用户消息)
    private Object recipient;       // 站内信接受者
    private Boolean sendToAllUsers;     // 是否发送给全体用户
    private Boolean sendToAllMerchants; // 是否发送给全体商家
    private Boolean sendToAllSellers; // 是否发送给全体卖家
    private String title;           // 站内信标题
    private String content;         // 站内信内容
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime; // 消息发送时间
    private Long senderID;          // 发送者ID
    private String type;            // 发送者身份:merchant(商家) 或Administrator(管理员)
} 