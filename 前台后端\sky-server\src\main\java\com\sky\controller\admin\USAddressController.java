package com.sky.controller.admin;

import com.sky.entity.USAddress;
import com.sky.result.Result;
import com.sky.service.USAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/user/address")
@Api(tags = "C端地址接口")
public class USAddressController {

    @Autowired
    private USAddressService addressService;

    @PostMapping("/create")
    @ApiOperation("添加收货地址")
    public Result<Void> addAddress(@RequestBody USAddress address) {
        address.setUserId(address.getUserId());
        addressService.addAddress(address);
        return Result.success();
    }

    @GetMapping("/list")
    @ApiOperation("查询地址")
    public Result<List<USAddress>> getAllAddresses(@RequestParam("userId") Long userId) {
        List<USAddress> addresses = addressService.getAllAddresses(userId);
        return Result.success(addresses);
    }

    @PutMapping("/update/{id}")
    @ApiOperation("更新收货地址")
    public Result<Void> updateAddress(@RequestBody USAddress address) {
        addressService.updateAddress(address);
        return Result.success();
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除收货地址")
    public Result<Void> deleteAddress(@PathVariable Long id) {
        addressService.deleteAddress(id);
        return Result.success();
    }

    @PutMapping("/default/{id}")
    public Result defaultSet(@PathVariable("id") Long id){
        addressService.defaultSet(id);
        return Result.success();
    }


}