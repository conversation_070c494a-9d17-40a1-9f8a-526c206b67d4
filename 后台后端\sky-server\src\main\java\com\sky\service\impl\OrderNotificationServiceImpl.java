package com.sky.service.impl;

import com.sky.entity.Orders;
import com.sky.service.OrderNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 订单通知服务实现
 */
@Service
@Slf4j
public class OrderNotificationServiceImpl implements OrderNotificationService {

    @Override
    public void sendOrderStatusChangeNotification(Orders order, Integer oldStatus, Integer newStatus) {
        log.info("发送订单状态变更通知，订单号：{}，状态：{} -> {}", 
                order.getNumber(), getStatusName(oldStatus), getStatusName(newStatus));
        
        try {
            // 根据状态变更类型发送不同的通知
            switch (newStatus) {
                case 2: // 已付款
                    sendPaymentSuccessNotification(order);
                    break;
                case 4: // 已发货
                    sendShippedNotification(order);
                    break;
                case 5: // 已完成
                    sendCompletedNotification(order);
                    break;
                case 6: // 已取消
                    sendCancelledNotification(order);
                    break;
                case 7: // 已退款
                    sendRefundedNotification(order);
                    break;
                default:
                    log.debug("订单状态 {} 无需发送通知", newStatus);
            }
            
        } catch (Exception e) {
            log.error("发送订单状态变更通知失败，订单号：{}", order.getNumber(), e);
        }
    }

    @Override
    public void sendPaymentSuccessNotification(Orders order) {
        log.info("发送支付成功通知，订单号：{}", order.getNumber());
        
        // TODO: 实现具体的通知逻辑
        // 1. 发送短信通知
        // 2. 发送邮件通知
        // 3. 推送APP通知
        // 4. 微信模板消息
        
        // 模拟通知发送
        String message = String.format("您的订单 %s 支付成功，金额：￥%.2f，我们将尽快为您发货。", 
                order.getNumber(), order.getAmount());
        
        sendNotification(order.getBuyerId(), "支付成功", message);
    }

    @Override
    public void sendShippedNotification(Orders order) {
        log.info("发送发货通知，订单号：{}", order.getNumber());
        
        String message = String.format("您的订单 %s 已发货，物流单号：%s，快递公司：%s，请注意查收。", 
                order.getNumber(), 
                order.getTrackingNumber() != null ? order.getTrackingNumber() : "暂无",
                order.getCourierName() != null ? order.getCourierName() : "暂无");
        
        sendNotification(order.getBuyerId(), "订单已发货", message);
    }

    @Override
    public void sendCompletedNotification(Orders order) {
        log.info("发送订单完成通知，订单号：{}", order.getNumber());
        
        String message = String.format("您的订单 %s 已完成，感谢您的购买！如有问题请联系客服。", 
                order.getNumber());
        
        sendNotification(order.getBuyerId(), "订单已完成", message);
    }

    @Override
    public void sendCancelledNotification(Orders order) {
        log.info("发送订单取消通知，订单号：{}", order.getNumber());
        
        String message = String.format("您的订单 %s 已取消，取消原因：%s。如有疑问请联系客服。", 
                order.getNumber(), 
                order.getCancelReason() != null ? order.getCancelReason() : "未知原因");
        
        sendNotification(order.getBuyerId(), "订单已取消", message);
    }

    @Override
    public void sendRefundedNotification(Orders order) {
        log.info("发送退款成功通知，订单号：{}", order.getNumber());
        
        String message = String.format("您的订单 %s 退款已完成，退款金额：￥%.2f，请注意查收。", 
                order.getNumber(), 
                order.getRefundAmount() != null ? order.getRefundAmount() : order.getAmount());
        
        sendNotification(order.getBuyerId(), "退款成功", message);
    }

    @Override
    public void sendLowStockAlert(Long productId, String productName, Integer currentStock) {
        log.warn("发送库存不足警告，商品：{}，当前库存：{}", productName, currentStock);
        
        String message = String.format("商品 %s (ID:%d) 库存不足，当前库存：%d，请及时补货。", 
                productName, productId, currentStock);
        
        // 发送给管理员
        sendAdminNotification("库存不足警告", message);
    }

    @Override
    public void sendRefundApplicationNotification(String refundNo, String buyerName, String reason) {
        log.info("发送退款申请通知，退款单号：{}", refundNo);
        
        String message = String.format("收到新的退款申请，退款单号：%s，申请人：%s，申请原因：%s，请及时处理。", 
                refundNo, buyerName, reason);
        
        // 发送给管理员
        sendAdminNotification("新退款申请", message);
    }

    /**
     * 发送通知给用户
     */
    private void sendNotification(Long userId, String title, String message) {
        try {
            // TODO: 实现具体的通知发送逻辑
            // 这里可以集成：
            // 1. 短信服务（阿里云SMS、腾讯云SMS等）
            // 2. 邮件服务（SMTP、SendGrid等）
            // 3. 推送服务（极光推送、个推等）
            // 4. 微信模板消息
            
            log.info("发送通知给用户 {}，标题：{}，内容：{}", userId, title, message);
            
            // 模拟发送成功
            Thread.sleep(100);
            
        } catch (Exception e) {
            log.error("发送通知失败，用户ID：{}，标题：{}", userId, title, e);
        }
    }

    /**
     * 发送通知给管理员
     */
    private void sendAdminNotification(String title, String message) {
        try {
            // TODO: 实现管理员通知逻辑
            // 可以通过邮件、钉钉、企业微信等方式通知管理员
            
            log.info("发送管理员通知，标题：{}，内容：{}", title, message);
            
            // 模拟发送成功
            Thread.sleep(100);
            
        } catch (Exception e) {
            log.error("发送管理员通知失败，标题：{}", title, e);
        }
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) return "未知";
        
        switch (status) {
            case 1: return "待付款";
            case 2: return "已付款";
            case 3: return "处理中";
            case 4: return "已发货";
            case 5: return "已完成";
            case 6: return "已取消";
            case 7: return "已退款";
            default: return "未知状态(" + status + ")";
        }
    }
}
