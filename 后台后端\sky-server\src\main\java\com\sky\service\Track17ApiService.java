package com.sky.service;

import com.sky.dto.track17.*;
import com.sky.vo.Track17Response;

import java.util.List;

/**
 * 17TRACK API调用服务接口
 */
public interface Track17ApiService {

    /**
     * 注册物流单号
     * @param requests 注册请求列表
     * @return API响应
     */
    Track17Response<Track17RegisterResponse> register(List<Track17RegisterRequest> requests);

    /**
     * 获取跟踪列表
     * @param request 查询请求
     * @return API响应
     */
    Track17Response<Track17TrackingListResponse> getTrackingList(Track17TrackingListRequest request);

    /**
     * 修改运输商
     * @param requests 修改请求列表
     * @return API响应
     */
    Track17Response<Track17ChangeCarrierResponse> changeCarrier(List<Track17ChangeCarrierRequest> requests);

    /**
     * 修改信息
     * @param requests 修改请求列表
     * @return API响应
     */
    Track17Response<Track17ChangeInfoResponse> changeInfo(List<Track17ChangeInfoRequest> requests);

    /**
     * 停止跟踪
     * @param requests 停止请求列表
     * @return API响应
     */
    Track17Response<Track17StopTrackResponse> stopTrack(List<Track17StopTrackRequest> requests);

    /**
     * 重启跟踪
     * @param requests 重启请求列表
     * @return API响应
     */
    Track17Response<Track17RetrackResponse> retrack(List<Track17RetrackRequest> requests);

    /**
     * 删除跟踪
     * @param requests 删除请求列表
     * @return API响应
     */
    Track17Response<Track17DeleteTrackResponse> deleteTrack(List<Track17DeleteTrackRequest> requests);

    /**
     * 获取配额
     * @return API响应
     */
    Track17Response<Track17QuotaResponse> getQuota();

    /**
     * 实时查询
     * @param requests 查询请求列表
     * @return API响应
     */
    Track17Response<Track17RealtimeResponse> realtime(List<Track17RealtimeRequest> requests);
}
