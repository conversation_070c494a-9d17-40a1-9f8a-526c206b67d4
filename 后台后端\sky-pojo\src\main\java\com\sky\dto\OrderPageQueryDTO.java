package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单分页查询数据传输对象
 */
@Data
public class OrderPageQueryDTO implements Serializable {

    private int page = 1;                       // 页码，默认第1页
    private int pageSize = 10;                  // 每页记录数，默认10条
    private String number;                      // 订单号（支持模糊查询）
    private Long buyerId;                       // 买家ID
    private Integer status;                     // 订单状态
    private List<Integer> statusList;           // 订单状态列表（多状态查询）
    private LocalDateTime beginTime;            // 开始时间
    private LocalDateTime endTime;              // 结束时间
    private BigDecimal minAmount;               // 最小金额
    private BigDecimal maxAmount;               // 最大金额
    private Integer payMethod;                  // 支付方式
    private String paymentTransactionId;        // 支付交易ID
    private String orderRemark;                 // 订单备注（支持模糊查询）
    private Long addressId;                     // 收货地址ID
    private Long shippingMethodId;              // 配送方式ID

    // 排序相关
    private String sortField = "order_time";    // 排序字段，默认按下单时间
    private String sortOrder = "desc";          // 排序方向，默认降序

    // 高级搜索
    private String productName;                 // 商品名称（支持模糊查询）
    private Long productId;                     // 商品ID
    private String buyerPhone;                  // 买家手机号
    private String buyerName;                   // 买家姓名

    // 时间快捷选择
    private String timeRange;                   // 时间范围：today, yesterday, week, month, quarter, year

    // 金额范围快捷选择
    private String amountRange;                 // 金额范围：0-100, 100-500, 500-1000, 1000+
}
