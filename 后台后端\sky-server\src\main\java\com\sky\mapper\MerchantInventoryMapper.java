package com.sky.mapper;

import com.sky.entity.MerchantInventory;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface MerchantInventoryMapper {
    @Select("SELECT * FROM merchant_inventory")
    List<MerchantInventory> findAll();

    @Select("SELECT * FROM merchant_inventory WHERE product_code = #{productCode}")
    List<MerchantInventory> findByProductCode(String productCode);

    @Select("SELECT * FROM merchant_inventory WHERE item_code = #{itemCode}")
    List<MerchantInventory> findByItemCode(String itemCode);

    @Select("SELECT * FROM merchant_inventory WHERE product_title LIKE CONCAT('%', #{productTitle}, '%')")
    List<MerchantInventory> findByProductTitle(String productTitle);

    @Select("SELECT * FROM merchant_inventory WHERE created_at BETWEEN #{startTime} AND #{endTime}")
    List<MerchantInventory> findByCreatedAtRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    @Update("UPDATE merchant_inventory SET safety_inventory = #{safetyInventory} WHERE id = #{id}")
    void updateSafetyInventory(@Param("id") Long id, @Param("safetyInventory") Integer safetyInventory);
}