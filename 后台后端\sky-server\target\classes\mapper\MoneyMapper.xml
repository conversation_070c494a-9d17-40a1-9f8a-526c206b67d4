<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.MoneyMapper">

    <select id="pageQuery" resultType="com.sky.entity.MessageNotifications"
            parameterType="com.sky.dto.DataRecordsPageDTO">
        select * from data_records
        <where>
            <if test="serialNumber != null and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="FirstTime != null">
                and created_time &gt;= #{FirstTime}
            </if>
            <if test="LastTime != null">
                and created_time &lt;= #{LastTime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>
    <select id="getStatus" resultType="com.sky.entity.DataRecords" parameterType="java.lang.Long">

    </select>
    <select id="fundFlowPageQuery" resultType="com.sky.entity.FundFlow"
            parameterType="com.sky.dto.FundFlowPageDTO">
        select * from fund_flow
        <where>
            <if test="operationType != null and operationType != ''">
                and operation_type = #{operationType}
            </if>
            <if test="FirstTime != null">
                and operation_time &gt;= #{FirstTime}
            </if>
            <if test="LastTime != null">
                and operation_time &lt;= #{LastTime}
            </if>
        </where>
    </select>
    <select id="listFundFlowData" resultType="com.sky.vo.FundFlowVO">
        SELECT
            operation_time AS operationTime,
            operation_type AS operationType,
            serial_number AS serialNumber,
            order_number AS orderNumber,
            fund_type AS fundType,
            order_amount_percent AS orderAmountPercent,
            platform_fund_percent AS platformFundPercent,
            fund_flow_percent AS fundFlowPercent,
            balance_percent AS balancePercent,
            created_time AS createdTime
        FROM fund_flow
        WHERE operation_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY operation_time DESC
    </select>
</mapper>
