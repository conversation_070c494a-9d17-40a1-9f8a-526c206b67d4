# 回款功能API测试文档

## 测试环境
- 后台后端服务地址：http://localhost:8080
- 数据库：已配置回款相关表

## 商家端接口测试

### 1. 查询商家回款汇总信息
```bash
curl -X GET "http://localhost:8080/merchant/settlement/summary" \
  -H "Content-Type: application/json"
```

### 2. 分页查询商家订单回款信息
```bash
curl -X GET "http://localhost:8080/merchant/settlement/orders?page=1&pageSize=10" \
  -H "Content-Type: application/json"
```

### 3. 根据回款状态查询订单
```bash
# 查询待回款订单（状态=1）
curl -X GET "http://localhost:8080/merchant/settlement/orders/status/1?page=1&pageSize=10" \
  -H "Content-Type: application/json"

# 查询已回款订单（状态=2）
curl -X GET "http://localhost:8080/merchant/settlement/orders/status/2?page=1&pageSize=10" \
  -H "Content-Type: application/json"
```

### 4. 根据账单周期查询订单
```bash
curl -X GET "http://localhost:8080/merchant/settlement/orders/cycle/2025-01?page=1&pageSize=10" \
  -H "Content-Type: application/json"
```

## 平台端接口测试

### 1. 查询所有商家回款汇总信息
```bash
curl -X GET "http://localhost:8080/admin/settlement/summary" \
  -H "Content-Type: application/json"
```

### 2. 分页查询所有订单回款信息
```bash
curl -X GET "http://localhost:8080/admin/settlement/orders?page=1&pageSize=10" \
  -H "Content-Type: application/json"
```

### 3. 查询待回款订单
```bash
curl -X GET "http://localhost:8080/admin/settlement/pending" \
  -H "Content-Type: application/json"
```

### 4. 根据商家查询回款信息
```bash
curl -X GET "http://localhost:8080/admin/settlement/seller/16?page=1&pageSize=10" \
  -H "Content-Type: application/json"
```

### 5. 根据商家查询回款汇总
```bash
curl -X GET "http://localhost:8080/admin/settlement/seller/16/summary" \
  -H "Content-Type: application/json"
```

### 6. 标记回款完成
```bash
curl -X PUT "http://localhost:8080/admin/settlement/complete" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "remark": "回款已完成"
  }'
```

### 7. 手动更新回款状态
```bash
curl -X POST "http://localhost:8080/admin/settlement/update-status" \
  -H "Content-Type: application/json"
```

### 8. 为指定订单创建回款信息
```bash
curl -X POST "http://localhost:8080/admin/settlement/create/1" \
  -H "Content-Type: application/json"
```

## 测试数据验证

### 回款状态说明
- 0：未到期
- 1：待回款
- 2：已回款

### 回款计算规则验证
1. **账单日期**：支付时间 + 14天
2. **账单周期**：账单日期所在的自然月（格式：YYYY-MM）
3. **回款日期**：账单周期下个月10号后的第一个工作日

### 测试场景
1. **商家端场景**：
   - 商家登录后查看自己的订单回款情况
   - 按状态筛选（未到期、待回款、已回款）
   - 按账单周期查看历史回款记录

2. **平台端场景**：
   - 管理员查看所有商家的回款状态
   - 查看哪些订单到了回款时间
   - 标记回款完成
   - 统计分析各商家回款情况

### 预期结果
- 所有接口返回格式统一（Result包装）
- 分页查询正常工作
- 回款日期计算正确
- 状态更新功能正常
- 数据统计准确

## 注意事项
1. 测试前确保数据库中有订单数据
2. 商家端接口需要商家登录认证（当前使用固定商家ID：16）
3. 平台端接口需要管理员权限
4. 测试时注意检查返回的数据格式和内容是否正确
