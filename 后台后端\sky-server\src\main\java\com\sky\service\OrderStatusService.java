package com.sky.service;

import com.sky.entity.Orders;

/**
 * 订单状态管理服务接口
 */
public interface OrderStatusService {
    
    /**
     * 验证订单状态流转是否合法
     */
    boolean validateStatusTransition(Integer currentStatus, Integer targetStatus);
    
    /**
     * 获取订单可执行的操作列表
     */
    String[] getAvailableActions(Integer status);
    
    /**
     * 获取状态描述
     */
    String getStatusDescription(Integer status);
    
    /**
     * 检查订单是否可以取消
     */
    boolean canCancel(Orders order);
    
    /**
     * 检查订单是否可以发货
     */
    boolean canShip(Orders order);
    
    /**
     * 检查订单是否可以完成
     */
    boolean canComplete(Orders order);
    
    /**
     * 检查订单是否可以退款
     */
    boolean canRefund(Orders order);
}
