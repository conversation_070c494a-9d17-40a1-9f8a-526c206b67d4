package com.sky.controller.Seller;

import com.sky.entity.AfterSalesOrder;
import com.sky.result.Result;
import com.sky.service.AfterSalesOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/afterSales")
@Api(tags = "售后订单管理接口")
public class AfterSalesOrderController {
    @Autowired
    private AfterSalesOrderService afterSalesOrderService;

    @GetMapping("/byAfterSalesNo/{afterSalesNo}")
    @ApiOperation("根据售后单号查找售后订单")
    public Result<AfterSalesOrder> getByAfterSalesNo(@PathVariable String afterSalesNo) {
        AfterSalesOrder afterSalesOrder = afterSalesOrderService.getByAfterSalesNo(afterSalesNo);
        return Result.success(afterSalesOrder);
    }

    @GetMapping("/byOrderNo/{orderNo}")
    @ApiOperation("根据订单号查找售后订单")
    public Result<AfterSalesOrder> getByOrderNo(@PathVariable String orderNo) {
        AfterSalesOrder afterSalesOrder = afterSalesOrderService.getByOrderNo(orderNo);
        return Result.success(afterSalesOrder);
    }

    @GetMapping("/byTransactionNo/{transactionNo}")
    @ApiOperation("根据流水号查找售后订单")
    public Result<AfterSalesOrder> getByTransactionNo(@PathVariable String transactionNo) {
        AfterSalesOrder afterSalesOrder = afterSalesOrderService.getByTransactionNo(transactionNo);
        return Result.success(afterSalesOrder);
    }

    @GetMapping("/byStatus/{status}")
    @ApiOperation("根据售后状态查找售后订单")
    public Result<List<AfterSalesOrder>> getByStatus(@PathVariable Integer status) {
        List<AfterSalesOrder> afterSalesOrders = afterSalesOrderService.getByStatus(status);
        return Result.success(afterSalesOrders);
    }
}