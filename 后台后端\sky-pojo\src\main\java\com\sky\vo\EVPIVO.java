package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EVPIVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;


    private String shopName;


    private String companyName;


    private String businessLicense;


    private LocalDate licenseValidity;


    private String companyIntro;


    private String contactPerson;


    private String contactPhone;


    private String province;


    private String city;


    private String district;


    private String addressDetail;


    private  String businessImgUrl;

    private List<String> warehouseImgUrl;

}

