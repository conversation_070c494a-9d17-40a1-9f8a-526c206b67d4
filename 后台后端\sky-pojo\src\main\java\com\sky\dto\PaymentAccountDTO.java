package com.sky.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 收账账户信息DTO
 */
@Data
public class PaymentAccountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（更新时需要）
     */
    private Long id;

    /**
     * 账户类型(1-银行卡,2-支付宝,3-微信,4-其他)
     */
    @NotNull(message = "账户类型不能为空")
    private Integer accountType;

    /**
     * 账户名称/持卡人姓名
     */
    @NotBlank(message = "账户名称不能为空")
    private String accountName;

    /**
     * 账户号码/卡号
     */
    @NotBlank(message = "账户号码不能为空")
    private String accountNumber;

    /**
     * 银行名称（银行卡类型时必填）
     */
    private String bankName;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 开户支行（银行卡类型时必填）
     */
    private String branchName;

    /**
     * 平台名称(支付宝/微信等)
     */
    private String platformName;

    /**
     * 平台账号(手机号/邮箱等)
     */
    private String platformAccount;

    /**
     * 是否设为默认账户(0-否,1-是)
     */
    private Integer isDefault;

    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 预留手机号
     */
    private String phone;

    /**
     * 备注信息
     */
    private String remark;
}
