package com.sky.controller.admin;

import com.sky.dto.BuyerRegisterDTO;
import com.sky.dto.PassWordDTO;
import com.sky.result.Result;
import com.sky.service.ChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/Change")
@Api(tags = "更改相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class ChangeController {

    @Autowired
    private ChangeService changeService;

    @PostMapping("/PassWord")
    @ApiOperation("更改密码")
    public Result ChangePassWord(@RequestBody PassWordDTO passWordDTO) {
        log.info("passWordDTO:{}", passWordDTO);
        changeService.ChangePassWord(passWordDTO);
        return Result.success();
    }
}
