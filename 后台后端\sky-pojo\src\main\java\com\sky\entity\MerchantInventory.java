package com.sky.entity;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class MerchantInventory {
    private Long id;
    private String productCode;
    private String itemCode;
    private String productTitle;
    private String image;
    private Integer inventory;
    private Integer pendingOrderInventory;
    private Integer safetyInventory;
    private Integer salesLast30Days;
    private String sku;
    private String customInventoryUnit;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}