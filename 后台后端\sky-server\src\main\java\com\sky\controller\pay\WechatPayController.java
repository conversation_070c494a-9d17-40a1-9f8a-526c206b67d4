package com.sky.controller.pay;

import com.sky.Utils.QuickStartUtil;
import com.sky.entity.PaymentRequest;
import com.sky.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pay")
@Api(tags = "微信支付相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class WechatPayController {

//前端需要传输：商品总金额，金额类（总金额，货币属性），商品标题，商户订单号
    @RequestMapping("/wechatPay")
    @ApiOperation("微信支付")
    public Result<String>  WechatPay(@RequestBody PaymentRequest paymentRequest){
        log.info("开始微信支付");
        QuickStartUtil quickStartUtil = new QuickStartUtil();
        String codeUrl=quickStartUtil.quickStart(paymentRequest.getTotal(), paymentRequest.getAmount(), paymentRequest.getTitle(), paymentRequest.getOutTradeNo());
        return Result.success(codeUrl);
    }

}
