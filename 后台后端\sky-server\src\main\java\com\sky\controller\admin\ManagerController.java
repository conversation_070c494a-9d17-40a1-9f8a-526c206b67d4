package com.sky.controller.admin;

import com.sky.dto.BuyerRegisterDTO;
import com.sky.dto.ManagerDTO;
import com.sky.result.Result;
import com.sky.service.ManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/manager")
@Api(tags = "平台管理方相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class ManagerController {

    @Autowired
    private ManagerService managerService;
    @PostMapping("/login")
    @ApiOperation("平台管理方登录")
    public Result login(@RequestBody ManagerDTO managerDTO) {
        log.info(managerDTO.toString());
        managerService.login(managerDTO);
        return Result.success();
    }

}
