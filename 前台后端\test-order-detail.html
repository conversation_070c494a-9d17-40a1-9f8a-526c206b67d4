<!DOCTYPE html>
<html>
<head>
    <title>订单详情测试</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        .field { margin: 5px 0; }
        .label { font-weight: bold; display: inline-block; width: 120px; }
        .value { color: #666; }
        .button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #005a87; }
        .error { color: red; }
        .success { color: green; }
        .logistics-trace { margin: 10px 0; padding: 10px; background: #f9f9f9; border-left: 3px solid #007cba; }
        .trace-time { font-weight: bold; color: #007cba; }
        .trace-desc { margin: 5px 0; }
        .trace-location { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>订单详情测试页面</h1>
        
        <div class="section">
            <h3>测试控制</h3>
            <input type="number" id="orderId" placeholder="订单ID" value="25">
            <button class="button" onclick="loadOrderDetail()">获取订单详情</button>
            <button class="button" onclick="syncLogistics()">同步物流</button>
            <div id="status"></div>
        </div>

        <div id="orderDetail" class="section" style="display: none;">
            <h3>订单基础信息</h3>
            <div class="field"><span class="label">订单ID:</span><span id="orderIdDisplay" class="value"></span></div>
            <div class="field"><span class="label">订单号:</span><span id="orderNumber" class="value"></span></div>
            <div class="field"><span class="label">订单状态:</span><span id="orderStatus" class="value"></span></div>
            <div class="field"><span class="label">订单金额:</span><span id="orderAmount" class="value"></span></div>
            <div class="field"><span class="label">下单时间:</span><span id="orderTime" class="value"></span></div>
            <div class="field"><span class="label">进度百分比:</span><span id="progressPercentage" class="value"></span></div>
            <div class="field"><span class="label">当前阶段:</span><span id="currentStageDesc" class="value"></span></div>
            <div class="field"><span class="label">下一步提示:</span><span id="nextActionTip" class="value"></span></div>
        </div>

        <div id="logisticsInfo" class="section" style="display: none;">
            <h3>物流信息</h3>
            <div class="field"><span class="label">物流单号:</span><span id="trackingNumber" class="value"></span></div>
            <div class="field"><span class="label">运输商:</span><span id="carrierName" class="value"></span></div>
            <div class="field"><span class="label">当前状态:</span><span id="logisticsStatus" class="value"></span></div>
            <div class="field"><span class="label">最新位置:</span><span id="latestLocation" class="value"></span></div>
            <div class="field"><span class="label">发货时间:</span><span id="shipTime" class="value"></span></div>
            
            <h4>物流轨迹</h4>
            <div id="logisticsTraces"></div>
        </div>

        <div id="addressInfo" class="section" style="display: none;">
            <h3>地址信息</h3>
            <h4>收货地址</h4>
            <div id="deliveryAddress"></div>
            <h4>发货地址</h4>
            <div id="senderAddress"></div>
        </div>

        <div id="paymentInfo" class="section" style="display: none;">
            <h3>支付信息</h3>
            <div class="field"><span class="label">支付方式:</span><span id="paymentMethod" class="value"></span></div>
            <div class="field"><span class="label">支付状态:</span><span id="paymentStatus" class="value"></span></div>
            <div class="field"><span class="label">支付金额:</span><span id="paymentAmount" class="value"></span></div>
            <div class="field"><span class="label">支付时间:</span><span id="paymentTime" class="value"></span></div>
            <div class="field"><span class="label">交易号:</span><span id="transactionId" class="value"></span></div>
        </div>

        <div id="orderActions" class="section" style="display: none;">
            <h3>订单操作</h3>
            <button id="cancelBtn" class="button" onclick="cancelOrder()" style="display: none;">取消订单</button>
            <button id="refundBtn" class="button" onclick="applyRefund()" style="display: none;">申请退款</button>
            <button id="confirmBtn" class="button" onclick="confirmReceipt()" style="display: none;">确认收货</button>
        </div>

        <div id="rawData" class="section" style="display: none;">
            <h3>原始数据</h3>
            <pre id="rawJson"></pre>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8443';
        
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.innerHTML = message;
            status.className = isError ? 'error' : 'success';
        }

        async function loadOrderDetail() {
            const orderId = document.getElementById('orderId').value;
            if (!orderId) {
                showStatus('请输入订单ID', true);
                return;
            }

            try {
                showStatus('正在加载订单详情...');
                
                const response = await fetch(`${baseUrl}/user/order-detail/${orderId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (result.code === 1 && result.data) {
                    displayOrderDetail(result.data);
                    showStatus('订单详情加载成功');
                } else {
                    showStatus(`加载失败: ${result.msg || '未知错误'}`, true);
                }
            } catch (error) {
                showStatus(`请求失败: ${error.message}`, true);
                console.error('Error:', error);
            }
        }

        function displayOrderDetail(data) {
            // 显示基础信息
            document.getElementById('orderIdDisplay').textContent = data.orderId || '';
            document.getElementById('orderNumber').textContent = data.orderNumber || '';
            document.getElementById('orderStatus').textContent = data.orderStatusName || '';
            document.getElementById('orderAmount').textContent = data.orderAmount ? `¥${data.orderAmount}` : '';
            document.getElementById('orderTime').textContent = data.orderTime || '';
            document.getElementById('progressPercentage').textContent = data.progressPercentage ? `${data.progressPercentage}%` : '';
            document.getElementById('currentStageDesc').textContent = data.currentStageDesc || '';
            document.getElementById('nextActionTip').textContent = data.nextActionTip || '';
            document.getElementById('orderDetail').style.display = 'block';

            // 显示物流信息
            if (data.logisticsTracking) {
                document.getElementById('trackingNumber').textContent = data.logisticsTracking.trackingNumber || '';
                document.getElementById('carrierName').textContent = data.logisticsTracking.carrierName || '';
                document.getElementById('logisticsStatus').textContent = data.logisticsTracking.currentStatusDesc || '';
                document.getElementById('latestLocation').textContent = data.logisticsTracking.latestLocation || '';
                document.getElementById('shipTime').textContent = data.logisticsTracking.shipTime || '';
                
                // 显示物流轨迹
                const tracesContainer = document.getElementById('logisticsTraces');
                tracesContainer.innerHTML = '';
                if (data.logisticsTraces && data.logisticsTraces.length > 0) {
                    data.logisticsTraces.forEach(trace => {
                        const traceDiv = document.createElement('div');
                        traceDiv.className = 'logistics-trace';
                        traceDiv.innerHTML = `
                            <div class="trace-time">${trace.traceTime || ''}</div>
                            <div class="trace-desc">${trace.traceDesc || ''}</div>
                            <div class="trace-location">${trace.traceLocation || ''}</div>
                        `;
                        tracesContainer.appendChild(traceDiv);
                    });
                } else {
                    tracesContainer.innerHTML = '<div>暂无物流轨迹信息</div>';
                }
                
                document.getElementById('logisticsInfo').style.display = 'block';
            } else {
                document.getElementById('logisticsInfo').style.display = 'none';
            }

            // 显示地址信息
            if (data.deliveryAddress || data.senderAddress) {
                const deliveryDiv = document.getElementById('deliveryAddress');
                const senderDiv = document.getElementById('senderAddress');
                
                if (data.deliveryAddress) {
                    deliveryDiv.innerHTML = `
                        <div class="field"><span class="label">收货人:</span><span class="value">${data.deliveryAddress.consigneeName || ''}</span></div>
                        <div class="field"><span class="label">电话:</span><span class="value">${data.deliveryAddress.phone || ''}</span></div>
                        <div class="field"><span class="label">地址:</span><span class="value">${getFullAddress(data.deliveryAddress)}</span></div>
                    `;
                } else {
                    deliveryDiv.innerHTML = '<div>暂无收货地址信息</div>';
                }
                
                if (data.senderAddress) {
                    senderDiv.innerHTML = `
                        <div class="field"><span class="label">发货人:</span><span class="value">${data.senderAddress.consigneeName || ''}</span></div>
                        <div class="field"><span class="label">电话:</span><span class="value">${data.senderAddress.phone || ''}</span></div>
                        <div class="field"><span class="label">地址:</span><span class="value">${getFullAddress(data.senderAddress)}</span></div>
                    `;
                } else {
                    senderDiv.innerHTML = '<div>暂无发货地址信息</div>';
                }
                
                document.getElementById('addressInfo').style.display = 'block';
            } else {
                document.getElementById('addressInfo').style.display = 'none';
            }

            // 显示支付信息
            if (data.paymentDetail) {
                document.getElementById('paymentMethod').textContent = data.paymentDetail.paymentMethodName || data.paymentDetail.paymentMethod || '';
                document.getElementById('paymentStatus').textContent = data.paymentDetail.paymentStatusName || data.paymentDetail.paymentStatus || '';
                document.getElementById('paymentAmount').textContent = data.paymentDetail.paymentAmount ? `¥${data.paymentDetail.paymentAmount}` : '';
                document.getElementById('paymentTime').textContent = data.paymentDetail.paymentTime || '';
                document.getElementById('transactionId').textContent = data.paymentDetail.transactionId || '';
                document.getElementById('paymentInfo').style.display = 'block';
            } else {
                document.getElementById('paymentInfo').style.display = 'none';
            }

            // 显示操作按钮
            document.getElementById('cancelBtn').style.display = data.canCancel ? 'inline-block' : 'none';
            document.getElementById('refundBtn').style.display = data.canRefund ? 'inline-block' : 'none';
            document.getElementById('confirmBtn').style.display = data.canConfirmReceipt ? 'inline-block' : 'none';
            document.getElementById('orderActions').style.display = 'block';

            // 显示原始数据
            document.getElementById('rawJson').textContent = JSON.stringify(data, null, 2);
            document.getElementById('rawData').style.display = 'block';
        }

        function getFullAddress(address) {
            if (!address) return '';
            return `${address.provinceName || ''}${address.cityName || ''}${address.districtName || ''}${address.detailAddress || ''}`;
        }

        async function syncLogistics() {
            const orderId = document.getElementById('orderId').value;
            if (!orderId) {
                showStatus('请输入订单ID', true);
                return;
            }

            try {
                showStatus('正在同步物流信息...');
                
                const response = await fetch(`${baseUrl}/user/order-detail/${orderId}/sync-logistics`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    showStatus('物流信息同步成功');
                    // 重新加载订单详情
                    setTimeout(() => loadOrderDetail(), 1000);
                } else {
                    showStatus(`同步失败: ${result.msg || '未知错误'}`, true);
                }
            } catch (error) {
                showStatus(`同步失败: ${error.message}`, true);
                console.error('Error:', error);
            }
        }

        async function cancelOrder() {
            const orderId = document.getElementById('orderId').value;
            const reason = prompt('请输入取消原因（可选）:');
            
            try {
                showStatus('正在取消订单...');
                
                const url = `${baseUrl}/user/order-detail/${orderId}/cancel${reason ? `?reason=${encodeURIComponent(reason)}` : ''}`;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    showStatus('订单取消成功');
                    setTimeout(() => loadOrderDetail(), 1000);
                } else {
                    showStatus(`取消失败: ${result.msg || '未知错误'}`, true);
                }
            } catch (error) {
                showStatus(`取消失败: ${error.message}`, true);
                console.error('Error:', error);
            }
        }

        async function applyRefund() {
            const orderId = document.getElementById('orderId').value;
            const reason = prompt('请输入退款原因:');
            if (!reason) return;
            
            try {
                showStatus('正在申请退款...');
                
                const response = await fetch(`${baseUrl}/user/order-detail/${orderId}/apply-refund?reason=${encodeURIComponent(reason)}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    showStatus('退款申请提交成功');
                    setTimeout(() => loadOrderDetail(), 1000);
                } else {
                    showStatus(`申请失败: ${result.msg || '未知错误'}`, true);
                }
            } catch (error) {
                showStatus(`申请失败: ${error.message}`, true);
                console.error('Error:', error);
            }
        }

        async function confirmReceipt() {
            if (!confirm('确认已收到商品？')) return;
            
            const orderId = document.getElementById('orderId').value;
            
            try {
                showStatus('正在确认收货...');
                
                const response = await fetch(`${baseUrl}/user/order-detail/${orderId}/confirm-receipt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    showStatus('确认收货成功');
                    setTimeout(() => loadOrderDetail(), 1000);
                } else {
                    showStatus(`确认失败: ${result.msg || '未知错误'}`, true);
                }
            } catch (error) {
                showStatus(`确认失败: ${error.message}`, true);
                console.error('Error:', error);
            }
        }

        // 页面加载时自动加载订单详情
        window.onload = function() {
            loadOrderDetail();
        };
    </script>
</body>
</html>
