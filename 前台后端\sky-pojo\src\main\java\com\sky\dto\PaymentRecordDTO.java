package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 主键ID
    private Long id;

    // 订单ID
    private Long orderId;

    // 支付方式ID
    private String paymentMethod;

    // 金额
    private BigDecimal amount;

    // 交易状态
    private String transactionStatus;

    // 交易时间
    private LocalDateTime transactionTime;

}
