package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 微信退款记录VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "微信退款记录VO")
public class WechatRefundRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录ID")
    private Long id;

    @ApiModelProperty("退款申请ID")
    private Long refundApplicationId;

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("微信支付退款订单号")
    private String wechatRefundId;

    @ApiModelProperty("商户退款单号")
    private String outRefundNo;

    @ApiModelProperty("微信支付交易订单号")
    private String transactionId;

    @ApiModelProperty("商户原交易订单号")
    private String outTradeNo;

    @ApiModelProperty("退款金额（单位：分）")
    private Integer refundAmount;

    @ApiModelProperty("原订单金额（单位：分）")
    private Integer totalAmount;

    @ApiModelProperty("货币类型")
    private String currency;

    @ApiModelProperty("用户退款金额（单位：分）")
    private Integer payerRefundAmount;

    @ApiModelProperty("结算币种退款金额（单位：分）")
    private Integer settlementRefundAmount;

    @ApiModelProperty("结算币种")
    private String settlementCurrency;

    @ApiModelProperty("退款状态")
    private String refundStatus;

    @ApiModelProperty("退款状态描述")
    private String refundStatusDesc;

    @ApiModelProperty("退款渠道")
    private String refundChannel;

    @ApiModelProperty("退款入账账户")
    private String recvAccount;

    @ApiModelProperty("退款资金来源")
    private String fundSource;

    @ApiModelProperty("退款原因")
    private String refundReason;

    @ApiModelProperty("退款备注")
    private String refundRemark;

    @ApiModelProperty("退款创建时间")
    private LocalDateTime refundCreateTime;

    @ApiModelProperty("退款成功时间")
    private LocalDateTime refundSuccessTime;

    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("记录更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("通知地址")
    private String notifyUrl;

    @ApiModelProperty("通知状态")
    private String notifyStatus;

    @ApiModelProperty("通知时间")
    private LocalDateTime notifyTime;

    /**
     * 获取退款状态描述
     */
    public String getRefundStatusDesc() {
        if (refundStatus == null) {
            return "未知状态";
        }
        switch (refundStatus) {
            case "SUCCESS": return "退款成功";
            case "REFUNDCLOSE": return "退款关闭";
            case "PROCESSING": return "退款处理中";
            case "ABNORMAL": return "退款异常";
            default: return "未知状态";
        }
    }

    /**
     * 判断退款是否成功
     */
    public boolean isRefundSuccess() {
        return "SUCCESS".equals(refundStatus);
    }

    /**
     * 判断退款是否处理中
     */
    public boolean isRefundProcessing() {
        return "PROCESSING".equals(refundStatus);
    }

    /**
     * 判断退款是否失败
     */
    public boolean isRefundFailed() {
        return "REFUNDCLOSE".equals(refundStatus) || "ABNORMAL".equals(refundStatus);
    }
}
