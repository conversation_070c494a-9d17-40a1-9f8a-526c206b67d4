package com.sky.service.impl;

import com.sky.dto.WebhookNotificationDTO;
import com.sky.entity.TrackingEvent;
import com.sky.entity.TrackingRecord;
import com.sky.entity.WebhookLog;
import com.sky.mapper.TrackingEventMapper;
import com.sky.mapper.TrackingRecordMapper;
import com.sky.mapper.WebhookLogMapper;
import com.sky.service.TrackingConfigService;
import com.sky.service.WebhookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 17TRACK Webhook服务实现
 */
@Service
@Slf4j
public class WebhookServiceImpl implements WebhookService {

    @Autowired
    private WebhookLogMapper webhookLogMapper;

    @Autowired
    private TrackingRecordMapper trackingRecordMapper;

    @Autowired
    private TrackingEventMapper trackingEventMapper;

    @Autowired
    private TrackingConfigService trackingConfigService;

    @Override
    @Transactional
    public boolean processWebhookNotification(WebhookNotificationDTO notification, String requestBody) {
        log.info("处理Webhook通知: {}", notification.getEvent());

        String trackingNumber = null;
        Integer carrierCode = null;
        
        try {
            if (notification.getData() != null) {
                trackingNumber = notification.getData().getNumber();
                carrierCode = notification.getData().getCarrier();
            }

            // 记录Webhook日志
            WebhookLog webhookLog = logWebhook(
                    notification.getEvent(),
                    trackingNumber,
                    carrierCode,
                    requestBody,
                    200,
                    "Processing",
                    WebhookLog.PROCESS_STATUS_PENDING,
                    null
            );

            // 处理跟踪更新事件
            if (WebhookLog.EVENT_TYPE_TRACKING_UPDATED.equals(notification.getEvent())) {
                processTrackingUpdated(notification);
            }

            // 更新处理状态为成功
            webhookLogMapper.updateProcessStatus(
                    webhookLog.getId(),
                    WebhookLog.PROCESS_STATUS_SUCCESS,
                    null,
                    0,
                    LocalDateTime.now()
            );

            log.info("Webhook处理成功: {}", trackingNumber);
            return true;

        } catch (Exception e) {
            log.error("Webhook处理失败: {}", e.getMessage(), e);

            // 更新处理状态为失败
            if (trackingNumber != null) {
                WebhookLog failedLog = logWebhook(
                        notification.getEvent(),
                        trackingNumber,
                        carrierCode,
                        requestBody,
                        500,
                        "Error",
                        WebhookLog.PROCESS_STATUS_FAILED,
                        e.getMessage()
                );
            }

            return false;
        }
    }

    /**
     * 处理跟踪更新事件
     */
    private void processTrackingUpdated(WebhookNotificationDTO notification) {
        WebhookNotificationDTO.TrackingData data = notification.getData();
        if (data == null || data.getTracking() == null) {
            log.warn("Webhook数据为空");
            return;
        }

        String trackingNumber = data.getNumber();
        Integer carrierCode = data.getCarrier();

        // 查找跟踪记录
        TrackingRecord record = trackingRecordMapper.findByTrackingNumberAndCarrier(trackingNumber, carrierCode);
        if (record == null) {
            log.warn("未找到跟踪记录: {}", trackingNumber);
            return;
        }

        // 处理运输商信息
        List<WebhookNotificationDTO.ProviderInfo> providers = data.getTracking().getProviders();
        if (providers != null && !providers.isEmpty()) {
            for (WebhookNotificationDTO.ProviderInfo provider : providers) {
                updateTrackingRecord(record, provider);
                processTrackingEvents(record, provider.getEvents());
            }
        }
    }

    /**
     * 更新跟踪记录
     */
    private void updateTrackingRecord(TrackingRecord record, WebhookNotificationDTO.ProviderInfo provider) {
        if (provider.getLatest_status() != null) {
            record.setStatus(provider.getLatest_status().getStatus());
            record.setSubStatus(provider.getLatest_status().getSub_status());
            record.setSubStatusDesc(provider.getLatest_status().getSub_status_descr());
        }

        record.setPushTime(LocalDateTime.now());
        record.setPushStatus(TrackingRecord.PUSH_STATUS_SUCCESS);
        record.setUpdateTime(LocalDateTime.now());

        trackingRecordMapper.update(record);
    }

    /**
     * 处理跟踪事件
     */
    private void processTrackingEvents(TrackingRecord record, List<WebhookNotificationDTO.EventInfo> events) {
        if (events == null || events.isEmpty()) {
            return;
        }

        List<TrackingEvent> newEvents = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;

        for (WebhookNotificationDTO.EventInfo eventInfo : events) {
            try {
                LocalDateTime eventTime = LocalDateTime.parse(eventInfo.getTime_iso(), formatter);

                // 检查事件是否已存在
                int existingCount = trackingEventMapper.countByTrackingRecordIdAndEventTimeAndDescription(
                        record.getId(), eventTime, eventInfo.getDescription());

                if (existingCount == 0) {
                    TrackingEvent trackingEvent = TrackingEvent.builder()
                            .trackingRecordId(record.getId())
                            .trackingNumber(record.getTrackingNumber())
                            .carrierCode(record.getCarrierCode())
                            .eventTime(eventTime)
                            .eventTimeRaw(eventInfo.getTime_raw())
                            .eventTimezone(eventInfo.getTime_zone())
                            .status(eventInfo.getStatus())
                            .subStatus(eventInfo.getSub_status())
                            .subStatusDesc(eventInfo.getSub_status_descr())
                            .location(eventInfo.getLocation())
                            .description(eventInfo.getDescription())
                            .createTime(LocalDateTime.now())
                            .build();

                    newEvents.add(trackingEvent);
                }
            } catch (Exception e) {
                log.error("解析事件时间失败: {}", eventInfo.getTime_iso(), e);
            }
        }

        // 批量插入新事件
        if (!newEvents.isEmpty()) {
            trackingEventMapper.insertBatch(newEvents);
            log.info("新增{}个物流事件: {}", newEvents.size(), record.getTrackingNumber());
        }
    }

    @Override
    public boolean validateWebhookSignature(String requestBody, String signature) {
        if (!StringUtils.hasText(signature)) {
            return true; // 如果没有签名，暂时允许通过
        }

        String webhookSecret = trackingConfigService.getConfigValue("17track.webhook.secret");
        if (!StringUtils.hasText(webhookSecret)) {
            log.warn("Webhook密钥未配置");
            return true; // 如果没有配置密钥，暂时允许通过
        }

        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(webhookSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] hash = mac.doFinal(requestBody.getBytes(StandardCharsets.UTF_8));
            String expectedSignature = Base64.getEncoder().encodeToString(hash);
            
            return expectedSignature.equals(signature);
        } catch (Exception e) {
            log.error("验证Webhook签名失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public WebhookLog logWebhook(String eventType, String trackingNumber, Integer carrierCode,
                                String requestBody, Integer responseStatus, String responseBody,
                                String processStatus, String errorMessage) {
        WebhookLog webhookLog = WebhookLog.builder()
                .eventType(eventType)
                .trackingNumber(trackingNumber)
                .carrierCode(carrierCode)
                .requestBody(requestBody)
                .responseStatus(responseStatus)
                .responseBody(responseBody)
                .processStatus(processStatus)
                .errorMessage(errorMessage)
                .retryCount(0)
                .createTime(LocalDateTime.now())
                .build();

        webhookLogMapper.insert(webhookLog);
        return webhookLog;
    }

    @Override
    public List<WebhookLog> getWebhookLogs(String trackingNumber) {
        return webhookLogMapper.findByTrackingNumber(trackingNumber);
    }

    @Override
    public boolean retryWebhookProcessing(Long webhookLogId) {
        WebhookLog webhookLog = webhookLogMapper.findById(webhookLogId);
        if (webhookLog == null) {
            log.warn("未找到Webhook日志: {}", webhookLogId);
            return false;
        }

        // TODO: 实现重试逻辑
        log.info("重试Webhook处理: {}", webhookLogId);
        return true;
    }

    @Override
    public int cleanupExpiredLogs(int days) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        int deletedCount = webhookLogMapper.deleteExpiredLogs(expireTime);
        log.info("清理过期Webhook日志: {}条", deletedCount);
        return deletedCount;
    }
}
