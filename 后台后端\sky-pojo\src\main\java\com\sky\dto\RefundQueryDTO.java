package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 退款查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 退款申请单号
     */
    private String refundNo;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 申请人ID
     */
    private Long buyerId;

    /**
     * 申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败
     */
    private Integer applicationStatus;

    /**
     * 退款类型：1-仅退款，2-退货退款
     */
    private Integer refundType;

    /**
     * 是否需要审核：0-不需要，1-需要
     */
    private Integer needApproval;

    /**
     * 审核状态：1-待审核，2-审核通过，3-审核拒绝
     */
    private Integer approvalStatus;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;
}
