package com.sky.service;

import com.sky.entity.TrackingConfig;

import java.util.List;
import java.util.Map;

/**
 * 17TRACK配置管理服务接口
 */
public interface TrackingConfigService {

    /**
     * 获取配置值
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 获取配置值（带默认值）
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 获取布尔类型配置
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    boolean getBooleanConfig(String configKey, boolean defaultValue);

    /**
     * 获取整数类型配置
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    int getIntConfig(String configKey, int defaultValue);

    /**
     * 设置配置值
     * @param configKey 配置键
     * @param configValue 配置值
     */
    void setConfigValue(String configKey, String configValue);

    /**
     * 批量设置配置
     * @param configs 配置映射
     */
    void setConfigs(Map<String, String> configs);

    /**
     * 获取所有配置
     * @return 配置列表
     */
    List<TrackingConfig> getAllConfigs();

    /**
     * 删除配置
     * @param configKey 配置键
     */
    void deleteConfig(String configKey);

    /**
     * 刷新配置缓存
     */
    void refreshCache();

    /**
     * 验证17TRACK API配置
     * @return 是否有效
     */
    boolean validateApiConfig();

    /**
     * 获取运输商代码映射
     * @return 运输商映射
     */
    Map<String, Integer> getCarrierMapping();

    /**
     * 更新运输商代码映射
     * @param mapping 运输商映射
     */
    void updateCarrierMapping(Map<String, Integer> mapping);
}
