package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PassWordDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    private String oldPassword;
    private String newPassword;
    private String phone;
}
