package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 17TRACK API响应基础类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17Response<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer code;           // 错误代码
    private String message;         // 响应消息
    private T data;                 // 响应数据
    private PageInfo page;          // 分页信息

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PageInfo implements Serializable {
        private Integer dataTotal;  // 数据总数
        private Integer pageTotal;  // 页面总数
        private Integer pageNo;     // 当前页面索引
        private Integer pageSize;   // 每页数据总数
    }
}
