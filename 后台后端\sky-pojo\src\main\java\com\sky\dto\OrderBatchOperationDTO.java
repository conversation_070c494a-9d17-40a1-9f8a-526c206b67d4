package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单批量操作数据传输对象
 */
@Data
public class OrderBatchOperationDTO implements Serializable {
    
    private List<Long> orderIds;        // 订单ID列表
    private String operation;           // 操作类型：ship, cancel, complete, export
    private String reason;              // 操作原因（取消订单时必填）
    private String remark;              // 备注信息
    
    // 批量发货相关
    private String logisticsCompany;    // 物流公司
    private String trackingNumber;      // 物流单号
    
    // 导出相关
    private String exportFormat;        // 导出格式：excel, csv, pdf
    private List<String> exportFields;  // 导出字段列表
}
