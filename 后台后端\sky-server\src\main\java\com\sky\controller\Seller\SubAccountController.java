package com.sky.controller.Seller;


import com.sky.dto.BatchDeleteDTO;
import com.sky.dto.BatchUpdateStatusDTO;
import com.sky.dto.SellerSubAccountDTO;
import com.sky.result.Result;
import com.sky.service.SellerSubAccountService;
import com.sky.vo.BatchDeleteResultVO;
import com.sky.vo.BatchUpdateStatusResultVO;
import com.sky.vo.SellerSubAccountUpdatePermissionsVO;
import com.sky.vo.SellerSubAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/seller/sub-accounts")
@Slf4j
@Api(tags = "商家子账户接口")
public class SubAccountController {

    @Autowired
    private SellerSubAccountService subAccountService;

    @PutMapping
    @ApiOperation("创建商家子账户")
    public Result createSubAccount(@RequestBody SellerSubAccountDTO sellerSubAccountDTO) {
        SellerSubAccountVO sellerSubAccountVO = subAccountService.createSubAccount(sellerSubAccountDTO);
        return Result.success("子账号创建成功",sellerSubAccountVO);
    }

    @GetMapping
    @ApiOperation("查询商家子账户列表")
    public Result<List<SellerSubAccountVO>> getSubAccountList(@RequestParam Long createdBy,@RequestParam(required = false) Integer accountStatus) {
        List<SellerSubAccountVO> sellerSubAccountVO = subAccountService.getSubAccountList(createdBy,accountStatus);
        return Result.success("查询成功",sellerSubAccountVO);
    }

    @GetMapping("/{id}")
    @ApiOperation("查询商家子账户")
    public Result<SellerSubAccountVO> getSubAccount(@PathVariable Long id,@RequestParam Long createdBy) {
        SellerSubAccountVO sellerSubAccountVO = subAccountService.getSubAccount(id,createdBy);
        return Result.success("查询成功",sellerSubAccountVO);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新商家子账户")
    @Transactional
    public Result<SellerSubAccountVO> updateSellerSubAccount(@PathVariable Long id, @RequestBody SellerSubAccountDTO sellerSubAccountDTO){
        SellerSubAccountVO sellerSubAccountVO = subAccountService.updateSellerSubAccount(id,sellerSubAccountDTO);
        return Result.success("更新成功",sellerSubAccountVO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除商家子账号")
    public Result deleteSellerSubAccount(@PathVariable Long id,@RequestParam Long createdBy){
        subAccountService.deleteSellerSubAccount(id,createdBy);
        return Result.success("商家子账号删除成功");
    }

    @PutMapping("/{id}/permissions")
    @ApiOperation("更新商家子账户权限")
    public Result updateSellerSubAccountPermission(@PathVariable Long id,@RequestParam Long createdBy,@RequestBody SellerSubAccountDTO sellerSubAccountDTO){
        SellerSubAccountUpdatePermissionsVO sellerSubAccountUpdatePermissionsVO = subAccountService.updateSellerSubAccountPermission(id,createdBy,sellerSubAccountDTO.getPermissions());
        return Result.success("更新成功",sellerSubAccountUpdatePermissionsVO);
    }

    @DeleteMapping("/batch-delete")
    @ApiOperation("批量删除商家子账号")
    public Result<BatchDeleteResultVO> batchDelete(@RequestBody BatchDeleteDTO batchDeleteDTO,@RequestParam Long createdBy){
        BatchDeleteResultVO batchDeleteResultVO = subAccountService.batchDeleteSubAccount(batchDeleteDTO.getIds(),createdBy);
        return Result.success("批量删除成功",batchDeleteResultVO);
    }

    @PostMapping("/batch-update-status")
    @ApiOperation("批量更新商家子账号状态")
    public Result<BatchUpdateStatusResultVO> batchUpdateStatus(@RequestBody BatchUpdateStatusDTO batchUpdateStatusDTO, @RequestParam Long createdBy){
        BatchUpdateStatusResultVO batchUpdateStatusResultVO = subAccountService.batchUpdateSubAccountStatus(batchUpdateStatusDTO.getIds(),batchUpdateStatusDTO.getStatus(),createdBy);
        return Result.success("批量更新成功",batchUpdateStatusResultVO);
    }

    @PostMapping("/{id}/reset-password")
    @ApiOperation("重置商家子账号密码")
    public Result resetPassword(@PathVariable("id") Long id,@RequestParam String password,@RequestParam Long createdBy){
        subAccountService.resetPassword(id,password,createdBy);
        return Result.success("重置密码成功");
    }
}
