package com.sky.controller.admin;

import com.sky.entity.OrderDetail;
import com.sky.result.Result;
import com.sky.service.OrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/order-detail")
@Api(tags = "C端订单详细接口")
public class OrderDetailController {

    @Autowired
    private OrderDetailService orderDetailService;

    // 根据 ID 获取订单明细
    @GetMapping("/{id}")
    @ApiOperation("获取订单明细")
    public Result<OrderDetail> getOrderDetailById(@PathVariable Long id) {
        OrderDetail orderDetail = orderDetailService.getOrderDetailById(id);
        return Result.success(orderDetail);
    }

    // 修改订单明细
    @PutMapping ("/update")
    @ApiOperation("修改订单明细")
    public Result<Void> updateOrderDetail(@RequestBody OrderDetail orderDetail) {
        orderDetailService.updateOrderDetail(orderDetail);
        return Result.success();
    }
}
