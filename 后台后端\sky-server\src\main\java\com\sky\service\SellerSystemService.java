package com.sky.service;

import com.sky.dto.MessageNotificationsDTO;
import com.sky.dto.SystemProgramsDTO;
import com.sky.entity.MessageNotifications;
import com.sky.result.PageResult;

public interface SellerSystemService {
    PageResult pageQuery(MessageNotificationsDTO messageNotificationsDTO);


    MessageNotifications getByStatus(String status);


    PageResult pageSystemQuery(SystemProgramsDTO systemProgramsDTO);
}
