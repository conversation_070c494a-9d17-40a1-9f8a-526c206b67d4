package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流事件VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingEventVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;                        // 主键ID
    private Long trackingRecordId;          // 跟踪记录ID
    private String trackingNumber;          // 物流单号
    private Integer carrierCode;            // 运输商代码
    private LocalDateTime eventTime;        // 事件时间
    private String eventTimeRaw;            // 原始事件时间
    private String eventTimezone;           // 事件时区
    private String status;                  // 物流主状态
    private String statusDesc;              // 主状态描述
    private String subStatus;               // 物流子状态
    private String subStatusDesc;           // 子状态描述
    private String location;                // 事件地点
    private String description;             // 事件描述
    private String descriptionTranslated;   // 翻译后的事件描述
    private LocalDateTime createTime;       // 创建时间
}
