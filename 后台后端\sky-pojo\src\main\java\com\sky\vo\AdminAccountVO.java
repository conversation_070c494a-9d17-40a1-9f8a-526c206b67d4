package com.sky.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sky.entity.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminAccountVO {
    private Long id;
    @TableField(value = "accountName")
    private String accountName;
    private String email;
    private String phone;
    private Integer accountStatus;
    @TableField(value = "createTime")
    private LocalDateTime createTime;
    @TableField(value = "updateTime")
    private LocalDateTime updateTime;
    @TableField(value = "lastLoginTime")
    private LocalDateTime lastLoginTime;
    @TableField(value = "loginCount")
    private List<String> permissions;
    private String remark;
}
