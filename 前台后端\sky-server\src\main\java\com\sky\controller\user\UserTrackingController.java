package com.sky.controller.user;

import com.sky.dto.TrackingQueryDTO;
import com.sky.result.Result;
import com.sky.service.Track17Service;
import com.sky.vo.TrackingListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户端物流跟踪控制器
 */
@RestController
@RequestMapping("/user/tracking")
@Api(tags = "用户端物流跟踪接口")
@Slf4j
public class UserTrackingController {

    @Autowired
    private Track17Service track17Service;

    /**
     * 根据物流单号查询物流信息
     */
    @GetMapping("/query/{trackingNumber}")
    @ApiOperation("根据物流单号查询物流信息")
    public Result<TrackingListVO> queryByTrackingNumber(@PathVariable String trackingNumber) {
        log.info("查询物流信息: {}", trackingNumber);

        try {
            TrackingQueryDTO queryDTO = TrackingQueryDTO.builder()
                    .trackingNumber(trackingNumber)
                    .page(1)
                    .pageSize(1)
                    .build();

            TrackingListVO result = track17Service.getTrackingList(queryDTO);
            
            if (result != null && result.getTrackingList() != null && !result.getTrackingList().isEmpty()) {
                return Result.success(result);
            } else {
                return Result.error("未找到该物流单号的信息");
            }

        } catch (Exception e) {
            log.error("查询物流信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询物流信息
     */
    @PostMapping("/query/batch")
    @ApiOperation("批量查询物流信息")
    public Result<TrackingListVO> queryBatch(@RequestBody List<String> trackingNumbers) {
        log.info("批量查询物流信息: {}", trackingNumbers);

        try {
            if (trackingNumbers == null || trackingNumbers.isEmpty()) {
                return Result.error("物流单号列表不能为空");
            }

            if (trackingNumbers.size() > 100) {
                return Result.error("单次查询物流单号数量不能超过100个");
            }

            TrackingQueryDTO queryDTO = TrackingQueryDTO.builder()
                    .trackingNumbers(trackingNumbers)
                    .page(1)
                    .pageSize(trackingNumbers.size())
                    .build();

            TrackingListVO result = track17Service.getTrackingList(queryDTO);
            return Result.success(result);

        } catch (Exception e) {
            log.error("批量查询物流信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 高级查询物流信息
     */
    @PostMapping("/query/advanced")
    @ApiOperation("高级查询物流信息")
    public Result<TrackingListVO> queryAdvanced(@RequestBody TrackingQueryDTO queryDTO) {
        log.info("高级查询物流信息: {}", queryDTO);

        try {
            // 设置默认分页参数
            if (queryDTO.getPage() == null || queryDTO.getPage() < 1) {
                queryDTO.setPage(1);
            }
            if (queryDTO.getPageSize() == null || queryDTO.getPageSize() < 1) {
                queryDTO.setPageSize(10);
            }
            if (queryDTO.getPageSize() > 100) {
                queryDTO.setPageSize(100);
            }

            TrackingListVO result = track17Service.getTrackingList(queryDTO);
            return Result.success(result);

        } catch (Exception e) {
            log.error("高级查询物流信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的运输商列表
     */
    @GetMapping("/carriers")
    @ApiOperation("获取支持的运输商列表")
    public Result<List<Object>> getSupportedCarriers() {
        log.info("获取支持的运输商列表");

        try {
            List<Object> carriers = track17Service.getSupportedCarriers();
            return Result.success(carriers);

        } catch (Exception e) {
            log.error("获取运输商列表失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询物流信息
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation("根据订单ID查询物流信息")
    public Result<TrackingListVO> queryByOrderId(@PathVariable Long orderId) {
        log.info("根据订单ID查询物流信息: {}", orderId);

        try {
            // 这里需要先从订单表中查询物流单号，然后再查询物流信息
            // 由于没有订单服务的依赖，这里先返回一个示例
            
            return Result.error("功能开发中，请使用物流单号直接查询");

        } catch (Exception e) {
            log.error("根据订单ID查询物流信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取物流状态统计
     */
    @GetMapping("/statistics")
    @ApiOperation("获取物流状态统计")
    public Result<Object> getTrackingStatistics() {
        log.info("获取物流状态统计");

        try {
            // 这里可以实现物流状态的统计功能
            // 比如各种状态的物流单数量等
            
            return Result.error("功能开发中");

        } catch (Exception e) {
            log.error("获取物流状态统计失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
}
