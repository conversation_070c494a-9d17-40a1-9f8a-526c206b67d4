package com.sky.service;

import com.sky.entity.Orders;

/**
 * 订单通知服务接口
 */
public interface OrderNotificationService {
    
    /**
     * 发送订单状态变更通知
     * @param order 订单信息
     * @param oldStatus 原状态
     * @param newStatus 新状态
     */
    void sendOrderStatusChangeNotification(Orders order, Integer oldStatus, Integer newStatus);
    
    /**
     * 发送支付成功通知
     * @param order 订单信息
     */
    void sendPaymentSuccessNotification(Orders order);
    
    /**
     * 发送发货通知
     * @param order 订单信息
     */
    void sendShippedNotification(Orders order);
    
    /**
     * 发送订单完成通知
     * @param order 订单信息
     */
    void sendCompletedNotification(Orders order);
    
    /**
     * 发送订单取消通知
     * @param order 订单信息
     */
    void sendCancelledNotification(Orders order);
    
    /**
     * 发送退款成功通知
     * @param order 订单信息
     */
    void sendRefundedNotification(Orders order);
    
    /**
     * 发送库存不足警告
     * @param productId 商品ID
     * @param productName 商品名称
     * @param currentStock 当前库存
     */
    void sendLowStockAlert(Long productId, String productName, Integer currentStock);
    
    /**
     * 发送退款申请通知
     * @param refundNo 退款单号
     * @param buyerName 买家姓名
     * @param reason 申请原因
     */
    void sendRefundApplicationNotification(String refundNo, String buyerName, String reason);
}
