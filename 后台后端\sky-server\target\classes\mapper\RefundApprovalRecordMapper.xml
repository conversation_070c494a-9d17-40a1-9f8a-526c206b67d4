<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.RefundApprovalRecordMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sky.entity.RefundApprovalRecord">
        <id column="id" property="id" />
        <result column="refund_application_id" property="refundApplicationId" />
        <result column="refund_no" property="refundNo" />
        <result column="approver_id" property="approverId" />
        <result column="approver_name" property="approverName" />
        <result column="approval_result" property="approvalResult" />
        <result column="approval_remark" property="approvalRemark" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 审核记录VO映射结果 -->
    <resultMap id="RefundApprovalRecordVOMap" type="com.sky.vo.RefundApprovalRecordVO">
        <id column="id" property="id" />
        <result column="refund_application_id" property="refundApplicationId" />
        <result column="refund_no" property="refundNo" />
        <result column="approver_id" property="approverId" />
        <result column="approver_name" property="approverName" />
        <result column="approval_result" property="approvalResult" />
        <result column="approval_remark" property="approvalRemark" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 插入审核记录 -->
    <insert id="insert" parameterType="com.sky.entity.RefundApprovalRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO refund_approval_record (
            refund_application_id, refund_no, approver_id, approver_name, 
            approval_result, approval_remark, create_time
        ) VALUES (
            #{refundApplicationId}, #{refundNo}, #{approverId}, #{approverName}, 
            #{approvalResult}, #{approvalRemark}, #{createTime}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM refund_approval_record WHERE id = #{id}
    </select>

    <!-- 根据退款申请ID查询审核记录 -->
    <select id="selectByRefundApplicationId" resultMap="BaseResultMap">
        SELECT * FROM refund_approval_record 
        WHERE refund_application_id = #{refundApplicationId} 
        ORDER BY create_time DESC
    </select>

    <!-- 根据退款申请单号查询审核记录 -->
    <select id="selectByRefundNo" resultMap="BaseResultMap">
        SELECT * FROM refund_approval_record 
        WHERE refund_no = #{refundNo} 
        ORDER BY create_time DESC
    </select>

    <!-- 根据审核人ID查询审核记录 -->
    <select id="selectByApproverId" resultMap="BaseResultMap">
        SELECT * FROM refund_approval_record 
        WHERE approver_id = #{approverId} 
        ORDER BY create_time DESC
    </select>

    <!-- 根据退款申请ID查询审核记录VO -->
    <select id="selectVOByRefundApplicationId" resultMap="RefundApprovalRecordVOMap">
        SELECT * FROM refund_approval_record 
        WHERE refund_application_id = #{refundApplicationId} 
        ORDER BY create_time DESC
    </select>

    <!-- 查询最新的审核记录 -->
    <select id="selectLatestByRefundApplicationId" resultMap="BaseResultMap">
        SELECT * FROM refund_approval_record 
        WHERE refund_application_id = #{refundApplicationId} 
        ORDER BY create_time DESC 
        LIMIT 1
    </select>
</mapper>
