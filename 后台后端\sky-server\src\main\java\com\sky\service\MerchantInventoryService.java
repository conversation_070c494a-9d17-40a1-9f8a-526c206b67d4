package com.sky.service;

import com.sky.entity.MerchantInventory;

import java.time.LocalDateTime;
import java.util.List;

public interface MerchantInventoryService {
    List<MerchantInventory> findAll();
    List<MerchantInventory> findByProductCode(String productCode);
    List<MerchantInventory> findByItemCode(String itemCode);
    List<MerchantInventory> findByProductTitle(String productTitle);
    List<MerchantInventory> findByCreatedAtRange(LocalDateTime startTime, LocalDateTime endTime);
    void updateSafetyInventory(Long id, Integer safetyInventory);
}