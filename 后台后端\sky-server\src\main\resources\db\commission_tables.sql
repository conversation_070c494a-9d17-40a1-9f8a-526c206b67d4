-- 佣金设置表
CREATE TABLE commission_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    basic_rate DECIMAL(5,4) NOT NULL COMMENT '基础佣金比例',
    team_rate DECIMAL(5,4) NOT NULL COMMENT '团队佣金比例',
    leader_rate DECIMAL(5,4) NOT NULL COMMENT '团长佣金比例',
    min_withdraw DECIMAL(10,2) NOT NULL COMMENT '最低提现金额',
    withdraw_cycle VARCHAR(20) NOT NULL COMMENT '提现周期：daily/weekly/monthly',
    withdraw_days JSON COMMENT '允许提现的日期',
    upgrade_rules JSON COMMENT '升级规则配置',
    rebate_policy JSON COMMENT '返利策略配置',
    create_time DATETIME NOT NULL,
    update_time DATETIME NOT NULL
) COMMENT '佣金设置表';

-- 团长表
CREATE TABLE team_leader (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '团长名称',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) NOT NULL COMMENT '手机号码',
    email VARCHAR(100) COMMENT '电子邮箱',
    id_number VARCHAR(18) NOT NULL COMMENT '身份证号码',
    bank_info JSON NOT NULL COMMENT '银行账户信息',
    address VARCHAR(255) COMMENT '详细地址',
    create_time DATETIME NOT NULL,
    level VARCHAR(20) NOT NULL DEFAULT '普通团长' COMMENT '团长等级',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '状态：active/pending/disabled',
    team_size INT NOT NULL DEFAULT 0 COMMENT '团队规模',
    total_sales DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '总销售额',
    commission_earned DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '已获得佣金',
    today_sales DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '今日销售额'
) COMMENT '团长表';

-- 团队成员表
CREATE TABLE team_member (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    leader_id BIGINT NOT NULL COMMENT '所属团长ID',
    name VARCHAR(50) NOT NULL COMMENT '成员名称',
    level VARCHAR(20) NOT NULL DEFAULT '普通会员' COMMENT '会员等级',
    join_time DATETIME NOT NULL COMMENT '加入时间',
    total_sales DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '总销售额',
    FOREIGN KEY (leader_id) REFERENCES team_leader(id)
) COMMENT '团队成员表';

-- 佣金记录表
CREATE TABLE commission_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '佣金记录ID',
    leader_id BIGINT NOT NULL COMMENT '团长ID',
    leader_name VARCHAR(50) NOT NULL COMMENT '团长名称',
    order_id VARCHAR(32) NOT NULL COMMENT '订单ID',
    order_amount DECIMAL(12,2) NOT NULL COMMENT '订单金额',
    commission_amount DECIMAL(12,2) NOT NULL COMMENT '佣金金额',
    commission_type VARCHAR(20) NOT NULL COMMENT '佣金类型：销售佣金/团队佣金',
    order_type VARCHAR(20) NOT NULL COMMENT '订单类型：normal/team',
    buyer_name VARCHAR(50) NOT NULL COMMENT '买家名称',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    settle_time DATETIME COMMENT '结算时间',
    withdraw_time DATETIME COMMENT '提现时间',
    status VARCHAR(20) NOT NULL COMMENT '状态：pending/paid/withdrawn',
    FOREIGN KEY (leader_id) REFERENCES team_leader(id)
) COMMENT '佣金记录表';

-- 销售趋势表
CREATE TABLE sales_trend (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    leader_id BIGINT NOT NULL COMMENT '团长ID',
    date DATE NOT NULL COMMENT '日期',
    sales DECIMAL(12,2) NOT NULL COMMENT '销售额',
    commission DECIMAL(12,2) NOT NULL COMMENT '佣金',
    FOREIGN KEY (leader_id) REFERENCES team_leader(id),
    UNIQUE KEY uk_leader_date (leader_id, date)
) COMMENT '销售趋势表'; 