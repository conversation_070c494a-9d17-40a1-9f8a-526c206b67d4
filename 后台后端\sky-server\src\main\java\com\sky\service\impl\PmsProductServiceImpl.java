package com.sky.service.impl;
import com.sky.entity.PmsProduct;
import com.sky.Utils.OssUtils;
import com.sky.mapper.PmsProductMapper;
import com.sky.service.PmsProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Random;

@Service
public class PmsProductServiceImpl implements PmsProductService {

    @Autowired
    private PmsProductMapper pmsProductMapper;

    @Autowired
    private OssUtils ossUtils;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public List<PmsProduct> getProductsBySellerId(Long sellerId) {
        return pmsProductMapper.getProductsBySellerId(sellerId);
    }

    @Override
    public List<PmsProduct> getAllProducts() {
        return pmsProductMapper.getAllProducts();
    }

    @Override
    public PmsProduct addProduct(Long sellerId, PmsProduct product) {
        // 生成唯一的 8 位商品编码
        String productCode = generateUniqueProductCode();
        product.setOutProductId(productCode);
        product.setCreatorUserId(sellerId);
        product.setCreateTime(new Date());
        product.setUpdateTime(new Date());
        product.setStatus(0);

        // 设置默认值
        if (product.getPublishStatus() == null) {
            product.setPublishStatus(0);
        }
        if (product.getSort() == null) {
            product.setSort(0);
        }
        if (product.getPrice() == null) {
            product.setPrice(BigDecimal.ZERO);
        }
        if (product.getUnit() == null) {
            product.setUnit("件");
        }
        if (product.getWeight() == null) {
            product.setWeight(BigDecimal.ZERO);
        }

        // 处理图片上传


        pmsProductMapper.insert(product);
        return product;
    }

    @Override
    public void deleteProduct(Long sellerId, Long productId) {
        PmsProduct product = pmsProductMapper.getProductById(productId);
        if (product != null && product.getCreatorUserId().equals(sellerId)) {
            pmsProductMapper.deleteProduct(productId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public PmsProduct updateProduct(Long sellerId, Long productId, PmsProduct product) {

        PmsProduct existingProduct = pmsProductMapper.getProductById(productId);
        if (existingProduct != null && existingProduct.getCreatorUserId().equals(sellerId)) {
            // 商品编码不能修改
            product.setOutProductId(existingProduct.getOutProductId());
            product.setId(productId);
            product.setCreatorUserId(sellerId);
            product.setUpdateTime(new Date());


            // 处理图片上传
            pmsProductMapper.updateProduct(product);
            Long id = product.getId();
            stringRedisTemplate.delete("product:" + id);
            return pmsProductMapper.getProductById(productId);
        }
        return null;
    }

    @Override
    public PmsProduct getProductsById(Long productId) {
        PmsProduct productById = pmsProductMapper.selectById(productId);
        return productById;
    }

    @Override
    public void deleteProductById(Long id) {
        pmsProductMapper.deleteProduct(id);
    }

    @Override
    public void changePass(Long id) {
        PmsProduct productById = pmsProductMapper.getProductById(id);
        productById.setStatus(1);
        pmsProductMapper.updateProduct(productById);
    }


    private String generateUniqueProductCode() {
        Random random = new Random();
        String code;
        do {
            code = String.format("%08d", random.nextInt(100000000));
        } while (pmsProductMapper.isProductCodeExists(code));
        return code;
    }


}