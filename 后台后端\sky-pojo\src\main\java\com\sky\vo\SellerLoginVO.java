package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SellerLoginVO implements Serializable {
    private Long id;


    private String accountName;

    private List<String> Permission = new ArrayList<>();


    private String token;
}
