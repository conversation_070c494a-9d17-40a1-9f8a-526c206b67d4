package com.sky.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MerchantProduct {
    // 产品代码，唯一标识一个产品
    private String productCode;

    // 产品标题，简要描述产品名称
    private String productTitle;

    // 产品类别，标识产品所属的分类
    private String productCategory;

    // 产品主图，展示产品的图片链接
    private String mainImage;

    // 产品价格，使用BigDecimal类型以支持精确的货币计算
    private BigDecimal price;

    // 过去7天的销售量
    private Integer salesLast7Days;

    // 过去30天的销售量
    private Integer salesLast30Days;

    // 产品状态，如“上架”、“下架”等
    private String status;

    // NBS（可能是一个特定的业务标识或分类）
    private String nbs;

    // 产品创建时间，记录产品被添加到系统的时间
    private LocalDateTime createdAt;

    // 产品描述，详细描述产品的特性和功能
    private String productDescription;
}