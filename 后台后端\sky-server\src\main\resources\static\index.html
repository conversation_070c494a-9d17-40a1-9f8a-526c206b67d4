<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>服务端生成签名上传文件到OSS</title>
</head>
<body>
<div class="container">
    <form>
        <div class="mb-3">
            <label for="file" class="form-label">选择文件:</label>
            <input type="file" class="form-control" id="file" name="file" required />
        </div>
        <button type="submit" class="btn btn-primary">上传</button>
    </form>
</div>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.querySelector("form");
        const fileInput = document.querySelector("#file");

        form.addEventListener("submit", (event) => {
            event.preventDefault();

            const file = fileInput.files[0];

            if (!file) {
                alert('请选择一个文件再上传。');
                return;
            }

            const filename = file.name;

            fetch("/get_post_signature_for_oss_upload", { method: "GET" })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("获取签名失败");
                    }
                    return response.json();
                })
                .then((data) => {
                    let formData = new FormData();
                    formData.append("success_action_status", "200");
                    formData.append("policy", data.policy);
                    formData.append("x-oss-signature", data.signature);
                    formData.append("x-oss-signature-version", "OSS4-HMAC-SHA256");
                    formData.append("x-oss-credential", data.x_oss_credential);
                    formData.append("x-oss-date", data.x_oss_date);
                    formData.append("key", data.dir + file.name); // 文件名
                    formData.append("x-oss-security-token", data.security_token);
                    formData.append("file", file); // file 必须为最后一个表单域

                    return fetch(data.host, {
                        method: "POST",
                        body: formData
                    });
                })
                .then((response) => {
                    if (response.ok) {
                        console.log("上传成功");
                        alert("文件已上传");
                    } else {
                        console.log("上传失败", response);
                        alert("上传失败，请稍后再试");
                    }
                })
                .catch((error) => {
                    console.error("发生错误:", error);
                });
        });
    });
</script>
</body>
</html>