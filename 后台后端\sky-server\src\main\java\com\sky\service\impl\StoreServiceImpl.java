package com.sky.service.impl;

import com.sky.Utils.Demo;
import com.sky.constant.MessageConstant;
import com.sky.dto.StoreDTO;
import com.sky.entity.Store;
import com.sky.exception.MessageNotFound;
import com.sky.mapper.PaymentRecordMapper;
import com.sky.mapper.StoreMapper;
import com.sky.result.Result;
import com.sky.service.StoreService;
import com.sky.vo.StoreVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
public class StoreServiceImpl implements StoreService {
    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private Demo demo;

    /**
     * 新增店铺
     * @param storeDTO
     */
    @Override
    public void createStore(StoreDTO storeDTO) throws Exception {
        if (storeDTO == null){
            throw new MessageNotFound(MessageConstant.DATA_NOT_FOUND);
        }
        String photo = storeDTO.getPhoto();
        String url1 = photo.split("dir")[1];
        String encode = URLEncoder.encode(url1, "utf-8");
        String url = photo.split("dir")[0]+"dir"+encode;
        Store store = new Store();
        BeanUtils.copyProperties(storeDTO, store);
        store.setPhoto(url);
        store.setCreateTime(LocalDateTime.now());
        store.setLastLoginTime(LocalDateTime.now());
        storeMapper.insert(store);
    }

    /**
     * 修改店铺信息
     * @param storeDTO
     */
    @Override
    public void updateStore(StoreDTO storeDTO) {
        if (storeDTO == null){
            throw new MessageNotFound(MessageConstant.DATA_NOT_FOUND);
        }
        Store store = new Store();
        BeanUtils.copyProperties(storeDTO, store);
        store.setLastLoginTime(LocalDateTime.now());
        storeMapper.update(store);
    }

    /**
     * 根据id查询店铺信息
     * @param id
     * @return
     */
    @Override
    public StoreVO getById(Long id) {
        if (id == null){
            throw new MessageNotFound(MessageConstant.ID_NOT_FOUND);
        }
        Store store = storeMapper.getById(id);
        StoreVO storeVO = new StoreVO();
        BeanUtils.copyProperties(store, storeVO);

        return storeVO;
    }

    @Override
    public void delete(Long id) {
        if (id == null){
            throw new MessageNotFound(MessageConstant.ID_NOT_FOUND);
        }
        storeMapper.delete(id);
    }
}
