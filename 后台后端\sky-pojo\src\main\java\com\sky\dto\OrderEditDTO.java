package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单编辑数据传输对象
 */
@Data
public class OrderEditDTO implements Serializable {
    
    private Long id;                    // 订单ID
    private Long addressId;             // 收货地址ID
    private Integer shippingMethodId;   // 配送方式ID
    private String orderRemark;         // 订单备注
    private BigDecimal amount;          // 订单金额
    private String editReason;          // 编辑原因
}
