package com.sky.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 微信退款查询响应VO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WechatRefundQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信支付退款订单号
     */
    private String id;

    /**
     * 商户退款单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 微信支付交易订单号
     */
    @JsonProperty("transaction_id")
    private String transactionId;

    /**
     * 商户原交易订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 退款渠道
     */
    private String channel;

    /**
     * 退款入账账户
     */
    @JsonProperty("recv_account")
    private String recvAccount;

    /**
     * 退款资金来源
     */
    @JsonProperty("fund_source")
    private String fundSource;

    /**
     * 退款成功时间
     */
    @JsonProperty("success_time")
    private String successTime;

    /**
     * 退款创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 退款状态
     * SUCCESS：退款成功
     * REFUNDCLOSE：退款关闭
     * PROCESSING：退款处理中
     * ABNORMAL：退款异常
     */
    private String status;

    /**
     * 退款金额信息
     */
    private RefundAmount amount;

    /**
     * 优惠退款详情
     */
    private List<RefundDetail> detail;

    /**
     * 退款金额信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RefundAmount {
        /**
         * 退款金额
         */
        private Integer refund;

        /**
         * 货币类型
         */
        private String currency;

        /**
         * 用户退款金额
         */
        @JsonProperty("payer_refund")
        private Integer payerRefund;

        /**
         * 支付币种
         */
        @JsonProperty("payer_currency")
        private String payerCurrency;

        /**
         * 结算币种退款金额
         */
        @JsonProperty("settlement_refund")
        private Integer settlementRefund;

        /**
         * 结算币种
         */
        @JsonProperty("settlement_currency")
        private String settlementCurrency;

        /**
         * 汇率信息
         */
        @JsonProperty("exchange_rate")
        private ExchangeRate exchangeRate;

        /**
         * 退款出资来源及金额
         */
        private List<RefundFrom> from;
    }

    /**
     * 汇率信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExchangeRate {
        /**
         * 汇率类型
         */
        private String type;

        /**
         * 汇率值
         */
        private Integer rate;
    }

    /**
     * 退款出资来源及金额
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RefundFrom {
        /**
         * 出资来源
         */
        @JsonProperty("fund_source")
        private String fundSource;

        /**
         * 出资金额
         */
        private Integer amount;
    }

    /**
     * 优惠退款详情
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RefundDetail {
        /**
         * 券ID
         */
        @JsonProperty("promotion_id")
        private String promotionId;

        /**
         * 优惠范围
         */
        private String scope;

        /**
         * 优惠类型
         */
        private String type;

        /**
         * 优惠券面额
         */
        private Integer amount;

        /**
         * 优惠券退款额
         */
        @JsonProperty("refund_amount")
        private Integer refundAmount;

        /**
         * 货币类型
         */
        private String currency;
    }
}
