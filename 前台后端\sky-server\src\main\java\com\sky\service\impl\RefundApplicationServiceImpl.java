package com.sky.service.impl;

import com.sky.dto.RefundApplicationDTO;
import com.sky.dto.RefundQueryDTO;
import com.sky.entity.Orders;
import com.sky.entity.RefundApplication;
import com.sky.exception.OrderException;
import com.sky.exception.RefundException;
import com.sky.mapper.OrdersMapper;
import com.sky.mapper.RefundApplicationMapper;
import com.sky.service.RefundApplicationService;
import com.sky.service.WechatRefundService;
import com.sky.vo.RefundApplicationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 退款申请服务实现类（前台用户端）
 */
@Service
@Slf4j
public class RefundApplicationServiceImpl implements RefundApplicationService {

    @Autowired
    private RefundApplicationMapper refundApplicationMapper;

    @Autowired
    private OrdersMapper ordersMapper;

    @Autowired
    private WechatRefundService wechatRefundService;

    /**
     * 申请退款
     */
    @Override
    @Transactional
    public RefundApplicationVO applyRefund(RefundApplicationDTO refundApplicationDTO, Long buyerId) {
        log.info("用户申请退款，buyerId: {}, dto: {}", buyerId, refundApplicationDTO);

        // 1. 检查退款条件
        RefundCheckResult checkResult = checkRefundEligibility(refundApplicationDTO.getOrderId(), buyerId);
        if (!checkResult.isCanRefund()) {
            throw new RefundException(checkResult.getReason());
        }

        // 2. 验证退款金额
        BigDecimal refundAmount = refundApplicationDTO.getRefundAmount();
        BigDecimal maxRefundAmount = checkResult.getMaxRefundAmount();

        // 统一精度到2位小数进行比较，避免精度问题
        BigDecimal normalizedRefundAmount = refundAmount.setScale(2, RoundingMode.HALF_UP);
        BigDecimal normalizedMaxAmount = maxRefundAmount.setScale(2, RoundingMode.HALF_UP);

        log.info("退款金额验证 - 申请退款金额: {} (标准化: {}), 最大可退款金额: {} (标准化: {}), 比较结果: {}",
                refundAmount, normalizedRefundAmount, maxRefundAmount, normalizedMaxAmount,
                normalizedRefundAmount.compareTo(normalizedMaxAmount));

        if (normalizedRefundAmount.compareTo(normalizedMaxAmount) > 0) {
            throw new RefundException(String.format("退款金额不能超过订单金额，申请金额: %s，订单金额: %s",
                    normalizedRefundAmount, normalizedMaxAmount));
        }

        // 3. 查询订单信息
        Orders order = ordersMapper.selectById(refundApplicationDTO.getOrderId());
        if (order == null) {
            throw new OrderException("订单不存在");
        }

        // 4. 创建退款申请
        RefundApplication refundApplication = RefundApplication.builder()
                .refundNo(generateRefundNo())
                .orderId(refundApplicationDTO.getOrderId())
                .orderNumber(order.getNumber())
                .buyerId(buyerId)
                .refundAmount(refundApplicationDTO.getRefundAmount())
                .refundReason(refundApplicationDTO.getRefundReason())
                .refundType(refundApplicationDTO.getRefundType())
                .applicationStatus(RefundApplication.STATUS_PENDING)
                .needApproval(checkResult.isNeedApproval() ? 1 : 0)
                .approvalStatus(checkResult.isNeedApproval() ? RefundApplication.APPROVAL_PENDING : null)
                .refundMethod(refundApplicationDTO.getRefundMethod())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        refundApplicationMapper.insert(refundApplication);

        // 5. 更新订单退款状态
        ordersMapper.updateRefundStatus(order.getId(), 1, null, null, LocalDateTime.now());

        // 6. 根据订单状态决定处理方式
        if (order.getStatus().equals(Orders.STATUS_PENDING_PAYMENT) ||
            order.getStatus().equals(Orders.STATUS_PAID)) {
            // 未发货订单：直接退款，不需要审核
            try {
                boolean refundSuccess = processDirectRefund(refundApplication, order);
                if (refundSuccess) {
                    log.info("直接退款处理成功，refundNo: {}", refundApplication.getRefundNo());
                } else {
                    log.warn("直接退款处理失败，refundNo: {}", refundApplication.getRefundNo());
                }
            } catch (Exception e) {
                log.error("直接退款处理异常，refundNo: " + refundApplication.getRefundNo(), e);
            }
        } else if (order.getStatus().equals(Orders.STATUS_SHIPPED) ||
                   order.getStatus().equals(Orders.STATUS_COMPLETED)) {
            // 已发货/已完成订单：需要审核
            log.info("已发货订单申请退款，需要后台审核，refundNo: {}", refundApplication.getRefundNo());
            // 发送通知给后台管理员
            sendRefundNotificationToAdmin(refundApplication, order);
        }

        log.info("退款申请创建成功，refundNo: {}", refundApplication.getRefundNo());

        // 7. 返回结果
        return getRefundApplicationById(refundApplication.getId());
    }

    /**
     * 取消退款申请
     */
    @Override
    @Transactional
    public boolean cancelRefundApplication(Long refundApplicationId, Long buyerId) {
        log.info("取消退款申请，refundApplicationId: {}, buyerId: {}", refundApplicationId, buyerId);

        RefundApplication refundApplication = refundApplicationMapper.selectById(refundApplicationId);
        if (refundApplication == null) {
            throw new RefundException("退款申请不存在");
        }

        if (!refundApplication.getBuyerId().equals(buyerId)) {
            throw new RefundException("无权限操作此退款申请");
        }

        if (!refundApplication.canCancel()) {
            throw new RefundException("当前状态不允许取消");
        }

        // 更新申请状态为已取消
        int result = refundApplicationMapper.updateApplicationStatus(
                refundApplicationId, 
                RefundApplication.STATUS_CANCELLED, 
                LocalDateTime.now()
        );

        // 更新订单退款状态
        if (result > 0) {
            ordersMapper.updateRefundStatus(refundApplication.getOrderId(), 0, null, null, LocalDateTime.now());
        }

        return result > 0;
    }

    /**
     * 根据ID查询退款申请详情
     */
    @Override
    public RefundApplicationVO getRefundApplicationById(Long refundApplicationId) {
        return refundApplicationMapper.selectDetailById(refundApplicationId);
    }

    /**
     * 根据退款申请单号查询详情
     */
    @Override
    public RefundApplicationVO getRefundApplicationByNo(String refundNo) {
        RefundApplication refundApplication = refundApplicationMapper.selectByRefundNo(refundNo);
        if (refundApplication == null) {
            return null;
        }
        return getRefundApplicationById(refundApplication.getId());
    }

    /**
     * 分页查询退款申请
     */
    @Override
    public List<RefundApplicationVO> pageQuery(RefundQueryDTO queryDTO) {
        int offset = (queryDTO.getPage() - 1) * queryDTO.getPageSize();
        return refundApplicationMapper.selectPageWithDetails(
                queryDTO.getBuyerId(),
                queryDTO.getApplicationStatus(),
                queryDTO.getNeedApproval(),
                queryDTO.getApprovalStatus(),
                queryDTO.getStartTime(),
                queryDTO.getEndTime(),
                offset,
                queryDTO.getPageSize()
        );
    }

    /**
     * 统计退款申请数量
     */
    @Override
    public Long countRefundApplications(RefundQueryDTO queryDTO) {
        return refundApplicationMapper.countRefundApplications(
                queryDTO.getBuyerId(),
                queryDTO.getApplicationStatus(),
                queryDTO.getNeedApproval(),
                queryDTO.getApprovalStatus(),
                queryDTO.getStartTime(),
                queryDTO.getEndTime()
        );
    }

    /**
     * 查询用户的退款申请列表
     */
    @Override
    public List<RefundApplicationVO> getUserRefundApplications(Long buyerId, Integer page, Integer pageSize) {
        RefundQueryDTO queryDTO = RefundQueryDTO.builder()
                .buyerId(buyerId)
                .page(page)
                .pageSize(pageSize)
                .build();
        return pageQuery(queryDTO);
    }







    /**
     * 检查订单是否可以申请退款
     */
    @Override
    public RefundCheckResult checkRefundEligibility(Long orderId, Long buyerId) {
        log.info("检查退款条件，orderId: {}, buyerId: {}", orderId, buyerId);

        // 1. 查询订单信息
        Orders order = ordersMapper.selectById(orderId);
        if (order == null) {
            return new RefundCheckResult(false, false, "订单不存在", BigDecimal.ZERO);
        }

        // 2. 检查订单所有者
        if (!order.getBuyerId().equals(buyerId)) {
            return new RefundCheckResult(false, false, "无权限操作此订单", BigDecimal.ZERO);
        }

        // 3. 检查订单状态
        if (order.getStatus().equals(Orders.STATUS_CANCELLED) ||
            order.getStatus().equals(Orders.STATUS_REFUNDED)) {
            return new RefundCheckResult(false, false, "订单已取消或已退款", BigDecimal.ZERO);
        }

        // 4. 检查是否已有退款申请
        if (order.getRefundStatus() != null && order.getRefundStatus() > 0) {
            return new RefundCheckResult(false, false, "订单已有退款申请", BigDecimal.ZERO);
        }

        // 5. 根据订单状态判断是否需要审核
        boolean needApproval = false;
        String reason = "可以申请退款";

        if (order.getStatus().equals(Orders.STATUS_SHIPPED)) {
            // 已发货，需要审核
            needApproval = true;
            reason = "订单已发货，需要审核";
        } else if (order.getStatus().equals(Orders.STATUS_COMPLETED)) {
            // 已完成，需要审核
            needApproval = true;
            reason = "订单已完成，需要审核";
        } else if (order.getStatus().equals(Orders.STATUS_PENDING_PAYMENT)) {
            // 待付款，不能退款
            return new RefundCheckResult(false, false, "订单未付款，无法退款", BigDecimal.ZERO);
        }

        log.info("退款条件检查通过 - 订单ID: {}, 订单金额: {}, 需要审核: {}",
                orderId, order.getAmount(), needApproval);

        return new RefundCheckResult(true, needApproval, reason, order.getAmount());
    }



    /**
     * 生成退款申请单号
     */
    @Override
    public String generateRefundNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%04d", ThreadLocalRandom.current().nextInt(10000));
        return "RF" + timestamp + random;
    }

    /**
     * 更新退款状态
     */
    @Override
    @Transactional
    public boolean updateRefundStatus(String refundNo, Integer applicationStatus, BigDecimal actualRefundAmount) {
        log.info("更新退款状态，refundNo: {}, applicationStatus: {}, actualRefundAmount: {}",
                refundNo, applicationStatus, actualRefundAmount);

        // 1. 根据退款单号查询退款申请
        RefundApplication refundApplication = refundApplicationMapper.selectByRefundNo(refundNo);
        if (refundApplication == null) {
            log.error("退款申请不存在，refundNo: {}", refundNo);
            return false;
        }

        // 2. 更新退款申请状态
        int result = refundApplicationMapper.updateApplicationStatus(
                refundApplication.getId(),
                applicationStatus,
                LocalDateTime.now()
        );

        // 3. 如果有实际退款金额，更新实际退款金额和退款时间
        if (actualRefundAmount != null && result > 0) {
            // 这里可以添加更新实际退款金额的逻辑
            // 目前先记录日志
            log.info("实际退款金额: {}", actualRefundAmount);
        }

        // 4. 如果退款成功，更新订单退款状态
        if (result > 0 && applicationStatus.equals(RefundApplication.STATUS_REFUND_SUCCESS)) {
            ordersMapper.updateRefundStatus(
                    refundApplication.getOrderId(),
                    3, // 全额退款
                    actualRefundAmount != null ? actualRefundAmount : refundApplication.getRefundAmount(),
                    LocalDateTime.now(),
                    LocalDateTime.now()
            );
        }

        return result > 0;
    }

    /**
     * 直接处理退款（未发货订单）
     * @param refundApplication 退款申请
     * @param order 订单信息
     * @return 是否成功
     */
    private boolean processDirectRefund(RefundApplication refundApplication, Orders order) {
        try {
            log.info("开始直接处理退款，refundNo: {}", refundApplication.getRefundNo());

            // 1. 更新退款申请状态为退款中
            refundApplicationMapper.updateApplicationStatus(
                    refundApplication.getId(),
                    RefundApplication.STATUS_REFUNDING,
                    LocalDateTime.now()
            );

            // 2. 调用微信退款API
            boolean refundSuccess = callWechatRefundAPI(refundApplication);

            if (refundSuccess) {
                // 3. 退款成功，更新状态
                refundApplicationMapper.updateApplicationStatus(
                        refundApplication.getId(),
                        RefundApplication.STATUS_REFUND_SUCCESS,
                        LocalDateTime.now()
                );

                // 4. 更新订单状态为已退款
                ordersMapper.updateStatus(order.getId(), Orders.STATUS_REFUNDED);
                ordersMapper.updateRefundStatus(
                        order.getId(),
                        3, // 全额退款
                        refundApplication.getRefundAmount(),
                        LocalDateTime.now(),
                        LocalDateTime.now()
                );

                log.info("直接退款处理成功，refundNo: {}", refundApplication.getRefundNo());
                return true;
            } else {
                // 5. 退款失败，更新状态
                refundApplicationMapper.updateApplicationStatus(
                        refundApplication.getId(),
                        RefundApplication.STATUS_REFUND_FAILED,
                        LocalDateTime.now()
                );

                log.error("直接退款处理失败，refundNo: {}", refundApplication.getRefundNo());
                return false;
            }

        } catch (Exception e) {
            log.error("直接退款处理异常，refundNo: " + refundApplication.getRefundNo(), e);

            // 异常情况下，将状态设置为待处理，需要人工干预
            try {
                refundApplicationMapper.updateApplicationStatus(
                        refundApplication.getId(),
                        RefundApplication.STATUS_PENDING,
                        LocalDateTime.now()
                );
            } catch (Exception ex) {
                log.error("更新退款状态异常", ex);
            }

            throw new RefundException("退款处理失败，请稍后重试", e);
        }
    }

    /**
     * 发送退款通知给后台管理员
     * @param refundApplication 退款申请
     * @param order 订单信息
     */
    private void sendRefundNotificationToAdmin(RefundApplication refundApplication, Orders order) {
        try {
            log.info("发送退款通知给后台管理员，refundNo: {}", refundApplication.getRefundNo());

            // TODO: 实现具体的通知逻辑
            // 1. 发送邮件通知
            // 2. 发送短信通知
            // 3. 推送到后台管理系统
            // 4. 记录通知日志

            String message = String.format(
                "收到新的退款申请：\n" +
                "退款单号：%s\n" +
                "订单号：%s\n" +
                "退款金额：￥%.2f\n" +
                "退款原因：%s\n" +
                "请及时处理。",
                refundApplication.getRefundNo(),
                order.getNumber(),
                refundApplication.getRefundAmount(),
                refundApplication.getRefundReason()
            );

            log.info("退款通知内容：{}", message);

        } catch (Exception e) {
            log.error("发送退款通知失败", e);
        }
    }

    /**
     * 自动处理退款
     * @param refundApplication 退款申请
     * @return 是否成功
     */
    private boolean processRefundAutomatically(RefundApplication refundApplication) {
        try {
            log.info("开始自动处理退款，refundNo: {}", refundApplication.getRefundNo());

            // 1. 更新状态为退款中
            refundApplicationMapper.updateApplicationStatus(
                    refundApplication.getId(),
                    RefundApplication.STATUS_REFUNDING,
                    LocalDateTime.now()
            );

            // 2. 调用微信退款API
            boolean refundSuccess = callWechatRefundAPI(refundApplication);

            if (refundSuccess) {
                // 3. 退款成功，更新状态
                refundApplicationMapper.updateApplicationStatus(
                        refundApplication.getId(),
                        RefundApplication.STATUS_REFUND_SUCCESS,
                        LocalDateTime.now()
                );

                // 4. 更新订单退款状态
                ordersMapper.updateRefundStatus(
                        refundApplication.getOrderId(),
                        3, // 全额退款
                        refundApplication.getRefundAmount(),
                        LocalDateTime.now(),
                        LocalDateTime.now()
                );

                log.info("自动退款处理成功，refundNo: {}", refundApplication.getRefundNo());
                return true;
            } else {
                // 5. 退款失败，更新状态
                refundApplicationMapper.updateApplicationStatus(
                        refundApplication.getId(),
                        RefundApplication.STATUS_REFUND_FAILED,
                        LocalDateTime.now()
                );

                log.error("自动退款处理失败，refundNo: {}", refundApplication.getRefundNo());
                return false;
            }

        } catch (Exception e) {
            log.error("自动退款处理异常，refundNo: " + refundApplication.getRefundNo(), e);

            // 异常情况下，将状态设置为待处理，需要人工干预
            try {
                refundApplicationMapper.updateApplicationStatus(
                        refundApplication.getId(),
                        RefundApplication.STATUS_PENDING,
                        LocalDateTime.now()
                );
            } catch (Exception ex) {
                log.error("更新退款状态异常", ex);
            }

            return false;
        }
    }

    /**
     * 调用微信退款API
     * @param refundApplication 退款申请
     * @return 是否成功
     */
    private boolean callWechatRefundAPI(RefundApplication refundApplication) {
        try {
            log.info("调用微信退款API，退款单号：{}，退款金额：{}",
                    refundApplication.getRefundNo(), refundApplication.getRefundAmount());

            // 查询订单信息获取微信订单号
            Orders order = ordersMapper.selectById(refundApplication.getOrderId());
            if (order == null) {
                log.error("订单不存在，无法退款");
                return false;
            }

            // 检查订单是否有微信支付的商户订单号
            if (order.getPaymentTransactionId() == null || order.getPaymentTransactionId().trim().isEmpty()) {
                log.error("订单没有微信支付的商户订单号，无法退款，orderId: {}", order.getId());
                return false;
            }

            // 构建微信退款请求
            com.sky.dto.WechatRefundRequestDTO refundRequest = com.sky.dto.WechatRefundRequestDTO.builder()
                    .outTradeNo(order.getPaymentTransactionId()) // 使用微信支付时的商户订单号
                    .outRefundNo(refundApplication.getRefundNo())
                    .reason(refundApplication.getRefundReason())
                    .refundAmount(refundApplication.getRefundAmount().multiply(new BigDecimal(100)).intValue()) // 转换为分
                    .totalAmount(order.getAmount().multiply(new BigDecimal(100)).intValue()) // 转换为分
                    .build();

            // 调用微信退款服务
            com.sky.vo.WechatRefundResponseVO response = wechatRefundService.applyRefund(refundRequest);

            if (response != null && response.getId() != null && !response.getId().trim().isEmpty()) {
                log.info("微信退款申请成功，退款单号：{}，微信退款单号：{}",
                        refundApplication.getRefundNo(), response.getId());
                return true;
            } else {
                log.error("微信退款申请失败，退款单号：{}，响应：{}",
                        refundApplication.getRefundNo(), response);
                return false;
            }

        } catch (Exception e) {
            log.error("调用微信退款API异常，退款单号：{}", refundApplication.getRefundNo(), e);
            return false;
        }
    }
}
