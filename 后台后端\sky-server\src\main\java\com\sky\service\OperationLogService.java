package com.sky.service;

/**
 * 操作日志服务接口
 */
public interface OperationLogService {
    
    /**
     * 记录订单操作日志
     * @param orderId 订单ID
     * @param orderNumber 订单号
     * @param operationType 操作类型
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param operatorType 操作人类型
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param operationDesc 操作描述
     */
    void logOrderOperation(Long orderId, String orderNumber, String operationType, 
                          Integer oldStatus, Integer newStatus, String operatorType, 
                          Long operatorId, String operatorName, String operationDesc);
    
    /**
     * 记录物流操作日志
     * @param orderId 订单ID
     * @param trackingNumber 物流单号
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param eventInfo 事件信息
     * @param location 事件位置
     * @param operator 操作人
     */
    void logLogisticsOperation(Long orderId, String trackingNumber, String oldStatus, 
                              String newStatus, String eventInfo, String location, String operator);
    
    /**
     * 记录退款操作日志
     * @param refundApplicationId 退款申请ID
     * @param refundNo 退款单号
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param operationType 操作类型
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param operationDesc 操作描述
     */
    void logRefundOperation(Long refundApplicationId, String refundNo, Integer oldStatus, 
                           Integer newStatus, String operationType, Long operatorId, 
                           String operatorName, String operationDesc);
}
