package com.sky.service.impl;

import com.sky.entity.ShoppingCart;
import com.sky.mapper.ShoppingCartMapper;
import com.sky.service.ShoppingCartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class ShoppingCartServiceImpl implements ShoppingCartService {

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    @Override
    public void addToCart(ShoppingCart shoppingCart) {
        ShoppingCart existingCart = shoppingCartMapper.findByBuyerIdAndProductId(shoppingCart.getBuyerId(), shoppingCart.getProductId());
        if (existingCart != null) {
            // 更新商品数量和总金额
            existingCart.setNumber(existingCart.getNumber() + shoppingCart.getNumber());
            existingCart.setPrice(existingCart.getAmount().multiply(BigDecimal.valueOf(existingCart.getNumber())));
            shoppingCartMapper.update(existingCart);
        } else {
            // 新增购物车记录
            shoppingCart.setPrice(shoppingCart.getAmount().multiply(BigDecimal.valueOf(shoppingCart.getNumber())));
            shoppingCartMapper.insert(shoppingCart);
        }
    }

    @Override
    public List<ShoppingCart> getCartByBuyerId(Long buyerId) {
        return shoppingCartMapper.findByBuyerId(buyerId);
    }

    @Override
    public void deleteCartItem(int id) {
        shoppingCartMapper.deleteById(id);
    }

    @Override
    public void update(ShoppingCart shoppingCart) {
        shoppingCartMapper.update(shoppingCart);
    }
}