package com.sky.mapper;

import com.sky.entity.OrderPaymentDetail;
import org.apache.ibatis.annotations.*;

/**
 * 订单支付详情Mapper
 */
@Mapper
public interface OrderPaymentDetailMapper {

    /**
     * 插入支付详情
     */
    @Insert("INSERT INTO order_payment_detail (order_id, order_number, payment_method, payment_channel, " +
            "transaction_id, payment_amount, currency, payment_status, payment_time, " +
            "refund_amount, refund_time, payment_desc, create_time, update_time) VALUES " +
            "(#{orderId}, #{orderNumber}, #{paymentMethod}, #{paymentChannel}, " +
            "#{transactionId}, #{paymentAmount}, #{currency}, #{paymentStatus}, #{paymentTime}, " +
            "#{refundAmount}, #{refundTime}, #{paymentDesc}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(OrderPaymentDetail paymentDetail);

    /**
     * 根据订单ID查询支付详情
     */
    @Select("SELECT * FROM order_payment_detail WHERE order_id = #{orderId}")
    OrderPaymentDetail getByOrderId(Long orderId);

    /**
     * 根据订单号查询支付详情
     */
    @Select("SELECT * FROM order_payment_detail WHERE order_number = #{orderNumber}")
    OrderPaymentDetail getByOrderNumber(String orderNumber);

    /**
     * 更新支付详情
     */
    @Update("UPDATE order_payment_detail SET payment_status = #{paymentStatus}, " +
            "payment_time = #{paymentTime}, refund_amount = #{refundAmount}, " +
            "refund_time = #{refundTime}, update_time = #{updateTime} WHERE order_id = #{orderId}")
    void update(OrderPaymentDetail paymentDetail);
}
