package com.sky.service;

import com.sky.dto.WechatRefundRequestDTO;
import com.sky.dto.WechatRefundQueryDTO;
import com.sky.dto.WechatRefundListQueryDTO;
import com.sky.vo.WechatRefundResponseVO;
import com.sky.vo.WechatRefundQueryVO;
import com.sky.vo.WechatRefundListQueryVO;

/**
 * 微信退款服务接口
 */
public interface WechatRefundService {

    /**
     * 申请退款
     * @param requestDTO 退款申请请求
     * @return 退款响应
     */
    WechatRefundResponseVO applyRefund(WechatRefundRequestDTO requestDTO);

    /**
     * 查询退款结果
     * @param queryDTO 退款查询请求
     * @return 退款查询响应
     */
    WechatRefundQueryVO queryRefund(WechatRefundQueryDTO queryDTO);

    /**
     * 查询所有退款
     * @param queryDTO 查询所有退款请求
     * @return 查询所有退款响应
     */
    WechatRefundListQueryVO queryAllRefunds(WechatRefundListQueryDTO queryDTO);
}
