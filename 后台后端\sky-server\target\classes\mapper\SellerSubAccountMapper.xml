<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SellerSubAccountMapper">

    <insert id="save">
        insert into ry_mall.seller (id, account_name, password, gender, phone, email, account_status, create_time, update_time, last_login_time, certification_info, photo_url, user_role, created_by, is_sub_account, remark)
        values (#{id}, #{accountName}, #{password}, #{gender}, #{phone}, #{email}, #{accountStatus}, #{createTime}, #{updateTime}, #{lastLoginTime}, #{certificationInfo}, #{photoUrl}, #{userRole}, #{createdBy}, #{isSubAccount},#{remark})
    </insert>

    <insert id="savePermissions">
         insert into ry_mall.seller_permission (seller_id, permission_code)
         values
        <foreach collection="permissions" item="permission" separator=",">
            (#{id}, #{permission})
        </foreach>
    </insert>

    <update id="update">
        update ry_mall.seller
        <set>
            <if test="accountName != null">
                account_name = #{accountName},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus},
            </if>
            <if test="remark != null">
                remark = #{remark}
            </if>
        </set>
        where id = #{id}
</update>

    <resultMap id="SellerSubAccountMap" type="com.sky.vo.SellerSubAccountVO">
        <id property="id" column="id" />
        <result property="accountName" column="account_name" />
        <result property="email" column="email" />
        <result property="phone" column="phone" />
        <result property="accountStatus" column="account_status" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="lastLoginTime" column="last_login_time" />
        <result property="remark" column="remark" />

        <!-- 一对多映射：一个seller对应多个permission_code -->
        <collection property="permissions" ofType="java.lang.String" column="id" select="selectPermissionsBySellerId" />
    </resultMap>

    <select id="selectPermissionsBySellerId" resultType="java.lang.String">
        SELECT permission_code
        FROM seller_permission
        WHERE seller_id = #{id}
    </select>

    <select id="getSubAccountList" resultMap="SellerSubAccountMap">
         SELECT
             s.id,
             s.account_name,
             s.email,
             s.phone,
             s.account_status,
             s.create_time,
             s.update_time,
             s.last_login_time,
             s.remark
         FROM seller s
         WHERE s.created_by = #{createdBy}
             <if test="accountStatus != null">
                 AND s.account_status = #{accountStatus}
             </if>
         order by s.id
     </select>

</mapper>
