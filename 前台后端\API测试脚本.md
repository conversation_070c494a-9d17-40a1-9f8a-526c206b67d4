# 订单详情集成功能API测试脚本

## 测试环境准备

### 1. 启动应用
```bash
cd 前台后端
mvn spring-boot:run
```

### 2. 获取JWT Token
首先需要登录获取JWT token，用于后续API调用的认证。

```bash
# 用户登录（请根据实际的登录接口调整）
curl -X POST "http://localhost:8443/user/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

将返回的token保存，用于后续请求的Authorization头。

## API测试用例

### 1. 获取订单完整详情
```bash
curl -X GET "http://localhost:8443/user/order-detail/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回订单的完整详情信息
- 包含订单基础信息、商品详情、物流跟踪、地址信息、支付详情
- 状态码200

### 2. 根据订单号查询订单详情
```bash
curl -X GET "http://localhost:8443/user/order-detail/by-number/202412190001" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回对应订单号的详情信息
- 数据结构与获取订单详情接口相同

### 3. 获取用户订单列表
```bash
# 获取所有订单
curl -X GET "http://localhost:8443/user/order-detail/list" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 按状态筛选（已发货状态）
curl -X GET "http://localhost:8443/user/order-detail/list?status=4" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 分页查询
curl -X GET "http://localhost:8443/user/order-detail/list?pageNum=1&pageSize=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回用户的订单列表
- 支持状态筛选和分页

### 4. 同步物流信息
```bash
curl -X POST "http://localhost:8443/user/order-detail/1/sync-logistics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回同步成功或失败的消息
- 状态码200表示请求成功

### 5. 获取实时物流信息
```bash
curl -X GET "http://localhost:8443/user/order-detail/1/realtime-logistics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回实时物流轨迹信息
- 包含最新的物流状态和轨迹详情

### 6. 确认收货
```bash
curl -X POST "http://localhost:8443/user/order-detail/1/confirm-receipt" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 订单状态为"已发货"时可以成功确认收货
- 订单状态变为"已完成"
- 其他状态返回错误信息

### 7. 申请退款
```bash
curl -X POST "http://localhost:8443/user/order-detail/1/apply-refund?reason=商品质量问题" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 符合退款条件的订单可以成功申请退款
- 订单退款状态更新
- 不符合条件的订单返回错误信息

### 8. 取消订单
```bash
curl -X POST "http://localhost:8443/user/order-detail/1/cancel?reason=不需要了" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 待支付状态的订单可以成功取消
- 订单状态变为"已取消"
- 其他状态返回错误信息

### 9. 获取订单统计
```bash
curl -X GET "http://localhost:8443/user/order-detail/statistics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回用户的订单统计信息
- 包含各状态订单数量

### 10. 获取最近物流更新
```bash
curl -X GET "http://localhost:8443/user/order-detail/recent-logistics?limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回用户最近的物流更新记录
- 按时间倒序排列

### 11. 检查操作权限
```bash
# 检查是否可以取消订单
curl -X GET "http://localhost:8443/user/order-detail/1/check-action/cancel" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 检查是否可以申请退款
curl -X GET "http://localhost:8443/user/order-detail/1/check-action/refund" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 检查是否可以确认收货
curl -X GET "http://localhost:8443/user/order-detail/1/check-action/confirm" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回true或false
- 根据订单状态正确判断权限

## 错误测试用例

### 1. 无权限访问
```bash
# 不提供JWT token
curl -X GET "http://localhost:8443/user/order-detail/1" \
  -H "Content-Type: application/json"

# 使用无效token
curl -X GET "http://localhost:8443/user/order-detail/1" \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回401未授权错误

### 2. 访问不存在的订单
```bash
curl -X GET "http://localhost:8443/user/order-detail/999999" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回404或相应错误信息

### 3. 访问其他用户的订单
```bash
# 尝试访问不属于当前用户的订单
curl -X GET "http://localhost:8443/user/order-detail/OTHER_USER_ORDER_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回403权限错误或订单不存在错误

### 4. 无效的操作请求
```bash
# 对不符合条件的订单执行操作
curl -X POST "http://localhost:8443/user/order-detail/COMPLETED_ORDER_ID/cancel" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**预期结果**：
- 返回业务错误信息，说明订单状态不允许该操作

## 性能测试

### 并发测试
使用Apache Bench进行并发测试：

```bash
# 并发50个请求，总共1000个请求
ab -n 1000 -c 50 -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:8443/user/order-detail/1"
```

**预期结果**：
- 响应时间 < 2秒
- 成功率 > 99%
- 无系统错误

## 测试数据验证

### 1. 数据完整性检查
验证返回的订单详情数据是否完整：
- 订单基础信息字段完整
- 物流信息正确关联
- 地址信息完整
- 支付信息正确
- 时间字段格式正确

### 2. 业务逻辑验证
- 订单状态流转正确
- 操作权限判断准确
- 物流状态映射正确
- 进度计算准确

### 3. 数据一致性验证
- 订单操作后状态正确更新
- 物流同步后数据一致
- 时间字段更新正确

## 注意事项

1. **测试环境**：确保使用独立的测试环境和测试数据
2. **JWT Token**：注意token的有效期，及时更新
3. **数据清理**：测试完成后清理测试数据
4. **日志监控**：关注应用日志中的错误和警告信息
5. **API限制**：注意17TRACK API的调用频率限制

## 自动化测试脚本

可以将上述测试用例整合成自动化测试脚本：

```bash
#!/bin/bash

# 设置基础URL和token
BASE_URL="http://localhost:8443"
TOKEN="YOUR_JWT_TOKEN"

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local expected_code=$3
    local description=$4
    
    echo "测试: $description"
    response=$(curl -s -w "%{http_code}" -X $method "$BASE_URL$url" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json")
    
    http_code="${response: -3}"
    if [ "$http_code" = "$expected_code" ]; then
        echo "✅ 通过"
    else
        echo "❌ 失败 (期望: $expected_code, 实际: $http_code)"
    fi
    echo ""
}

# 执行测试
test_api "GET" "/user/order-detail/1" "200" "获取订单详情"
test_api "GET" "/user/order-detail/list" "200" "获取订单列表"
test_api "GET" "/user/order-detail/statistics" "200" "获取订单统计"
test_api "GET" "/user/order-detail/1/check-action/cancel" "200" "检查取消权限"

echo "测试完成"
```
