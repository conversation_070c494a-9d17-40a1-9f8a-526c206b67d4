package com.sky.service;

import com.sky.dto.RefundApplicationDTO;
import com.sky.dto.RefundApprovalDTO;
import com.sky.dto.RefundQueryDTO;
import com.sky.entity.RefundApplication;
import com.sky.vo.RefundApplicationVO;
import com.sky.vo.RefundStatistics;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款申请服务接口
 */
public interface RefundApplicationService {

    /**
     * 申请退款
     * @param refundApplicationDTO 退款申请DTO
     * @param buyerId 申请人ID
     * @return 退款申请VO
     */
    RefundApplicationVO applyRefund(RefundApplicationDTO refundApplicationDTO, Long buyerId);

    /**
     * 取消退款申请
     * @param refundApplicationId 退款申请ID
     * @param buyerId 申请人ID
     * @return 是否成功
     */
    boolean cancelRefundApplication(Long refundApplicationId, Long buyerId);

    /**
     * 根据ID查询退款申请详情
     * @param refundApplicationId 退款申请ID
     * @return 退款申请VO
     */
    RefundApplicationVO getRefundApplicationById(Long refundApplicationId);

    /**
     * 根据退款申请单号查询详情
     * @param refundNo 退款申请单号
     * @return 退款申请VO
     */
    RefundApplicationVO getRefundApplicationByNo(String refundNo);

    /**
     * 分页查询退款申请
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    List<RefundApplicationVO> pageQuery(RefundQueryDTO queryDTO);

    /**
     * 统计退款申请数量
     * @param queryDTO 查询条件
     * @return 总数量
     */
    Long countRefundApplications(RefundQueryDTO queryDTO);

    /**
     * 查询用户的退款申请列表
     * @param buyerId 买家ID
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    List<RefundApplicationVO> getUserRefundApplications(Long buyerId, Integer page, Integer pageSize);

    /**
     * 查询待审核的退款申请
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    List<RefundApplicationVO> getPendingApprovalApplications(Integer page, Integer pageSize);

    /**
     * 审核退款申请
     * @param approvalDTO 审核DTO
     * @param approverId 审核人ID
     * @return 是否成功
     */
    boolean approveRefundApplication(RefundApprovalDTO approvalDTO, Long approverId);

    /**
     * 处理退款（调用微信退款接口）
     * @param refundApplicationId 退款申请ID
     * @return 是否成功
     */
    boolean processRefund(Long refundApplicationId);

    /**
     * 更新退款状态（用于微信回调）
     * @param refundNo 退款申请单号
     * @param refundStatus 退款状态
     * @param actualRefundAmount 实际退款金额
     * @return 是否成功
     */
    boolean updateRefundStatus(String refundNo, Integer refundStatus, BigDecimal actualRefundAmount);

    /**
     * 检查订单是否可以申请退款
     * @param orderId 订单ID
     * @param buyerId 买家ID
     * @return 检查结果
     */
    RefundCheckResult checkRefundEligibility(Long orderId, Long buyerId);

    /**
     * 生成退款申请单号
     * @return 退款申请单号
     */
    String generateRefundNo();

    /**
     * 获取退款统计信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 退款统计信息
     */
    RefundStatistics getRefundStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 退款检查结果
     */
    class RefundCheckResult {
        private boolean canRefund;
        private boolean needApproval;
        private String reason;
        private BigDecimal maxRefundAmount;

        public RefundCheckResult(boolean canRefund, boolean needApproval, String reason, BigDecimal maxRefundAmount) {
            this.canRefund = canRefund;
            this.needApproval = needApproval;
            this.reason = reason;
            this.maxRefundAmount = maxRefundAmount;
        }

        // Getters and setters
        public boolean isCanRefund() { return canRefund; }
        public void setCanRefund(boolean canRefund) { this.canRefund = canRefund; }
        
        public boolean isNeedApproval() { return needApproval; }
        public void setNeedApproval(boolean needApproval) { this.needApproval = needApproval; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public BigDecimal getMaxRefundAmount() { return maxRefundAmount; }
        public void setMaxRefundAmount(BigDecimal maxRefundAmount) { this.maxRefundAmount = maxRefundAmount; }
    }
}
