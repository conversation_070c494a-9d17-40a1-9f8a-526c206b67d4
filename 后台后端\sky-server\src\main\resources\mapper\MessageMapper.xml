<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sky.mapper.MessageMapper">

    <!-- 插入消息 -->
    <insert id="insertMessage" parameterType="com.sky.entity.Message">
        INSERT INTO message (
            msgID, msgType, title, content, senderId, senderName,
            sendTime, recipientType, recipientId, isRead, createTime, updateTime
        ) VALUES (
                     #{msgID}, #{msgType}, #{title}, #{content}, #{senderId}, #{senderName},
                     #{sendTime}, #{recipientType}, #{recipientId}, #{isRead}, #{createTime}, #{updateTime}
                 )
    </insert>

    <!-- 插入消息接收者关联 -->
    <insert id="insertMessageRecipient" parameterType="com.sky.entity.MessageRecipient">
        INSERT INTO message_recipient (
            msgID, recipientType, recipientId, isRead, createTime, updateTime
        ) VALUES (
                     #{msgID}, #{recipientType}, #{recipientId}, #{isRead}, #{createTime}, #{updateTime}
                 )
    </insert>

    <!-- 批量插入消息接收者关联 -->
    <insert id="batchInsertMessageRecipient" parameterType="java.util.List">
        INSERT INTO message_recipient (
        msgID, recipientType, recipientId, isRead, createTime, updateTime
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.msgID}, #{item.recipientType}, #{item.recipientId}, #{item.isRead}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据接收者类型和ID查询消息列表 -->
    <select id="getMessagesByRecipient" resultType="com.sky.entity.Message">
        SELECT m.*, mr.isRead as is_read
        FROM message m
                 INNER JOIN message_recipient mr ON m.msgID = mr.msgID
        WHERE mr.recipientType = #{recipientType}
          AND mr.recipientId = #{recipientId}
        ORDER BY m.sendTime DESC
    </select>

    <!-- 根据消息ID和接收者查询消息详情（带isRead） -->
    <select id="getMessageById" resultType="com.sky.entity.Message">
        SELECT m.*, mr.isRead as is_read
        FROM message m
        INNER JOIN message_recipient mr ON m.msgID = mr.msgID
        WHERE m.msgID = #{msgID}
          AND mr.recipientType = #{recipientType}
          AND mr.recipientId = #{recipientId}
    </select>

    <!-- 根据接收者类型和ID查询未读消息数量 -->
    <select id="getUnreadCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM message m
                 INNER JOIN message_recipient mr ON m.msgID = mr.msgID
        WHERE mr.recipientType = #{recipientType}
          AND mr.recipientId = #{recipientId}
          AND mr.isRead = 0
    </select>

    <!-- 标记消息为已读 -->
    <update id="markAsRead">
        UPDATE message_recipient
        SET isRead = 1, updateTime = NOW()
        WHERE msgID = #{msgID}
          AND recipientType = #{recipientType}
          AND recipientId = #{recipientId}
    </update>

    <!-- 批量标记消息为已读 -->
    <update id="batchMarkAsRead">
        UPDATE message_recipient
        SET isRead = 1, updateTime = NOW()
        WHERE recipientType = #{recipientType}
        AND recipientId = #{recipientId}
        AND msgID IN
        <foreach collection="msgIDs" item="msgID" open="(" separator="," close=")">
            #{msgID}
        </foreach>
    </update>

    <!-- 标记所有消息为已读 -->
    <update id="markAllAsRead">
        UPDATE message_recipient
        SET isRead = 1, updateTime = NOW()
        WHERE recipientType = #{recipientType}
          AND recipientId = #{recipientId}
          AND isRead = 0
    </update>

    <!-- 删除消息 -->
    <delete id="deleteMessage">
        DELETE FROM message_recipient
        WHERE msgID = #{msgID}
          AND recipientType = #{recipientType}
          AND recipientId = #{recipientId}
    </delete>

    <!-- 批量删除消息 -->
    <delete id="batchDeleteMessage">
        DELETE FROM message_recipient
        WHERE recipientType = #{recipientType}
        AND recipientId = #{recipientId}
        AND msgID IN
        <foreach collection="msgIDs" item="msgID" open="(" separator="," close=")">
            #{msgID}
        </foreach>
    </delete>

    <!-- 查询卖家列表 -->
    <select id="getSellerList" resultType="com.sky.vo.RecipientVO">
        SELECT
        id,
        account_name as name,
        phone
        FROM seller
        <where>
            user_role='普通用户'
            <if test="keyword != null and keyword != ''">
                AND (account_name LIKE CONCAT('%', #{keyword}, '%') OR phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 查询用户列表 -->
    <select id="getUserList" resultType="com.sky.vo.RecipientVO">
        SELECT
        id,
        account_name as name,
        phone
        FROM buyer
        <where>
            <if test="keyword != null and keyword != ''">
                AND (account_name LIKE CONCAT('%', #{keyword}, '%') OR phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper> 