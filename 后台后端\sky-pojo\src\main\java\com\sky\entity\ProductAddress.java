package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductAddress {
    private Long id;              // 主键 ID
    private Long buyerId;         // 买家 ID
    private String name;          // 收货人姓名
    private String phoneNumber;   // 手机号
    private String addressDetail; // 地址详情
    private String city;          // 城市
    private String province;      // 省份
    private String postalCode;    // 邮政编码
    private String country;       // 国家
    private Integer isDefault;    // 是否默认地址 (0: 否, 1: 是)
}
