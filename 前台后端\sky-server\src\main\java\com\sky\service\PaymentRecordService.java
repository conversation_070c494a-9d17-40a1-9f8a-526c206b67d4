package com.sky.service;

import com.sky.dto.PaymentRecordPageQueryDTO;
import com.sky.entity.PaymentRecord;
import com.sky.result.PageResult;
import com.sky.vo.PaymentRecordVO;

public interface PaymentRecordService {
    /**
     * 根据id查询支付记录
     * @param id
     * @return
     */
    PaymentRecordVO getPaymentRecord(Long id);

    /**
     * 支付记录分页查询
     * @param paymentRecordPageQueryDTO
     * @return
     */
    PageResult pageQuery(PaymentRecordPageQueryDTO paymentRecordPageQueryDTO);

    /**
     * 保存支付记录
     * @param paymentRecord 支付记录对象
     */
    void save(PaymentRecord paymentRecord);

}
