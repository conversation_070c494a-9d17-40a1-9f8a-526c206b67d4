package com.sky.controller.admin;

import com.sky.constant.MessageConstant;
import com.sky.dto.PaymentAccountQueryDTO;
import com.sky.result.Result;
import com.sky.service.PaymentAccountService;
import com.sky.vo.PageResult;
import com.sky.vo.PaymentAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员端收账账户管理控制器
 */
@RestController
@RequestMapping("/admin/payment-account")
@Api(tags = "管理员端收账账户管理接口")
@Slf4j
public class AdminPaymentAccountController {

    @Autowired
    private PaymentAccountService paymentAccountService;

    /**
     * 分页查询所有收账账户
     */
    @GetMapping("/page")
    @ApiOperation("分页查询所有收账账户")
    public Result<PageResult> pageQuery(PaymentAccountQueryDTO queryDTO) {
        log.info("管理员分页查询收账账户：{}", queryDTO);
        PageResult pageResult = paymentAccountService.adminPageQuery(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询收账账户详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询收账账户详情")
    public Result<PaymentAccountVO> getPaymentAccountById(@PathVariable Long id) {
        log.info("管理员查询收账账户详情，ID：{}", id);
        PaymentAccountVO paymentAccountVO = paymentAccountService.adminGetPaymentAccountById(id);
        return Result.success(paymentAccountVO);
    }

    /**
     * 根据商家ID查询收账账户
     */
    @GetMapping("/seller/{sellerId}")
    @ApiOperation("根据商家ID查询收账账户")
    public Result<PageResult> getPaymentAccountsBySellerId(@PathVariable Long sellerId, PaymentAccountQueryDTO queryDTO) {
        log.info("管理员查询商家收账账户，商家ID：{}，查询条件：{}", sellerId, queryDTO);
        queryDTO.setSellerId(sellerId);
        PageResult pageResult = paymentAccountService.adminPageQuery(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 验证收账账户
     */
    @PutMapping("/{id}/verify")
    @ApiOperation("验证收账账户")
    public Result<String> verifyPaymentAccount(@PathVariable Long id) {
        log.info("管理员验证收账账户，ID：{}", id);
        paymentAccountService.adminVerifyPaymentAccount(id);
        return Result.success(MessageConstant.ACCOUNT_VERIFY_SUCCESS);
    }

    /**
     * 启用收账账户
     */
    @PutMapping("/{id}/enable")
    @ApiOperation("启用收账账户")
    public Result<String> enablePaymentAccount(@PathVariable Long id) {
        log.info("管理员启用收账账户，ID：{}", id);
        paymentAccountService.adminUpdateAccountStatus(id, 1);
        return Result.success(MessageConstant.ACCOUNT_STATUS_UPDATE_SUCCESS);
    }

    /**
     * 禁用收账账户
     */
    @PutMapping("/{id}/disable")
    @ApiOperation("禁用收账账户")
    public Result<String> disablePaymentAccount(@PathVariable Long id) {
        log.info("管理员禁用收账账户，ID：{}", id);
        paymentAccountService.adminUpdateAccountStatus(id, 0);
        return Result.success(MessageConstant.ACCOUNT_STATUS_UPDATE_SUCCESS);
    }

    /**
     * 按账户类型统计
     */
    @GetMapping("/statistics/type")
    @ApiOperation("按账户类型统计")
    public Result<Object> getStatisticsByType() {
        log.info("管理员查询账户类型统计");
        // 这里可以添加统计逻辑
        return Result.success("统计功能待实现");
    }

    /**
     * 按验证状态统计
     */
    @GetMapping("/statistics/verification")
    @ApiOperation("按验证状态统计")
    public Result<Object> getStatisticsByVerification() {
        log.info("管理员查询验证状态统计");
        // 这里可以添加统计逻辑
        return Result.success("统计功能待实现");
    }
}
