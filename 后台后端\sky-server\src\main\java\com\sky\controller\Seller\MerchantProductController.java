package com.sky.controller.Seller;

import com.sky.annotation.PreAuthorize;
import com.sky.entity.MerchantProduct;
import com.sky.result.Result;
import com.sky.service.MerchantProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/merchantProduct")
@Api(tags = "商家产品管理接口")
public class MerchantProductController {
    @Autowired
    private MerchantProductService merchantProductService;

    @GetMapping("/list")
    @ApiOperation("获取所有产品列表")
    public Result<List<MerchantProduct>> findAll() {
        List<MerchantProduct> products = merchantProductService.findAll();
        return Result.success(products);
    }

    @GetMapping("/byProductCode/{productCode}")
    @ApiOperation("根据产品编码查找产品")
    public Result<List<MerchantProduct>> findByProductCode(@PathVariable String productCode) {
        List<MerchantProduct> products = merchantProductService.findByProductCode(productCode);
        return Result.success(products);
    }

    @GetMapping("/byProductTitle/{productTitle}")
    @ApiOperation("根据产品标题查找产品")
    public Result<List<MerchantProduct>> findByProductTitle(@PathVariable String productTitle) {
        List<MerchantProduct> products = merchantProductService.findByProductTitle(productTitle);
        return Result.success(products);
    }

    @GetMapping("/byStatus/{status}")
    @ApiOperation("根据产品状态查找产品")
    public Result<List<MerchantProduct>> findByStatus(@PathVariable String status) {
        List<MerchantProduct> products = merchantProductService.findByStatus(status);
        return Result.success(products);
    }

    @PostMapping("/publish")
    @ApiOperation("发布产品")
    public Result<String> publishProduct(@RequestBody MerchantProduct merchantProduct) {
        merchantProductService.publishProduct(merchantProduct);
        return Result.success("产品发布成功");
    }
}