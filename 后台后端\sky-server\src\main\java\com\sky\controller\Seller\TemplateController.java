package com.sky.controller.Seller;

import com.sky.entity.Template;
import com.sky.result.Result;
import com.sky.service.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/template")
@Api(tags = "模板管理接口")
public class TemplateController {
    @Autowired
    private TemplateService templateService;

    @GetMapping("/list")
    @ApiOperation("获取所有模板列表")
    public Result<List<Template>> findAll() {
        List<Template> templates = templateService.findAll();
        return Result.success(templates);
    }

    @PostMapping("/add")
    @ApiOperation("添加模板")
    public Result<String> addTemplate(@RequestBody Template template) {
        templateService.addTemplate(template);
        return Result.success("模板添加成功");
    }
}