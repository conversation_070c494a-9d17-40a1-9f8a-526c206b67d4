<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.BuyerMapper">
    <insert id="save" parameterType="com.sky.entity.Buyer">
        insert into buyer (id,account_name,password,gender,phone,email,photo_url,account_status,create_time,last_login_time)
        values (#{id},#{accountName},#{password},#{gender},#{phone},#{email},#{photoUrl},#{accountStatus},#{createTime},#{lastLoginTime})
    </insert>
    <update id="update" parameterType="com.sky.entity.Buyer">
        update buyer
        <set>
            <if test="accountName != null">
                account_name = #{accountName},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="gender != null">
                gender = #{gender},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="photoUrl != null">
                photo_url = #{photoUrl},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime}
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
