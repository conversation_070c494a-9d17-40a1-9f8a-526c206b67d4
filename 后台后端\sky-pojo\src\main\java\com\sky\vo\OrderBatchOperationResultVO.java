package com.sky.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单批量操作结果视图对象
 */
@Data
public class OrderBatchOperationResultVO implements Serializable {
    
    private Integer totalCount;         // 总数量
    private Integer successCount;       // 成功数量
    private Integer failCount;          // 失败数量
    private List<String> successOrders; // 成功的订单号列表
    private List<String> failOrders;    // 失败的订单号列表
    private List<String> failReasons;   // 失败原因列表
    private String downloadUrl;         // 导出文件下载地址（导出操作时使用）
}
