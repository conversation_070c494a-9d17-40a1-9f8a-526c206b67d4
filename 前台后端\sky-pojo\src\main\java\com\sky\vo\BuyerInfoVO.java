package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BuyerInfoVO implements Serializable {
    private Long id;


    private String accountName;


    private String gender;
    private String photoUrl;

    private String phone;


    private String email;


    private String accountStatus;


    private LocalDateTime createTime;


    private Date lastLoginTime;
}
