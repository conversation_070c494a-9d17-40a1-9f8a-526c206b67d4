package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageListVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer total;          // 总消息数
    private Integer unreadCount;    // 未读消息数
    private List<MessageItemVO> list; // 消息列表
} 