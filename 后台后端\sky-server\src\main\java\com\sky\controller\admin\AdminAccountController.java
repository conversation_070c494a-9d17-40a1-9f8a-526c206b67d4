package com.sky.controller.admin;

import com.sky.constant.MessageConstant;
import com.sky.dto.*;
import com.sky.entity.AdminAccount;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.AdminAccountService;
import com.sky.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/admin/accounts")
@Slf4j
@Api(tags = "管理员账号管理")
public class AdminAccountController {
    @Autowired
    private AdminAccountService adminAccountService;

    /**
     * 获取管理员账号列表
     */
    @GetMapping
    @ApiOperation("获取管理员账号列表")
    public Result<PageResult<AdminAccountVO>> getAdminAccountList(AdminAccountPageQueryDTO query) {
        PageResult<AdminAccountVO> adminAccountList = adminAccountService.getAdminAccountList(query);
        return Result.success("success",adminAccountList);
    }

    /**
     * 获取单个管理员信息
     */
    @GetMapping("/{id}")
    @ApiOperation("获取单个管理员信息")
    public Result<AdminAccount> getAdminById(@PathVariable Long id) {
        AdminAccount account = adminAccountService.getAdminById(id);
        return Result.success("success",account);
    }

    /**
     * 创建管理员账号
     */
    @PostMapping
    @ApiOperation("创建管理员账号")
    public Result<AdminAccountCreateVO> createAdminAccount(@RequestBody AdminAccountDTO dto) {
        AdminAccountCreateVO vo = adminAccountService.createAdminAccount(dto);
        return Result.success(MessageConstant.ADMIN_CREATE_SUCCESS,vo);
    }

    /**
     * 更新管理员账号信息
     */
    @PutMapping("/{id}")
    @ApiOperation("更新管理员账号")
    public Result<AdminAccountUpdateVO> updateAdminAccount(
            @PathVariable Long id,
            @RequestBody AdminAccountUpdateDTO dto) {
        dto.setId(id); // 确保 ID 正确传入
        AdminAccountUpdateVO vo = adminAccountService.updateAdminAccount(dto);
        return Result.success(MessageConstant.ADMIN_UPDATE_SUCCESS,vo);
    }

    /**
     * 删除管理员账号
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除管理员账号")
    public Result deleteAdminAccount(@PathVariable Long id) {
        adminAccountService.deleteAdminAccountById(id);
        return Result.success(MessageConstant.ADMIN_DELETE_SUCCESS,true);
    }

    /**
     * 更新管理员权限
     */
    @PutMapping("/{id}/permissions")
    @ApiOperation("更新管理员权限")
    public Result<AdminPermissionUpdateVO> updatePermissions(
            @PathVariable Long id,
            @RequestBody AdminPermissionUpdateDTO adminPermissionUpdateDTO) {
        AdminPermissionUpdateVO vo = adminAccountService.updatePermissions(id,adminPermissionUpdateDTO.getPermissions());

        return Result.success(MessageConstant.PERMISSION_UPDATE_SUCCESS,vo);
    }
    /**
     * 批量删除管理员账号
     */
    @PostMapping("/batch-delete")
    @ApiOperation("批量删除管理员账号")
    public Result<BatchDeleteResultVO> batchDeleteAdminAccounts(@RequestBody BatchDeleteDTO dto) {
        return Result.success(MessageConstant.BATCH_DELETE_SUCCESS,adminAccountService.batchDelete(dto.getIds()));
    }

    /**
     * 批量更新管理员账号状态
     */
    @PostMapping("/batch-update-status")
    @ApiOperation("批量更新管理员账号状态")
    public Result<BatchUpdateStatusResultVO> batchUpdateStatus(@RequestBody BatchUpdateStatusDTO dto) {
        return Result.success(MessageConstant.BATCH_UPDATE_STATUS_SUCCESS,adminAccountService.batchUpdateStatus(dto.getIds(), dto.getStatus()));
    }

    /**
     * 重置管理员密码
     */
    @PostMapping("/{id}/reset-password")
    @ApiOperation("重置管理员密码")
    public Result resetPassword(@PathVariable Long id, @RequestParam String password) {
        log.info("重置管理员密码：id={}, password={}", id, password);
        adminAccountService.resetPassword(id, password);
        return Result.success(MessageConstant.PASSWORD_RESET_SUCCESS);
    }

    /**
     * 获取管理员操作日志
     */
    @GetMapping("/{id}/logs")
    @ApiOperation("获取管理员操作日志")
    public Result<PageResult<AdminLogVO>> getAdminLogs(
            @PathVariable Long id,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String actionType) {

        AdminLogQueryDTO dto = new AdminLogQueryDTO();
        dto.setAdminId(id);
        dto.setPage(page == null ? 1 : page);
        dto.setPageSize(pageSize == null ? 10 : pageSize);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setActionType(actionType);

        PageResult<AdminLogVO> logPage = adminAccountService.getAdminLogs(dto);
        return Result.success("success",logPage);
    }
}
