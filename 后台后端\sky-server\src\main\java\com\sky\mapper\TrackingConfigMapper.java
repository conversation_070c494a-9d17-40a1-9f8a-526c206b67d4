package com.sky.mapper;

import com.sky.entity.TrackingConfig;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 17TRACK配置Mapper
 */
@Mapper
public interface TrackingConfigMapper {

    /**
     * 根据配置键查询
     */
    @Select("SELECT * FROM tracking_config WHERE config_key = #{configKey}")
    TrackingConfig getByKey(String configKey);

    /**
     * 查询所有配置
     */
    @Select("SELECT * FROM tracking_config ORDER BY config_key")
    List<TrackingConfig> getAll();

    /**
     * 插入配置
     */
    @Insert("INSERT INTO tracking_config (config_key, config_value, config_desc, config_type, " +
            "is_encrypted, create_time, update_time) " +
            "VALUES (#{configKey}, #{configValue}, #{configDesc}, #{configType}, " +
            "#{isEncrypted}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(TrackingConfig config);

    /**
     * 更新配置
     */
    @Update("UPDATE tracking_config SET config_value = #{configValue}, config_desc = #{configDesc}, " +
            "config_type = #{configType}, is_encrypted = #{isEncrypted}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    void update(TrackingConfig config);

    /**
     * 根据配置键删除
     */
    @Delete("DELETE FROM tracking_config WHERE config_key = #{configKey}")
    void deleteByKey(String configKey);
}
