package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单详情视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "订单详情视图对象")
public class OrderDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("订单ID")
    private Long orderId;
    
    @ApiModelProperty("商品ID")
    private Long productId;
    
    @ApiModelProperty("商品名称")
    private String productName;
    
    @ApiModelProperty("商品图片")
    private String productImage;
    
    @ApiModelProperty("商品规格")
    private String productSpec;
    
    @ApiModelProperty("商品数量")
    private Integer quantity;
    
    @ApiModelProperty("商品单价")
    private BigDecimal unitPrice;
    
    @ApiModelProperty("折扣金额")
    private BigDecimal discount;
    
    @ApiModelProperty("小计金额")
    private BigDecimal subtotal;
    
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
} 