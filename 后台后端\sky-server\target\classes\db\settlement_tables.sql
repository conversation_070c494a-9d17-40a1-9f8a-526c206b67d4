-- ========================================
-- 回款业务相关数据库表设计
-- ========================================

-- 1. 订单回款信息表
CREATE TABLE order_settlement_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    
    -- 订单信息
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_number VARCHAR(50) NOT NULL COMMENT '订单号',
    
    -- 商家信息
    seller_id BIGINT NOT NULL COMMENT '商家ID',
    seller_name VARCHAR(100) NOT NULL COMMENT '商家名称',
    
    -- 时间信息
    pay_time DATETIME NOT NULL COMMENT '支付时间',
    billing_date DATE NOT NULL COMMENT '账单日期(支付时间+14天)',
    billing_cycle VARCHAR(7) NOT NULL COMMENT '账单周期(格式:2025-01)',
    settlement_date DATE NOT NULL COMMENT '回款日期',
    
    -- 金额信息
    order_amount DECIMAL(12,2) NOT NULL COMMENT '订单金额',
    settlement_amount DECIMAL(12,2) NOT NULL COMMENT '回款金额(扣除平台佣金)',
    
    -- 状态信息
    settlement_status TINYINT NOT NULL DEFAULT 0 COMMENT '回款状态(0-未到期,1-待回款,2-已回款)',
    settlement_time DATETIME NULL COMMENT '实际回款时间',
    remark VARCHAR(500) NULL COMMENT '备注信息',
    
    -- 系统字段
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_order_id (order_id),
    INDEX idx_seller_id (seller_id),
    INDEX idx_settlement_status (settlement_status),
    INDEX idx_billing_cycle (billing_cycle),
    INDEX idx_settlement_date (settlement_date),
    INDEX idx_order_number (order_number),
    INDEX idx_pay_time (pay_time),
    INDEX idx_create_time (create_time)
) COMMENT '订单回款信息表';

-- 2. 回款配置表
CREATE TABLE settlement_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    
    -- 配置信息
    config_key VARCHAR(50) NOT NULL UNIQUE COMMENT '配置键',
    config_value VARCHAR(200) NOT NULL COMMENT '配置值',
    config_desc VARCHAR(200) NULL COMMENT '配置描述',
    
    -- 系统字段
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_config_key (config_key)
) COMMENT '回款配置表';

-- ========================================
-- 初始化配置数据
-- ========================================

-- 插入默认配置
INSERT INTO settlement_config (config_key, config_value, config_desc) VALUES
('commission_rate', '0.05', '平台佣金比例(默认5%)'),
('billing_days', '14', '账单天数(T+14)'),
('settlement_day', '10', '回款基准日(每月10号后)'),
('working_days', '1,2,3,4,5', '工作日(1-7表示周一到周日)');

-- ========================================
-- 为现有订单初始化回款数据的SQL
-- ========================================

-- 注意：这个SQL需要根据实际的订单表结构调整
-- 以下是示例SQL，实际使用时需要根据具体情况修改

/*
INSERT INTO order_settlement_info (
    order_id, order_number, seller_id, seller_name, pay_time,
    billing_date, billing_cycle, settlement_date, order_amount, settlement_amount,
    settlement_status, create_time, update_time
)
SELECT 
    o.id as order_id,
    o.number as order_number,
    COALESCE(p.creator_user_id, 1) as seller_id,
    COALESCE(s.account_name, '默认商家') as seller_name,
    o.pay_time,
    DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY) as billing_date,
    DATE_FORMAT(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY), '%Y-%m') as billing_cycle,
    -- 计算回款日期：账单日期下个月10号后的第一个工作日
    CASE 
        WHEN DAYOFWEEK(DATE_ADD(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY), INTERVAL 1 MONTH - DAY(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY)) + 10 DAY)) IN (2,3,4,5,6) 
        THEN DATE_ADD(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY), INTERVAL 1 MONTH - DAY(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY)) + 10 DAY)
        WHEN DAYOFWEEK(DATE_ADD(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY), INTERVAL 1 MONTH - DAY(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY)) + 10 DAY)) = 1
        THEN DATE_ADD(DATE_ADD(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY), INTERVAL 1 MONTH - DAY(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY)) + 10 DAY), INTERVAL 1 DAY)
        ELSE DATE_ADD(DATE_ADD(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY), INTERVAL 1 MONTH - DAY(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY)) + 10 DAY), INTERVAL 2 DAY)
    END as settlement_date,
    o.amount as order_amount,
    o.amount * 0.95 as settlement_amount,  -- 扣除5%平台佣金
    CASE 
        WHEN CURDATE() >= DATE_ADD(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY), INTERVAL 1 MONTH - DAY(DATE_ADD(DATE(o.pay_time), INTERVAL 14 DAY)) + 10 DAY)
        THEN 1  -- 待回款
        ELSE 0  -- 未到期
    END as settlement_status,
    NOW() as create_time,
    NOW() as update_time
FROM orders o
LEFT JOIN order_detail od ON o.id = od.order_id
LEFT JOIN pms_product p ON od.product_id = p.id
LEFT JOIN seller s ON p.creator_user_id = s.id
WHERE o.status = 5  -- 已完成订单
  AND o.pay_time IS NOT NULL  -- 已支付
  AND o.id NOT IN (SELECT order_id FROM order_settlement_info)  -- 避免重复插入
GROUP BY o.id;
*/

-- ========================================
-- 常用查询SQL示例
-- ========================================

-- 1. 查询商家回款汇总信息
/*
SELECT
    seller_id,
    seller_name,
    COUNT(*) as total_orders,
    SUM(order_amount) as total_order_amount,
    SUM(settlement_amount) as total_settlement_amount,
    SUM(CASE WHEN settlement_status = 0 THEN 1 ELSE 0 END) as not_due_orders,
    SUM(CASE WHEN settlement_status = 0 THEN settlement_amount ELSE 0 END) as not_due_amount,
    SUM(CASE WHEN settlement_status = 1 THEN 1 ELSE 0 END) as pending_orders,
    SUM(CASE WHEN settlement_status = 1 THEN settlement_amount ELSE 0 END) as pending_amount,
    SUM(CASE WHEN settlement_status = 2 THEN 1 ELSE 0 END) as completed_orders,
    SUM(CASE WHEN settlement_status = 2 THEN settlement_amount ELSE 0 END) as completed_amount
FROM order_settlement_info
WHERE seller_id = ?
GROUP BY seller_id, seller_name;
*/

-- 2. 查询待回款订单
/*
SELECT *,
    '待回款' as settlement_status_desc,
    1 as is_due,
    DATEDIFF(settlement_date, CURDATE()) as days_to_settlement
FROM order_settlement_info
WHERE settlement_status = 1
  AND CURDATE() >= settlement_date
ORDER BY settlement_date ASC;
*/

-- 3. 分页查询回款信息
/*
SELECT *,
    CASE
        WHEN settlement_status = 0 THEN '未到期'
        WHEN settlement_status = 1 THEN '待回款'
        WHEN settlement_status = 2 THEN '已回款'
        ELSE '未知'
    END as settlement_status_desc,
    CASE
        WHEN CURDATE() >= settlement_date THEN 1
        ELSE 0
    END as is_due,
    DATEDIFF(settlement_date, CURDATE()) as days_to_settlement
FROM order_settlement_info
WHERE seller_id = ?
ORDER BY settlement_date DESC, create_time DESC
LIMIT ?, ?;
*/

-- 4. 更新回款状态（定时任务）
/*
UPDATE order_settlement_info
SET settlement_status = 1, update_time = NOW()
WHERE settlement_status = 0
  AND CURDATE() >= settlement_date;
*/

-- 5. 标记回款完成
/*
UPDATE order_settlement_info
SET settlement_status = 2,
    settlement_time = NOW(),
    remark = ?,
    update_time = NOW()
WHERE id = ?;
*/

-- ========================================
-- 用户收账账户信息表设计
-- ========================================

-- 3. 用户收账账户信息表
CREATE TABLE seller_payment_account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',

    -- 关联信息
    seller_id BIGINT NOT NULL COMMENT '商家ID',

    -- 账户基本信息
    account_type TINYINT NOT NULL COMMENT '账户类型(1-银行卡,2-支付宝,3-微信,4-其他)',
    account_name VARCHAR(100) NOT NULL COMMENT '账户名称/持卡人姓名',
    account_number VARCHAR(50) NOT NULL COMMENT '账户号码/卡号',

    -- 银行卡专用字段
    bank_name VARCHAR(100) NULL COMMENT '银行名称',
    bank_code VARCHAR(20) NULL COMMENT '银行代码',
    branch_name VARCHAR(200) NULL COMMENT '开户支行',

    -- 第三方支付专用字段
    platform_name VARCHAR(50) NULL COMMENT '平台名称(支付宝/微信等)',
    platform_account VARCHAR(100) NULL COMMENT '平台账号(手机号/邮箱等)',

    -- 状态信息
    is_default TINYINT NOT NULL DEFAULT 0 COMMENT '是否默认账户(0-否,1-是)',
    account_status TINYINT NOT NULL DEFAULT 1 COMMENT '账户状态(0-禁用,1-启用)',
    verification_status TINYINT NOT NULL DEFAULT 0 COMMENT '验证状态(0-未验证,1-已验证)',

    -- 附加信息
    id_card_number VARCHAR(18) NULL COMMENT '身份证号码',
    phone VARCHAR(20) NULL COMMENT '预留手机号',
    remark VARCHAR(500) NULL COMMENT '备注信息',

    -- 系统字段
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_seller_id (seller_id),
    INDEX idx_account_type (account_type),
    INDEX idx_account_status (account_status),
    INDEX idx_is_default (is_default),
    INDEX idx_verification_status (verification_status),
    UNIQUE KEY uk_seller_account (seller_id, account_number, account_type),

    -- 外键约束
    FOREIGN KEY (seller_id) REFERENCES seller(id) ON DELETE CASCADE
) COMMENT '商家收账账户信息表';

-- ========================================
-- 收账账户相关查询SQL示例
-- ========================================

-- 1. 查询商家的所有收账账户
/*
SELECT
    id, seller_id, account_type, account_name, account_number,
    bank_name, branch_name, platform_name, platform_account,
    is_default, account_status, verification_status,
    CASE account_type
        WHEN 1 THEN '银行卡'
        WHEN 2 THEN '支付宝'
        WHEN 3 THEN '微信'
        WHEN 4 THEN '其他'
        ELSE '未知'
    END as account_type_desc,
    CASE account_status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        ELSE '未知'
    END as account_status_desc,
    CASE verification_status
        WHEN 0 THEN '未验证'
        WHEN 1 THEN '已验证'
        ELSE '未知'
    END as verification_status_desc,
    create_time, update_time
FROM seller_payment_account
WHERE seller_id = ?
ORDER BY is_default DESC, create_time DESC;
*/

-- 2. 查询商家的默认收账账户
/*
SELECT * FROM seller_payment_account
WHERE seller_id = ? AND is_default = 1 AND account_status = 1
LIMIT 1;
*/

-- 3. 设置默认账户（先清除其他默认，再设置新默认）
/*
-- 清除该商家的所有默认账户
UPDATE seller_payment_account
SET is_default = 0, update_time = NOW()
WHERE seller_id = ?;

-- 设置新的默认账户
UPDATE seller_payment_account
SET is_default = 1, update_time = NOW()
WHERE id = ? AND seller_id = ?;
*/

-- 4. 验证账户
/*
UPDATE seller_payment_account
SET verification_status = 1, update_time = NOW()
WHERE id = ? AND seller_id = ?;
*/

-- 5. 启用/禁用账户
/*
UPDATE seller_payment_account
SET account_status = ?, update_time = NOW()
WHERE id = ? AND seller_id = ?;
*/
