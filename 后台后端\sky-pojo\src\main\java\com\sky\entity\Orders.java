package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Orders {

    // 订单状态常量 - 与前台后端保持一致
    public static final Integer STATUS_PENDING_PAYMENT = 1; // 待付款
    public static final Integer STATUS_PAID = 2;            // 已付款
    public static final Integer STATUS_PROCESSING = 3;      // 处理中
    public static final Integer STATUS_SHIPPED = 4;         // 已发货
    public static final Integer STATUS_COMPLETED = 5;       // 已完成
    public static final Integer STATUS_CANCELLED = 6;       // 已取消
    public static final Integer STATUS_REFUNDED = 7;        // 已退款

    // 支付方式常量
    public static final Integer PAY_METHOD_WECHAT = 1;      // 微信支付
    public static final Integer PAY_METHOD_ALIPAY = 2;      // 支付宝
    public static final Integer PAY_METHOD_BANK_CARD = 3;   // 银行卡

    private Long id;                        // 订单ID
    private String number;                  // 订单号
    private Long buyerId;                   // 买家ID
    private Long addressId;                 // 收货地址ID
    private Integer shippingMethodId;       // 配送方式ID
    private Integer status;                 // 订单状态：1-待支付，2-已支付，3-已取消，4-已发货，5-已完成，6-已关闭
    private BigDecimal amount;              // 订单总金额
    private LocalDateTime orderTime;        // 下单时间
    private LocalDateTime payTime;          // 支付时间
    private LocalDateTime checkoutTime;     // 结算时间
    private LocalDateTime shipTime;         // 发货时间
    private LocalDateTime completeTime;     // 完成时间
    private LocalDateTime cancelTime;       // 取消时间
    private Integer payMethod;              // 支付方式：1-微信支付，2-支付宝，3-银行卡
    private String paymentTransactionId;    // 支付交易ID
    private String orderRemark;             // 订单备注
    private String cancelReason;            // 取消原因
    private String rejectionReason;         // 拒绝原因
    private String cancelRequest;           // 取消请求
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间

    // 物流相关字段
    private String trackingNumber;          // 物流单号
    private String courierCode;             // 快递公司代码
    private String courierName;             // 快递公司名称

    // 退款相关字段
    private Integer refundStatus;           // 退款状态：0-无退款，1-退款申请中，2-部分退款，3-全额退款
    private BigDecimal refundAmount;        // 退款金额
    private LocalDateTime refundTime;       // 退款时间
}
