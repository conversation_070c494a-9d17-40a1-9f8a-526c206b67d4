package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 物流跟踪列表VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "物流跟踪列表VO")
public class TrackingListVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总数量")
    private Integer total;

    @ApiModelProperty("当前页")
    private Integer page;

    @ApiModelProperty("每页大小")
    private Integer pageSize;

    @ApiModelProperty("总页数")
    private Integer totalPages;

    @ApiModelProperty("物流跟踪列表")
    private List<TrackingDetailVO> trackingList;

    /**
     * 物流跟踪详情VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "物流跟踪详情VO")
    public static class TrackingDetailVO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("物流单号")
        private String trackingNumber;

        @ApiModelProperty("运输商代码")
        private Integer carrierCode;

        @ApiModelProperty("运输商名称")
        private String carrierName;

        @ApiModelProperty("物流状态")
        private String status;

        @ApiModelProperty("物流状态描述")
        private String statusDescription;

        @ApiModelProperty("订单号")
        private String orderNumber;

        @ApiModelProperty("标签")
        private String tag;

        @ApiModelProperty("备注")
        private String remark;

        @ApiModelProperty("创建时间")
        private String createTime;

        @ApiModelProperty("更新时间")
        private String updateTime;

        @ApiModelProperty("最后跟踪时间")
        private String lastTrackTime;

        @ApiModelProperty("目的地国家")
        private String destinationCountry;

        @ApiModelProperty("原产地国家")
        private String originCountry;

        @ApiModelProperty("客户邮箱")
        private String customerEmail;

        @ApiModelProperty("收件人姓名")
        private String recipientName;

        @ApiModelProperty("收件人地址")
        private String recipientAddress;

        @ApiModelProperty("是否已停止跟踪")
        private Boolean stopped;

        @ApiModelProperty("物流轨迹列表")
        private List<TrackingEventVO> trackingEvents;
    }

    /**
     * 物流轨迹事件VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "物流轨迹事件VO")
    public static class TrackingEventVO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("事件时间")
        private String eventTime;

        @ApiModelProperty("事件描述")
        private String eventDescription;

        @ApiModelProperty("事件地点")
        private String eventLocation;

        @ApiModelProperty("事件状态")
        private String eventStatus;

        @ApiModelProperty("事件详情")
        private String eventDetails;
    }
}
