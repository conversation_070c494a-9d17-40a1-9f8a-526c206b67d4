<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="8.218.65.151">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>exact</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||user||FILE|G
ry_mall|schema||user||ALTER|G
ry_mall|schema||user||ALTER ROUTINE|G
ry_mall|schema||user||CREATE|G
ry_mall|schema||user||CREATE ROUTINE|G
ry_mall|schema||user||CREATE TEMPORARY TABLES|G
ry_mall|schema||user||CREATE VIEW|G
ry_mall|schema||user||DELETE|G
ry_mall|schema||user||DROP|G
ry_mall|schema||user||EVENT|G
ry_mall|schema||user||EXECUTE|G
ry_mall|schema||user||INDEX|G
ry_mall|schema||user||INSERT|G
ry_mall|schema||user||LOCK TABLES|G
ry_mall|schema||user||REFERENCES|G
ry_mall|schema||user||SELECT|G
ry_mall|schema||user||SHOW VIEW|G
ry_mall|schema||user||TRIGGER|G
ry_mall|schema||user||UPDATE|G</Grants>
      <ServerVersion>5.7.44</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="224" parent="1" name="information_schema">
      <LastIntrospectionLocalTimestamp>2025-07-29.08:12:19</LastIntrospectionLocalTimestamp>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="ry_mall">
      <LastIntrospectionLocalTimestamp>2025-07-29.08:12:26</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="226" parent="1" name="user"/>
    <table id="227" parent="224" name="CHARACTER_SETS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
43690</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="228" parent="224" name="COLLATIONS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
72628</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="229" parent="224" name="COLLATION_CHARACTER_SET_APPLICABILITY">
      <Engine>MEMORY</Engine>
      <Options>max_rows
86037</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="230" parent="224" name="COLUMNS">
      <Engine>InnoDB</Engine>
      <Options>max_rows
11156</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="231" parent="224" name="COLUMN_PRIVILEGES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
6540</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="232" parent="224" name="ENGINES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
34239</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="233" parent="224" name="EVENTS">
      <Engine>InnoDB</Engine>
      <Options>max_rows
2473</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="234" parent="224" name="FILES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
1159</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="235" parent="224" name="GLOBAL_STATUS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
5133</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="236" parent="224" name="GLOBAL_VARIABLES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
5133</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="237" parent="224" name="INNODB_BUFFER_PAGE">
      <Engine>MEMORY</Engine>
      <Options>max_rows
2448</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="238" parent="224" name="INNODB_BUFFER_PAGE_LRU">
      <Engine>MEMORY</Engine>
      <Options>max_rows
2515</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="239" parent="224" name="INNODB_BUFFER_POOL_STATS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
65280</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="240" parent="224" name="INNODB_CMP">
      <Engine>MEMORY</Engine>
      <Options>max_rows
671088</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="241" parent="224" name="INNODB_CMPMEM">
      <Engine>MEMORY</Engine>
      <Options>max_rows
578524</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="242" parent="224" name="INNODB_CMPMEM_RESET">
      <Engine>MEMORY</Engine>
      <Options>max_rows
578524</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="243" parent="224" name="INNODB_CMP_PER_INDEX">
      <Engine>MEMORY</Engine>
      <Options>max_rows
9559</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="244" parent="224" name="INNODB_CMP_PER_INDEX_RESET">
      <Engine>MEMORY</Engine>
      <Options>max_rows
9559</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="245" parent="224" name="INNODB_CMP_RESET">
      <Engine>MEMORY</Engine>
      <Options>max_rows
671088</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="246" parent="224" name="INNODB_FT_BEING_DELETED">
      <Engine>MEMORY</Engine>
      <Options>max_rows
1864135</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="247" parent="224" name="INNODB_FT_CONFIG">
      <Engine>MEMORY</Engine>
      <Options>max_rows
14425</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="248" parent="224" name="INNODB_FT_DEFAULT_STOPWORD">
      <Engine>MEMORY</Engine>
      <Options>max_rows
299593</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="249" parent="224" name="INNODB_FT_DELETED">
      <Engine>MEMORY</Engine>
      <Options>max_rows
1864135</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="250" parent="224" name="INNODB_FT_INDEX_CACHE">
      <Engine>MEMORY</Engine>
      <Options>max_rows
15917</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="251" parent="224" name="INNODB_FT_INDEX_TABLE">
      <Engine>MEMORY</Engine>
      <Options>max_rows
15917</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="252" parent="224" name="INNODB_LOCKS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
536</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="253" parent="224" name="INNODB_LOCK_WAITS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
28008</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="254" parent="224" name="INNODB_METRICS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
5603</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="255" parent="224" name="INNODB_SYS_COLUMNS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
27503</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="256" parent="224" name="INNODB_SYS_DATAFILES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
1397</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="257" parent="224" name="INNODB_SYS_FIELDS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
28244</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="258" parent="224" name="INNODB_SYS_FOREIGN">
      <Engine>MEMORY</Engine>
      <Options>max_rows
9576</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="259" parent="224" name="INNODB_SYS_FOREIGN_COLS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
9597</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="260" parent="224" name="INNODB_SYS_INDEXES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
27147</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="261" parent="224" name="INNODB_SYS_TABLES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
8023</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="262" parent="224" name="INNODB_SYS_TABLESPACES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
7865</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="263" parent="224" name="INNODB_SYS_TABLESTATS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
13808</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="264" parent="224" name="INNODB_SYS_VIRTUAL">
      <Engine>MEMORY</Engine>
      <Options>max_rows
986895</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="265" parent="224" name="INNODB_TEMP_TABLE_INFO">
      <Engine>MEMORY</Engine>
      <Options>max_rows
16594</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="266" parent="224" name="INNODB_TRX">
      <Engine>MEMORY</Engine>
      <Options>max_rows
3698</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="267" parent="224" name="KEY_COLUMN_USAGE">
      <Engine>MEMORY</Engine>
      <Options>max_rows
3618</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="268" parent="224" name="OPTIMIZER_TRACE">
      <Engine>InnoDB</Engine>
      <Options>max_rows
2314098</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="269" parent="224" name="PARAMETERS">
      <Engine>InnoDB</Engine>
      <Options>max_rows
24087</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="270" parent="224" name="PARTITIONS">
      <Engine>InnoDB</Engine>
      <Options>max_rows
22384</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="271" parent="224" name="PLUGINS">
      <Engine>InnoDB</Engine>
      <Options>max_rows
45313</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="272" parent="224" name="PROCESSLIST">
      <Engine>InnoDB</Engine>
      <Options>max_rows
89478</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="273" parent="224" name="PROFILING">
      <Engine>MEMORY</Engine>
      <Options>max_rows
54471</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="274" parent="224" name="REFERENTIAL_CONSTRAINTS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
3485</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="275" parent="224" name="ROUTINES">
      <Engine>InnoDB</Engine>
      <Options>max_rows
2329</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="276" parent="224" name="SCHEMATA">
      <Engine>MEMORY</Engine>
      <Options>max_rows
4843</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="277" parent="224" name="SCHEMA_PRIVILEGES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
7699</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="278" parent="224" name="SESSION_STATUS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
5133</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="279" parent="224" name="SESSION_VARIABLES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
5133</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="280" parent="224" name="STATISTICS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
2916</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="281" parent="224" name="TABLES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
1777</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="282" parent="224" name="TABLESPACES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
2413</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="283" parent="224" name="TABLE_CONSTRAINTS">
      <Engine>MEMORY</Engine>
      <Options>max_rows
6700</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="284" parent="224" name="TABLE_PRIVILEGES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
7073</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="285" parent="224" name="TRIGGERS">
      <Engine>InnoDB</Engine>
      <Options>max_rows
2274</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="286" parent="224" name="USER_PRIVILEGES">
      <Engine>MEMORY</Engine>
      <Options>max_rows
8447</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="287" parent="224" name="VIEWS">
      <Engine>InnoDB</Engine>
      <Options>max_rows
27191</Options>
      <System>1</System>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="288" parent="225" name="Manager">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="289" parent="225" name="address">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="290" parent="225" name="address_book">
      <Comment>收货地址表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="291" parent="225" name="admin_account">
      <Comment>平台管理员账号表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="292" parent="225" name="admin_account_log">
      <Comment>管理员操作日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="293" parent="225" name="buyer">
      <Comment>买家表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="294" parent="225" name="category">
      <Comment>商品分类表（支持三级结构）</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="295" parent="225" name="commission_leader_team_members">
      <Comment>团长团队成员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="296" parent="225" name="commission_leaders">
      <Comment>团长信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="297" parent="225" name="commission_record">
      <Comment>佣金记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="298" parent="225" name="commission_settings">
      <Comment>佣金设置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="299" parent="225" name="customer_service_message">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="300" parent="225" name="gen_table">
      <Comment>代码生成业务表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="301" parent="225" name="gen_table_column">
      <Comment>代码生成业务表字段</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="302" parent="225" name="logistics">
      <Comment>物流信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="303" parent="225" name="logistics_info">
      <Comment>物流信息</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="304" parent="225" name="logistics_log">
      <Comment>物流状态变更日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="305" parent="225" name="logistics_trace_detail">
      <Comment>物流轨迹详情表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="306" parent="225" name="merchant">
      <Comment>商家信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="307" parent="225" name="merchant_order">
      <Comment>商家订单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="308" parent="225" name="merchant_product">
      <Comment>商家产品信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="309" parent="225" name="message">
      <Comment>站内信表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="310" parent="225" name="message_recipient">
      <Comment>消息接收者关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="311" parent="225" name="order_address_info">
      <Comment>订单地址信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="312" parent="225" name="order_detail">
      <Comment>订单详情表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="313" parent="225" name="order_log">
      <Comment>订单操作日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="314" parent="225" name="order_logistics_tracking">
      <Comment>订单物流跟踪表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="315" parent="225" name="order_payment_detail">
      <Comment>订单支付详情表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="316" parent="225" name="order_settlement_info">
      <Comment>订单回款信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="317" parent="225" name="order_status_log">
      <Comment>订单状态变更日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="318" parent="225" name="orders">
      <Comment>订单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="319" parent="225" name="payment_method">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="320" parent="225" name="payment_record">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="321" parent="225" name="pms_brand">
      <Comment>品牌管理</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="322" parent="225" name="pms_product">
      <Comment>商品信息</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="323" parent="225" name="pms_product_category">
      <Comment>商品分类</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="324" parent="225" name="pms_product_snapshot">
      <Comment>商品信息</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="325" parent="225" name="pms_sku">
      <Comment>sku信息</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="326" parent="225" name="pms_sku_snapshot">
      <Comment>sku信息</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="327" parent="225" name="product_address">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="328" parent="225" name="product_category">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="329" parent="225" name="product_favorite">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="330" parent="225" name="product_review">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="331" parent="225" name="refund_application">
      <Comment>退款申请表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="332" parent="225" name="refund_approval_record">
      <Comment>退款审核记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="333" parent="225" name="refund_log">
      <Comment>退款状态变更日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="334" parent="225" name="return_record">
      <Comment>退货记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="335" parent="225" name="sales_trend">
      <Comment>销售趋势表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="336" parent="225" name="seller">
      <Comment>卖家信息表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="337" parent="225" name="seller_payment_account">
      <Comment>商家收账账户信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="338" parent="225" name="seller_permission">
      <Comment>合并权限表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="339" parent="225" name="seller_role">
      <Comment>卖家-角色关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="340" parent="225" name="settlement_config">
      <Comment>回款配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="341" parent="225" name="shipping_method">
      <Comment>配送方式表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="342" parent="225" name="shop_info">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="343" parent="225" name="shopping_cart">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="344" parent="225" name="store">
      <Comment>店铺</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="345" parent="225" name="sys_permission">
      <Comment>系统权限表（含路由/API路径）</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="346" parent="225" name="sys_role">
      <Comment>系统角色表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="347" parent="225" name="sys_role_permission">
      <Comment>角色权限关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="348" parent="225" name="team_leader">
      <Comment>团长表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="349" parent="225" name="team_member">
      <Comment>团队成员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="350" parent="225" name="tracking_record">
      <Comment>17TRACK物流跟踪记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="351" parent="225" name="us_city_dict">
      <Comment>存储美国城市与所属州映射关系</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="352" parent="225" name="user_address">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="353" parent="225" name="view_record">
      <Comment>浏览记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="354" parent="225" name="wechat_refund_record">
      <Comment>微信退款记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <column id="355" parent="227" name="CHARACTER_SET_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="356" parent="227" name="DEFAULT_COLLATE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="357" parent="227" name="DESCRIPTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="358" parent="227" name="MAXLEN">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(3)|0s</StoredType>
    </column>
    <column id="359" parent="228" name="COLLATION_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="360" parent="228" name="CHARACTER_SET_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="361" parent="228" name="ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(11)|0s</StoredType>
    </column>
    <column id="362" parent="228" name="IS_DEFAULT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="363" parent="228" name="IS_COMPILED">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="364" parent="228" name="SORTLEN">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(3)|0s</StoredType>
    </column>
    <column id="365" parent="229" name="COLLATION_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="366" parent="229" name="CHARACTER_SET_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="367" parent="230" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="368" parent="230" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="369" parent="230" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="370" parent="230" name="COLUMN_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="371" parent="230" name="ORDINAL_POSITION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="372" parent="230" name="COLUMN_DEFAULT">
      <Position>6</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="373" parent="230" name="IS_NULLABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="374" parent="230" name="DATA_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="375" parent="230" name="CHARACTER_MAXIMUM_LENGTH">
      <Position>9</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="376" parent="230" name="CHARACTER_OCTET_LENGTH">
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="377" parent="230" name="NUMERIC_PRECISION">
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="378" parent="230" name="NUMERIC_SCALE">
      <Position>12</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="379" parent="230" name="DATETIME_PRECISION">
      <Position>13</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="380" parent="230" name="CHARACTER_SET_NAME">
      <Position>14</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="381" parent="230" name="COLLATION_NAME">
      <Position>15</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="382" parent="230" name="COLUMN_TYPE">
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="383" parent="230" name="COLUMN_KEY">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="384" parent="230" name="EXTRA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="385" parent="230" name="PRIVILEGES">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <column id="386" parent="230" name="COLUMN_COMMENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="387" parent="230" name="GENERATION_EXPRESSION">
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="388" parent="231" name="GRANTEE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="389" parent="231" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="390" parent="231" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="391" parent="231" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="392" parent="231" name="COLUMN_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="393" parent="231" name="PRIVILEGE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="394" parent="231" name="IS_GRANTABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="395" parent="232" name="ENGINE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="396" parent="232" name="SUPPORT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(8)|0s</StoredType>
    </column>
    <column id="397" parent="232" name="COMMENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <column id="398" parent="232" name="TRANSACTIONS">
      <Position>4</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="399" parent="232" name="XA">
      <Position>5</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="400" parent="232" name="SAVEPOINTS">
      <Position>6</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="401" parent="233" name="EVENT_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="402" parent="233" name="EVENT_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="403" parent="233" name="EVENT_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="404" parent="233" name="DEFINER">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(93)|0s</StoredType>
    </column>
    <column id="405" parent="233" name="TIME_ZONE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="406" parent="233" name="EVENT_BODY">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(8)|0s</StoredType>
    </column>
    <column id="407" parent="233" name="EVENT_DEFINITION">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="408" parent="233" name="EVENT_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(9)|0s</StoredType>
    </column>
    <column id="409" parent="233" name="EXECUTE_AT">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="410" parent="233" name="INTERVAL_VALUE">
      <Position>10</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="411" parent="233" name="INTERVAL_FIELD">
      <Position>11</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="412" parent="233" name="SQL_MODE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(8192)|0s</StoredType>
    </column>
    <column id="413" parent="233" name="STARTS">
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="414" parent="233" name="ENDS">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="415" parent="233" name="STATUS">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="416" parent="233" name="ON_COMPLETION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>varchar(12)|0s</StoredType>
    </column>
    <column id="417" parent="233" name="CREATED">
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="418" parent="233" name="LAST_ALTERED">
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="419" parent="233" name="LAST_EXECUTED">
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="420" parent="233" name="EVENT_COMMENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="421" parent="233" name="ORIGINATOR">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>bigint(10)|0s</StoredType>
    </column>
    <column id="422" parent="233" name="CHARACTER_SET_CLIENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="423" parent="233" name="COLLATION_CONNECTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="424" parent="233" name="DATABASE_COLLATION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="425" parent="234" name="FILE_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="426" parent="234" name="FILE_NAME">
      <Position>2</Position>
      <StoredType>varchar(4000)|0s</StoredType>
    </column>
    <column id="427" parent="234" name="FILE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="428" parent="234" name="TABLESPACE_NAME">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="429" parent="234" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="430" parent="234" name="TABLE_SCHEMA">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="431" parent="234" name="TABLE_NAME">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="432" parent="234" name="LOGFILE_GROUP_NAME">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="433" parent="234" name="LOGFILE_GROUP_NUMBER">
      <Position>9</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="434" parent="234" name="ENGINE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="435" parent="234" name="FULLTEXT_KEYS">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="436" parent="234" name="DELETED_ROWS">
      <Position>12</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="437" parent="234" name="UPDATE_COUNT">
      <Position>13</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="438" parent="234" name="FREE_EXTENTS">
      <Position>14</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="439" parent="234" name="TOTAL_EXTENTS">
      <Position>15</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="440" parent="234" name="EXTENT_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="441" parent="234" name="INITIAL_SIZE">
      <Position>17</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="442" parent="234" name="MAXIMUM_SIZE">
      <Position>18</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="443" parent="234" name="AUTOEXTEND_SIZE">
      <Position>19</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="444" parent="234" name="CREATION_TIME">
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="445" parent="234" name="LAST_UPDATE_TIME">
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="446" parent="234" name="LAST_ACCESS_TIME">
      <Position>22</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="447" parent="234" name="RECOVER_TIME">
      <Position>23</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="448" parent="234" name="TRANSACTION_COUNTER">
      <Position>24</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="449" parent="234" name="VERSION">
      <Position>25</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="450" parent="234" name="ROW_FORMAT">
      <Position>26</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="451" parent="234" name="TABLE_ROWS">
      <Position>27</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="452" parent="234" name="AVG_ROW_LENGTH">
      <Position>28</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="453" parent="234" name="DATA_LENGTH">
      <Position>29</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="454" parent="234" name="MAX_DATA_LENGTH">
      <Position>30</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="455" parent="234" name="INDEX_LENGTH">
      <Position>31</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="456" parent="234" name="DATA_FREE">
      <Position>32</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="457" parent="234" name="CREATE_TIME">
      <Position>33</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="458" parent="234" name="UPDATE_TIME">
      <Position>34</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="459" parent="234" name="CHECK_TIME">
      <Position>35</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="460" parent="234" name="CHECKSUM">
      <Position>36</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="461" parent="234" name="STATUS">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>37</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="462" parent="234" name="EXTRA">
      <Position>38</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="463" parent="235" name="VARIABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="464" parent="235" name="VARIABLE_VALUE">
      <Position>2</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="465" parent="236" name="VARIABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="466" parent="236" name="VARIABLE_VALUE">
      <Position>2</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="467" parent="237" name="POOL_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="468" parent="237" name="BLOCK_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="469" parent="237" name="SPACE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="470" parent="237" name="PAGE_NUMBER">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="471" parent="237" name="PAGE_TYPE">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="472" parent="237" name="FLUSH_TYPE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="473" parent="237" name="FIX_COUNT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="474" parent="237" name="IS_HASHED">
      <Position>8</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="475" parent="237" name="NEWEST_MODIFICATION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="476" parent="237" name="OLDEST_MODIFICATION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="477" parent="237" name="ACCESS_TIME">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="478" parent="237" name="TABLE_NAME">
      <Position>12</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="479" parent="237" name="INDEX_NAME">
      <Position>13</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="480" parent="237" name="NUMBER_RECORDS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="481" parent="237" name="DATA_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="482" parent="237" name="COMPRESSED_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="483" parent="237" name="PAGE_STATE">
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="484" parent="237" name="IO_FIX">
      <Position>18</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="485" parent="237" name="IS_OLD">
      <Position>19</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="486" parent="237" name="FREE_PAGE_CLOCK">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="487" parent="238" name="POOL_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="488" parent="238" name="LRU_POSITION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="489" parent="238" name="SPACE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="490" parent="238" name="PAGE_NUMBER">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="491" parent="238" name="PAGE_TYPE">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="492" parent="238" name="FLUSH_TYPE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="493" parent="238" name="FIX_COUNT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="494" parent="238" name="IS_HASHED">
      <Position>8</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="495" parent="238" name="NEWEST_MODIFICATION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="496" parent="238" name="OLDEST_MODIFICATION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="497" parent="238" name="ACCESS_TIME">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="498" parent="238" name="TABLE_NAME">
      <Position>12</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="499" parent="238" name="INDEX_NAME">
      <Position>13</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="500" parent="238" name="NUMBER_RECORDS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="501" parent="238" name="DATA_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="502" parent="238" name="COMPRESSED_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="503" parent="238" name="COMPRESSED">
      <Position>17</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="504" parent="238" name="IO_FIX">
      <Position>18</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="505" parent="238" name="IS_OLD">
      <Position>19</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="506" parent="238" name="FREE_PAGE_CLOCK">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="507" parent="239" name="POOL_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="508" parent="239" name="POOL_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="509" parent="239" name="FREE_BUFFERS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="510" parent="239" name="DATABASE_PAGES">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="511" parent="239" name="OLD_DATABASE_PAGES">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="512" parent="239" name="MODIFIED_DATABASE_PAGES">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="513" parent="239" name="PENDING_DECOMPRESS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="514" parent="239" name="PENDING_READS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="515" parent="239" name="PENDING_FLUSH_LRU">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="516" parent="239" name="PENDING_FLUSH_LIST">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="517" parent="239" name="PAGES_MADE_YOUNG">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="518" parent="239" name="PAGES_NOT_MADE_YOUNG">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="519" parent="239" name="PAGES_MADE_YOUNG_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="520" parent="239" name="PAGES_MADE_NOT_YOUNG_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="521" parent="239" name="NUMBER_PAGES_READ">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="522" parent="239" name="NUMBER_PAGES_CREATED">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="523" parent="239" name="NUMBER_PAGES_WRITTEN">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="524" parent="239" name="PAGES_READ_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="525" parent="239" name="PAGES_CREATE_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="526" parent="239" name="PAGES_WRITTEN_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="527" parent="239" name="NUMBER_PAGES_GET">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="528" parent="239" name="HIT_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="529" parent="239" name="YOUNG_MAKE_PER_THOUSAND_GETS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="530" parent="239" name="NOT_YOUNG_MAKE_PER_THOUSAND_GETS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="531" parent="239" name="NUMBER_PAGES_READ_AHEAD">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="532" parent="239" name="NUMBER_READ_AHEAD_EVICTED">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="533" parent="239" name="READ_AHEAD_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="534" parent="239" name="READ_AHEAD_EVICTED_RATE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="535" parent="239" name="LRU_IO_TOTAL">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>29</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="536" parent="239" name="LRU_IO_CURRENT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>30</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="537" parent="239" name="UNCOMPRESS_TOTAL">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>31</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="538" parent="239" name="UNCOMPRESS_CURRENT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>32</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="539" parent="240" name="page_size">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(5)|0s</StoredType>
    </column>
    <column id="540" parent="240" name="compress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="541" parent="240" name="compress_ops_ok">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="542" parent="240" name="compress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="543" parent="240" name="uncompress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="544" parent="240" name="uncompress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="545" parent="241" name="page_size">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(5)|0s</StoredType>
    </column>
    <column id="546" parent="241" name="buffer_pool_instance">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="547" parent="241" name="pages_used">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="548" parent="241" name="pages_free">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="549" parent="241" name="relocation_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="550" parent="241" name="relocation_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="551" parent="242" name="page_size">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(5)|0s</StoredType>
    </column>
    <column id="552" parent="242" name="buffer_pool_instance">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="553" parent="242" name="pages_used">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="554" parent="242" name="pages_free">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="555" parent="242" name="relocation_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="556" parent="242" name="relocation_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="557" parent="243" name="database_name">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(192)|0s</StoredType>
    </column>
    <column id="558" parent="243" name="table_name">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(192)|0s</StoredType>
    </column>
    <column id="559" parent="243" name="index_name">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(192)|0s</StoredType>
    </column>
    <column id="560" parent="243" name="compress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="561" parent="243" name="compress_ops_ok">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="562" parent="243" name="compress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="563" parent="243" name="uncompress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="564" parent="243" name="uncompress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="565" parent="244" name="database_name">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(192)|0s</StoredType>
    </column>
    <column id="566" parent="244" name="table_name">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(192)|0s</StoredType>
    </column>
    <column id="567" parent="244" name="index_name">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(192)|0s</StoredType>
    </column>
    <column id="568" parent="244" name="compress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="569" parent="244" name="compress_ops_ok">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="570" parent="244" name="compress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="571" parent="244" name="uncompress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="572" parent="244" name="uncompress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="573" parent="245" name="page_size">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(5)|0s</StoredType>
    </column>
    <column id="574" parent="245" name="compress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="575" parent="245" name="compress_ops_ok">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="576" parent="245" name="compress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="577" parent="245" name="uncompress_ops">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="578" parent="245" name="uncompress_time">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="579" parent="246" name="DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="580" parent="247" name="KEY">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="581" parent="247" name="VALUE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="582" parent="248" name="value">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="583" parent="249" name="DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="584" parent="250" name="WORD">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(337)|0s</StoredType>
    </column>
    <column id="585" parent="250" name="FIRST_DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="586" parent="250" name="LAST_DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="587" parent="250" name="DOC_COUNT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="588" parent="250" name="DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="589" parent="250" name="POSITION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="590" parent="251" name="WORD">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(337)|0s</StoredType>
    </column>
    <column id="591" parent="251" name="FIRST_DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="592" parent="251" name="LAST_DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="593" parent="251" name="DOC_COUNT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="594" parent="251" name="DOC_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="595" parent="251" name="POSITION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="596" parent="252" name="lock_id">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="597" parent="252" name="lock_trx_id">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="598" parent="252" name="lock_mode">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="599" parent="252" name="lock_type">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="600" parent="252" name="lock_table">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="601" parent="252" name="lock_index">
      <Position>6</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="602" parent="252" name="lock_space">
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="603" parent="252" name="lock_page">
      <Position>8</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="604" parent="252" name="lock_rec">
      <Position>9</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="605" parent="252" name="lock_data">
      <Position>10</Position>
      <StoredType>varchar(8192)|0s</StoredType>
    </column>
    <column id="606" parent="253" name="requesting_trx_id">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="607" parent="253" name="requested_lock_id">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="608" parent="253" name="blocking_trx_id">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="609" parent="253" name="blocking_lock_id">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="610" parent="254" name="NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="611" parent="254" name="SUBSYSTEM">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="612" parent="254" name="COUNT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="613" parent="254" name="MAX_COUNT">
      <Position>4</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="614" parent="254" name="MIN_COUNT">
      <Position>5</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="615" parent="254" name="AVG_COUNT">
      <Position>6</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="616" parent="254" name="COUNT_RESET">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="617" parent="254" name="MAX_COUNT_RESET">
      <Position>8</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="618" parent="254" name="MIN_COUNT_RESET">
      <Position>9</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="619" parent="254" name="AVG_COUNT_RESET">
      <Position>10</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="620" parent="254" name="TIME_ENABLED">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="621" parent="254" name="TIME_DISABLED">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="622" parent="254" name="TIME_ELAPSED">
      <Position>13</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="623" parent="254" name="TIME_RESET">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="624" parent="254" name="STATUS">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="625" parent="254" name="TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="626" parent="254" name="COMMENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="627" parent="255" name="TABLE_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="628" parent="255" name="NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="629" parent="255" name="POS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="630" parent="255" name="MTYPE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="631" parent="255" name="PRTYPE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="632" parent="255" name="LEN">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="633" parent="256" name="SPACE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="634" parent="256" name="PATH">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(4000)|0s</StoredType>
    </column>
    <column id="635" parent="257" name="INDEX_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="636" parent="257" name="NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="637" parent="257" name="POS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="638" parent="258" name="ID">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="639" parent="258" name="FOR_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="640" parent="258" name="REF_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="641" parent="258" name="N_COLS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="642" parent="258" name="TYPE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="643" parent="259" name="ID">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="644" parent="259" name="FOR_COL_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="645" parent="259" name="REF_COL_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="646" parent="259" name="POS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="647" parent="260" name="INDEX_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="648" parent="260" name="NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="649" parent="260" name="TABLE_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="650" parent="260" name="TYPE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="651" parent="260" name="N_FIELDS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="652" parent="260" name="PAGE_NO">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="653" parent="260" name="SPACE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="654" parent="260" name="MERGE_THRESHOLD">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="655" parent="261" name="TABLE_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="656" parent="261" name="NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(655)|0s</StoredType>
    </column>
    <column id="657" parent="261" name="FLAG">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="658" parent="261" name="N_COLS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="659" parent="261" name="SPACE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="660" parent="261" name="FILE_FORMAT">
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="661" parent="261" name="ROW_FORMAT">
      <Position>7</Position>
      <StoredType>varchar(12)|0s</StoredType>
    </column>
    <column id="662" parent="261" name="ZIP_PAGE_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="663" parent="261" name="SPACE_TYPE">
      <Position>9</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="664" parent="262" name="SPACE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="665" parent="262" name="NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(655)|0s</StoredType>
    </column>
    <column id="666" parent="262" name="FLAG">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="667" parent="262" name="FILE_FORMAT">
      <Position>4</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="668" parent="262" name="ROW_FORMAT">
      <Position>5</Position>
      <StoredType>varchar(22)|0s</StoredType>
    </column>
    <column id="669" parent="262" name="PAGE_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="670" parent="262" name="ZIP_PAGE_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="671" parent="262" name="SPACE_TYPE">
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="672" parent="262" name="FS_BLOCK_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="673" parent="262" name="FILE_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="674" parent="262" name="ALLOCATED_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="675" parent="263" name="TABLE_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="676" parent="263" name="NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="677" parent="263" name="STATS_INITIALIZED">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(193)|0s</StoredType>
    </column>
    <column id="678" parent="263" name="NUM_ROWS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="679" parent="263" name="CLUST_INDEX_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="680" parent="263" name="OTHER_INDEX_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="681" parent="263" name="MODIFIED_COUNTER">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="682" parent="263" name="AUTOINC">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="683" parent="263" name="REF_COUNT">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="684" parent="264" name="TABLE_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="685" parent="264" name="POS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="686" parent="264" name="BASE_POS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="687" parent="265" name="TABLE_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="688" parent="265" name="NAME">
      <Position>2</Position>
      <StoredType>varchar(202)|0s</StoredType>
    </column>
    <column id="689" parent="265" name="N_COLS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="690" parent="265" name="SPACE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11) unsigned|0s</StoredType>
    </column>
    <column id="691" parent="265" name="PER_TABLE_TABLESPACE">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="692" parent="265" name="IS_COMPRESSED">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="693" parent="266" name="trx_id">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="694" parent="266" name="trx_state">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(13)|0s</StoredType>
    </column>
    <column id="695" parent="266" name="trx_started">
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="696" parent="266" name="trx_requested_lock_id">
      <Position>4</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="697" parent="266" name="trx_wait_started">
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="698" parent="266" name="trx_weight">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="699" parent="266" name="trx_mysql_thread_id">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="700" parent="266" name="trx_query">
      <Position>8</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="701" parent="266" name="trx_operation_state">
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="702" parent="266" name="trx_tables_in_use">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="703" parent="266" name="trx_tables_locked">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="704" parent="266" name="trx_lock_structs">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="705" parent="266" name="trx_lock_memory_bytes">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="706" parent="266" name="trx_rows_locked">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="707" parent="266" name="trx_rows_modified">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="708" parent="266" name="trx_concurrency_tickets">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="709" parent="266" name="trx_isolation_level">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="710" parent="266" name="trx_unique_checks">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="711" parent="266" name="trx_foreign_key_checks">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="712" parent="266" name="trx_last_foreign_key_error">
      <Position>20</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="713" parent="266" name="trx_adaptive_hash_latched">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="714" parent="266" name="trx_adaptive_hash_timeout">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="715" parent="266" name="trx_is_read_only">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="716" parent="266" name="trx_autocommit_non_locking">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="717" parent="267" name="CONSTRAINT_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="718" parent="267" name="CONSTRAINT_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="719" parent="267" name="CONSTRAINT_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="720" parent="267" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="721" parent="267" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="722" parent="267" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="723" parent="267" name="COLUMN_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="724" parent="267" name="ORDINAL_POSITION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(10)|0s</StoredType>
    </column>
    <column id="725" parent="267" name="POSITION_IN_UNIQUE_CONSTRAINT">
      <Position>9</Position>
      <StoredType>bigint(10)|0s</StoredType>
    </column>
    <column id="726" parent="267" name="REFERENCED_TABLE_SCHEMA">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="727" parent="267" name="REFERENCED_TABLE_NAME">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="728" parent="267" name="REFERENCED_COLUMN_NAME">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="729" parent="268" name="QUERY">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="730" parent="268" name="TRACE">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="731" parent="268" name="MISSING_BYTES_BEYOND_MAX_MEM_SIZE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="732" parent="268" name="INSUFFICIENT_PRIVILEGES">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="733" parent="269" name="SPECIFIC_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="734" parent="269" name="SPECIFIC_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="735" parent="269" name="SPECIFIC_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="736" parent="269" name="ORDINAL_POSITION">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(21)|0s</StoredType>
    </column>
    <column id="737" parent="269" name="PARAMETER_MODE">
      <Position>5</Position>
      <StoredType>varchar(5)|0s</StoredType>
    </column>
    <column id="738" parent="269" name="PARAMETER_NAME">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="739" parent="269" name="DATA_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="740" parent="269" name="CHARACTER_MAXIMUM_LENGTH">
      <Position>8</Position>
      <StoredType>int(21)|0s</StoredType>
    </column>
    <column id="741" parent="269" name="CHARACTER_OCTET_LENGTH">
      <Position>9</Position>
      <StoredType>int(21)|0s</StoredType>
    </column>
    <column id="742" parent="269" name="NUMERIC_PRECISION">
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="743" parent="269" name="NUMERIC_SCALE">
      <Position>11</Position>
      <StoredType>int(21)|0s</StoredType>
    </column>
    <column id="744" parent="269" name="DATETIME_PRECISION">
      <Position>12</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="745" parent="269" name="CHARACTER_SET_NAME">
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="746" parent="269" name="COLLATION_NAME">
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="747" parent="269" name="DTD_IDENTIFIER">
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="748" parent="269" name="ROUTINE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>varchar(9)|0s</StoredType>
    </column>
    <column id="749" parent="270" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="750" parent="270" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="751" parent="270" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="752" parent="270" name="PARTITION_NAME">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="753" parent="270" name="SUBPARTITION_NAME">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="754" parent="270" name="PARTITION_ORDINAL_POSITION">
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="755" parent="270" name="SUBPARTITION_ORDINAL_POSITION">
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="756" parent="270" name="PARTITION_METHOD">
      <Position>8</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="757" parent="270" name="SUBPARTITION_METHOD">
      <Position>9</Position>
      <StoredType>varchar(12)|0s</StoredType>
    </column>
    <column id="758" parent="270" name="PARTITION_EXPRESSION">
      <Position>10</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="759" parent="270" name="SUBPARTITION_EXPRESSION">
      <Position>11</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="760" parent="270" name="PARTITION_DESCRIPTION">
      <Position>12</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="761" parent="270" name="TABLE_ROWS">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="762" parent="270" name="AVG_ROW_LENGTH">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="763" parent="270" name="DATA_LENGTH">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="764" parent="270" name="MAX_DATA_LENGTH">
      <Position>16</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="765" parent="270" name="INDEX_LENGTH">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="766" parent="270" name="DATA_FREE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="767" parent="270" name="CREATE_TIME">
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="768" parent="270" name="UPDATE_TIME">
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="769" parent="270" name="CHECK_TIME">
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="770" parent="270" name="CHECKSUM">
      <Position>22</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="771" parent="270" name="PARTITION_COMMENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <column id="772" parent="270" name="NODEGROUP">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>varchar(12)|0s</StoredType>
    </column>
    <column id="773" parent="270" name="TABLESPACE_NAME">
      <Position>25</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="774" parent="271" name="PLUGIN_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="775" parent="271" name="PLUGIN_VERSION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="776" parent="271" name="PLUGIN_STATUS">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="777" parent="271" name="PLUGIN_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <column id="778" parent="271" name="PLUGIN_TYPE_VERSION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="779" parent="271" name="PLUGIN_LIBRARY">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="780" parent="271" name="PLUGIN_LIBRARY_VERSION">
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="781" parent="271" name="PLUGIN_AUTHOR">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="782" parent="271" name="PLUGIN_DESCRIPTION">
      <Position>9</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="783" parent="271" name="PLUGIN_LICENSE">
      <Position>10</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <column id="784" parent="271" name="LOAD_OPTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="785" parent="272" name="ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="786" parent="272" name="USER">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="787" parent="272" name="HOST">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="788" parent="272" name="DB">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="789" parent="272" name="COMMAND">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="790" parent="272" name="TIME">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(7)|0s</StoredType>
    </column>
    <column id="791" parent="272" name="STATE">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="792" parent="272" name="INFO">
      <Position>8</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="793" parent="273" name="QUERY_ID">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="794" parent="273" name="SEQ">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="795" parent="273" name="STATE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="796" parent="273" name="DURATION">
      <DefaultExpression>0.000000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>decimal(9,6 digit)|0s</StoredType>
    </column>
    <column id="797" parent="273" name="CPU_USER">
      <Position>5</Position>
      <StoredType>decimal(9,6 digit)|0s</StoredType>
    </column>
    <column id="798" parent="273" name="CPU_SYSTEM">
      <Position>6</Position>
      <StoredType>decimal(9,6 digit)|0s</StoredType>
    </column>
    <column id="799" parent="273" name="CONTEXT_VOLUNTARY">
      <Position>7</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="800" parent="273" name="CONTEXT_INVOLUNTARY">
      <Position>8</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="801" parent="273" name="BLOCK_OPS_IN">
      <Position>9</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="802" parent="273" name="BLOCK_OPS_OUT">
      <Position>10</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="803" parent="273" name="MESSAGES_SENT">
      <Position>11</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="804" parent="273" name="MESSAGES_RECEIVED">
      <Position>12</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="805" parent="273" name="PAGE_FAULTS_MAJOR">
      <Position>13</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="806" parent="273" name="PAGE_FAULTS_MINOR">
      <Position>14</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="807" parent="273" name="SWAPS">
      <Position>15</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="808" parent="273" name="SOURCE_FUNCTION">
      <Position>16</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="809" parent="273" name="SOURCE_FILE">
      <Position>17</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="810" parent="273" name="SOURCE_LINE">
      <Position>18</Position>
      <StoredType>int(20)|0s</StoredType>
    </column>
    <column id="811" parent="274" name="CONSTRAINT_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="812" parent="274" name="CONSTRAINT_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="813" parent="274" name="CONSTRAINT_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="814" parent="274" name="UNIQUE_CONSTRAINT_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="815" parent="274" name="UNIQUE_CONSTRAINT_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="816" parent="274" name="UNIQUE_CONSTRAINT_NAME">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="817" parent="274" name="MATCH_OPTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="818" parent="274" name="UPDATE_RULE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="819" parent="274" name="DELETE_RULE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="820" parent="274" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="821" parent="274" name="REFERENCED_TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="822" parent="275" name="SPECIFIC_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="823" parent="275" name="ROUTINE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="824" parent="275" name="ROUTINE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="825" parent="275" name="ROUTINE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="826" parent="275" name="ROUTINE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(9)|0s</StoredType>
    </column>
    <column id="827" parent="275" name="DATA_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="828" parent="275" name="CHARACTER_MAXIMUM_LENGTH">
      <Position>7</Position>
      <StoredType>int(21)|0s</StoredType>
    </column>
    <column id="829" parent="275" name="CHARACTER_OCTET_LENGTH">
      <Position>8</Position>
      <StoredType>int(21)|0s</StoredType>
    </column>
    <column id="830" parent="275" name="NUMERIC_PRECISION">
      <Position>9</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="831" parent="275" name="NUMERIC_SCALE">
      <Position>10</Position>
      <StoredType>int(21)|0s</StoredType>
    </column>
    <column id="832" parent="275" name="DATETIME_PRECISION">
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="833" parent="275" name="CHARACTER_SET_NAME">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="834" parent="275" name="COLLATION_NAME">
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="835" parent="275" name="DTD_IDENTIFIER">
      <Position>14</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="836" parent="275" name="ROUTINE_BODY">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(8)|0s</StoredType>
    </column>
    <column id="837" parent="275" name="ROUTINE_DEFINITION">
      <Position>16</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="838" parent="275" name="EXTERNAL_NAME">
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="839" parent="275" name="EXTERNAL_LANGUAGE">
      <Position>18</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="840" parent="275" name="PARAMETER_STYLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(8)|0s</StoredType>
    </column>
    <column id="841" parent="275" name="IS_DETERMINISTIC">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="842" parent="275" name="SQL_DATA_ACCESS">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="843" parent="275" name="SQL_PATH">
      <Position>22</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="844" parent="275" name="SECURITY_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>varchar(7)|0s</StoredType>
    </column>
    <column id="845" parent="275" name="CREATED">
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="846" parent="275" name="LAST_ALTERED">
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="847" parent="275" name="SQL_MODE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>varchar(8192)|0s</StoredType>
    </column>
    <column id="848" parent="275" name="ROUTINE_COMMENT">
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="849" parent="275" name="DEFINER">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>varchar(93)|0s</StoredType>
    </column>
    <column id="850" parent="275" name="CHARACTER_SET_CLIENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>29</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="851" parent="275" name="COLLATION_CONNECTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>30</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="852" parent="275" name="DATABASE_COLLATION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>31</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="853" parent="276" name="CATALOG_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="854" parent="276" name="SCHEMA_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="855" parent="276" name="DEFAULT_CHARACTER_SET_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="856" parent="276" name="DEFAULT_COLLATION_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="857" parent="276" name="SQL_PATH">
      <Position>5</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="858" parent="277" name="GRANTEE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="859" parent="277" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="860" parent="277" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="861" parent="277" name="PRIVILEGE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="862" parent="277" name="IS_GRANTABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="863" parent="278" name="VARIABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="864" parent="278" name="VARIABLE_VALUE">
      <Position>2</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="865" parent="279" name="VARIABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="866" parent="279" name="VARIABLE_VALUE">
      <Position>2</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="867" parent="280" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="868" parent="280" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="869" parent="280" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="870" parent="280" name="NON_UNIQUE">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(1)|0s</StoredType>
    </column>
    <column id="871" parent="280" name="INDEX_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="872" parent="280" name="INDEX_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="873" parent="280" name="SEQ_IN_INDEX">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(2)|0s</StoredType>
    </column>
    <column id="874" parent="280" name="COLUMN_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="875" parent="280" name="COLLATION">
      <Position>9</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="876" parent="280" name="CARDINALITY">
      <Position>10</Position>
      <StoredType>bigint(21)|0s</StoredType>
    </column>
    <column id="877" parent="280" name="SUB_PART">
      <Position>11</Position>
      <StoredType>bigint(3)|0s</StoredType>
    </column>
    <column id="878" parent="280" name="PACKED">
      <Position>12</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="879" parent="280" name="NULLABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="880" parent="280" name="INDEX_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="881" parent="280" name="COMMENT">
      <Position>15</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="882" parent="280" name="INDEX_COMMENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="883" parent="281" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="884" parent="281" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="885" parent="281" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="886" parent="281" name="TABLE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="887" parent="281" name="ENGINE">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="888" parent="281" name="VERSION">
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="889" parent="281" name="ROW_FORMAT">
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="890" parent="281" name="TABLE_ROWS">
      <Position>8</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="891" parent="281" name="AVG_ROW_LENGTH">
      <Position>9</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="892" parent="281" name="DATA_LENGTH">
      <Position>10</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="893" parent="281" name="MAX_DATA_LENGTH">
      <Position>11</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="894" parent="281" name="INDEX_LENGTH">
      <Position>12</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="895" parent="281" name="DATA_FREE">
      <Position>13</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="896" parent="281" name="AUTO_INCREMENT">
      <Position>14</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="897" parent="281" name="CREATE_TIME">
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="898" parent="281" name="UPDATE_TIME">
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="899" parent="281" name="CHECK_TIME">
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="900" parent="281" name="TABLE_COLLATION">
      <Position>18</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="901" parent="281" name="CHECKSUM">
      <Position>19</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="902" parent="281" name="CREATE_OPTIONS">
      <Position>20</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="903" parent="281" name="TABLE_COMMENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(2048)|0s</StoredType>
    </column>
    <column id="904" parent="282" name="TABLESPACE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="905" parent="282" name="ENGINE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="906" parent="282" name="TABLESPACE_TYPE">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="907" parent="282" name="LOGFILE_GROUP_NAME">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="908" parent="282" name="EXTENT_SIZE">
      <Position>5</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="909" parent="282" name="AUTOEXTEND_SIZE">
      <Position>6</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="910" parent="282" name="MAXIMUM_SIZE">
      <Position>7</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="911" parent="282" name="NODEGROUP_ID">
      <Position>8</Position>
      <StoredType>bigint(21) unsigned|0s</StoredType>
    </column>
    <column id="912" parent="282" name="TABLESPACE_COMMENT">
      <Position>9</Position>
      <StoredType>varchar(2048)|0s</StoredType>
    </column>
    <column id="913" parent="283" name="CONSTRAINT_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="914" parent="283" name="CONSTRAINT_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="915" parent="283" name="CONSTRAINT_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="916" parent="283" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="917" parent="283" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="918" parent="283" name="CONSTRAINT_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="919" parent="284" name="GRANTEE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="920" parent="284" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="921" parent="284" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="922" parent="284" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="923" parent="284" name="PRIVILEGE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="924" parent="284" name="IS_GRANTABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="925" parent="285" name="TRIGGER_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="926" parent="285" name="TRIGGER_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="927" parent="285" name="TRIGGER_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="928" parent="285" name="EVENT_MANIPULATION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="929" parent="285" name="EVENT_OBJECT_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="930" parent="285" name="EVENT_OBJECT_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="931" parent="285" name="EVENT_OBJECT_TABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="932" parent="285" name="ACTION_ORDER">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(4)|0s</StoredType>
    </column>
    <column id="933" parent="285" name="ACTION_CONDITION">
      <Position>9</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="934" parent="285" name="ACTION_STATEMENT">
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="935" parent="285" name="ACTION_ORIENTATION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(9)|0s</StoredType>
    </column>
    <column id="936" parent="285" name="ACTION_TIMING">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="937" parent="285" name="ACTION_REFERENCE_OLD_TABLE">
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="938" parent="285" name="ACTION_REFERENCE_NEW_TABLE">
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="939" parent="285" name="ACTION_REFERENCE_OLD_ROW">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="940" parent="285" name="ACTION_REFERENCE_NEW_ROW">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="941" parent="285" name="CREATED">
      <Position>17</Position>
      <StoredType>datetime(2)|0s</StoredType>
    </column>
    <column id="942" parent="285" name="SQL_MODE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>varchar(8192)|0s</StoredType>
    </column>
    <column id="943" parent="285" name="DEFINER">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(93)|0s</StoredType>
    </column>
    <column id="944" parent="285" name="CHARACTER_SET_CLIENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="945" parent="285" name="COLLATION_CONNECTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="946" parent="285" name="DATABASE_COLLATION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="947" parent="286" name="GRANTEE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(81)|0s</StoredType>
    </column>
    <column id="948" parent="286" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="949" parent="286" name="PRIVILEGE_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="950" parent="286" name="IS_GRANTABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="951" parent="287" name="TABLE_CATALOG">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="952" parent="287" name="TABLE_SCHEMA">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="953" parent="287" name="TABLE_NAME">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="954" parent="287" name="VIEW_DEFINITION">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="955" parent="287" name="CHECK_OPTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(8)|0s</StoredType>
    </column>
    <column id="956" parent="287" name="IS_UPDATABLE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(3)|0s</StoredType>
    </column>
    <column id="957" parent="287" name="DEFINER">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(93)|0s</StoredType>
    </column>
    <column id="958" parent="287" name="SECURITY_TYPE">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(7)|0s</StoredType>
    </column>
    <column id="959" parent="287" name="CHARACTER_SET_CLIENT">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="960" parent="287" name="COLLATION_CONNECTION">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="961" parent="288" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="962" parent="288" name="account_name">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="963" parent="288" name="password">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="964" parent="288" name="create_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>4</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="965" parent="288" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="966" parent="288" name="account_name">
      <ColNames>account_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="967" parent="288" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="968" parent="288" name="account_name">
      <UnderlyingIndexName>account_name</UnderlyingIndexName>
    </key>
    <column id="969" parent="289" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(10) unsigned|0s</StoredType>
    </column>
    <column id="970" parent="289" name="code">
      <Comment>地区邮编</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="971" parent="289" name="parent_code">
      <Comment>父地区邮编</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="972" parent="289" name="name">
      <Comment>地区名</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="973" parent="289" name="level">
      <Comment>地区层级</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="974" parent="289" name="created_at">
      <Position>6</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="975" parent="289" name="updated_at">
      <Position>7</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="976" parent="289" name="deleted_at">
      <Position>8</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="977" parent="289" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="978" parent="289" name="address_code_parent_code_unique">
      <ColNames>code
parent_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="979" parent="289" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="980" parent="289" name="address_code_parent_code_unique">
      <UnderlyingIndexName>address_code_parent_code_unique</UnderlyingIndexName>
    </key>
    <column id="981" parent="290" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>地址ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="982" parent="290" name="buyer_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="983" parent="290" name="consignee">
      <Comment>收货人</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="984" parent="290" name="phone">
      <Comment>手机号</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="985" parent="290" name="province">
      <Comment>省份</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="986" parent="290" name="city">
      <Comment>城市</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="987" parent="290" name="district">
      <Comment>区县</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="988" parent="290" name="detail">
      <Comment>详细地址</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="989" parent="290" name="label">
      <Comment>地址标签</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="990" parent="290" name="is_default">
      <Comment>是否默认地址</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="991" parent="290" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="992" parent="290" name="update_time">
      <Comment>更新时间</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="993" parent="290" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="994" parent="290" name="idx_buyer_id">
      <ColNames>buyer_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="995" parent="290" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="996" parent="291" name="id">
      <AutoIncrement>8</AutoIncrement>
      <Comment>账号ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="997" parent="291" name="accountName">
      <Comment>管理员名称</Comment>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="998" parent="291" name="email">
      <Comment>邮箱</Comment>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="999" parent="291" name="phone">
      <Comment>手机号</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1000" parent="291" name="password">
      <Comment>密码</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1001" parent="291" name="status">
      <Comment>状态: 1=启用, 0=禁用, 2=锁定</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1002" parent="291" name="createTime">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1003" parent="291" name="updateTime">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1004" parent="291" name="lastLoginTime">
      <Comment>最后登录时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1005" parent="291" name="loginCount">
      <Comment>登录次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1006" parent="291" name="permissions">
      <Comment>权限代码列表，JSON格式存储</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1007" parent="291" name="roleId">
      <Comment>角色ID</Comment>
      <Position>12</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1008" parent="291" name="createdBy">
      <Comment>创建者ID</Comment>
      <Position>13</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1009" parent="291" name="remark">
      <Comment>备注信息</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="1010" parent="291" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1011" parent="291" name="uk_account_name">
      <ColNames>accountName</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1012" parent="291" name="uk_email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1013" parent="291" name="uk_phone">
      <ColNames>phone</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1014" parent="291" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1015" parent="291" name="idx_create_time">
      <ColNames>createTime</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1016" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1017" parent="291" name="uk_account_name">
      <UnderlyingIndexName>uk_account_name</UnderlyingIndexName>
    </key>
    <key id="1018" parent="291" name="uk_email">
      <UnderlyingIndexName>uk_email</UnderlyingIndexName>
    </key>
    <key id="1019" parent="291" name="uk_phone">
      <UnderlyingIndexName>uk_phone</UnderlyingIndexName>
    </key>
    <column id="1020" parent="292" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>日志ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1021" parent="292" name="adminId">
      <Comment>管理员ID</Comment>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1022" parent="292" name="adminName">
      <Comment>管理员名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1023" parent="292" name="actionType">
      <Comment>操作类型</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1024" parent="292" name="actionDesc">
      <Comment>操作描述</Comment>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1025" parent="292" name="ipAddress">
      <Comment>IP地址</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1026" parent="292" name="userAgent">
      <Comment>用户代理</Comment>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1027" parent="292" name="createTime">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1028" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1029" parent="292" name="idx_admin_id">
      <ColNames>adminId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1030" parent="292" name="idx_action_type">
      <ColNames>actionType</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1031" parent="292" name="idx_create_time">
      <ColNames>createTime</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1032" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1033" parent="293" name="id">
      <AutoIncrement>17</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1034" parent="293" name="account_name">
      <Comment>账号名</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1035" parent="293" name="password">
      <Comment>密码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1036" parent="293" name="gender">
      <Comment>性别</Comment>
      <Position>4</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1037" parent="293" name="photo_url">
      <Comment>用户头像URL</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1038" parent="293" name="phone">
      <Comment>联系电话</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1039" parent="293" name="email">
      <Comment>电子邮箱</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1040" parent="293" name="account_status">
      <Comment>账号状态，1启用 0停用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1041" parent="293" name="invitation_code">
      <Comment>邀请码</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1042" parent="293" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>10</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1043" parent="293" name="last_login_time">
      <Comment>最后登录时间</Comment>
      <Position>11</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="1044" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1045" parent="293" name="account_name">
      <ColNames>account_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1046" parent="293" name="phone">
      <ColNames>phone</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1047" parent="293" name="email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1048" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1049" parent="293" name="account_name">
      <UnderlyingIndexName>account_name</UnderlyingIndexName>
    </key>
    <key id="1050" parent="293" name="phone">
      <UnderlyingIndexName>phone</UnderlyingIndexName>
    </key>
    <key id="1051" parent="293" name="email">
      <UnderlyingIndexName>email</UnderlyingIndexName>
    </key>
    <column id="1052" parent="294" name="id">
      <AutoIncrement>294</AutoIncrement>
      <Comment>分类唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="1053" parent="294" name="name">
      <Comment>分类名称（最大50字符）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1054" parent="294" name="parent_id">
      <Comment>父分类ID（NULL表示一级分类）</Comment>
      <Position>3</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="1055" parent="294" name="level">
      <Comment>分类层级: 0(一级),1(二级),2(三级)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(3) unsigned|0s</StoredType>
    </column>
    <column id="1056" parent="294" name="sort_order">
      <Comment>显示排序（升序排列）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1057" parent="294" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1058" parent="294" name="update_time">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="1059" parent="294" name="fk_parent">
      <ColNames>parent_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>category</RefTableName>
    </foreign-key>
    <index id="1060" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1061" parent="294" name="uniq_parent_name">
      <ColNames>parent_id
name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1062" parent="294" name="idx_parent_level">
      <ColNames>parent_id
level</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1063" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1064" parent="294" name="uniq_parent_name">
      <UnderlyingIndexName>uniq_parent_name</UnderlyingIndexName>
    </key>
    <column id="1065" parent="295" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1066" parent="295" name="leader_id">
      <Comment>团长ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1067" parent="295" name="member_id">
      <Comment>成员ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1068" parent="295" name="member_name">
      <Comment>成员名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1069" parent="295" name="member_level">
      <Comment>成员等级</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1070" parent="295" name="join_time">
      <Comment>加入时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1071" parent="295" name="total_sales">
      <Comment>总销售额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>7</Position>
      <StoredType>decimal(15,2 digit)|0s</StoredType>
    </column>
    <foreign-key id="1072" parent="295" name="commission_leader_team_members_ibfk_1">
      <ColNames>leader_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>commission_leaders</RefTableName>
    </foreign-key>
    <index id="1073" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1074" parent="295" name="leader_id">
      <ColNames>leader_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1075" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1076" parent="296" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1077" parent="296" name="name">
      <Comment>团长名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1078" parent="296" name="real_name">
      <Comment>真实姓名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1079" parent="296" name="phone">
      <Comment>手机号码</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1080" parent="296" name="email">
      <Comment>电子邮箱</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1081" parent="296" name="id_number">
      <Comment>身份证号码</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1082" parent="296" name="bank_info">
      <Comment>银行账户信息</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1083" parent="296" name="address">
      <Comment>详细地址</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1084" parent="296" name="level">
      <Comment>团长等级</Comment>
      <DefaultExpression>&apos;普通团长&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1085" parent="296" name="status">
      <Comment>团长状态</Comment>
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1086" parent="296" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1087" parent="296" name="total_sales">
      <Comment>总销售额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>12</Position>
      <StoredType>decimal(15,2 digit)|0s</StoredType>
    </column>
    <column id="1088" parent="296" name="commission_earned">
      <Comment>已赚取佣金</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>13</Position>
      <StoredType>decimal(15,2 digit)|0s</StoredType>
    </column>
    <column id="1089" parent="296" name="today_sales">
      <Comment>今日销售额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>14</Position>
      <StoredType>decimal(15,2 digit)|0s</StoredType>
    </column>
    <column id="1090" parent="296" name="month_sales">
      <Comment>本月销售额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>15</Position>
      <StoredType>decimal(15,2 digit)|0s</StoredType>
    </column>
    <column id="1091" parent="296" name="sales_trend">
      <Comment>销售额趋势</Comment>
      <Position>16</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <index id="1092" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1093" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1094" parent="297" name="id">
      <Comment>佣金记录ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1095" parent="297" name="leader_id">
      <Comment>团长ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1096" parent="297" name="leader_name">
      <Comment>团长名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1097" parent="297" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1098" parent="297" name="order_amount">
      <Comment>订单金额</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1099" parent="297" name="commission_amount">
      <Comment>佣金金额</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1100" parent="297" name="commission_type">
      <Comment>佣金类型：销售佣金/团队佣金</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1101" parent="297" name="order_type">
      <Comment>订单类型：normal/team</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1102" parent="297" name="buyer_name">
      <Comment>买家名称</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1103" parent="297" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1104" parent="297" name="settle_time">
      <Comment>结算时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1105" parent="297" name="withdraw_time">
      <Comment>提现时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1106" parent="297" name="status">
      <Comment>状态：pending/paid/withdrawn</Comment>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <foreign-key id="1107" parent="297" name="commission_record_ibfk_1">
      <ColNames>leader_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>team_leader</RefTableName>
    </foreign-key>
    <index id="1108" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1109" parent="297" name="leader_id">
      <ColNames>leader_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1110" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1111" parent="298" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1112" parent="298" name="basic_rate">
      <Comment>基础佣金比例</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>decimal(5,4 digit)|0s</StoredType>
    </column>
    <column id="1113" parent="298" name="team_rate">
      <Comment>团队佣金比例</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>decimal(5,4 digit)|0s</StoredType>
    </column>
    <column id="1114" parent="298" name="leader_rate">
      <Comment>团长佣金比例</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>decimal(5,4 digit)|0s</StoredType>
    </column>
    <column id="1115" parent="298" name="min_withdraw">
      <Comment>最低提现金额</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1116" parent="298" name="withdraw_cycle">
      <Comment>提现周期：daily/weekly/monthly</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1117" parent="298" name="withdraw_days">
      <Comment>允许提现的日期</Comment>
      <Position>7</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1118" parent="298" name="upgrade_rules">
      <Comment>升级规则配置</Comment>
      <Position>8</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1119" parent="298" name="rebate_policy">
      <Comment>返利策略配置</Comment>
      <Position>9</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1120" parent="298" name="create_time">
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1121" parent="298" name="update_time">
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1122" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1123" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1124" parent="299" name="id">
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1125" parent="299" name="buyer_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1126" parent="299" name="customer_service_id">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1127" parent="299" name="message_content">
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1128" parent="299" name="message_type">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1129" parent="299" name="create_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1130" parent="299" name="reply_status">
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1131" parent="299" name="priority">
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1132" parent="299" name="status">
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1133" parent="299" name="close_time">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1134" parent="300" name="table_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1135" parent="300" name="table_name">
      <Comment>表名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1136" parent="300" name="table_comment">
      <Comment>表描述</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1137" parent="300" name="sub_table_name">
      <Comment>关联子表的表名</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1138" parent="300" name="sub_table_fk_name">
      <Comment>子表关联的外键名</Comment>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1139" parent="300" name="class_name">
      <Comment>实体类名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1140" parent="300" name="tpl_category">
      <Comment>使用的模板（crud单表操作 tree树表操作）</Comment>
      <DefaultExpression>&apos;crud&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1141" parent="300" name="package_name">
      <Comment>生成包路径</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1142" parent="300" name="module_name">
      <Comment>生成模块名</Comment>
      <Position>9</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1143" parent="300" name="business_name">
      <Comment>生成业务名</Comment>
      <Position>10</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1144" parent="300" name="function_name">
      <Comment>生成功能名</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1145" parent="300" name="function_author">
      <Comment>生成功能作者</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1146" parent="300" name="gen_type">
      <Comment>生成代码方式（0zip压缩包 1自定义路径）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1147" parent="300" name="gen_path">
      <Comment>生成路径（不填默认项目路径）</Comment>
      <DefaultExpression>&apos;/&apos;</DefaultExpression>
      <Position>14</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1148" parent="300" name="options">
      <Comment>其它生成选项</Comment>
      <Position>15</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1149" parent="300" name="create_by">
      <Comment>创建者</Comment>
      <Position>16</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1150" parent="300" name="create_time">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1151" parent="300" name="update_by">
      <Comment>更新者</Comment>
      <Position>18</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1152" parent="300" name="update_time">
      <Comment>更新时间</Comment>
      <Position>19</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1153" parent="300" name="remark">
      <Comment>备注</Comment>
      <Position>20</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="1154" parent="300" name="PRIMARY">
      <ColNames>table_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1155" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1156" parent="301" name="column_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1157" parent="301" name="table_id">
      <Comment>归属表编号</Comment>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1158" parent="301" name="column_name">
      <Comment>列名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1159" parent="301" name="column_comment">
      <Comment>列描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1160" parent="301" name="column_type">
      <Comment>列类型</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1161" parent="301" name="java_type">
      <Comment>JAVA类型</Comment>
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1162" parent="301" name="java_field">
      <Comment>JAVA字段名</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1163" parent="301" name="is_pk">
      <Comment>是否主键（1是）</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1164" parent="301" name="is_increment">
      <Comment>是否自增（1是）</Comment>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1165" parent="301" name="is_required">
      <Comment>是否必填（1是）</Comment>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1166" parent="301" name="is_insert">
      <Comment>是否为插入字段（1是）</Comment>
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1167" parent="301" name="is_edit">
      <Comment>是否编辑字段（1是）</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1168" parent="301" name="is_list">
      <Comment>是否列表字段（1是）</Comment>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1169" parent="301" name="is_query">
      <Comment>是否查询字段（1是）</Comment>
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1170" parent="301" name="query_type">
      <Comment>查询方式（等于、不等于、大于、小于、范围）</Comment>
      <DefaultExpression>&apos;EQ&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1171" parent="301" name="html_type">
      <Comment>显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）</Comment>
      <Position>16</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1172" parent="301" name="dict_type">
      <Comment>字典类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1173" parent="301" name="sort">
      <Comment>排序</Comment>
      <Position>18</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1174" parent="301" name="create_by">
      <Comment>创建者</Comment>
      <Position>19</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1175" parent="301" name="create_time">
      <Comment>创建时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1176" parent="301" name="update_by">
      <Comment>更新者</Comment>
      <Position>21</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1177" parent="301" name="update_time">
      <Comment>更新时间</Comment>
      <Position>22</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <index id="1178" parent="301" name="PRIMARY">
      <ColNames>column_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1179" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1180" parent="302" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>物流ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1181" parent="302" name="logistics_number">
      <Comment>物流单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1182" parent="302" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1183" parent="302" name="logistics_company">
      <Comment>物流公司</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1184" parent="302" name="logistics_status">
      <Comment>物流状态：pending-待发货，shipped-已发货，in_transit-运输中，delivered-已送达</Comment>
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1185" parent="302" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1186" parent="302" name="shipping_date">
      <Comment>发货时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1187" parent="302" name="estimated_delivery_time">
      <Comment>预计送达时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1188" parent="302" name="actual_delivery_time">
      <Comment>实际送达时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1189" parent="302" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1190" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1191" parent="302" name="idx_logistics_number">
      <ColNames>logistics_number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1192" parent="302" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1193" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1194" parent="302" name="idx_logistics_number">
      <UnderlyingIndexName>idx_logistics_number</UnderlyingIndexName>
    </key>
    <column id="1195" parent="303" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1196" parent="303" name="logistics_number">
      <Comment>物流单号</Comment>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1197" parent="303" name="order_id">
      <Comment>订单ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1198" parent="303" name="logistics_company">
      <Comment>物流公司</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1199" parent="303" name="logistics_status">
      <Comment>物流状态</Comment>
      <Position>5</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1200" parent="303" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1201" parent="303" name="shipping_date">
      <Comment>发货日期</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1202" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1203" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1204" parent="304" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1205" parent="304" name="logistics_id">
      <Comment>物流记录ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1206" parent="304" name="tracking_number">
      <Comment>物流单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1207" parent="304" name="old_status">
      <Comment>原状态</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1208" parent="304" name="new_status">
      <Comment>新状态</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1209" parent="304" name="status_desc">
      <Comment>状态描述</Comment>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1210" parent="304" name="location">
      <Comment>当前位置</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1211" parent="304" name="operator_type">
      <Comment>操作类型：system-系统，admin-管理员，api-接口</Comment>
      <DefaultExpression>&apos;system&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1212" parent="304" name="operator_name">
      <Comment>操作人</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1213" parent="304" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1214" parent="304" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1215" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1216" parent="304" name="idx_logistics_id">
      <ColNames>logistics_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1217" parent="304" name="idx_tracking_number">
      <ColNames>tracking_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1218" parent="304" name="idx_status">
      <ColNames>old_status
new_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1219" parent="304" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1220" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1221" parent="305" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1222" parent="305" name="tracking_id">
      <Comment>物流跟踪ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1223" parent="305" name="tracking_number">
      <Comment>物流单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1224" parent="305" name="trace_time">
      <Comment>轨迹时间</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1225" parent="305" name="trace_location">
      <Comment>轨迹位置</Comment>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1226" parent="305" name="trace_status">
      <Comment>轨迹状态</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1227" parent="305" name="trace_desc">
      <Comment>轨迹描述</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1228" parent="305" name="operator">
      <Comment>操作员</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1229" parent="305" name="phone">
      <Comment>联系电话</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1230" parent="305" name="sort_order">
      <Comment>排序顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1231" parent="305" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1232" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1233" parent="305" name="idx_tracking_id">
      <ColNames>tracking_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1234" parent="305" name="idx_tracking_number">
      <ColNames>tracking_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1235" parent="305" name="idx_trace_time">
      <ColNames>trace_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1236" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1237" parent="306" name="shop_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>店铺ID（自增主键）</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="1238" parent="306" name="shop_name">
      <Comment>店铺名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1239" parent="306" name="mobile">
      <Comment>联系手机号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1240" parent="306" name="contact_name">
      <Comment>联系人姓名</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1241" parent="306" name="province">
      <Comment>联系人所在省份</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1242" parent="306" name="city">
      <Comment>联系人所在城市</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1243" parent="306" name="detail_address">
      <Comment>详细地址</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1244" parent="306" name="status">
      <Comment>账号状态：0-正常 1-禁用</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1245" parent="306" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1246" parent="306" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1247" parent="306" name="PRIMARY">
      <ColNames>shop_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1248" parent="306" name="uk_mobile">
      <ColNames>mobile</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1249" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1250" parent="306" name="uk_mobile">
      <UnderlyingIndexName>uk_mobile</UnderlyingIndexName>
    </key>
    <column id="1251" parent="307" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="1252" parent="307" name="order_no">
      <Comment>订单号（格式：ORD-YYYYMMDD-XXXXXX）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1253" parent="307" name="transaction_no">
      <Comment>支付流水号</Comment>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1254" parent="307" name="product_info">
      <Comment>商品信息（包含名称、规格、单价、数量等）</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1255" parent="307" name="shipping_fee">
      <Comment>运费（USD，精确到分）</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1256" parent="307" name="total_amount">
      <Comment>总金额（USD，精确到分）</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1257" parent="307" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1258" parent="307" name="tracking_no">
      <Comment>物流单号</Comment>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1259" parent="307" name="status">
      <Comment>订单状态（0:待支付 1:待分销商上传标签 2:待确认 3:待发货 4:待收货 5:已完成 6:已取消）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(3) unsigned|0s</StoredType>
    </column>
    <column id="1260" parent="307" name="order_source">
      <Comment>订单来源（APP/PC/H5/第三方平台）</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1261" parent="307" name="delivery_method">
      <Comment>配送方式（快递/平邮/自提）</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1262" parent="307" name="shipping_info">
      <Comment>收货信息（包含姓名、电话、地址、邮编等）</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1263" parent="307" name="created_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1264" parent="307" name="updated_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1265" parent="307" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1266" parent="307" name="order_no">
      <ColNames>order_no</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1267" parent="307" name="idx_order_no">
      <ColNames>order_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1268" parent="307" name="idx_payment_time">
      <ColNames>payment_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1269" parent="307" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1270" parent="307" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1271" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1272" parent="307" name="order_no">
      <UnderlyingIndexName>order_no</UnderlyingIndexName>
    </key>
    <column id="1273" parent="308" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID，自增唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1274" parent="308" name="product_code">
      <Comment>产品代码，唯一标识一个产品</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1275" parent="308" name="product_title">
      <Comment>产品标题，简要描述产品名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1276" parent="308" name="product_category">
      <Comment>产品类别，标识产品所属的分类</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1277" parent="308" name="main_image">
      <Comment>产品主图，展示产品的图片链接</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1278" parent="308" name="price">
      <Comment>产品价格，支持精确的货币计算</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1279" parent="308" name="sales_last_7_days">
      <Comment>过去7天的销售量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1280" parent="308" name="sales_last_30_days">
      <Comment>过去30天的销售量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1281" parent="308" name="status">
      <Comment>产品状态，如“上架”、“下架”等</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1282" parent="308" name="nbs">
      <Comment>NBS（可能是一个特定的业务标识或分类）</Comment>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1283" parent="308" name="created_at">
      <Comment>产品创建时间，记录产品被添加到系统的时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1284" parent="308" name="product_description">
      <Comment>产品描述，详细描述产品的特性和功能</Comment>
      <Position>12</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1285" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1286" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1287" parent="309" name="msgID">
      <Comment>消息唯一标识</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1288" parent="309" name="msgType">
      <Comment>消息类型：系统公告、商家消息、用户消息</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1289" parent="309" name="title">
      <Comment>消息标题</Comment>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1290" parent="309" name="content">
      <Comment>消息内容</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1291" parent="309" name="senderId">
      <Comment>发送者ID</Comment>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1292" parent="309" name="senderName">
      <Comment>发送者名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1293" parent="309" name="sendTime">
      <Comment>发送时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1294" parent="309" name="recipientType">
      <Comment>接收者类型：merchant(商家)、user(用户)、all(全部)</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1295" parent="309" name="recipientId">
      <Comment>接收者ID</Comment>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1296" parent="309" name="createTime">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1297" parent="309" name="updateTime">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1298" parent="309" name="isRead">
      <Comment>是否已读：0未读，1已读</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="1299" parent="309" name="PRIMARY">
      <ColNames>msgID</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1300" parent="309" name="idx_sender">
      <ColNames>senderId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1301" parent="309" name="idx_sendTime">
      <ColNames>sendTime</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1302" parent="309" name="idx_recipient">
      <ColNames>recipientType
recipientId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1303" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1304" parent="310" name="id">
      <AutoIncrement>598</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1305" parent="310" name="msgID">
      <Comment>消息ID</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1306" parent="310" name="recipientType">
      <Comment>接收者类型：merchant(商家)、user(用户)、Administrator(管理员)</Comment>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1307" parent="310" name="recipientId">
      <Comment>接收者ID</Comment>
      <Position>4</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1308" parent="310" name="isRead">
      <Comment>是否已读：0未读，1已读</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1309" parent="310" name="createTime">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1310" parent="310" name="updateTime">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1311" parent="310" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1312" parent="310" name="idx_msgID">
      <ColNames>msgID</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1313" parent="310" name="idx_recipient">
      <ColNames>recipientType
recipientId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1314" parent="310" name="idx_isRead">
      <ColNames>isRead</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1315" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1316" parent="311" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1317" parent="311" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1318" parent="311" name="address_type">
      <Comment>地址类型：1-收货地址，2-发货地址</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1319" parent="311" name="consignee_name">
      <Comment>收货人姓名</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1320" parent="311" name="phone">
      <Comment>手机号</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1321" parent="311" name="province_code">
      <Comment>省份编码</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1322" parent="311" name="province_name">
      <Comment>省份名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1323" parent="311" name="city_code">
      <Comment>城市编码</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1324" parent="311" name="city_name">
      <Comment>城市名称</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1325" parent="311" name="district_code">
      <Comment>区县编码</Comment>
      <Position>10</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1326" parent="311" name="district_name">
      <Comment>区县名称</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1327" parent="311" name="detail_address">
      <Comment>详细地址</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1328" parent="311" name="postal_code">
      <Comment>邮政编码</Comment>
      <Position>13</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1329" parent="311" name="is_default">
      <Comment>是否默认地址：0-否，1-是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1330" parent="311" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1331" parent="311" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1332" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1333" parent="311" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1334" parent="311" name="idx_address_type">
      <ColNames>address_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1335" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1336" parent="312" name="id">
      <AutoIncrement>30</AutoIncrement>
      <Comment>详情ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1337" parent="312" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1338" parent="312" name="product_id">
      <Comment>商品ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1339" parent="312" name="product_name">
      <Comment>商品名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1340" parent="312" name="product_image">
      <Comment>商品图片</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1341" parent="312" name="product_spec">
      <Comment>商品规格</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1342" parent="312" name="quantity">
      <Comment>购买数量</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1343" parent="312" name="unit_price">
      <Comment>单价</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1344" parent="312" name="discount">
      <Comment>折扣金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>9</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1345" parent="312" name="subtotal">
      <Comment>小计金额</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1346" parent="312" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1347" parent="312" name="update_time">
      <Comment>更新时间</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1348" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1349" parent="312" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1350" parent="312" name="idx_product_id">
      <ColNames>product_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1351" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1352" parent="313" name="id">
      <AutoIncrement>13</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1353" parent="313" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1354" parent="313" name="order_number">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1355" parent="313" name="operation">
      <Comment>操作类型：create, pay, ship, complete, cancel, refund, approve, reject, edit, update_logistics, delivered</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1356" parent="313" name="old_status">
      <Comment>原状态</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1357" parent="313" name="new_status">
      <Comment>新状态</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1358" parent="313" name="operator_type">
      <Comment>操作者类型：admin, system, user</Comment>
      <DefaultExpression>&apos;admin&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1359" parent="313" name="operator_id">
      <Comment>操作者ID</Comment>
      <Position>8</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1360" parent="313" name="operator_name">
      <Comment>操作者姓名</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1361" parent="313" name="remark">
      <Comment>备注信息</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1362" parent="313" name="details">
      <Comment>详细信息（JSON格式）</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1363" parent="313" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <foreign-key id="1364" parent="313" name="fk_order_log_order_id">
      <ColNames>order_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>orders</RefTableName>
    </foreign-key>
    <index id="1365" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1366" parent="313" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1367" parent="313" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1368" parent="313" name="idx_operation">
      <ColNames>operation</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1369" parent="313" name="idx_order_log_status">
      <ColNames>old_status
new_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1370" parent="313" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1371" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1372" parent="314" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1373" parent="314" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1374" parent="314" name="order_number">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1375" parent="314" name="tracking_number">
      <Comment>物流单号</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1376" parent="314" name="carrier_code">
      <Comment>运输商代码</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1377" parent="314" name="carrier_name">
      <Comment>运输商名称</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1378" parent="314" name="current_status">
      <Comment>当前物流状态</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1379" parent="314" name="current_status_desc">
      <Comment>当前状态描述</Comment>
      <Position>8</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1380" parent="314" name="latest_location">
      <Comment>最新位置</Comment>
      <Position>9</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1381" parent="314" name="estimated_delivery_time">
      <Comment>预计送达时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1382" parent="314" name="actual_delivery_time">
      <Comment>实际送达时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1383" parent="314" name="ship_time">
      <Comment>发货时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1384" parent="314" name="last_update_time">
      <Comment>最后更新时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1385" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1386" parent="314" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1387" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1388" parent="314" name="uk_order_tracking">
      <ColNames>order_id
tracking_number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1389" parent="314" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1390" parent="314" name="idx_tracking_number">
      <ColNames>tracking_number</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1391" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1392" parent="314" name="uk_order_tracking">
      <UnderlyingIndexName>uk_order_tracking</UnderlyingIndexName>
    </key>
    <column id="1393" parent="315" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1394" parent="315" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1395" parent="315" name="order_number">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1396" parent="315" name="payment_method">
      <Comment>支付方式：wechat-微信，alipay-支付宝，bank-银行卡</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1397" parent="315" name="payment_channel">
      <Comment>支付渠道</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1398" parent="315" name="transaction_id">
      <Comment>第三方交易号</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1399" parent="315" name="payment_amount">
      <Comment>支付金额</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1400" parent="315" name="currency">
      <Comment>货币类型</Comment>
      <DefaultExpression>&apos;CNY&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1401" parent="315" name="payment_status">
      <Comment>支付状态：pending-待支付，success-成功，failed-失败，refunded-已退款</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1402" parent="315" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1403" parent="315" name="refund_amount">
      <Comment>退款金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>11</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1404" parent="315" name="refund_time">
      <Comment>退款时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1405" parent="315" name="payment_desc">
      <Comment>支付描述</Comment>
      <Position>13</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1406" parent="315" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1407" parent="315" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1408" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1409" parent="315" name="uk_order_payment">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1410" parent="315" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1411" parent="315" name="idx_transaction_id">
      <ColNames>transaction_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1412" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1413" parent="315" name="uk_order_payment">
      <UnderlyingIndexName>uk_order_payment</UnderlyingIndexName>
    </key>
    <column id="1414" parent="316" name="id">
      <AutoIncrement>32</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1415" parent="316" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1416" parent="316" name="order_number">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1417" parent="316" name="seller_id">
      <Comment>商家ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1418" parent="316" name="seller_name">
      <Comment>商家名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1419" parent="316" name="pay_time">
      <Comment>支付时间</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1420" parent="316" name="billing_date">
      <Comment>账单日期(支付时间+14天)</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1421" parent="316" name="billing_cycle">
      <Comment>账单周期(格式:2025-01)</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(7)|0s</StoredType>
    </column>
    <column id="1422" parent="316" name="settlement_date">
      <Comment>回款日期</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1423" parent="316" name="order_amount">
      <Comment>订单金额</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1424" parent="316" name="settlement_amount">
      <Comment>回款金额</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1425" parent="316" name="settlement_status">
      <Comment>回款状态(0-未到期,1-待回款,2-已回款)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1426" parent="316" name="settlement_time">
      <Comment>实际回款时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1427" parent="316" name="remark">
      <Comment>备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1428" parent="316" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1429" parent="316" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1430" parent="316" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1431" parent="316" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1432" parent="316" name="idx_seller_id">
      <ColNames>seller_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1433" parent="316" name="idx_billing_cycle">
      <ColNames>billing_cycle</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1434" parent="316" name="idx_settlement_date">
      <ColNames>settlement_date</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1435" parent="316" name="idx_settlement_status">
      <ColNames>settlement_status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1436" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1437" parent="317" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1438" parent="317" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1439" parent="317" name="order_number">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1440" parent="317" name="old_status">
      <Comment>原状态</Comment>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1441" parent="317" name="new_status">
      <Comment>新状态</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1442" parent="317" name="status_desc">
      <Comment>状态描述</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1443" parent="317" name="operator_type">
      <Comment>操作者类型：user-用户，admin-管理员，system-系统</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1444" parent="317" name="operator_id">
      <Comment>操作者ID</Comment>
      <Position>8</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1445" parent="317" name="operator_name">
      <Comment>操作者姓名</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1446" parent="317" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1447" parent="317" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1448" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1449" parent="317" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1450" parent="317" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1451" parent="317" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1452" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1453" parent="318" name="id">
      <AutoIncrement>39</AutoIncrement>
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1454" parent="318" name="number">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1455" parent="318" name="buyer_id">
      <Comment>买家ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1456" parent="318" name="address_id">
      <Comment>收货地址ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1457" parent="318" name="shipping_method_id">
      <Comment>配送方式ID</Comment>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1458" parent="318" name="status">
      <Comment>订单状态：1-待支付，2-已支付，3-已取消，4-已发货，5-已完成，6-已关闭</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1459" parent="318" name="amount">
      <Comment>订单总金额</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1460" parent="318" name="order_time">
      <Comment>下单时间</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1461" parent="318" name="pay_time">
      <Comment>支付时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1462" parent="318" name="checkout_time">
      <Comment>结算时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1463" parent="318" name="ship_time">
      <Comment>发货时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1464" parent="318" name="complete_time">
      <Comment>完成时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1465" parent="318" name="cancel_time">
      <Comment>取消时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1466" parent="318" name="pay_method">
      <Comment>支付方式：1-微信支付，2-支付宝，3-银行卡</Comment>
      <Position>14</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1467" parent="318" name="payment_transaction_id">
      <Comment>支付交易ID</Comment>
      <Position>15</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1468" parent="318" name="order_remark">
      <Comment>订单备注</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1469" parent="318" name="cancel_reason">
      <Comment>取消原因</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1470" parent="318" name="rejection_reason">
      <Comment>拒绝原因</Comment>
      <Position>18</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1471" parent="318" name="cancel_request">
      <Comment>是否申请取消</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>19</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1472" parent="318" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1473" parent="318" name="update_time">
      <Comment>更新时间</Comment>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1474" parent="318" name="refund_status">
      <Comment>退款状态：0-无退款，1-退款申请中，2-部分退款，3-全额退款</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>22</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1475" parent="318" name="refund_amount">
      <Comment>退款金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>23</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1476" parent="318" name="refund_time">
      <Comment>退款时间</Comment>
      <Position>24</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1477" parent="318" name="tracking_number">
      <Comment>物流单号</Comment>
      <Position>25</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1478" parent="318" name="courier_code">
      <Comment>快递公司代码</Comment>
      <Position>26</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1479" parent="318" name="courier_name">
      <Comment>快递公司名称</Comment>
      <Position>27</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1480" parent="318" name="estimated_delivery_time">
      <Comment>预计送达时间</Comment>
      <Position>28</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1481" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1482" parent="318" name="idx_number">
      <ColNames>number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1483" parent="318" name="idx_orders_buyer_status">
      <ColNames>buyer_id
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1484" parent="318" name="idx_buyer_id">
      <ColNames>buyer_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1485" parent="318" name="idx_orders_status_create_time">
      <ColNames>status
create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1486" parent="318" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1487" parent="318" name="idx_orders_ship_time">
      <ColNames>ship_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1488" parent="318" name="idx_orders_complete_time">
      <ColNames>complete_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1489" parent="318" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1490" parent="318" name="idx_orders_refund_status">
      <ColNames>refund_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1491" parent="318" name="idx_orders_tracking_number">
      <ColNames>tracking_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1492" parent="318" name="idx_orders_courier_code">
      <ColNames>courier_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1493" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1494" parent="318" name="idx_number">
      <UnderlyingIndexName>idx_number</UnderlyingIndexName>
    </key>
    <column id="1495" parent="319" name="id">
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1496" parent="319" name="name">
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1497" parent="319" name="create_time">
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1498" parent="319" name="update_time">
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1499" parent="319" name="status">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1500" parent="320" name="id">
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1501" parent="320" name="order_id">
      <Position>2</Position>
      <StoredType>bigint(11)|0s</StoredType>
    </column>
    <column id="1502" parent="320" name="payment_method">
      <Position>3</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="1503" parent="320" name="amount">
      <Position>4</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1504" parent="320" name="transaction_status">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1505" parent="320" name="transaction_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1506" parent="320" name="create_time">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1507" parent="320" name="update_time">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1508" parent="321" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1509" parent="321" name="name">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1510" parent="321" name="sort">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1511" parent="321" name="show_status">
      <Position>4</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="1512" parent="321" name="logo">
      <Comment>品牌logo</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1513" parent="321" name="create_by">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1514" parent="321" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1515" parent="321" name="update_by">
      <Comment>修改人</Comment>
      <Position>8</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1516" parent="321" name="update_time">
      <Comment>修改时间</Comment>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1517" parent="321" name="inventory">
      <Comment>库存</Comment>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="1518" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1519" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1520" parent="322" name="id">
      <AutoIncrement>364</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1521" parent="322" name="product_snapshot_id">
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1522" parent="322" name="brand_id">
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1523" parent="322" name="creator_user_id">
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1524" parent="322" name="category_id">
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1525" parent="322" name="out_product_id">
      <Comment>商品编码</Comment>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1526" parent="322" name="name">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1527" parent="322" name="pic">
      <Comment>主图</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1528" parent="322" name="album_pics">
      <Comment>画册图片，连产品图片限制为5张，以逗号分割</Comment>
      <Position>9</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1529" parent="322" name="publish_status">
      <Comment>上架状态：0-&gt;下架；1-&gt;上架</Comment>
      <Position>10</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="1530" parent="322" name="sort">
      <Comment>排序</Comment>
      <Position>11</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1531" parent="322" name="price">
      <Position>12</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1532" parent="322" name="unit">
      <Comment>单位</Comment>
      <Position>13</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1533" parent="322" name="weight">
      <Comment>商品重量，默认为克</Comment>
      <Position>14</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1534" parent="322" name="detail_html">
      <Comment>产品详情网页内容</Comment>
      <Position>15</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1535" parent="322" name="detail_mobile_html">
      <Comment>移动端网页详情</Comment>
      <Position>16</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1536" parent="322" name="brand_name">
      <Comment>品牌名称</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1537" parent="322" name="product_category_name">
      <Comment>商品分类名称</Comment>
      <Position>18</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1538" parent="322" name="create_by">
      <Comment>创建人</Comment>
      <Position>19</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1539" parent="322" name="create_time">
      <Comment>创建时间</Comment>
      <Position>20</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1540" parent="322" name="update_by">
      <Comment>修改人</Comment>
      <Position>21</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1541" parent="322" name="update_time">
      <Comment>修改时间</Comment>
      <Position>22</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1542" parent="322" name="product_attr">
      <Comment>商品销售属性，json格式</Comment>
      <Position>23</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1543" parent="322" name="status">
      <Comment>商品是否被允许上架</Comment>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1544" parent="322" name="inventory">
      <Comment>库存</Comment>
      <Position>25</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1545" parent="322" name="introduct_pics">
      <Comment>图文介绍照片</Comment>
      <Position>26</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1546" parent="322" name="pdf_document">
      <Comment>pdf文件</Comment>
      <Position>27</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <index id="1547" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1548" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1549" parent="323" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1550" parent="323" name="parent_id">
      <Comment>上机分类的编号：0表示一级分类</Comment>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1551" parent="323" name="name">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1552" parent="323" name="level">
      <Comment>分类级别：0-&gt;1级；1-&gt;2级</Comment>
      <Position>4</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="1553" parent="323" name="show_status">
      <Comment>显示状态：0-&gt;不显示；1-&gt;显示</Comment>
      <Position>5</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="1554" parent="323" name="sort">
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1555" parent="323" name="icon">
      <Comment>图标</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1556" parent="323" name="create_by">
      <Comment>创建人</Comment>
      <Position>8</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1557" parent="323" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1558" parent="323" name="update_by">
      <Comment>修改人</Comment>
      <Position>10</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1559" parent="323" name="update_time">
      <Comment>修改时间</Comment>
      <Position>11</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <index id="1560" parent="323" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1561" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1562" parent="324" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1563" parent="324" name="product_id">
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1564" parent="324" name="brand_id">
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1565" parent="324" name="category_id">
      <Position>4</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1566" parent="324" name="out_product_id">
      <Comment>商品编码</Comment>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1567" parent="324" name="name">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1568" parent="324" name="pic">
      <Comment>主图</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1569" parent="324" name="album_pics">
      <Comment>画册图片，连产品图片限制为5张，以逗号分割</Comment>
      <Position>8</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1570" parent="324" name="publish_status">
      <Comment>上架状态：0-&gt;下架；1-&gt;上架</Comment>
      <Position>9</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="1571" parent="324" name="sort">
      <Comment>排序</Comment>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1572" parent="324" name="price">
      <Position>11</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1573" parent="324" name="unit">
      <Comment>单位</Comment>
      <Position>12</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1574" parent="324" name="weight">
      <Comment>商品重量，默认为克</Comment>
      <Position>13</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1575" parent="324" name="detail_html">
      <Comment>产品详情网页内容</Comment>
      <Position>14</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1576" parent="324" name="detail_mobile_html">
      <Comment>移动端网页详情</Comment>
      <Position>15</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1577" parent="324" name="brand_name">
      <Comment>品牌名称</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1578" parent="324" name="product_category_name">
      <Comment>商品分类名称</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1579" parent="324" name="create_by">
      <Comment>创建人</Comment>
      <Position>18</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1580" parent="324" name="create_time">
      <Comment>创建时间</Comment>
      <Position>19</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1581" parent="324" name="update_by">
      <Comment>修改人</Comment>
      <Position>20</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1582" parent="324" name="update_time">
      <Comment>修改时间</Comment>
      <Position>21</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1583" parent="324" name="product_attr">
      <Comment>商品销售属性，json格式</Comment>
      <Position>22</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="1584" parent="324" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1585" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1586" parent="325" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1587" parent="325" name="sku_snapshot_id">
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1588" parent="325" name="product_id">
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1589" parent="325" name="out_sku_id">
      <Comment>sku编码</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1590" parent="325" name="price">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1591" parent="325" name="pic">
      <Comment>展示图片</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1592" parent="325" name="stock">
      <Comment>库存</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1593" parent="325" name="sp_data">
      <Comment>商品销售属性，json格式</Comment>
      <Position>8</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1594" parent="325" name="create_by">
      <Comment>创建人</Comment>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1595" parent="325" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1596" parent="325" name="update_by">
      <Comment>修改人</Comment>
      <Position>11</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1597" parent="325" name="update_time">
      <Comment>修改时间</Comment>
      <Position>12</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <index id="1598" parent="325" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1599" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1600" parent="326" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1601" parent="326" name="sku_id">
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1602" parent="326" name="product_id">
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1603" parent="326" name="out_sku_id">
      <Comment>sku编码</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1604" parent="326" name="price">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1605" parent="326" name="pic">
      <Comment>展示图片</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1606" parent="326" name="sp_data">
      <Comment>商品销售属性，json格式</Comment>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1607" parent="326" name="create_by">
      <Comment>创建人</Comment>
      <Position>8</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1608" parent="326" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="1609" parent="326" name="update_by">
      <Comment>修改人</Comment>
      <Position>10</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1610" parent="326" name="update_time">
      <Comment>修改时间</Comment>
      <Position>11</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <index id="1611" parent="326" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1612" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1613" parent="327" name="id">
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1614" parent="327" name="buyer_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1615" parent="327" name="name">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1616" parent="327" name="phone_number">
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1617" parent="327" name="address_detail">
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1618" parent="327" name="city">
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1619" parent="327" name="province">
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1620" parent="327" name="postal_code">
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1621" parent="327" name="country">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1622" parent="327" name="is_default">
      <Position>10</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1623" parent="328" name="product_id">
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1624" parent="328" name="category_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1625" parent="329" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1626" parent="329" name="buyer_id">
      <Position>2</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1627" parent="329" name="storeName">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1628" parent="329" name="product_id">
      <Position>4</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1629" parent="329" name="product_name">
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1630" parent="329" name="collect_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1631" parent="329" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1632" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1633" parent="330" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1634" parent="330" name="buyer_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1635" parent="330" name="product_id">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1636" parent="330" name="buyer_name">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1637" parent="330" name="store_name">
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1638" parent="330" name="product_name">
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1639" parent="330" name="rating">
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1640" parent="330" name="comment">
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1641" parent="330" name="is_recommended">
      <Position>9</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1642" parent="330" name="comment_time">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1643" parent="330" name="status">
      <Position>11</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <index id="1644" parent="330" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1645" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1646" parent="331" name="id">
      <AutoIncrement>23</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1647" parent="331" name="refund_no">
      <Comment>退款申请单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1648" parent="331" name="order_id">
      <Comment>关联的订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1649" parent="331" name="order_number">
      <Comment>关联的订单号</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1650" parent="331" name="buyer_id">
      <Comment>申请人ID</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1651" parent="331" name="refund_amount">
      <Comment>申请退款金额</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1652" parent="331" name="refund_reason">
      <Comment>退款理由</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1653" parent="331" name="refund_type">
      <Comment>退款类型：1-仅退款，2-退货退款</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1654" parent="331" name="application_status">
      <Comment>申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1655" parent="331" name="need_approval">
      <Comment>是否需要审核：0-不需要，1-需要</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1656" parent="331" name="approval_status">
      <Comment>审核状态：1-待审核，2-审核通过，3-审核拒绝</Comment>
      <Position>11</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1657" parent="331" name="approver_id">
      <Comment>审核人ID</Comment>
      <Position>12</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1658" parent="331" name="approval_time">
      <Comment>审核时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1659" parent="331" name="approval_remark">
      <Comment>审核备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1660" parent="331" name="refund_method">
      <Comment>退款方式：1-原路退回，2-余额退款</Comment>
      <Position>15</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1661" parent="331" name="actual_refund_amount">
      <Comment>实际退款金额</Comment>
      <Position>16</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1662" parent="331" name="refund_time">
      <Comment>退款完成时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1663" parent="331" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1664" parent="331" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1665" parent="331" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1666" parent="331" name="refund_no">
      <ColNames>refund_no</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1667" parent="331" name="idx_refund_no">
      <ColNames>refund_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1668" parent="331" name="idx_refund_order_status">
      <ColNames>order_id
application_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1669" parent="331" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1670" parent="331" name="idx_buyer_id">
      <ColNames>buyer_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1671" parent="331" name="idx_application_status">
      <ColNames>application_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1672" parent="331" name="idx_refund_refund_time">
      <ColNames>refund_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1673" parent="331" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1674" parent="331" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1675" parent="331" name="refund_no">
      <UnderlyingIndexName>refund_no</UnderlyingIndexName>
    </key>
    <column id="1676" parent="332" name="id">
      <AutoIncrement>14</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1677" parent="332" name="refund_application_id">
      <Comment>退款申请ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1678" parent="332" name="refund_no">
      <Comment>退款申请单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1679" parent="332" name="approver_id">
      <Comment>审核人ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1680" parent="332" name="approver_name">
      <Comment>审核人姓名</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1681" parent="332" name="approval_result">
      <Comment>审核结果：1-通过，2-拒绝</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1682" parent="332" name="approval_remark">
      <Comment>审核备注</Comment>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1683" parent="332" name="create_time">
      <Comment>审核时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <foreign-key id="1684" parent="332" name="refund_approval_record_ibfk_1">
      <ColNames>refund_application_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>refund_application</RefTableName>
    </foreign-key>
    <index id="1685" parent="332" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1686" parent="332" name="idx_refund_application_id">
      <ColNames>refund_application_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1687" parent="332" name="idx_refund_no">
      <ColNames>refund_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1688" parent="332" name="idx_approver_id">
      <ColNames>approver_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1689" parent="332" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1690" parent="333" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1691" parent="333" name="refund_application_id">
      <Comment>退款申请ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1692" parent="333" name="refund_no">
      <Comment>退款单号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1693" parent="333" name="old_status">
      <Comment>原状态</Comment>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1694" parent="333" name="new_status">
      <Comment>新状态</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1695" parent="333" name="operation">
      <Comment>操作类型</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1696" parent="333" name="operator_type">
      <Comment>操作类型：user-用户，admin-管理员，system-系统</Comment>
      <DefaultExpression>&apos;admin&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1697" parent="333" name="operator_id">
      <Comment>操作人ID</Comment>
      <Position>8</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1698" parent="333" name="operator_name">
      <Comment>操作人姓名</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1699" parent="333" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1700" parent="333" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1701" parent="333" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1702" parent="333" name="idx_refund_application_id">
      <ColNames>refund_application_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1703" parent="333" name="idx_refund_no">
      <ColNames>refund_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1704" parent="333" name="idx_status">
      <ColNames>old_status
new_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1705" parent="333" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1706" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1707" parent="334" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1708" parent="334" name="product_id">
      <Comment>商品ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1709" parent="334" name="shop_id">
      <Comment>店铺ID</Comment>
      <Position>3</Position>
      <StoredType>int(10) unsigned|0s</StoredType>
    </column>
    <column id="1710" parent="334" name="return_time">
      <Comment>退货时间</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1711" parent="334" name="user_id">
      <Comment>用户ID</Comment>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1712" parent="334" name="return_count">
      <Comment>退货数量</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <foreign-key id="1713" parent="334" name="fk_return_product_id">
      <ColNames>product_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>pms_product</RefTableName>
    </foreign-key>
    <foreign-key id="1714" parent="334" name="fk_return_shop_id">
      <ColNames>shop_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>shop_info</RefTableName>
    </foreign-key>
    <index id="1715" parent="334" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1716" parent="334" name="idx_product_id">
      <ColNames>product_id</ColNames>
      <Comment>商品ID索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="1717" parent="334" name="idx_shop_id">
      <ColNames>shop_id</ColNames>
      <Comment>店铺ID索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="1718" parent="334" name="idx_return_time">
      <ColNames>return_time</ColNames>
      <Comment>退货时间索引</Comment>
      <Type>btree</Type>
    </index>
    <key id="1719" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1720" parent="335" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1721" parent="335" name="leader_id">
      <Comment>团长ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1722" parent="335" name="date">
      <Comment>日期</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1723" parent="335" name="sales">
      <Comment>销售额</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1724" parent="335" name="commission">
      <Comment>佣金</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <foreign-key id="1725" parent="335" name="sales_trend_ibfk_1">
      <ColNames>leader_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>team_leader</RefTableName>
    </foreign-key>
    <index id="1726" parent="335" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1727" parent="335" name="uk_leader_date">
      <ColNames>leader_id
date</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1728" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1729" parent="335" name="uk_leader_date">
      <UnderlyingIndexName>uk_leader_date</UnderlyingIndexName>
    </key>
    <column id="1730" parent="336" name="id">
      <AutoIncrement>160</AutoIncrement>
      <Comment>用户唯一标识，主键，自增</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1731" parent="336" name="account_name">
      <Comment>账号名称</Comment>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1732" parent="336" name="password">
      <Comment>密码</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1733" parent="336" name="gender">
      <Comment>性别</Comment>
      <Position>4</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1734" parent="336" name="phone">
      <Comment>手机号</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1735" parent="336" name="email">
      <Comment>邮箱</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1736" parent="336" name="account_status">
      <Comment>账号状态</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1737" parent="336" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1738" parent="336" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1739" parent="336" name="last_login_time">
      <Comment>最后登录时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1740" parent="336" name="certification_info">
      <Comment>认证信息</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1741" parent="336" name="photo_url">
      <Comment>用户头像</Comment>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1742" parent="336" name="user_role">
      <Comment>用户角色（严格枚举）</Comment>
      <DefaultExpression>&apos;普通用户&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>enum(&apos;超级管理员&apos;, &apos;管理员&apos;, &apos;普通用户&apos;)|0e</StoredType>
    </column>
    <column id="1743" parent="336" name="created_by">
      <Position>14</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1744" parent="336" name="is_sub_account">
      <Position>15</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1745" parent="336" name="remark">
      <Position>16</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1746" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1747" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1748" parent="337" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1749" parent="337" name="seller_id">
      <Comment>商家ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1750" parent="337" name="account_type">
      <Comment>账户类型(1-银行卡,2-支付宝,3-微信,4-其他)</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1751" parent="337" name="account_name">
      <Comment>账户名称/持卡人姓名</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1752" parent="337" name="account_number">
      <Comment>账户号码/卡号</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1753" parent="337" name="bank_name">
      <Comment>银行名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1754" parent="337" name="bank_code">
      <Comment>银行代码</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1755" parent="337" name="branch_name">
      <Comment>开户支行</Comment>
      <Position>8</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1756" parent="337" name="platform_name">
      <Comment>平台名称(支付宝/微信等)</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1757" parent="337" name="platform_account">
      <Comment>平台账号(手机号/邮箱等)</Comment>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1758" parent="337" name="is_default">
      <Comment>是否默认账户(0-否,1-是)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1759" parent="337" name="account_status">
      <Comment>账户状态(0-禁用,1-启用)</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1760" parent="337" name="verification_status">
      <Comment>验证状态(0-未验证,1-已验证)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="1761" parent="337" name="id_card_number">
      <Comment>身份证号码</Comment>
      <Position>14</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="1762" parent="337" name="phone">
      <Comment>预留手机号</Comment>
      <Position>15</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1763" parent="337" name="remark">
      <Comment>备注信息</Comment>
      <Position>16</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1764" parent="337" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1765" parent="337" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1766" parent="337" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1767" parent="337" name="uk_seller_account">
      <ColNames>seller_id
account_number
account_type</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1768" parent="337" name="idx_seller_id">
      <ColNames>seller_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1769" parent="337" name="idx_account_type">
      <ColNames>account_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1770" parent="337" name="idx_is_default">
      <ColNames>is_default</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1771" parent="337" name="idx_account_status">
      <ColNames>account_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1772" parent="337" name="idx_verification_status">
      <ColNames>verification_status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1773" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1774" parent="337" name="uk_seller_account">
      <UnderlyingIndexName>uk_seller_account</UnderlyingIndexName>
    </key>
    <column id="1775" parent="338" name="id">
      <AutoIncrement>860</AutoIncrement>
      <Comment>逻辑主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1776" parent="338" name="seller_id">
      <Comment>商家ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1777" parent="338" name="permission_code">
      <Comment>权限标识（路由name）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1778" parent="338" name="description">
      <Comment>权限描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <foreign-key id="1779" parent="338" name="seller_permission_ibfk_1">
      <ColNames>seller_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>seller</RefTableName>
    </foreign-key>
    <index id="1780" parent="338" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1781" parent="338" name="seller_id">
      <ColNames>seller_id
permission_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1782" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1783" parent="338" name="seller_id">
      <UnderlyingIndexName>seller_id</UnderlyingIndexName>
    </key>
    <column id="1784" parent="339" name="seller_id">
      <Comment>卖家ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1785" parent="339" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <foreign-key id="1786" parent="339" name="seller_role_ibfk_2">
      <ColNames>role_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>sys_role</RefTableName>
    </foreign-key>
    <index id="1787" parent="339" name="PRIMARY">
      <ColNames>seller_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1788" parent="339" name="role_id">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1789" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1790" parent="340" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1791" parent="340" name="config_key">
      <Comment>配置键</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1792" parent="340" name="config_value">
      <Comment>配置值</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1793" parent="340" name="config_desc">
      <Comment>配置描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1794" parent="340" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1795" parent="340" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1796" parent="340" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1797" parent="340" name="config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1798" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1799" parent="340" name="config_key">
      <UnderlyingIndexName>config_key</UnderlyingIndexName>
    </key>
    <column id="1800" parent="341" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>配送方式ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1801" parent="341" name="name">
      <Comment>配送方式名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1802" parent="341" name="description">
      <Comment>配送方式描述</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1803" parent="341" name="fee">
      <Comment>配送费用</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1804" parent="341" name="status">
      <Comment>状态：0-禁用，1-启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1805" parent="341" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1806" parent="341" name="update_time">
      <Comment>更新时间</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1807" parent="341" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1808" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1809" parent="342" name="id">
      <AutoIncrement>76</AutoIncrement>
      <Comment>店铺唯一ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(10) unsigned|0s</StoredType>
    </column>
    <column id="1810" parent="342" name="shop_name">
      <Comment>店铺名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1811" parent="342" name="company_name">
      <Comment>公司全称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1812" parent="342" name="business_license">
      <Comment>营业执照编号</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1813" parent="342" name="license_validity">
      <Comment>执照有效期</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1814" parent="342" name="company_intro">
      <Comment>公司简介</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1815" parent="342" name="contact_person">
      <Comment>联系人</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1816" parent="342" name="contact_phone">
      <Comment>联系方式</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1817" parent="342" name="province">
      <Comment>省份</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1818" parent="342" name="city">
      <Comment>城市</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1819" parent="342" name="district">
      <Comment>区县</Comment>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1820" parent="342" name="address_detail">
      <Comment>详细地址</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1821" parent="342" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1822" parent="342" name="last_updated">
      <Comment>最后更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1823" parent="342" name="business_img_url">
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1824" parent="342" name="warehouse_img_url1">
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1825" parent="342" name="warehouse_img_url2">
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1826" parent="342" name="warehouse_img_url3">
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1827" parent="342" name="seller_id">
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1828" parent="342" name="ID_card1">
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1829" parent="342" name="ID_card2">
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1830" parent="342" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1831" parent="342" name="business_license">
      <ColNames>business_license</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1832" parent="342" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1833" parent="342" name="business_license">
      <UnderlyingIndexName>business_license</UnderlyingIndexName>
    </key>
    <column id="1834" parent="343" name="id">
      <AutoIncrement>32</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1835" parent="343" name="buyer_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1836" parent="343" name="product_id">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1837" parent="343" name="product_name">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1838" parent="343" name="number">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1839" parent="343" name="amount">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1840" parent="343" name="price">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1841" parent="343" name="image">
      <Position>8</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <index id="1842" parent="343" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1843" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1844" parent="344" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1845" parent="344" name="seller_id">
      <Comment>卖家ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1846" parent="344" name="store_name">
      <Comment>店铺名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1847" parent="344" name="photo">
      <Comment>照片（logo）</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1848" parent="344" name="store_status">
      <Comment>店铺状态</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1849" parent="344" name="store_description">
      <Comment>店铺描述</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1850" parent="344" name="store_address">
      <Comment>店铺地址（可选填）</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1851" parent="344" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1852" parent="344" name="last_login_time">
      <Comment>最后登录时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1853" parent="344" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1854" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1855" parent="345" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>权限ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(10) unsigned|0s</StoredType>
    </column>
    <column id="1856" parent="345" name="parent_id">
      <Comment>父权限ID（用于树形结构）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int(10) unsigned|0s</StoredType>
    </column>
    <column id="1857" parent="345" name="perm_name">
      <Comment>权限名称（如：商品管理）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1858" parent="345" name="perm_code">
      <Comment>唯一权限标识（如：product:manage）</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1859" parent="345" name="perm_type">
      <Comment>权限类型</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>enum(&apos;MENU&apos;, &apos;BUTTON&apos;, &apos;API&apos;)|0e</StoredType>
    </column>
    <column id="1860" parent="345" name="path">
      <Comment>前端路由/API路径（如：/product）</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1861" parent="345" name="component">
      <Comment>前端组件路径（Vue/React路由用）</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1862" parent="345" name="method">
      <Comment>HTTP方法（API类型时必填，如GET/POST）</Comment>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1863" parent="345" name="icon">
      <Comment>菜单图标（如el-icon-goods）</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1864" parent="345" name="order_num">
      <Comment>显示顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1865" parent="345" name="is_hidden">
      <Comment>是否隐藏菜单（0显示 1隐藏）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1866" parent="345" name="status">
      <DefaultExpression>&apos;ENABLED&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>enum(&apos;ENABLED&apos;, &apos;DISABLED&apos;)|0e</StoredType>
    </column>
    <column id="1867" parent="345" name="create_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1868" parent="345" name="update_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1869" parent="345" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1870" parent="345" name="perm_code">
      <ColNames>perm_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1871" parent="345" name="idx_perm_code">
      <ColNames>perm_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1872" parent="345" name="idx_perm_type">
      <ColNames>perm_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1873" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1874" parent="345" name="perm_code">
      <UnderlyingIndexName>perm_code</UnderlyingIndexName>
    </key>
    <column id="1875" parent="346" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1876" parent="346" name="role_name">
      <Comment>角色名称（如：卖家管理员）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1877" parent="346" name="role_desc">
      <Comment>角色描述</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1878" parent="346" name="status">
      <DefaultExpression>&apos;ENABLED&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1879" parent="346" name="create_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1880" parent="346" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1881" parent="346" name="role_name">
      <ColNames>role_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1882" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1883" parent="346" name="role_name">
      <UnderlyingIndexName>role_name</UnderlyingIndexName>
    </key>
    <column id="1884" parent="347" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1885" parent="347" name="permission_id">
      <Comment>权限ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(10) unsigned|0s</StoredType>
    </column>
    <column id="1886" parent="347" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <foreign-key id="1887" parent="347" name="fk_role_permission_role">
      <ColNames>role_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>sys_role</RefTableName>
    </foreign-key>
    <foreign-key id="1888" parent="347" name="fk_role_permission_perm">
      <ColNames>permission_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>sys_permission</RefTableName>
    </foreign-key>
    <index id="1889" parent="347" name="PRIMARY">
      <ColNames>role_id
permission_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1890" parent="347" name="fk_role_permission_perm">
      <ColNames>permission_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1891" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1892" parent="348" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1893" parent="348" name="name">
      <Comment>团长名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1894" parent="348" name="real_name">
      <Comment>真实姓名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1895" parent="348" name="avatar">
      <Comment>头像URL</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1896" parent="348" name="phone">
      <Comment>手机号码</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1897" parent="348" name="email">
      <Comment>电子邮箱</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1898" parent="348" name="id_number">
      <Comment>身份证号码</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="1899" parent="348" name="bank_info">
      <Comment>银行账户信息</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="1900" parent="348" name="address">
      <Comment>详细地址</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1901" parent="348" name="create_time">
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1902" parent="348" name="level">
      <Comment>团长等级</Comment>
      <DefaultExpression>&apos;普通团长&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1903" parent="348" name="status">
      <Comment>状态：active/pending/disabled</Comment>
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1904" parent="348" name="team_size">
      <Comment>团队规模</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1905" parent="348" name="total_sales">
      <Comment>总销售额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1906" parent="348" name="commission_earned">
      <Comment>已获得佣金</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1907" parent="348" name="today_sales">
      <Comment>今日销售额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <index id="1908" parent="348" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1909" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1910" parent="349" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1911" parent="349" name="leader_id">
      <Comment>所属团长ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1912" parent="349" name="name">
      <Comment>成员名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1913" parent="349" name="level">
      <Comment>会员等级</Comment>
      <DefaultExpression>&apos;普通会员&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1914" parent="349" name="join_time">
      <Comment>加入时间</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1915" parent="349" name="total_sales">
      <Comment>总销售额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <foreign-key id="1916" parent="349" name="team_member_ibfk_1">
      <ColNames>leader_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>team_leader</RefTableName>
    </foreign-key>
    <index id="1917" parent="349" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1918" parent="349" name="leader_id">
      <ColNames>leader_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1919" parent="349" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1920" parent="350" name="id">
      <AutoIncrement>6</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1921" parent="350" name="tracking_number">
      <Comment>物流单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1922" parent="350" name="carrier_code">
      <Comment>运输商代码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1923" parent="350" name="carrier_name">
      <Comment>运输商名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1924" parent="350" name="order_id">
      <Comment>关联订单ID</Comment>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1925" parent="350" name="order_number">
      <Comment>订单号</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1926" parent="350" name="origin_country">
      <Comment>发货国家代码</Comment>
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1927" parent="350" name="destination_country">
      <Comment>目的地国家代码</Comment>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1928" parent="350" name="status">
      <Comment>物流主状态</Comment>
      <DefaultExpression>&apos;NotFound&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1929" parent="350" name="sub_status">
      <Comment>物流子状态</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1930" parent="350" name="sub_status_desc">
      <Comment>状态描述</Comment>
      <Position>11</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1931" parent="350" name="tracking_status">
      <Comment>跟踪状态</Comment>
      <DefaultExpression>&apos;Tracking&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1932" parent="350" name="register_time">
      <Comment>注册时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1933" parent="350" name="track_time">
      <Comment>最后跟踪时间</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1934" parent="350" name="push_time">
      <Comment>最后推送时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1935" parent="350" name="push_status">
      <Comment>推送状态</Comment>
      <DefaultExpression>&apos;NotPushed&apos;</DefaultExpression>
      <Position>16</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1936" parent="350" name="stop_track_time">
      <Comment>停止跟踪时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1937" parent="350" name="stop_track_reason">
      <Comment>停止跟踪原因</Comment>
      <Position>18</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1938" parent="350" name="is_retracked">
      <Comment>是否已重新跟踪</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>19</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1939" parent="350" name="carrier_change_count">
      <Comment>运输商修改次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>20</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1940" parent="350" name="tag">
      <Comment>自定义标签</Comment>
      <Position>21</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1941" parent="350" name="remark">
      <Comment>备注信息</Comment>
      <Position>22</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1942" parent="350" name="lang">
      <Comment>翻译语言代码</Comment>
      <DefaultExpression>&apos;en&apos;</DefaultExpression>
      <Position>23</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1943" parent="350" name="param">
      <Comment>附加跟踪参数</Comment>
      <Position>24</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1944" parent="350" name="latest_event_time">
      <Comment>最新事件时间</Comment>
      <Position>25</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1945" parent="350" name="latest_event_info">
      <Comment>最新事件信息</Comment>
      <Position>26</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1946" parent="350" name="pickup_time">
      <Comment>揽收时间</Comment>
      <Position>27</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1947" parent="350" name="delivery_time">
      <Comment>签收时间</Comment>
      <Position>28</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1948" parent="350" name="days_after_order">
      <Comment>下单后天数</Comment>
      <Position>29</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1949" parent="350" name="days_after_last_update">
      <Comment>最后更新后天数</Comment>
      <Position>30</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1950" parent="350" name="days_of_transit">
      <Comment>运输天数</Comment>
      <Position>31</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1951" parent="350" name="days_of_transit_done">
      <Comment>已完成运输天数</Comment>
      <Position>32</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1952" parent="350" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>33</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1953" parent="350" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>34</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1954" parent="350" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1955" parent="350" name="uk_tracking_carrier">
      <ColNames>tracking_number
carrier_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1956" parent="350" name="idx_tracking_number">
      <ColNames>tracking_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1957" parent="350" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1958" parent="350" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1959" parent="350" name="idx_tracking_status">
      <ColNames>tracking_status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1960" parent="350" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1961" parent="350" name="uk_tracking_carrier">
      <UnderlyingIndexName>uk_tracking_carrier</UnderlyingIndexName>
    </key>
    <column id="1962" parent="351" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>自增主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1963" parent="351" name="city">
      <Comment>城市名称（全英文）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1964" parent="351" name="state">
      <Comment>所属州缩写（2字母大写，如 CA、NY）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1965" parent="351" name="created_at">
      <Comment>记录创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>4</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="1966" parent="351" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1967" parent="351" name="city_name">
      <ColNames>city</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1968" parent="351" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1969" parent="351" name="city_name">
      <UnderlyingIndexName>city_name</UnderlyingIndexName>
    </key>
    <column id="1970" parent="352" name="id">
      <AutoIncrement>60</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1971" parent="352" name="user_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1972" parent="352" name="name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1973" parent="352" name="street">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1974" parent="352" name="city">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1975" parent="352" name="state">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1976" parent="352" name="zip_code">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1977" parent="352" name="addressDetail">
      <Position>8</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1978" parent="352" name="phone_number">
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1979" parent="352" name="third_party_order_id">
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1980" parent="352" name="created_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1981" parent="352" name="Default">
      <Comment>默认地址</Comment>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="1982" parent="352" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1983" parent="352" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1984" parent="353" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1985" parent="353" name="product_id">
      <Comment>商品ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1986" parent="353" name="shop_id">
      <Comment>店铺ID</Comment>
      <Position>3</Position>
      <StoredType>int(10) unsigned|0s</StoredType>
    </column>
    <column id="1987" parent="353" name="view_time">
      <Comment>浏览时间</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1988" parent="353" name="user_id">
      <Comment>用户ID</Comment>
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <foreign-key id="1989" parent="353" name="fk_view_product_id">
      <ColNames>product_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>pms_product</RefTableName>
    </foreign-key>
    <foreign-key id="1990" parent="353" name="fk_view_shop_id">
      <ColNames>shop_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>shop_info</RefTableName>
    </foreign-key>
    <index id="1991" parent="353" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1992" parent="353" name="idx_product_id">
      <ColNames>product_id</ColNames>
      <Comment>商品ID索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="1993" parent="353" name="idx_shop_id">
      <ColNames>shop_id</ColNames>
      <Comment>店铺ID索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="1994" parent="353" name="idx_view_time">
      <ColNames>view_time</ColNames>
      <Comment>浏览时间索引</Comment>
      <Type>btree</Type>
    </index>
    <key id="1995" parent="353" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1996" parent="354" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1997" parent="354" name="refund_application_id">
      <Comment>退款申请ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1998" parent="354" name="order_id">
      <Comment>关联的订单ID</Comment>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1999" parent="354" name="order_number">
      <Comment>关联的订单号</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="2000" parent="354" name="wechat_refund_id">
      <Comment>微信支付退款订单号</Comment>
      <Position>5</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="2001" parent="354" name="out_refund_no">
      <Comment>商户退款单号</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="2002" parent="354" name="transaction_id">
      <Comment>微信支付交易订单号</Comment>
      <Position>7</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="2003" parent="354" name="out_trade_no">
      <Comment>商户原交易订单号</Comment>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="2004" parent="354" name="refund_amount">
      <Comment>退款金额（单位：分）</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="2005" parent="354" name="total_amount">
      <Comment>原订单金额（单位：分）</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="2006" parent="354" name="currency">
      <Comment>货币类型</Comment>
      <DefaultExpression>&apos;USD&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="2007" parent="354" name="payer_refund_amount">
      <Comment>用户退款金额（单位：分）</Comment>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="2008" parent="354" name="settlement_refund_amount">
      <Comment>结算币种退款金额（单位：分）</Comment>
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="2009" parent="354" name="settlement_currency">
      <Comment>结算币种</Comment>
      <Position>14</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="2010" parent="354" name="refund_status">
      <Comment>退款状态：SUCCESS/REFUNDCLOSE/PROCESSING/ABNORMAL</Comment>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="2011" parent="354" name="refund_channel">
      <Comment>退款渠道：ORIGINAL/BALANCE/OTHER_BALANCE/OTHER_BANKCARD</Comment>
      <Position>16</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="2012" parent="354" name="recv_account">
      <Comment>退款入账账户</Comment>
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="2013" parent="354" name="fund_source">
      <Comment>退款资金来源</Comment>
      <Position>18</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="2014" parent="354" name="refund_reason">
      <Comment>退款原因</Comment>
      <Position>19</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <column id="2015" parent="354" name="refund_remark">
      <Comment>退款备注</Comment>
      <Position>20</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2016" parent="354" name="refund_create_time">
      <Comment>退款创建时间</Comment>
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2017" parent="354" name="refund_success_time">
      <Comment>退款成功时间</Comment>
      <Position>22</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2018" parent="354" name="create_time">
      <Comment>记录创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2019" parent="354" name="update_time">
      <Comment>记录更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>24</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2020" parent="354" name="notify_url">
      <Comment>退款通知地址</Comment>
      <Position>25</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="2021" parent="354" name="notify_status">
      <Comment>通知状态：PENDING/SUCCESS/FAILED</Comment>
      <DefaultExpression>&apos;PENDING&apos;</DefaultExpression>
      <Position>26</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="2022" parent="354" name="notify_time">
      <Comment>通知时间</Comment>
      <Position>27</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="2023" parent="354" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2024" parent="354" name="uk_out_refund_no">
      <ColNames>out_refund_no</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2025" parent="354" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2026" parent="354" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2027" parent="354" name="idx_wechat_refund_id">
      <ColNames>wechat_refund_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2028" parent="354" name="idx_transaction_id">
      <ColNames>transaction_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2029" parent="354" name="idx_out_trade_no">
      <ColNames>out_trade_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2030" parent="354" name="idx_refund_status">
      <ColNames>refund_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2031" parent="354" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2032" parent="354" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2033" parent="354" name="uk_out_refund_no">
      <UnderlyingIndexName>uk_out_refund_no</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>