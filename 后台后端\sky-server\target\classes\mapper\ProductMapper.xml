<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.ProductMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into product (product_number,store_id, product_name, price, product_introduction, product_image, product_video, product_status, create_time, type)
        values (#{productNumber}, #{storeId}, #{productName}, #{price}, #{productIntroduction}, #{productImage}, #{productVideo}, #{productStatus},
                #{createTime}, #{type})
    </insert>

    <select id="pageQuery" resultType="com.sky.vo.ProductVO">
        select d.* , c.name as categoryName from product d left outer join category c on d.category_id = c.id
        <where>
            <if test="productName != null">
                and d.product_name like concat('%',#{productName},'%')
            </if>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}
            </if>
            <if test="productStatus != null">
                and d.product_status = #{productStatus}
            </if>
        </where>
        order by d.create_time desc
    </select>

    <update id="update">
        update product
    <set>
        <if test="productNumber != null">product_number = #{productNumber},</if>
        <if test="storeId != null">store_id = #{storeId},</if>
        <if test="productName != null">product_name = #{productName},</if>
        <if test="price != null">price = #{price},</if>
        <if test="productIntroduction != null">product_introduction = #{productIntroduction},</if>
        <if test="productImage != null">product_image = #{productImage},</if>
        <if test="productVideo != null">product_video = #{productVideo},</if>
        <if test="productStatus != null">product_status = #{productStatus},</if>
        <if test="createTime != null">create_time = #{createTime},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        <if test="type != null">type = #{type},</if>
        <if test="categoryId != null">category_id = #{categoryId},</if>
    </set>
    where id = #{id}
    </update>
</mapper>
