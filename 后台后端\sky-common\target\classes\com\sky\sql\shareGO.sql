CREATE TABLE `logistics_info` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `logistics_number` VARCHAR(255) COMMENT '物流单号',
    `order_id` INT COMMENT '订单ID',
    `logistics_company` VARCHAR(255) COMMENT '物流公司',
    `logistics_status` VARCHAR(50) COMMENT '物流状态',
    `create_time` DATETIME COMMENT '创建时间',
    `shipping_date` DATETIME COMMENT '发货日期',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='物流信息';

CREATE TABLE store
(
    id                INT AUTO_INCREMENT PRIMARY KEY,
    seller_id         INT          NULL COMMENT '卖家ID',
    store_name        VARCHAR(255) NULL COMMENT '店铺名称',
    photo            VARCHAR(255) NULL COMMENT '照片（logo）',
    store_status      VARCHAR(50)  NULL COMMENT '店铺状态',
    store_description TEXT         NULL COMMENT '店铺描述',
    store_address     VARCHAR(255) NULL COMMENT '店铺地址（可选填）',
    create_time       DATETIME     NULL COMMENT '创建时间',
    last_login_time   DATETIME     NULL COMMENT '最后登录时间'
)
COMMENT '店铺' ROW_FORMAT = DYNAMIC;


-- 店铺基本信息表（增强版）
CREATE TABLE `shop_info` (
                             `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '店铺唯一ID',
                             `shop_name` VARCHAR(100) NOT NULL COMMENT '店铺名称',
                             `company_name` VARCHAR(200) NOT NULL COMMENT '公司全称',
                             `business_license` VARCHAR(50) NOT NULL UNIQUE COMMENT '营业执照编号',
                             `license_validity` DATE NOT NULL COMMENT '执照有效期',
                             `company_intro` TEXT COMMENT '公司简介',
                             `contact_person` VARCHAR(50) NOT NULL COMMENT '联系人',
                             `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系方式',
                             `province` VARCHAR(20) NOT NULL COMMENT '省份',
                             `city` VARCHAR(20) NOT NULL COMMENT '城市',
                             `district` VARCHAR(20) COMMENT '区县',
                             `address_detail` VARCHAR(200) NOT NULL COMMENT '详细地址',
                             `created_user` VARCHAR(50) NOT NULL COMMENT '创建人（系统用户ID）',
                             `created_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*
 * 应用市场
 */
CREATE TABLE `system_programs` (
                                   `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
                                   `program_name` VARCHAR(50) NOT NULL UNIQUE COMMENT '程序名称',
                                   `program_status` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '程序状态',
                                   `test_status` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '测试状态',
                                   `token` VARCHAR(50) NOT NULL COMMENT '是否关联第三方TMMA（< 表示未关联）',
                                   `expiration_date` DATETIME DEFAULT NULL COMMENT '到期时间',
                                   `operation` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '可执行操作',
                                   `created_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updated_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*
 * 消息通知
 */
CREATE TABLE `message_notifications` (
                                         `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '消息唯一ID',
                                         `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '通知时间',
                                         `type` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '添加类型',
                                         `content` TEXT NOT NULL COMMENT '添加内容（支持富文本或JSON格式）',
                                         `status` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '消息状态',
                                         `operation` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '允许的操作类型',
                                         `created_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*
 * 提现记录
 */
CREATE TABLE `data_records` (
                                `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',

                                `serial_number` VARCHAR(255) NOT NULL UNIQUE COMMENT '转账流水号',
                                `seller_id` INT UNSIGNED NOT NULL COMMENT '卖家ID',
                                `status` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '提现状态',
                                `content` TEXT NOT NULL COMMENT '提现账户信息',
                                `amount` DECIMAL(10, 2) NOT NULL COMMENT '提现金额',
                                `operation` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '允许的操作类型',
                                `created_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `updated_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';

# 流水明细
CREATE TABLE fund_flow (
                           id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
                           seller_id INT UNSIGNED NOT NULL COMMENT '卖家ID',
                           operation_time DATETIME NOT NULL COMMENT '操作时间',
                           operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
                           serial_number VARCHAR(100) UNIQUE NOT NULL COMMENT '流水号',
                           order_number VARCHAR(100) NOT NULL COMMENT '关联订单号',
                           fund_type VARCHAR(50) NOT NULL COMMENT '资金类型',
                           order_amount_percent DECIMAL(10,2) NOT NULL COMMENT '订单金额',
                           platform_fund_percent DECIMAL(10,2) NOT NULL COMMENT '平台佣金',
                           fund_flow_percent DECIMAL(10,2) NOT NULL COMMENT '资金变动',
                           balance_percent DECIMAL(10,2) NOT NULL COMMENT '余额',  -- 扩大数值范围
                           created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资金流水表';

-- ========================================
-- 17TRACK 物流跟踪相关表
-- ========================================

-- 物流跟踪记录表
CREATE TABLE `tracking_record` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `tracking_number` VARCHAR(100) NOT NULL COMMENT '物流单号',
    `carrier_code` INT NOT NULL COMMENT '运输商代码',
    `carrier_name` VARCHAR(100) COMMENT '运输商名称',
    `order_id` BIGINT COMMENT '关联订单ID',
    `order_number` VARCHAR(100) COMMENT '订单号',
    `origin_country` VARCHAR(10) COMMENT '发货国家代码',
    `destination_country` VARCHAR(10) COMMENT '目的地国家代码',
    `status` VARCHAR(50) NOT NULL DEFAULT 'NotFound' COMMENT '物流主状态',
    `sub_status` VARCHAR(100) COMMENT '物流子状态',
    `sub_status_desc` VARCHAR(255) COMMENT '状态描述',
    `tracking_status` VARCHAR(20) NOT NULL DEFAULT 'Tracking' COMMENT '跟踪状态：Tracking/Stopped',
    `register_time` DATETIME NOT NULL COMMENT '注册时间',
    `track_time` DATETIME COMMENT '最后跟踪时间',
    `push_time` DATETIME COMMENT '最后推送时间',
    `push_status` VARCHAR(20) COMMENT '推送状态：Success/Failure/NotPushed',
    `stop_track_time` DATETIME COMMENT '停止跟踪时间',
    `stop_track_reason` VARCHAR(50) COMMENT '停止跟踪原因',
    `is_retracked` BOOLEAN DEFAULT FALSE COMMENT '是否已重新跟踪',
    `carrier_change_count` INT DEFAULT 0 COMMENT '运输商修改次数',
    `tag` VARCHAR(100) COMMENT '自定义标签',
    `remark` VARCHAR(1000) COMMENT '备注信息',
    `lang` VARCHAR(10) COMMENT '翻译语言代码',
    `param` VARCHAR(255) COMMENT '附加跟踪参数',
    `latest_event_time` DATETIME COMMENT '最新事件时间',
    `latest_event_info` TEXT COMMENT '最新事件信息',
    `pickup_time` DATETIME COMMENT '揽收时间',
    `delivery_time` DATETIME COMMENT '签收时间',
    `days_after_order` INT COMMENT '下单后天数',
    `days_after_last_update` INT COMMENT '最后更新后天数',
    `days_of_transit` INT COMMENT '运输天数',
    `days_of_transit_done` INT COMMENT '已完成运输天数',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_tracking_carrier` (`tracking_number`, `carrier_code`),
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_tracking_status` (`tracking_status`),
    INDEX `idx_register_time` (`register_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='17TRACK物流跟踪记录表';

-- 物流事件表
CREATE TABLE `tracking_event` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `tracking_record_id` BIGINT UNSIGNED NOT NULL COMMENT '跟踪记录ID',
    `tracking_number` VARCHAR(100) NOT NULL COMMENT '物流单号',
    `carrier_code` INT NOT NULL COMMENT '运输商代码',
    `event_time` DATETIME NOT NULL COMMENT '事件时间',
    `event_time_raw` VARCHAR(50) COMMENT '原始事件时间',
    `event_timezone` VARCHAR(50) COMMENT '事件时区',
    `status` VARCHAR(50) NOT NULL COMMENT '物流主状态',
    `sub_status` VARCHAR(100) COMMENT '物流子状态',
    `sub_status_desc` VARCHAR(255) COMMENT '状态描述',
    `location` VARCHAR(255) COMMENT '事件地点',
    `description` TEXT COMMENT '事件描述',
    `description_translated` TEXT COMMENT '翻译后的事件描述',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_tracking_record` (`tracking_record_id`),
    INDEX `idx_tracking_number` (`tracking_number`),
    INDEX `idx_event_time` (`event_time`),
    INDEX `idx_status` (`status`),
    FOREIGN KEY (`tracking_record_id`) REFERENCES `tracking_record`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='17TRACK物流事件表';

-- 运输商信息表
CREATE TABLE `carrier_info` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `carrier_code` INT NOT NULL UNIQUE COMMENT '运输商代码',
    `carrier_name` VARCHAR(100) NOT NULL COMMENT '运输商名称',
    `carrier_name_en` VARCHAR(100) COMMENT '运输商英文名称',
    `carrier_type` VARCHAR(50) COMMENT '运输商类型',
    `country_code` VARCHAR(10) COMMENT '国家代码',
    `website` VARCHAR(255) COMMENT '官方网站',
    `phone` VARCHAR(50) COMMENT '联系电话',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `param_required` BOOLEAN DEFAULT FALSE COMMENT '是否需要附加参数',
    `param_type` VARCHAR(50) COMMENT '参数类型：邮编/手机/日期等',
    `param_example` VARCHAR(100) COMMENT '参数示例',
    `param_required_flag` BOOLEAN DEFAULT FALSE COMMENT '参数是否必填',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_carrier_code` (`carrier_code`),
    INDEX `idx_country_code` (`country_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输商信息表';

-- Webhook日志表
CREATE TABLE `webhook_log` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `event_type` VARCHAR(50) NOT NULL COMMENT '事件类型',
    `tracking_number` VARCHAR(100) NOT NULL COMMENT '物流单号',
    `carrier_code` INT COMMENT '运输商代码',
    `request_body` TEXT COMMENT '请求体内容',
    `response_status` INT COMMENT '响应状态码',
    `response_body` TEXT COMMENT '响应内容',
    `process_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending/success/failed',
    `error_message` TEXT COMMENT '错误信息',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `process_time` DATETIME COMMENT '处理时间',
    INDEX `idx_tracking_number` (`tracking_number`),
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_process_status` (`process_status`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='17TRACK Webhook日志表';

-- 17TRACK配置表
CREATE TABLE `tracking_config` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `config_key` VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    `config_value` TEXT COMMENT '配置值',
    `config_desc` VARCHAR(255) COMMENT '配置描述',
    `config_type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string/number/boolean/json',
    `is_encrypted` BOOLEAN DEFAULT FALSE COMMENT '是否加密存储',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='17TRACK配置表';

-- 插入默认配置数据
INSERT INTO `tracking_config` (`config_key`, `config_value`, `config_desc`, `config_type`) VALUES
('17track.api.key', '', '17TRACK API密钥', 'string'),
('17track.api.base_url', 'https://api.17track.net/track/v2.2', '17TRACK API基础URL', 'string'),
('17track.webhook.url', '', 'Webhook接收地址', 'string'),
('17track.webhook.secret', '', 'Webhook密钥', 'string'),
('17track.auto_register', 'true', '是否自动注册物流单号', 'boolean'),
('17track.default_lang', 'zh-hans', '默认翻译语言', 'string'),
('17track.max_retry_count', '3', '最大重试次数', 'number'),
('17track.request_timeout', '30000', '请求超时时间(毫秒)', 'number');

-- 订单物流关联表
CREATE TABLE `order_tracking` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `tracking_record_id` BIGINT UNSIGNED NOT NULL COMMENT '跟踪记录ID',
    `is_primary` BOOLEAN DEFAULT TRUE COMMENT '是否主要物流单号',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY `uk_order_tracking` (`order_id`, `tracking_record_id`),
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_tracking_record_id` (`tracking_record_id`),
    FOREIGN KEY (`tracking_record_id`) REFERENCES `tracking_record`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单物流关联表';

-- 插入常用运输商数据
INSERT INTO `carrier_info` (`carrier_code`, `carrier_name`, `carrier_name_en`, `carrier_type`, `country_code`, `param_required`, `param_type`, `param_example`, `param_required_flag`) VALUES
(3011, '中国邮政', 'China Post', 'postal', 'CN', FALSE, NULL, NULL, FALSE),
(21051, '俄罗斯邮政', 'Russian Post', 'postal', 'RU', FALSE, NULL, NULL, FALSE),
(100003, 'FedEx', 'FedEx', 'express', 'US', FALSE, 'date', '2024/1/1', FALSE),
(100005, 'GLS', 'GLS', 'express', 'DE', TRUE, 'postcode', '12345', TRUE),
(14041, 'PostNL', 'PostNL', 'postal', 'NL', TRUE, 'country', 'FR', TRUE),
(2061, 'Bpost', 'Bpost', 'postal', 'BE', FALSE, 'postcode', '1000', FALSE),
(7041, 'DHL Paket', 'DHL Paket', 'express', 'DE', FALSE, 'postcode', '1000 AA', FALSE),
(100074, 'J&T Express (ID)', 'J&T Express Indonesia', 'express', 'ID', TRUE, 'phone', '8888', TRUE),
(100271, 'J&T Express (TH)', 'J&T Express Thailand', 'express', 'TH', TRUE, 'phone', '8888', TRUE),
(190415, '优速快递', 'UC Express', 'express', 'CN', TRUE, 'phone', '8888', TRUE),
(190766, '顺丰速运', 'SF Express', 'express', 'CN', TRUE, 'phone', '8888', TRUE),
(190845, '跨越速运', 'KYE Express', 'express', 'CN', TRUE, 'phone', '8888', TRUE);


