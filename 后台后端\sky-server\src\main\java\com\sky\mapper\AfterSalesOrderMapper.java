package com.sky.mapper;

import com.sky.entity.AfterSalesOrder;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AfterSalesOrderMapper {
    @Select("SELECT * FROM after_sales_order WHERE after_sales_no = #{afterSalesNo}")
    AfterSalesOrder findByAfterSalesNo(String afterSalesNo);

    @Select("SELECT * FROM after_sales_order WHERE order_no = #{orderNo}")
    AfterSalesOrder findByOrderNo(String orderNo);

    @Select("SELECT * FROM after_sales_order WHERE transaction_no = #{transactionNo}")
    AfterSalesOrder findByTransactionNo(String transactionNo);

    @Select("SELECT * FROM after_sales_order WHERE status = #{status}")
    List<AfterSalesOrder> findByStatus(Integer status);

}