package com.sky.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 封装分页查询结果
 */
@Data
public class PageResult<T> implements Serializable {
    private List<T> list;
    private Long total;       // 总记录数
    private Integer page;     // 当前页码
    private Integer pageSize; // 每页条数
    private Integer totalPages; // 总页数

    public PageResult(Long total, List<T> list) {
        this.total = total;
        this.list = list;
        this.page = 1;
        this.pageSize = 10;
        this.totalPages = total > 0 ? (int) ((total + pageSize - 1) / pageSize) : 0;
    }

    public PageResult(Long total, List<T> list, Integer page, Integer pageSize) {
        this.total = total;
        this.list = list;
        this.page = page == null ? 1 : page;
        this.pageSize = pageSize == null ? 10 : pageSize;
        this.totalPages = this.pageSize > 0 && total > 0 ?
                (int) Math.ceil((double) total / this.pageSize) : 0;
    }



}
