package com.sky.service.impl;

import com.sky.dto.ManagerDTO;
import com.sky.exception.AccountIsExitException;
import com.sky.mapper.ManagerMapper;
import com.sky.service.ManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ManagerServiceImpl implements ManagerService {
    @Autowired
    private ManagerMapper managerMapper;

    @Override
    public void login(ManagerDTO managerDTO) {
        String accountName = managerDTO.getAccountName();
        String password = managerDTO.getPassword();
        ManagerDTO managerDTO1 =  managerMapper.login(accountName,password);
        if(managerDTO1 == null){
            throw new AccountIsExitException("账户或密码错误");
        }
    }
}
