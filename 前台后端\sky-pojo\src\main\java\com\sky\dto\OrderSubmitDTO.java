package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单提交数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "订单提交数据传输对象")
public class OrderSubmitDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("收货地址ID")
    @NotNull(message = "收货地址不能为空")
    private Long addressId;

    @ApiModelProperty("支付方式")
    @NotNull(message = "支付方式不能为空")
    private Integer payMethod;

    @ApiModelProperty("配送方式ID")
    @NotNull(message = "配送方式不能为空")
    private Integer shippingMethodId;

    @ApiModelProperty("订单备注")
    private String orderRemark;

    @ApiModelProperty("订单详情")
    @NotEmpty(message = "订单详情不能为空")
    private List<OrderItemDTO> orderItems;

    @ApiModelProperty("订单金额")
    @NotNull(message = "订单金额不能为空")
    private BigDecimal amount;
    
    @ApiModelProperty("支付交易ID")
    @NotNull(message = "支付交易ID不能为空")
    private String paymentTransactionId;
    
    @ApiModelProperty("支付时间戳")
    @NotNull(message = "支付时间戳不能为空")
    private Long payTimeStamp;
} 