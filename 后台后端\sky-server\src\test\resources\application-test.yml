spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

# 测试环境的物流跟踪配置
tracking:
  api:
    key: test-api-key  # 测试用的API密钥
    base-url: https://api.51tracking.com/v4
    connect-timeout: 5000
    read-timeout: 10000
  enable-validation: false  # 测试环境关闭验证
  validation-timeout: 10
  default-courier-code: fedex

logging:
  level:
    com.sky: DEBUG
    org.springframework.web: DEBUG
