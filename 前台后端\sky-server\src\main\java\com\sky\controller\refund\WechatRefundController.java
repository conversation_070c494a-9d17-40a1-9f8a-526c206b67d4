package com.sky.controller.refund;

import com.sky.dto.WechatRefundRequestDTO;
import com.sky.dto.WechatRefundQueryDTO;
import com.sky.dto.WechatRefundListQueryDTO;
import com.sky.result.Result;
import com.sky.service.WechatRefundService;
import com.sky.vo.WechatRefundResponseVO;
import com.sky.vo.WechatRefundQueryVO;
import com.sky.vo.WechatRefundListQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信退款控制器
 */
@Slf4j
@RestController
@RequestMapping("/refund/wechat")
public class WechatRefundController {

    @Autowired
    private WechatRefundService wechatRefundService;

    /**
     * 申请退款
     * @param requestDTO 退款申请请求
     * @return 退款响应
     */
    @PostMapping("/apply")
    public Result<WechatRefundResponseVO> applyRefund(@RequestBody WechatRefundRequestDTO requestDTO) {
        log.info("接收到退款申请请求: {}", requestDTO);
        
        try {
            // 调用退款服务申请退款
            WechatRefundResponseVO response = wechatRefundService.applyRefund(requestDTO);
            log.info("申请退款成功，响应: {}", response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("申请退款失败: {}", e.getMessage(), e);
            return Result.error("申请退款失败: " + e.getMessage());
        }
    }

    /**
     * 查询单笔退款结果
     * @param queryDTO 退款查询请求
     * @return 退款查询响应
     */
    @PostMapping("/query")
    public Result<WechatRefundQueryVO> queryRefund(@RequestBody WechatRefundQueryDTO queryDTO) {
        log.info("接收到退款查询请求: {}", queryDTO);
        
        try {
            // 调用退款服务查询退款结果
            WechatRefundQueryVO response = wechatRefundService.queryRefund(queryDTO);
            log.info("查询退款结果成功，响应: {}", response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("查询退款结果失败: {}", e.getMessage(), e);
            return Result.error("查询退款结果失败: " + e.getMessage());
        }
    }

    /**
     * 根据商户退款单号查询退款结果（GET方式）
     * @param outRefundNo 商户退款单号
     * @return 退款查询响应
     */
    @GetMapping("/query/{outRefundNo}")
    public Result<WechatRefundQueryVO> queryRefundByOutRefundNo(@PathVariable String outRefundNo) {
        log.info("根据商户退款单号查询退款结果: {}", outRefundNo);
        
        try {
            // 构建查询DTO
            WechatRefundQueryDTO queryDTO = WechatRefundQueryDTO.builder()
                    .outRefundNo(outRefundNo)
                    .build();
            
            // 调用退款服务查询退款结果
            WechatRefundQueryVO response = wechatRefundService.queryRefund(queryDTO);
            log.info("查询退款结果成功，响应: {}", response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("查询退款结果失败: {}", e.getMessage(), e);
            return Result.error("查询退款结果失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有退款（POST方式）
     * @param queryDTO 查询所有退款请求
     * @return 查询所有退款响应
     */
    @PostMapping("/query/all")
    public Result<WechatRefundListQueryVO> queryAllRefunds(@RequestBody WechatRefundListQueryDTO queryDTO) {
        log.info("接收到查询所有退款请求: {}", queryDTO);
        
        try {
            // 调用退款服务查询所有退款
            WechatRefundListQueryVO response = wechatRefundService.queryAllRefunds(queryDTO);
            log.info("查询所有退款成功，响应: {}", response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("查询所有退款失败: {}", e.getMessage(), e);
            return Result.error("查询所有退款失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单号查询所有退款（GET方式）
     * @param outTradeNo 商户订单号
     * @param offset 记录起始位置（可选，默认0）
     * @param count 每页笔数（可选，默认10，最大20）
     * @return 查询所有退款响应
     */
    @GetMapping("/query/all/{outTradeNo}")
    public Result<WechatRefundListQueryVO> queryAllRefundsByOutTradeNo(
            @PathVariable String outTradeNo,
            @RequestParam(defaultValue = "0") Integer offset,
            @RequestParam(defaultValue = "10") Integer count) {
        log.info("根据商户订单号查询所有退款: {}, offset: {}, count: {}", outTradeNo, offset, count);
        
        try {
            // 构建查询DTO
            WechatRefundListQueryDTO queryDTO = WechatRefundListQueryDTO.builder()
                    .outTradeNo(outTradeNo)
                    .offset(offset)
                    .count(count)
                    .build();
            
            // 调用退款服务查询所有退款
            WechatRefundListQueryVO response = wechatRefundService.queryAllRefunds(queryDTO);
            log.info("查询所有退款成功，响应: {}", response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("查询所有退款失败: {}", e.getMessage(), e);
            return Result.error("查询所有退款失败: " + e.getMessage());
        }
    }

    /**
     * 根据微信支付订单号查询所有退款（GET方式）
     * @param transactionId 微信支付订单号
     * @param offset 记录起始位置（可选，默认0）
     * @param count 每页笔数（可选，默认10，最大20）
     * @return 查询所有退款响应
     */
    @GetMapping("/query/all/transaction/{transactionId}")
    public Result<WechatRefundListQueryVO> queryAllRefundsByTransactionId(
            @PathVariable String transactionId,
            @RequestParam(defaultValue = "0") Integer offset,
            @RequestParam(defaultValue = "10") Integer count) {
        log.info("根据微信支付订单号查询所有退款: {}, offset: {}, count: {}", transactionId, offset, count);
        
        try {
            // 构建查询DTO
            WechatRefundListQueryDTO queryDTO = WechatRefundListQueryDTO.builder()
                    .transactionId(transactionId)
                    .offset(offset)
                    .count(count)
                    .build();
            
            // 调用退款服务查询所有退款
            WechatRefundListQueryVO response = wechatRefundService.queryAllRefunds(queryDTO);
            log.info("查询所有退款成功，响应: {}", response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("查询所有退款失败: {}", e.getMessage(), e);
            return Result.error("查询所有退款失败: " + e.getMessage());
        }
    }
}
