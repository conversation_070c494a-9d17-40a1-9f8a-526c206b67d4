package com.sky.service.impl;

import com.sky.entity.CarrierInfo;
import com.sky.service.CarrierInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 运输商信息服务实现
 */
@Service
@Slf4j
public class CarrierInfoServiceImpl implements CarrierInfoService {

    @Override
    public CarrierInfo getByCarrierCode(Integer carrierCode) {
        if (carrierCode == null) {
            return null;
        }
        // 不再查询数据库，返回null或根据需要创建CarrierInfo对象
        return null;
    }

    @Override
    public List<CarrierInfo> getAllActiveCarriers() {
        // 返回硬编码的运输商列表，不再查询数据库
        return createDefaultCarrierList();
    }

    @Override
    public List<CarrierInfo> getByCountryCode(String countryCode) {
        List<CarrierInfo> allCarriers = getAllActiveCarriers();
        if (countryCode == null || countryCode.trim().isEmpty()) {
            return allCarriers;
        }

        // 根据国家代码过滤
        return allCarriers.stream()
                .filter(carrier -> countryCode.equals(carrier.getCountryCode()))
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public boolean isValidCarrierCode(Integer carrierCode) {
        if (carrierCode == null) {
            return false;
        }
        // 不再查询数据库，使用硬编码验证
        return isValidNumericCarrierCode(carrierCode);
    }

    @Override
    public boolean isValidCarrierCode(String carrierCode) {
        if (carrierCode == null || carrierCode.trim().isEmpty()) {
            return false;
        }

        // 对于字符串类型的运输商代码，我们需要根据字符串查找对应的运输商
        // 这里先实现一个简单的映射，实际项目中可能需要从数据库查询
        return isValidCarrierCodeString(carrierCode.toLowerCase());
    }

    /**
     * 验证字符串类型的运输商代码
     * @param carrierCode 运输商代码（小写）
     * @return 是否有效
     */
    private boolean isValidCarrierCodeString(String carrierCode) {
        // 获取对应的17TRACK数字代码
        Integer numericCode = getCarrierNumericCode(carrierCode);
        return numericCode != null;
    }

    /**
     * 将字符串运输商代码转换为17TRACK的数字代码
     * @param carrierCode 字符串运输商代码
     * @return 对应的数字代码，如果不存在则返回null
     */
    public Integer getCarrierNumericCode(String carrierCode) {
        if (carrierCode == null) {
            return null;
        }

        // 基于17TRACK官方运输商代码映射
        switch (carrierCode.toLowerCase()) {
            // 美国运输商
            case "usps": return 21051;
            case "fedex": return 3002;
            case "ups": return 3001;
            case "dhl": return 3003;

            // 国际运输商
            case "tnt": return 3004;
            case "aramex": return 6;
            case "dpd": return 3005;
            case "gls": return 3006;

            // 中国运输商
            case "china-post": return 3011;
            case "ems": return 3012;
            case "sf-express": return 190094; // 顺丰
            case "yto": return 190157; // 圆通
            case "sto": return 190158; // 申通
            case "zto": return 190455; // 中通
            case "yunda": return 191197; // 韵达

            default:
                log.warn("未知的运输商代码: {}", carrierCode);
                return null;
        }
    }

    @Override
    public int syncCarrierInfo() {
        // TODO: 从17TRACK API同步运输商信息
        log.info("同步运输商信息");
        return 0;
    }

    /**
     * 创建默认的运输商列表
     * @return 运输商列表
     */
    private List<CarrierInfo> createDefaultCarrierList() {
        List<CarrierInfo> carriers = new java.util.ArrayList<>();

        // 美国运输商
        carriers.add(createCarrierInfo(21051, "usps", "USPS", "United States Postal Service", "US"));
        carriers.add(createCarrierInfo(3002, "fedex", "FedEx", "Federal Express", "US"));
        carriers.add(createCarrierInfo(3001, "ups", "UPS", "United Parcel Service", "US"));
        carriers.add(createCarrierInfo(3003, "dhl", "DHL", "DHL Express", "DE"));

        // 国际运输商
        carriers.add(createCarrierInfo(3004, "tnt", "TNT", "TNT Express", "NL"));
        carriers.add(createCarrierInfo(6, "aramex", "Aramex", "Aramex International", "AE"));
        carriers.add(createCarrierInfo(3005, "dpd", "DPD", "Dynamic Parcel Distribution", "DE"));
        carriers.add(createCarrierInfo(3006, "gls", "GLS", "General Logistics Systems", "DE"));

        // 中国运输商
        carriers.add(createCarrierInfo(3011, "china-post", "China Post", "中国邮政", "CN"));
        carriers.add(createCarrierInfo(3012, "ems", "EMS", "邮政特快专递", "CN"));
        carriers.add(createCarrierInfo(190094, "sf-express", "SF Express", "顺丰速运", "CN"));
        carriers.add(createCarrierInfo(190157, "yto", "YTO Express", "圆通速递", "CN"));
        carriers.add(createCarrierInfo(190158, "sto", "STO Express", "申通快递", "CN"));
        carriers.add(createCarrierInfo(190455, "zto", "ZTO Express", "中通快递", "CN"));
        carriers.add(createCarrierInfo(191197, "yunda", "Yunda Express", "韵达快递", "CN"));

        return carriers;
    }

    /**
     * 创建运输商信息对象
     */
    private CarrierInfo createCarrierInfo(Integer carrierCode, String code, String name, String nameEn, String countryCode) {
        CarrierInfo carrier = new CarrierInfo();
        carrier.setCarrierCode(carrierCode);
        carrier.setCarrierName(name);
        carrier.setCarrierNameEn(nameEn);
        carrier.setCountryCode(countryCode);
        carrier.setIsActive(true);
        return carrier;
    }

    /**
     * 验证数字类型的运输商代码
     * @param carrierCode 数字运输商代码
     * @return 是否有效
     */
    private boolean isValidNumericCarrierCode(Integer carrierCode) {
        // 检查是否在支持的运输商代码列表中
        return carrierCode.equals(21051) || // USPS
               carrierCode.equals(3002) ||  // FedEx
               carrierCode.equals(3001) ||  // UPS
               carrierCode.equals(3003) ||  // DHL
               carrierCode.equals(3004) ||  // TNT
               carrierCode.equals(6) ||     // Aramex
               carrierCode.equals(3005) ||  // DPD
               carrierCode.equals(3006) ||  // GLS
               carrierCode.equals(3011) ||  // China Post
               carrierCode.equals(3012) ||  // EMS
               carrierCode.equals(190094) || // SF Express
               carrierCode.equals(190157) || // YTO
               carrierCode.equals(190158) || // STO
               carrierCode.equals(190455) || // ZTO
               carrierCode.equals(191197);   // Yunda
    }
}
