package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量操作结果视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchOperationResultVO {
    
    private Integer successCount;           // 成功数量
    private Integer failCount;              // 失败数量
    private List<Long> successOrders;       // 成功的订单ID列表
    private List<Long> failOrders;          // 失败的订单ID列表
    private List<String> errorMessages;    // 错误信息列表
}
