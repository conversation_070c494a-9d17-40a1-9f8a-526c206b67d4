package com.sky.controller.webhook;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sky.dto.WebhookNotificationDTO;
import com.sky.result.Result;
import com.sky.service.WebhookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * 17TRACK Webhook接收控制器
 */
@RestController
@RequestMapping("/api/tracking/webhook")
@Api(tags = "17TRACK Webhook接收")
@Slf4j
public class Track17WebhookController {

    @Autowired
    private WebhookService webhookService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostMapping("/17track")
    @ApiOperation("接收17TRACK Webhook通知")
    public Result<String> receiveWebhook(HttpServletRequest request) {
        log.info("收到17TRACK Webhook通知");

        try {
            // 读取请求体
            String requestBody = getRequestBody(request);
            log.debug("Webhook请求体: {}", requestBody);

            // 获取签名
            String signature = request.getHeader("X-17Track-Signature");
            
            // 验证签名
            if (!webhookService.validateWebhookSignature(requestBody, signature)) {
                log.warn("Webhook签名验证失败");
                return Result.error("签名验证失败");
            }

            // 解析通知数据
            WebhookNotificationDTO notification = objectMapper.readValue(requestBody, WebhookNotificationDTO.class);
            
            // 处理通知
            boolean success = webhookService.processWebhookNotification(notification, requestBody);
            
            if (success) {
                log.info("Webhook处理成功");
                return Result.success("OK");
            } else {
                log.error("Webhook处理失败");
                return Result.error("处理失败");
            }

        } catch (Exception e) {
            log.error("Webhook处理异常: {}", e.getMessage(), e);
            return Result.error("处理异常: " + e.getMessage());
        }
    }

    @GetMapping("/test")
    @ApiOperation("测试Webhook接收")
    public Result<String> testWebhook() {
        log.info("Webhook测试接口");
        return Result.success("Webhook接收正常");
    }

    /**
     * 读取请求体
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder requestBody = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
        }
        return requestBody.toString();
    }
}
