package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单明细实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetail {
    private Long id;                    // 主键ID
    private Long orderId;               // 订单ID
    private Long productId;             // 商品ID
    private String productName;         // 商品名称
    private String productImage;        // 商品图片
    private String productSpec;         // 商品规格
    private Integer quantity;           // 商品数量
    private BigDecimal unitPrice;       // 商品单价
    private BigDecimal discount;        // 折扣金额
    private BigDecimal subtotal;        // 小计金额
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
}

