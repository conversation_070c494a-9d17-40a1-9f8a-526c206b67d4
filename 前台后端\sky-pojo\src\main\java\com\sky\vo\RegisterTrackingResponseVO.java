package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 注册物流跟踪响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "注册物流跟踪响应VO")
public class RegisterTrackingResponseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("响应代码")
    private Integer code;

    @ApiModelProperty("响应消息")
    private String message;

    @ApiModelProperty("成功数量")
    private Integer successCount;

    @ApiModelProperty("失败数量")
    private Integer failureCount;

    @ApiModelProperty("成功的物流单号列表")
    private List<TrackingItemVO> successItems;

    @ApiModelProperty("失败的物流单号列表")
    private List<TrackingErrorVO> failureItems;

    @ApiModelProperty("剩余配额")
    private Integer remainingQuota;

    /**
     * 物流跟踪项VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "物流跟踪项VO")
    public static class TrackingItemVO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("物流单号")
        private String trackingNumber;

        @ApiModelProperty("运输商代码")
        private Integer carrierCode;

        @ApiModelProperty("运输商名称")
        private String carrierName;

        @ApiModelProperty("状态")
        private String status;

        @ApiModelProperty("创建时间")
        private String createTime;

        @ApiModelProperty("更新时间")
        private String updateTime;
    }

    /**
     * 物流跟踪错误VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "物流跟踪错误VO")
    public static class TrackingErrorVO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("物流单号")
        private String trackingNumber;

        @ApiModelProperty("运输商代码")
        private Integer carrierCode;

        @ApiModelProperty("错误代码")
        private Integer errorCode;

        @ApiModelProperty("错误消息")
        private String errorMessage;
    }
}
