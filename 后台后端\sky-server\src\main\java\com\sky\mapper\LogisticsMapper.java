package com.sky.mapper;

import com.sky.entity.LogisticsInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LogisticsMapper {

    /**
     * 新增物流信息
     * @param logisticsInfo
     */
    void insertLogistics(LogisticsInfo logisticsInfo);

    /**
     * 根据id查询物流信息
     * @param id
     * @return
     */
    @Select("select * from logistics where id = #{id}")
    LogisticsInfo getById(Long id);

    /**
     * 修改物流信息状态
     * @param id
     * @param logisticsStatus
     */
    @Update("update logistics set logistics_status = #{logisticsStatus} where id = #{id}")
    void updateLogistics(Long id, String logisticsStatus);

}
