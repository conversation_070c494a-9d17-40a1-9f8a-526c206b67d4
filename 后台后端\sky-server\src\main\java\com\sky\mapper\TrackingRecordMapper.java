package com.sky.mapper;

import com.sky.entity.TrackingRecord;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 物流跟踪记录Mapper
 */
@Mapper
public interface TrackingRecordMapper {

    /**
     * 插入跟踪记录
     */
    @Insert("INSERT INTO tracking_record (tracking_number, carrier_code, carrier_name, order_id, order_number, " +
            "origin_country, destination_country, status, sub_status, sub_status_desc, tracking_status, " +
            "register_time, tag, remark, lang, param, create_time, update_time) " +
            "VALUES (#{trackingNumber}, #{carrierCode}, #{carrierName}, #{orderId}, #{orderNumber}, " +
            "#{originCountry}, #{destinationCountry}, #{status}, #{subStatus}, #{subStatusDesc}, #{trackingStatus}, " +
            "#{registerTime}, #{tag}, #{remark}, #{lang}, #{param}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(TrackingRecord trackingRecord);

    /**
     * 根据物流单号和运输商代码查询
     */
    @Select("SELECT * FROM tracking_record WHERE tracking_number = #{trackingNumber} AND carrier_code = #{carrierCode}")
    TrackingRecord findByTrackingNumberAndCarrier(@Param("trackingNumber") String trackingNumber, 
                                                  @Param("carrierCode") Integer carrierCode);

    /**
     * 根据订单ID查询
     */
    @Select("SELECT * FROM tracking_record WHERE order_id = #{orderId} ORDER BY create_time DESC")
    List<TrackingRecord> findByOrderId(Long orderId);

    /**
     * 根据ID查询
     */
    @Select("SELECT * FROM tracking_record WHERE id = #{id}")
    TrackingRecord findById(Long id);

    /**
     * 更新跟踪记录
     */
    @Update("UPDATE tracking_record SET carrier_name = #{carrierName}, status = #{status}, " +
            "sub_status = #{subStatus}, sub_status_desc = #{subStatusDesc}, tracking_status = #{trackingStatus}, " +
            "track_time = #{trackTime}, push_time = #{pushTime}, push_status = #{pushStatus}, " +
            "stop_track_time = #{stopTrackTime}, stop_track_reason = #{stopTrackReason}, " +
            "is_retracked = #{isRetracked}, carrier_change_count = #{carrierChangeCount}, " +
            "tag = #{tag}, remark = #{remark}, latest_event_time = #{latestEventTime}, " +
            "latest_event_info = #{latestEventInfo}, pickup_time = #{pickupTime}, delivery_time = #{deliveryTime}, " +
            "days_after_order = #{daysAfterOrder}, days_after_last_update = #{daysAfterLastUpdate}, " +
            "days_of_transit = #{daysOfTransit}, days_of_transit_done = #{daysOfTransitDone}, " +
            "update_time = #{updateTime} WHERE id = #{id}")
    void update(TrackingRecord trackingRecord);

    /**
     * 删除跟踪记录
     */
    @Delete("DELETE FROM tracking_record WHERE tracking_number = #{trackingNumber} AND carrier_code = #{carrierCode}")
    void deleteByTrackingNumberAndCarrier(@Param("trackingNumber") String trackingNumber, 
                                          @Param("carrierCode") Integer carrierCode);

    /**
     * 根据状态查询跟踪记录
     */
    @Select("SELECT * FROM tracking_record WHERE tracking_status = #{trackingStatus}")
    List<TrackingRecord> findByTrackingStatus(String trackingStatus);

    /**
     * 查询需要同步的跟踪记录
     */
    @Select("SELECT * FROM tracking_record WHERE tracking_status = 'Tracking' " +
            "AND (track_time IS NULL OR track_time < DATE_SUB(NOW(), INTERVAL #{hours} HOUR))")
    List<TrackingRecord> findNeedSyncRecords(@Param("hours") int hours);
}
