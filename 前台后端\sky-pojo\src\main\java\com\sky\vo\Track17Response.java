package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 17TRACK API响应包装类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17Response<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 响应代码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 创建成功响应
     */
    public static <T> Track17Response<T> success(T data) {
        return Track17Response.<T>builder()
                .code(200)
                .message("success")
                .data(data)
                .success(true)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static <T> Track17Response<T> error(String message) {
        return Track17Response.<T>builder()
                .code(500)
                .message(message)
                .success(false)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static <T> Track17Response<T> error(Integer code, String message) {
        return Track17Response.<T>builder()
                .code(code)
                .message(message)
                .success(false)
                .build();
    }
}
