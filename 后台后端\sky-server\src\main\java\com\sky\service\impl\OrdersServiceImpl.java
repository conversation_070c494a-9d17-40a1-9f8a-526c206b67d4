package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sky.dto.OrderPageQueryDTO;
import com.sky.dto.ShipOrderDTO;
import com.sky.entity.OrderDetail;
import com.sky.entity.Orders;
import com.sky.entity.OrderLogisticsTracking;
import com.sky.entity.OrderAddressInfo;
import com.sky.entity.OrderPaymentDetail;
import com.sky.entity.LogisticsTraceDetail;
import com.sky.exception.OrderException;
import com.sky.exception.LogisticsException;
import com.sky.mapper.OrderDetailMapper;
import com.sky.mapper.OrdersMapper;
import com.sky.mapper.OrderLogisticsTrackingMapper;
import com.sky.mapper.OrderAddressInfoMapper;
import com.sky.mapper.OrderPaymentDetailMapper;
import com.sky.mapper.UserAddressMapper;
import com.sky.mapper.LogisticsTraceDetailMapper;
import com.sky.service.OrderLogService;
import com.sky.service.OrdersService;
import com.sky.service.TrackingService;
import com.sky.service.Track17Service;
import com.sky.dto.RegisterTrackingDTO;
import com.sky.service.CarrierInfoService;

import com.sky.vo.OrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Service
@Slf4j
public class OrdersServiceImpl implements OrdersService {
    @Autowired
    private OrdersMapper ordersMapper;

    @Autowired
    private OrderDetailMapper orderDetailMapper;

    @Autowired
    private OrderLogService orderLogService;

    @Autowired
    private OrderLogisticsTrackingMapper orderLogisticsTrackingMapper;

    @Autowired
    private OrderAddressInfoMapper orderAddressInfoMapper;

    @Autowired
    private OrderPaymentDetailMapper orderPaymentDetailMapper;

    @Autowired
    private UserAddressMapper userAddressMapper;

    @Autowired
    private LogisticsTraceDetailMapper logisticsTraceDetailMapper;

    @Autowired
    private Track17Service track17Service;

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private CarrierInfoService carrierInfoService;



    @Override
    public OrderVO getOrderById(Long id) {
        Orders order = ordersMapper.findById(id);
        if (order == null) {
            throw new OrderException("订单不存在");
        }
        List<OrderDetail> details = orderDetailMapper.findByOrderId(id);

        OrderVO vo = new OrderVO();
        BeanUtils.copyProperties(order, vo);
        vo.setOrderDetails(details);

        return vo;
    }

    @Override
    public OrderVO getOrderByNumber(String number) {
        Orders order = ordersMapper.getByNumber(number);
        if (order == null) {
            throw new OrderException("订单不存在");
        }
        List<OrderDetail> details = orderDetailMapper.findByOrderId(order.getId());

        OrderVO vo = new OrderVO();
        BeanUtils.copyProperties(order, vo);
        vo.setOrderDetails(details);

        return vo;
    }

    @Override
    @Transactional
    public void cancelOrder(Long id, String cancelReason) {
        LocalDateTime now = LocalDateTime.now();
        ordersMapper.cancelOrder(id, Orders.STATUS_CANCELLED, now, cancelReason, now);
    }

    @Override
    public List<OrderVO> getOrderHistory(Long buyerId) {
        List<Orders> orders = ordersMapper.findHistoryByBuyerId(buyerId);
        return orders.stream().map(order -> {
            List<OrderDetail> details = orderDetailMapper.findByOrderId(order.getId());
            OrderVO vo = new OrderVO();
            BeanUtils.copyProperties(order, vo);
            vo.setOrderDetails(details);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public PageInfo<OrderVO> pageQuery(OrderPageQueryDTO orderPageQueryDTO) {
        // 预处理排序字段名，将驼峰格式转换为下划线格式
        String sortField = orderPageQueryDTO.getSortField();
        if (sortField != null) {
            switch (sortField) {
                case "orderTime":
                    orderPageQueryDTO.setSortField("order_time");
                    break;
                case "payTime":
                    orderPageQueryDTO.setSortField("pay_time");
                    break;
                case "shipTime":
                    orderPageQueryDTO.setSortField("ship_time");
                    break;
                case "completeTime":
                    orderPageQueryDTO.setSortField("complete_time");
                    break;
                case "cancelTime":
                    orderPageQueryDTO.setSortField("cancel_time");
                    break;
                case "createTime":
                    orderPageQueryDTO.setSortField("create_time");
                    break;
                case "updateTime":
                    orderPageQueryDTO.setSortField("update_time");
                    break;
                case "buyerId":
                    orderPageQueryDTO.setSortField("buyer_id");
                    break;
                case "addressId":
                    orderPageQueryDTO.setSortField("address_id");
                    break;
                case "shippingMethodId":
                    orderPageQueryDTO.setSortField("shipping_method_id");
                    break;
                case "payMethod":
                    orderPageQueryDTO.setSortField("pay_method");
                    break;
                case "paymentTransactionId":
                    orderPageQueryDTO.setSortField("payment_transaction_id");
                    break;
                case "orderRemark":
                    orderPageQueryDTO.setSortField("order_remark");
                    break;
                case "cancelReason":
                    orderPageQueryDTO.setSortField("cancel_reason");
                    break;
                case "rejectionReason":
                    orderPageQueryDTO.setSortField("rejection_reason");
                    break;
                case "cancelRequest":
                    orderPageQueryDTO.setSortField("cancel_request");
                    break;
                // 对于已经是下划线格式的字段名，保持不变
                default:
                    // 不做处理，保持原值
                    break;
            }
        }

        PageHelper.startPage(orderPageQueryDTO.getPage(), orderPageQueryDTO.getPageSize());
        Page<Orders> page = ordersMapper.pageQuery(orderPageQueryDTO);

        List<OrderVO> orderVOList = page.getResult().stream().map(order -> {
            List<OrderDetail> details = orderDetailMapper.findByOrderId(order.getId());
            OrderVO vo = new OrderVO();
            BeanUtils.copyProperties(order, vo);
            vo.setOrderDetails(details);
            return vo;
        }).collect(Collectors.toList());

        return new PageInfo<>(orderVOList);
    }

    @Override
    @Transactional
    public void shipOrder(Long id) {
        LocalDateTime now = LocalDateTime.now();
        ordersMapper.shipOrder(id, Orders.STATUS_SHIPPED, now, now);
    }

    @Override
    @Transactional
    public void shipOrder(ShipOrderDTO shipOrderDTO) {
        // 1. 验证订单是否存在
        Orders order = ordersMapper.findById(shipOrderDTO.getOrderId());
        if (order == null) {
            throw new OrderException("订单不存在，订单ID：" + shipOrderDTO.getOrderId());
        }

        // 2. 验证订单状态是否可以发货
        if (!Orders.STATUS_PAID.equals(order.getStatus())) {
            String statusDesc = getStatusDescription(order.getStatus());
            throw new OrderException("订单状态不允许发货，当前状态：" + statusDesc);
        }

        // 3. 验证物流单号（使用17TRACK API进行真实性验证）
        try {
            boolean isValidTracking = trackingService.validateTrackingNumber(
                    shipOrderDTO.getTrackingNumber(),
                    shipOrderDTO.getCourierCode()
            );

            if (!isValidTracking) {
                String errorMsg = String.format("物流单号验证失败，请检查以下信息：\n" +
                        "1. 物流单号：%s\n" +
                        "2. 运输商代码：%s\n" +
                        "3. 确保物流单号格式正确且已在运输商系统中生成",
                        shipOrderDTO.getTrackingNumber(),
                        shipOrderDTO.getCourierCode());
                throw new LogisticsException(errorMsg);
            }
        } catch (Exception e) {
            // 验证过程中的任何异常都应该阻止发货
            String errorMsg = String.format("物流单号验证失败：%s\n" +
                    "物流单号：%s\n" +
                    "运输商代码：%s\n" +
                    "请确保：\n" +
                    "1. 17TRACK API配置正确\n" +
                    "2. 物流单号在运输商系统中真实存在\n" +
                    "3. 运输商代码正确",
                    e.getMessage(),
                    shipOrderDTO.getTrackingNumber(),
                    shipOrderDTO.getCourierCode());
            log.error("物流单号验证异常，阻止发货：{}", errorMsg, e);
            throw new LogisticsException(errorMsg);
        }

        // 4. 验证运输商代码并获取数字代码
        Integer numericCarrierCode = carrierInfoService.getCarrierNumericCode(shipOrderDTO.getCourierCode());

        if (numericCarrierCode == null) {
            String errorMsg = String.format("运输商代码无效，请检查以下信息：\n" +
                    "1. 物流单号：%s\n" +
                    "2. 运输商代码：%s\n" +
                    "3. 支持的运输商代码：usps, fedex, ups, dhl, tnt, aramex, dpd, gls, china-post, ems, sf-express, yto, sto, zto, yunda",
                    shipOrderDTO.getTrackingNumber(),
                    shipOrderDTO.getCourierCode());
            throw new LogisticsException(errorMsg);
        }

        // 5. 获取运输商名称（使用映射或前端传递的名称）
        String carrierName = getCarrierDisplayName(shipOrderDTO.getCourierCode(), shipOrderDTO.getCourierName());

        // 5. 更新订单状态和物流信息
        LocalDateTime now = LocalDateTime.now();
        ordersMapper.shipOrderWithTracking(
                shipOrderDTO.getOrderId(),
                Orders.STATUS_SHIPPED,
                now,
                now,
                shipOrderDTO.getTrackingNumber(),
                shipOrderDTO.getCourierCode(),
                carrierName
        );

        // 6. 记录操作日志
        orderLogService.recordStatusChangeLog(
                order,
                Orders.STATUS_PAID,
                "发货",
                "系统管理员",
                "物流单号：" + shipOrderDTO.getTrackingNumber() + "，快递公司：" + carrierName
        );

        // 7. 创建17TRACK物流跟踪记录
        try {
            RegisterTrackingDTO registerDTO = RegisterTrackingDTO.builder()
                    .trackingNumber(shipOrderDTO.getTrackingNumber())
                    .carrierCode(numericCarrierCode) // 使用映射后的数字代码
                    .orderId(order.getId())
                    .orderNumber(order.getNumber())
                    .tag("订单发货")
                    .remark("订单发货创建的物流跟踪")
                    .autoDetection(true)
                    .build();

            track17Service.registerTracking(registerDTO);
            log.info("订单 {} 发货成功，物流单号：{}，运输商：{}", order.getNumber(),
                    shipOrderDTO.getTrackingNumber(), carrierName);
        } catch (Exception e) {
            log.warn("创建17TRACK物流跟踪记录失败：{}", e.getMessage());
            // 不影响发货流程，只记录警告日志
        }

        // 8. 同步数据到前台后端表（为前台后端订单详情集成功能提供数据）
        try {
            syncDataToFrontendTables(order, shipOrderDTO, carrierName, now);
            log.info("订单 {} 数据同步到前台后端表成功", order.getNumber());
        } catch (Exception e) {
            log.warn("同步数据到前台后端表失败：{}", e.getMessage());
            // 不影响发货流程，只记录警告日志
        }
    }



    @Override
    @Transactional
    public void completeOrder(Long id) {
        LocalDateTime now = LocalDateTime.now();
        ordersMapper.completeOrder(id, Orders.STATUS_COMPLETED, now, now);
    }

    /**
     * 获取运输商显示名称
     * @param courierCode 运输商代码
     * @param courierName 前端传递的运输商名称
     * @return 显示名称
     */
    private String getCarrierDisplayName(String courierCode, String courierName) {
        if (courierName != null && !courierName.trim().isEmpty()) {
            return courierName;
        }

        // 如果前端没有传递名称，使用默认映射
        switch (courierCode.toLowerCase()) {
            case "usps": return "USPS";
            case "fedex": return "FedEx";
            case "ups": return "UPS";
            case "dhl": return "DHL";
            case "tnt": return "TNT";
            case "aramex": return "Aramex";
            case "dpd": return "DPD";
            case "gls": return "GLS";
            case "china-post": return "China Post";
            case "ems": return "EMS";
            case "sf-express": return "顺丰速运";
            case "yto": return "圆通速递";
            case "sto": return "申通快递";
            case "zto": return "中通快递";
            case "yunda": return "韵达快递";
            default: return courierCode.toUpperCase();
        }
    }

    @Override
    public Map<String, Object> getOrderStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 统计各状态订单数量
        statistics.put("pendingPayment", ordersMapper.countByStatus(Orders.STATUS_PENDING_PAYMENT));
        statistics.put("paid", ordersMapper.countByStatus(Orders.STATUS_PAID));
        statistics.put("processing", ordersMapper.countByStatus(Orders.STATUS_PROCESSING));
        statistics.put("shipped", ordersMapper.countByStatus(Orders.STATUS_SHIPPED));
        statistics.put("completed", ordersMapper.countByStatus(Orders.STATUS_COMPLETED));
        statistics.put("cancelled", ordersMapper.countByStatus(Orders.STATUS_CANCELLED));
        statistics.put("refunded", ordersMapper.countByStatus(Orders.STATUS_REFUNDED));

        // 统计销售额（已完成和已发货的订单）
        Double totalSales = ordersMapper.sumAmountByStatus(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED);
        statistics.put("totalSales", totalSales != null ? totalSales : 0.0);

        // 按状态分组统计
        List<Map<String, Object>> statusGroup = ordersMapper.countGroupByStatus();
        statistics.put("statusDistribution", statusGroup);

        return statistics;
    }

    @Override
    public List<OrderVO> getOrdersByBuyerId(Long buyerId) {
        List<Orders> orders = ordersMapper.getByBuyerId(buyerId);
        return orders.stream().map(order -> {
            List<OrderDetail> details = orderDetailMapper.findByOrderId(order.getId());
            OrderVO vo = new OrderVO();
            BeanUtils.copyProperties(order, vo);
            vo.setOrderDetails(details);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void editOrder(Long orderId, String addressInfo, String remark, String editReason) {
        Orders order = ordersMapper.findById(orderId);
        if (order == null) {
            throw new OrderException("订单不存在");
        }

        // 记录编辑日志
        orderLogService.recordLog(orderId, order.getNumber(), "edit",
                                 order.getStatus(), order.getStatus(), "admin",
                                 1L, "管理员", editReason);
    }

    @Override
    @Transactional
    public void refundOrder(Long orderId, String refundReason, String refundAmount) {
        Orders order = ordersMapper.findById(orderId);
        if (order == null) {
            throw new OrderException("订单不存在");
        }

        Integer oldStatus = order.getStatus();
        LocalDateTime now = LocalDateTime.now();

        // Update order status to refunded
        ordersMapper.updateStatus(orderId, Orders.STATUS_REFUNDED, now);

        // Record refund log
        orderLogService.recordLog(orderId, order.getNumber(), "refund",
                                 oldStatus, Orders.STATUS_REFUNDED, "admin",
                                 1L, "Admin", "Refund reason: " + refundReason + ", Amount: " + refundAmount);
    }

    @Override
    public List<Object> getOrderLogs(Long orderId) {
        return orderLogService.getLogsByOrderId(orderId).stream()
                .map(log -> (Object) log)
                .collect(Collectors.toList());
    }



    @Override
    public Map<String, Object> getOrderOverview() {
        Map<String, Object> overview = new HashMap<>();

        // 今日订单统计
        overview.put("todayOrders", ordersMapper.countByStatus(Orders.STATUS_COMPLETED));
        overview.put("pendingOrders", ordersMapper.countByStatus(Orders.STATUS_PENDING_PAYMENT));
        overview.put("paidOrders", ordersMapper.countByStatus(Orders.STATUS_PAID));
        overview.put("shippedOrders", ordersMapper.countByStatus(Orders.STATUS_SHIPPED));

        // Sales statistics (completed and shipped orders)
        Double totalSales = ordersMapper.sumAmountByStatus(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED);
        overview.put("totalSales", totalSales != null ? totalSales : 0.0);

        return overview;
    }

    @Override
    public String exportOrders(List<Long> orderIds, String format) {
        // 实现订单导出逻辑
        // 这里可以根据format参数导出为Excel、CSV、PDF等格式
        String fileName = "orders_export_" + System.currentTimeMillis();

        switch (format.toLowerCase()) {
            case "excel":
                fileName += ".xlsx";
                break;
            case "csv":
                fileName += ".csv";
                break;
            case "pdf":
                fileName += ".pdf";
                break;
            default:
                fileName += ".xlsx";
        }

        // 返回下载链接
        return "/api/download/" + fileName;
    }

    /**
     * 获取订单状态描述
     */
    private String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case 1: // Orders.STATUS_PENDING_PAYMENT
                return "待付款";
            case 2: // Orders.STATUS_PAID
                return "已付款";
            case 3: // Orders.STATUS_PROCESSING
                return "处理中";
            case 4: // Orders.STATUS_SHIPPED
                return "已发货";
            case 5: // Orders.STATUS_COMPLETED
                return "已完成";
            case 6: // Orders.STATUS_CANCELLED
                return "已取消";
            case 7: // Orders.STATUS_REFUNDED
                return "已退款";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 同步数据到前台后端表
     * 为前台后端订单详情集成功能提供数据支持
     */
    private void syncDataToFrontendTables(Orders order, ShipOrderDTO shipOrderDTO, String carrierName, LocalDateTime now) {
        // 1. 创建物流跟踪记录
        OrderLogisticsTracking tracking = OrderLogisticsTracking.builder()
                .orderId(order.getId())
                .orderNumber(order.getNumber())
                .trackingNumber(shipOrderDTO.getTrackingNumber())
                .carrierCode(shipOrderDTO.getCourierCode())
                .carrierName(carrierName)
                .currentStatus("InTransit")
                .currentStatusDesc("运输途中")
                .latestLocation("发货地")
                .shipTime(now)
                .lastUpdateTime(now)
                .createTime(now)
                .updateTime(now)
                .build();

        orderLogisticsTrackingMapper.insert(tracking);
        log.info("创建物流跟踪记录成功，订单：{}，物流单号：{}", order.getNumber(), shipOrderDTO.getTrackingNumber());

        // 创建初始物流轨迹记录
        createInitialLogisticsTraces(tracking.getId(), shipOrderDTO.getTrackingNumber(), now);

        // 2. 创建收货地址信息（从user_address表获取真实地址）
        if (order.getAddressId() != null) {
            try {
                // 从user_address表获取真实地址信息
                OrderAddressInfo deliveryAddress = getUserAddressInfo(order.getAddressId(), order.getId(), 1, now);

                if (deliveryAddress != null) {
                    orderAddressInfoMapper.insert(deliveryAddress);
                    log.info("创建收货地址记录成功，订单：{}，收货人：{}", order.getNumber(), deliveryAddress.getConsigneeName());
                }
            } catch (Exception e) {
                log.warn("创建收货地址记录失败：{}", e.getMessage());
            }
        }

        // 3. 创建发货地址信息
        try {
            OrderAddressInfo senderAddress = OrderAddressInfo.builder()
                    .orderId(order.getId())
                    .addressType(2) // 发货地址
                    .consigneeName("发货仓库")
                    .phone("************")
                    .provinceName("广东省")
                    .cityName("深圳市")
                    .districtName("宝安区")
                    .detailAddress("西乡街道物流园区A区1号仓库")
                    .postalCode("518000")
                    .isDefault(0)
                    .createTime(now)
                    .updateTime(now)
                    .build();

            orderAddressInfoMapper.insert(senderAddress);
            log.info("创建发货地址记录成功，订单：{}", order.getNumber());
        } catch (Exception e) {
            log.warn("创建发货地址记录失败：{}", e.getMessage());
        }

        // 4. 创建支付详情信息（从订单中获取）
        try {
            OrderPaymentDetail paymentDetail = OrderPaymentDetail.builder()
                    .orderId(order.getId())
                    .orderNumber(order.getNumber())
                    .paymentMethod(getPaymentMethodCode(order.getPayMethod()))
                    .paymentChannel(getPaymentChannelCode(order.getPayMethod()))
                    .transactionId(order.getNumber() + "_" + System.currentTimeMillis())
                    .paymentAmount(order.getAmount())
                    .currency("CNY")
                    .paymentStatus("success")
                    .paymentTime(order.getPayTime())
                    .refundAmount(java.math.BigDecimal.ZERO)
                    .paymentDesc(getPaymentMethodName(order.getPayMethod()))
                    .createTime(now)
                    .updateTime(now)
                    .build();

            orderPaymentDetailMapper.insert(paymentDetail);
            log.info("创建支付详情记录成功，订单：{}", order.getNumber());
        } catch (Exception e) {
            log.warn("创建支付详情记录失败：{}", e.getMessage());
        }
    }

    /**
     * 获取支付方式代码
     */
    private String getPaymentMethodCode(Integer payMethod) {
        if (payMethod == null) return "unknown";
        switch (payMethod) {
            case 1: return "wechat";
            case 2: return "alipay";
            case 3: return "bank";
            default: return "unknown";
        }
    }

    /**
     * 获取支付渠道代码
     */
    private String getPaymentChannelCode(Integer payMethod) {
        if (payMethod == null) return "unknown";
        switch (payMethod) {
            case 1: return "wxpay";
            case 2: return "alipay";
            case 3: return "bank";
            default: return "unknown";
        }
    }

    /**
     * 获取支付方式名称
     */
    private String getPaymentMethodName(Integer payMethod) {
        if (payMethod == null) return "未知支付方式";
        switch (payMethod) {
            case 1: return "微信支付";
            case 2: return "支付宝";
            case 3: return "银行卡";
            default: return "未知支付方式";
        }
    }

    /**
     * 从user_address表获取地址信息并转换为OrderAddressInfo
     */
    private OrderAddressInfo getUserAddressInfo(Long addressId, Long orderId, Integer addressType, LocalDateTime now) {
        try {
            // 从数据库查询真实的用户地址信息
            Map<String, Object> userAddress = userAddressMapper.getUserAddressById(addressId);

            if (userAddress == null) {
                log.warn("未找到地址信息，addressId: {}", addressId);
                return null;
            }

            // 转换为OrderAddressInfo
            return OrderAddressInfo.builder()
                    .orderId(orderId)
                    .addressType(addressType)
                    .consigneeName((String) userAddress.get("name"))
                    .phone((String) userAddress.get("phone_number"))
                    .provinceName((String) userAddress.get("state"))
                    .cityName((String) userAddress.get("city"))
                    .districtName("") // user_address表中没有区县信息
                    .detailAddress(userAddress.get("street") + " " + userAddress.get("addressDetail"))
                    .postalCode((String) userAddress.get("zip_code"))
                    .isDefault((Integer) userAddress.get("Default"))
                    .createTime(now)
                    .updateTime(now)
                    .build();
        } catch (Exception e) {
            log.warn("查询用户地址失败，addressId: {}, error: {}", addressId, e.getMessage());
            return null;
        }
    }

    /**
     * 创建初始物流轨迹记录
     */
    private void createInitialLogisticsTraces(Long trackingId, String trackingNumber, LocalDateTime now) {
        try {
            // 创建发货轨迹
            LogisticsTraceDetail shipTrace = LogisticsTraceDetail.builder()
                    .trackingId(trackingId)
                    .trackingNumber(trackingNumber)
                    .traceTime(now)
                    .traceLocation("深圳发货仓库")
                    .traceStatus("Shipped")
                    .traceDesc("商品已从发货仓库发出")
                    .operator("仓库管理员")
                    .phone("************")
                    .sortOrder(1)
                    .createTime(now)
                    .build();

            logisticsTraceDetailMapper.insert(shipTrace);

            // 创建运输中轨迹
            LogisticsTraceDetail transitTrace = LogisticsTraceDetail.builder()
                    .trackingId(trackingId)
                    .trackingNumber(trackingNumber)
                    .traceTime(now.plusMinutes(30))
                    .traceLocation("深圳分拣中心")
                    .traceStatus("InTransit")
                    .traceDesc("商品已到达分拣中心，正在处理中")
                    .operator("分拣员")
                    .phone("************")
                    .sortOrder(2)
                    .createTime(now)
                    .build();

            logisticsTraceDetailMapper.insert(transitTrace);

            log.info("创建初始物流轨迹成功，物流单号：{}", trackingNumber);
        } catch (Exception e) {
            log.warn("创建初始物流轨迹失败：{}", e.getMessage());
        }
    }
}
