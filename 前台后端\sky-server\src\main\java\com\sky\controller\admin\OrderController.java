package com.sky.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.sky.context.BaseContext;
import com.sky.dto.OrderSubmitDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.OrderService;
import com.sky.vo.OrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/orders")
@Api(tags = "订单相关接口")
@Slf4j
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 提交订单（已支付）
     */
    @PostMapping("/submit")
    @ApiOperation("提交订单（已支付）")
    public Result<OrderVO> submit(@RequestBody @Valid OrderSubmitDTO orderSubmitDTO) {
        log.info("提交已支付订单：{}", orderSubmitDTO);
        
        // 添加详细的参数验证日志
        if (orderSubmitDTO.getAddressId() == null) {
            log.error("提交订单失败：收货地址ID不能为空");
            return Result.error("收货地址ID不能为空");
        }
        if (orderSubmitDTO.getPayMethod() == null) {
            log.error("提交订单失败：支付方式不能为空");
            return Result.error("支付方式不能为空");
        }
        if (orderSubmitDTO.getShippingMethodId() == null) {
            log.error("提交订单失败：配送方式不能为空");
            return Result.error("配送方式不能为空");
        }
        if (orderSubmitDTO.getOrderItems() == null || orderSubmitDTO.getOrderItems().isEmpty()) {
            log.error("提交订单失败：订单详情不能为空");
            return Result.error("订单详情不能为空");
        }
        if (orderSubmitDTO.getAmount() == null) {
            log.error("提交订单失败：订单金额不能为空");
            return Result.error("订单金额不能为空");
        }
        if (orderSubmitDTO.getPaymentTransactionId() == null) {
            log.error("提交订单失败：支付交易ID不能为空");
            return Result.error("支付交易ID不能为空");
        }
        if (orderSubmitDTO.getPayTimeStamp() == null) {
            log.error("提交订单失败：支付时间戳不能为空");
            return Result.error("支付时间戳不能为空");
        }
        
        // 获取当前登录用户ID
        Long userId = BaseContext.getCurrentId();
        OrderVO orderVO = orderService.createOrder(orderSubmitDTO, userId);
        return Result.success(orderVO);
    }

    /**
     * 订单取消
     */
    @PutMapping("/{id}/cancel")
    @ApiOperation("订单取消")
    public Result<Boolean> cancel(
            @PathVariable("id") Long id,
            @RequestParam(value = "cancelReason", required = false) String cancelReason) {
        log.info("订单取消：id={}, cancelReason={}", id, cancelReason);
        boolean success = orderService.cancelOrder(id, cancelReason);
        return Result.success(success);
    }

    /**
     * 订单发货
     */
    @PutMapping("/{id}/ship")
    @ApiOperation("订单发货")
    public Result<Boolean> ship(
            @PathVariable("id") Long id,
            @RequestParam("logisticsCompany") String logisticsCompany,
            @RequestParam("logisticsNumber") String logisticsNumber) {
        log.info("订单发货：id={}, logisticsCompany={}, logisticsNumber={}", id, logisticsCompany, logisticsNumber);
        boolean success = orderService.shipOrder(id, logisticsCompany, logisticsNumber);
        return Result.success(success);
    }

    /**
     * 确认收货
     */
    @PutMapping("/{id}/confirm")
    @ApiOperation("确认收货")
    public Result<Boolean> confirm(@PathVariable("id") Long id) {
        log.info("确认收货：id={}", id);
        boolean success = orderService.confirmOrder(id);
        return Result.success(success);
    }

    /**
     * 根据ID查询订单
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询订单")
    public Result<OrderVO> getById(@PathVariable("id") Long id) {
        log.info("根据ID查询订单：id={}", id);
        OrderVO orderVO = orderService.getOrderById(id);
        return Result.success(orderVO);
    }

    /**
     * 根据订单号查询订单
     */
    @GetMapping("/number/{number}")
    @ApiOperation("根据订单号查询订单")
    public Result<OrderVO> getByNumber(@PathVariable("number") String number) {
        log.info("根据订单号查询订单：number={}", number);
        OrderVO orderVO = orderService.getOrderByNumber(number);
        return Result.success(orderVO);
    }

    /**
     * 查询当前用户的订单
     */
    @GetMapping("/user")
    @ApiOperation("查询当前用户的订单")
    public Result<List<OrderVO>> getCurrentUserOrders(
            @RequestParam(value = "status", required = false) Integer status) {
        log.info("查询当前用户的订单：status={}", status);
        List<OrderVO> orderVOList = orderService.getOrdersByBuyerId(BaseContext.getCurrentId(), status);
        return Result.success(orderVOList);
    }

    /**
     * 分页查询订单
     */
    @GetMapping("/page")
    @ApiOperation("分页查询订单")
    public Result<PageResult> page(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "status", required = false) Integer status) {
        log.info("分页查询订单：page={}, pageSize={}, userId={}, status={}", page, pageSize, userId, status);
        Page<OrderVO> pageResult = orderService.pageQuery(page, pageSize, userId, status);
        return Result.success(new PageResult(pageResult.getTotal(), pageResult.getRecords()));
    }
} 