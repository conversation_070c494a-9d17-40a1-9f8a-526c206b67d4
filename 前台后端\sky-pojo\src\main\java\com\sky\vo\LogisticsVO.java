package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流信息视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "物流信息视图对象")
public class LogisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("物流ID")
    private Long id;

    @ApiModelProperty("物流单号")
    private String logisticsNumber;

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("物流公司")
    private String logisticsCompany;

    @ApiModelProperty("物流状态")
    private String logisticsStatus;

    @ApiModelProperty("物流状态描述")
    private String logisticsStatusDesc;

    @ApiModelProperty("发货时间")
    private LocalDateTime shippingDate;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
} 