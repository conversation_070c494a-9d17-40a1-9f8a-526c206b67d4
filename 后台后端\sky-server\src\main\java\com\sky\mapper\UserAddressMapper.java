package com.sky.mapper;

import org.apache.ibatis.annotations.*;

import java.util.Map;

/**
 * 用户地址Mapper（用于查询地址信息）
 */
@Mapper
public interface UserAddressMapper {

    /**
     * 根据地址ID查询用户地址信息
     */
    @Select("SELECT id, user_id, name, street, city, state, zip_code, addressDetail, phone_number, `Default` " +
            "FROM user_address WHERE id = #{addressId}")
    Map<String, Object> getUserAddressById(@Param("addressId") Long addressId);
}
