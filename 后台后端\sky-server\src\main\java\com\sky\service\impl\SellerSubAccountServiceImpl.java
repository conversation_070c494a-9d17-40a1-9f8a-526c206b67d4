package com.sky.service.impl;

import com.sky.constant.MessageConstant;
import com.sky.context.BaseContext;
import com.sky.dto.AdminAccountUpdateDTO;
import com.sky.dto.SellerSubAccountDTO;
import com.sky.entity.AdminAccount;
import com.sky.entity.BusinessException;
import com.sky.entity.Seller;
import com.sky.entity.SellerSubAccount;
import com.sky.exception.*;
import com.sky.mapper.SellerSubAccountMapper;
import com.sky.service.SellerSubAccountService;
import com.sky.vo.BatchDeleteResultVO;
import com.sky.vo.BatchUpdateStatusResultVO;
import com.sky.vo.SellerSubAccountUpdatePermissionsVO;
import com.sky.vo.SellerSubAccountVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class SellerSubAccountServiceImpl implements SellerSubAccountService {

    @Autowired
    private SellerSubAccountMapper sellerSubAccountMapper;

    @Transactional
    @Override
    public SellerSubAccountVO createSubAccount(SellerSubAccountDTO sellerSubAccountDTO) {

        SellerSubAccount selectByAccountName = sellerSubAccountMapper.selectByAccountName(sellerSubAccountDTO.getAccountName());
        if (selectByAccountName != null){
            throw new AccountNameIsExistException("账户名已存在");
        }

        SellerSubAccount selectByEmail = sellerSubAccountMapper.selectByEmail(sellerSubAccountDTO.getEmail());
        if (selectByEmail != null){
            throw new EmailIsExistException("邮箱已存在");
        }

        SellerSubAccount sellerSubAccount = new SellerSubAccount();
        BeanUtils.copyProperties(sellerSubAccountDTO, sellerSubAccount);
        sellerSubAccount.setCreateTime(LocalDateTime.now());
        sellerSubAccount.setUpdateTime(LocalDateTime.now());
        if (sellerSubAccountDTO.getAccountStatus() == null){
            sellerSubAccount.setAccountStatus(1);
        }
        sellerSubAccount.setIsSubAccount(1);
        sellerSubAccount.setPhotoUrl("https://hiram.oss-cn-beijing.aliyuncs.com/%E9%BB%98%E8%AE%A4%E5%A4%B4%E5%83%8F/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250302163653.jpg");
        sellerSubAccount.setUserRole("普通用户");
        sellerSubAccountMapper.save(sellerSubAccount);

        Long id = sellerSubAccountMapper.selectIdByAccountName(sellerSubAccount.getAccountName());
        List<String> permissions = sellerSubAccountDTO.getPermissions();
        sellerSubAccountMapper.savePermissions(id, permissions);


        SellerSubAccountVO sellerSubAccountVO = new SellerSubAccountVO();
        BeanUtils.copyProperties(sellerSubAccount, sellerSubAccountVO);
        sellerSubAccountVO.setId(id);
        return sellerSubAccountVO;
    }

    @Override
    public List<SellerSubAccountVO> getSubAccountList(Long createdBy, Integer accountStatus) {
        if (createdBy == null){
            createdBy = BaseContext.getCurrentId();
        }
        if (accountStatus == null){
            return sellerSubAccountMapper.getSubAccountList(createdBy,accountStatus);
        }

        List<SellerSubAccountVO> sellerSubAccountVO = sellerSubAccountMapper.getSubAccountList(createdBy,accountStatus);
        return sellerSubAccountVO;
    }

    @Override
    public SellerSubAccountVO getSubAccount(Long id,Long createdBy) {
        if (id == null){
            throw new IdIsNullException("账号不存在");
        }
        SellerSubAccount sellerSubAccount = sellerSubAccountMapper.getSubAccount(id,createdBy);

        List<String> permissions = sellerSubAccountMapper.getPermissionsBySellerId(id);

        SellerSubAccountVO sellerSubAccountVO = new SellerSubAccountVO();
        BeanUtils.copyProperties(sellerSubAccount, sellerSubAccountVO);
        sellerSubAccountVO.setPermissions(permissions);
        return sellerSubAccountVO;
    }

    @Transactional
    @Override
    public SellerSubAccountVO updateSellerSubAccount(Long id, SellerSubAccountDTO sellerSubAccountDTO) {
        if (id == null){
            throw new IdIsNullException("账号不存在");
        }

        SellerSubAccount selectByAccountName = sellerSubAccountMapper.selectByAccountName(sellerSubAccountDTO.getAccountName());
        String accountName = sellerSubAccountMapper.selectByAccountNameById(id);
        if(!accountName.equals(sellerSubAccountDTO.getAccountName())) {
            if (selectByAccountName != null) {
                throw new AccountNameIsExistException("账户名已存在");
            }
        }

        SellerSubAccount selectByEmail = sellerSubAccountMapper.selectByEmail(sellerSubAccountDTO.getEmail());
        String email = sellerSubAccountMapper.selectEmailById(id);
        if (!email.equals(sellerSubAccountDTO.getEmail())) {
            if (selectByEmail != null) {
                throw new EmailIsExistException("邮箱已存在");
            }
        }

        SellerSubAccount selectByPhone = sellerSubAccountMapper.selectByPhone(sellerSubAccountDTO.getPhone());
        String phone = sellerSubAccountMapper.selectPhoneById(id);
        if (!phone.equals(sellerSubAccountDTO.getPhone())){
            if (selectByPhone != null) {
                throw new BusinessException("手机号已存在");
            }
        }

        SellerSubAccount sellerSubAccount = new SellerSubAccount();
        BeanUtils.copyProperties(sellerSubAccountDTO, sellerSubAccount);
        sellerSubAccount.setId(id);
        sellerSubAccount.setUpdateTime(LocalDateTime.now());
        sellerSubAccountMapper.update(sellerSubAccount);

        SellerSubAccount subAccount = sellerSubAccountMapper.getSubAccount(id, sellerSubAccountDTO.getCreatedBy());
        List<String> permissions = sellerSubAccountMapper.getPermissionsBySellerId(id);

        SellerSubAccountVO sellerSubAccountVO = new SellerSubAccountVO();
        BeanUtils.copyProperties(subAccount, sellerSubAccountVO);
        sellerSubAccountVO.setPermissions(permissions);

        return sellerSubAccountVO;
    }

    @Transactional
    @Override
    public void deleteSellerSubAccount(Long id, Long createdBy) {
        if (id == null){
            throw new IdIsNullException(MessageConstant.ID_NOT_FOUND);
        }

        Long createdById = sellerSubAccountMapper.selectCreatedById(id);
        if (!createdById.equals(createdBy) || createdById == null){
            throw new BusinessException("无法删除非本账号下的子账号或者非子账号的账号");
        }

        //删除子账号前要先删除对应的权限
        sellerSubAccountMapper.deleteAllPermissions(id);

        //删除子账号
        sellerSubAccountMapper.deleteSubAccount(id,createdBy);
    }

    @Transactional
    @Override
    public SellerSubAccountUpdatePermissionsVO updateSellerSubAccountPermission(Long id, Long createdBy,List<String> permissions) {
        if (id == null){
            throw new IdIsNullException(MessageConstant.ID_NOT_FOUND);
        }

        Long createdById = sellerSubAccountMapper.selectCreatedById(id);
        if (!createdById.equals(createdBy) || createdById == null){
            throw new BusinessException("无法更新非本账号下的子账号");
        }

        List<String> permissionsBySellerId = sellerSubAccountMapper.getPermissionsBySellerId(createdById);
        if (!permissionsBySellerId.contains("ManageStaff")){
            throw new BusinessException("无权限修改子账号权限");
        }

        if (!permissionsBySellerId.containsAll(permissions)){
            throw new BusinessException("该账户没有被授予分配列表中的权限");
        }

        //清空权限
        sellerSubAccountMapper.deleteAllPermissions(id);

        //保存权限
        sellerSubAccountMapper.savePermissions(id, permissions);

        SellerSubAccountUpdatePermissionsVO vo = new SellerSubAccountUpdatePermissionsVO();
        vo.setId(id);
        vo.setPermissions(permissions);
        vo.setAccountName(sellerSubAccountMapper.getSubAccount(id, createdBy).getAccountName());
        vo.setUpdateTime(LocalDateTime.now());
        return vo;
    }

    @Transactional
    @Override
    public BatchDeleteResultVO batchDeleteSubAccount(List<Long> ids, Long createdBy) {
        BatchDeleteResultVO batchDeleteResultVO = new BatchDeleteResultVO();

        if (ids == null || ids.isEmpty()) {
            batchDeleteResultVO.addFailId(0L, MessageConstant.ID_NOT_FOUND);
            return batchDeleteResultVO;
        }

        for (Long id : ids) {
            // 查询管理员是否存在
            SellerSubAccount sellerSubAccount = sellerSubAccountMapper.getSubAccount(id, createdBy);
            if (sellerSubAccount == null) {
                batchDeleteResultVO.addFailId(id, MessageConstant.ACCOUNT_NOT_FOUND);
                continue;
            }

            if (!(sellerSubAccount.getCreatedBy().equals(createdBy))){
                batchDeleteResultVO.addFailId(id, "无法删除非本账号下的子账号");
            }

            // 防止删除超级管理员
            if (id == 16) {
                batchDeleteResultVO.addFailId(id, MessageConstant.CANNOT_DELETE_ADMIN);
                continue;
            }
            // 删除账号
            //删除账号前将关联的权限删除
            sellerSubAccountMapper.deleteAllPermissions(id);
            sellerSubAccountMapper.deleteSubAccount(id, createdBy);

            batchDeleteResultVO.incrementSuccess();
        }

        return batchDeleteResultVO;
    }

    @Transactional
    @Override
    public BatchUpdateStatusResultVO batchUpdateSubAccountStatus(List<Long> ids,Integer status,Long createdBy) {
        BatchUpdateStatusResultVO result = new BatchUpdateStatusResultVO();

        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException(MessageConstant.ID_NOT_FOUND);
        }

        if (status == null || (status < 0 && status > 2)) {
            throw new IllegalArgumentException(MessageConstant.STATUS_IS_INVALID);
        }

        for (Long id : ids) {
            try {
                // 查询管理员是否存在
                SellerSubAccount sellerSubAccount = sellerSubAccountMapper.getSubAccount(id, createdBy);
                if (sellerSubAccount == null) {
                    result.addFailId(id);
                    continue;
                }

                if (!(sellerSubAccount.getCreatedBy().equals(createdBy))){
                   result.addFailId(id);
                }

                // 更新状态
                sellerSubAccountMapper.updateStatus(id, status);
                result.incrementSuccess();
            } catch (Exception e) {
                result.addFailId(id);
            }
        }

        return result;
    }

    @Override
    public void resetPassword(Long id, String password, Long createdBy) {
        // 校验管理员是否存在
        SellerSubAccount sellerSubAccount = sellerSubAccountMapper.getSubAccount(id, createdBy);

        if (sellerSubAccount == null) {
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        Long createdById = sellerSubAccountMapper.selectCreatedById(id);
        if (!createdById.equals(createdBy) || createdById == null){
            throw new BusinessException("无法重置非本账号下的子账号的密码");
        }

        AdminAccountUpdateDTO adminAccountUpdateDTO = new AdminAccountUpdateDTO();
        adminAccountUpdateDTO.setId(id);
        adminAccountUpdateDTO.setPassword(password);

        // 更新密码
        sellerSubAccountMapper.resetPassword(id, password);
    }
}
