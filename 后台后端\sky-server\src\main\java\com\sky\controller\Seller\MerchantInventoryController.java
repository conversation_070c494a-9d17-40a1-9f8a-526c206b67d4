package com.sky.controller.Seller;

import com.sky.entity.MerchantInventory;
import com.sky.result.Result;
import com.sky.service.MerchantInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/merchantInventory")
@Api(tags = "商家库存管理接口")
public class MerchantInventoryController {
    @Autowired
    private MerchantInventoryService merchantInventoryService;

    @GetMapping("/list")
    @ApiOperation("获取所有库存列表")
    public Result<List<MerchantInventory>> findAll() {
        List<MerchantInventory> inventories = merchantInventoryService.findAll();
        return Result.success(inventories);
    }

    @GetMapping("/byProductCode/{productCode}")
    @ApiOperation("根据产品编码查找库存")
    public Result<List<MerchantInventory>> findByProductCode(@PathVariable String productCode) {
        List<MerchantInventory> inventories = merchantInventoryService.findByProductCode(productCode);
        return Result.success(inventories);
    }

    @GetMapping("/byItemCode/{itemCode}")
    @ApiOperation("根据商品编码查找库存")
    public Result<List<MerchantInventory>> findByItemCode(@PathVariable String itemCode) {
        List<MerchantInventory> inventories = merchantInventoryService.findByItemCode(itemCode);
        return Result.success(inventories);
    }

    @GetMapping("/byProductTitle/{productTitle}")
    @ApiOperation("根据产品标题查找库存")
    public Result<List<MerchantInventory>> findByProductTitle(@PathVariable String productTitle) {
        List<MerchantInventory> inventories = merchantInventoryService.findByProductTitle(productTitle);
        return Result.success(inventories);
    }

    @GetMapping("/byCreatedAtRange")
    @ApiOperation("根据创建时间范围查找库存")
    public Result<List<MerchantInventory>> findByCreatedAtRange(@RequestParam LocalDateTime startTime, @RequestParam LocalDateTime endTime) {
        List<MerchantInventory> inventories = merchantInventoryService.findByCreatedAtRange(startTime, endTime);
        return Result.success(inventories);
    }

    @PostMapping("/updateSafetyInventory")
    @ApiOperation("更新库存预警值")
    public Result<String> updateSafetyInventory(@RequestParam Long id, @RequestParam Integer safetyInventory) {
        merchantInventoryService.updateSafetyInventory(id, safetyInventory);
        return Result.success("库存预警值更新成功");
    }
}