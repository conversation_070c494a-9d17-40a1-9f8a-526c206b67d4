<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.PaymentRecordMapper">

    <select id="pageQuery" resultType="com.sky.vo.PaymentRecordVO">
        select d.* , c.pay_method as payMethod from payment_record d left outer join orders c on d.order_id = c.id
        <where>
            <if test="paymentMethod != null">
                and d.payment_method like concat('%',#{paymentMethod},'%')
            </if>
            <if test="orderId != null">
                and d.order_id = #{orderId}
            </if>
            <if test="amount != null">
                and d.amount = #{amount}
            </if>
            <if test="transactionStatus != null">
                and d.transaction_status = #{transactionStatus}
            </if>
        </where>
        order by d.create_time desc
    </select>

</mapper>
