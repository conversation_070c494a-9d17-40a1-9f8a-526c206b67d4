package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BuyerLoginDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String accountName;

    private String email;

    private String password;
/*    *//**
     * 验证码
     *//*
    private String code;

    *//**
     * 唯一标识
     *//*
    private String uuid;*/
}
