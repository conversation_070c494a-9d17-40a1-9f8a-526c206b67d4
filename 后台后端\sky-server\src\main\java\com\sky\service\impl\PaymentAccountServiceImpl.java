package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.dto.PaymentAccountDTO;
import com.sky.dto.PaymentAccountQueryDTO;
import com.sky.entity.SellerPaymentAccount;
import com.sky.exception.BaseException;
import com.sky.mapper.SellerPaymentAccountMapper;
import com.sky.service.PaymentAccountService;
import com.sky.vo.PageResult;
import com.sky.vo.PaymentAccountVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 收账账户信息服务实现类
 */
@Service
@Slf4j
public class PaymentAccountServiceImpl implements PaymentAccountService {

    @Autowired
    private SellerPaymentAccountMapper sellerPaymentAccountMapper;

    /**
     * 添加收账账户
     */
    @Override
    @Transactional
    public void addPaymentAccount(PaymentAccountDTO paymentAccountDTO, Long sellerId) {
        log.info("添加收账账户，商家ID：{}，账户信息：{}", sellerId, paymentAccountDTO);

        // 验证账户号码是否已存在
        int count = sellerPaymentAccountMapper.countByAccountNumber(
                sellerId, paymentAccountDTO.getAccountNumber(), paymentAccountDTO.getAccountType(), null);
        if (count > 0) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NUMBER_EXISTS);
        }

        // 验证银行卡类型必填字段
        if (paymentAccountDTO.getAccountType().equals(SellerPaymentAccount.ACCOUNT_TYPE_BANK_CARD)) {
            if (paymentAccountDTO.getBankName() == null || paymentAccountDTO.getBankName().trim().isEmpty()) {
                throw new BaseException(MessageConstant.BANK_NAME_REQUIRED);
            }
            if (paymentAccountDTO.getBranchName() == null || paymentAccountDTO.getBranchName().trim().isEmpty()) {
                throw new BaseException(MessageConstant.BRANCH_NAME_REQUIRED);
            }
        }

        // 构建实体对象
        SellerPaymentAccount sellerPaymentAccount = SellerPaymentAccount.builder()
                .sellerId(sellerId)
                .accountType(paymentAccountDTO.getAccountType())
                .accountName(paymentAccountDTO.getAccountName())
                .accountNumber(paymentAccountDTO.getAccountNumber())
                .bankName(paymentAccountDTO.getBankName())
                .bankCode(paymentAccountDTO.getBankCode())
                .branchName(paymentAccountDTO.getBranchName())
                .platformName(paymentAccountDTO.getPlatformName())
                .platformAccount(paymentAccountDTO.getPlatformAccount())
                .isDefault(paymentAccountDTO.getIsDefault() != null ? paymentAccountDTO.getIsDefault() : SellerPaymentAccount.IS_DEFAULT_NO)
                .accountStatus(SellerPaymentAccount.ACCOUNT_STATUS_ENABLED)
                .verificationStatus(SellerPaymentAccount.VERIFICATION_STATUS_UNVERIFIED)
                .idCardNumber(paymentAccountDTO.getIdCardNumber())
                .phone(paymentAccountDTO.getPhone())
                .remark(paymentAccountDTO.getRemark())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 如果设置为默认账户，先清除其他默认账户
        if (SellerPaymentAccount.IS_DEFAULT_YES.equals(sellerPaymentAccount.getIsDefault())) {
            sellerPaymentAccountMapper.clearDefaultBySellerId(sellerId);
        }

        // 插入数据
        sellerPaymentAccountMapper.insert(sellerPaymentAccount);
        log.info("收账账户添加成功，账户ID：{}", sellerPaymentAccount.getId());
    }

    /**
     * 更新收账账户
     */
    @Override
    @Transactional
    public void updatePaymentAccount(PaymentAccountDTO paymentAccountDTO, Long sellerId) {
        log.info("更新收账账户，商家ID：{}，账户信息：{}", sellerId, paymentAccountDTO);

        // 查询原账户信息
        SellerPaymentAccount existingAccount = sellerPaymentAccountMapper.selectByIdAndSellerId(paymentAccountDTO.getId(), sellerId);
        if (existingAccount == null) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NOT_FOUND);
        }

        // 验证账户号码是否已存在（排除当前账户）
        int count = sellerPaymentAccountMapper.countByAccountNumber(
                sellerId, paymentAccountDTO.getAccountNumber(), paymentAccountDTO.getAccountType(), paymentAccountDTO.getId());
        if (count > 0) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NUMBER_EXISTS);
        }

        // 验证银行卡类型必填字段
        if (paymentAccountDTO.getAccountType().equals(SellerPaymentAccount.ACCOUNT_TYPE_BANK_CARD)) {
            if (paymentAccountDTO.getBankName() == null || paymentAccountDTO.getBankName().trim().isEmpty()) {
                throw new BaseException(MessageConstant.BANK_NAME_REQUIRED);
            }
            if (paymentAccountDTO.getBranchName() == null || paymentAccountDTO.getBranchName().trim().isEmpty()) {
                throw new BaseException(MessageConstant.BRANCH_NAME_REQUIRED);
            }
        }

        // 更新实体对象
        BeanUtils.copyProperties(paymentAccountDTO, existingAccount);
        existingAccount.setUpdateTime(LocalDateTime.now());

        // 如果设置为默认账户，先清除其他默认账户
        if (SellerPaymentAccount.IS_DEFAULT_YES.equals(existingAccount.getIsDefault())) {
            sellerPaymentAccountMapper.clearDefaultBySellerId(sellerId);
        }

        // 更新数据
        sellerPaymentAccountMapper.update(existingAccount);
        log.info("收账账户更新成功，账户ID：{}", existingAccount.getId());
    }

    /**
     * 删除收账账户
     */
    @Override
    @Transactional
    public void deletePaymentAccount(Long id, Long sellerId) {
        log.info("删除收账账户，账户ID：{}，商家ID：{}", id, sellerId);

        // 查询账户信息
        SellerPaymentAccount account = sellerPaymentAccountMapper.selectByIdAndSellerId(id, sellerId);
        if (account == null) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NOT_FOUND);
        }

        // 删除账户
        sellerPaymentAccountMapper.deleteByIdAndSellerId(id, sellerId);
        log.info("收账账户删除成功，账户ID：{}", id);
    }

    /**
     * 根据ID查询收账账户
     */
    @Override
    public PaymentAccountVO getPaymentAccountById(Long id, Long sellerId) {
        log.info("查询收账账户，账户ID：{}，商家ID：{}", id, sellerId);

        List<PaymentAccountVO> accounts = sellerPaymentAccountMapper.selectBySellerId(sellerId);
        return accounts.stream()
                .filter(account -> account.getId().equals(id))
                .findFirst()
                .orElse(null);
    }

    /**
     * 分页查询收账账户
     */
    @Override
    public PageResult pageQuery(PaymentAccountQueryDTO queryDTO, Long sellerId) {
        log.info("分页查询收账账户，商家ID：{}，查询条件：{}", sellerId, queryDTO);

        // 设置商家ID
        queryDTO.setSellerId(sellerId);

        // 分页查询
        PageHelper.startPage(queryDTO.getPage(), queryDTO.getPageSize());
        Page<PaymentAccountVO> page = sellerPaymentAccountMapper.selectPage(queryDTO);

        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 查询商家的所有收账账户
     */
    @Override
    public List<PaymentAccountVO> getPaymentAccountsBySellerId(Long sellerId) {
        log.info("查询商家的所有收账账户，商家ID：{}", sellerId);
        return sellerPaymentAccountMapper.selectBySellerId(sellerId);
    }

    /**
     * 查询商家的默认收账账户
     */
    @Override
    public PaymentAccountVO getDefaultPaymentAccount(Long sellerId) {
        log.info("查询商家的默认收账账户，商家ID：{}", sellerId);
        return sellerPaymentAccountMapper.selectDefaultBySellerId(sellerId);
    }

    /**
     * 设置默认收账账户
     */
    @Override
    @Transactional
    public void setDefaultPaymentAccount(Long id, Long sellerId) {
        log.info("设置默认收账账户，账户ID：{}，商家ID：{}", id, sellerId);

        // 验证账户是否存在
        SellerPaymentAccount account = sellerPaymentAccountMapper.selectByIdAndSellerId(id, sellerId);
        if (account == null) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NOT_FOUND);
        }

        // 先清除其他默认账户
        sellerPaymentAccountMapper.clearDefaultBySellerId(sellerId);

        // 设置新的默认账户
        sellerPaymentAccountMapper.setDefault(id, sellerId);
        log.info("默认收账账户设置成功，账户ID：{}", id);
    }

    /**
     * 启用/禁用收账账户
     */
    @Override
    @Transactional
    public void updateAccountStatus(Long id, Long sellerId, Integer accountStatus) {
        log.info("更新收账账户状态，账户ID：{}，商家ID：{}，状态：{}", id, sellerId, accountStatus);

        // 验证账户是否存在
        SellerPaymentAccount account = sellerPaymentAccountMapper.selectByIdAndSellerId(id, sellerId);
        if (account == null) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NOT_FOUND);
        }

        // 更新状态
        sellerPaymentAccountMapper.updateStatus(id, sellerId, accountStatus);
        log.info("收账账户状态更新成功，账户ID：{}", id);
    }

    /**
     * 验证收账账户
     */
    @Override
    @Transactional
    public void verifyPaymentAccount(Long id, Long sellerId) {
        log.info("验证收账账户，账户ID：{}，商家ID：{}", id, sellerId);

        // 验证账户是否存在
        SellerPaymentAccount account = sellerPaymentAccountMapper.selectByIdAndSellerId(id, sellerId);
        if (account == null) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NOT_FOUND);
        }

        // 更新验证状态
        sellerPaymentAccountMapper.updateVerificationStatus(id, sellerId, SellerPaymentAccount.VERIFICATION_STATUS_VERIFIED);
        log.info("收账账户验证成功，账户ID：{}", id);
    }

    /**
     * 管理员分页查询所有收账账户
     */
    @Override
    public PageResult adminPageQuery(PaymentAccountQueryDTO queryDTO) {
        log.info("管理员分页查询收账账户，查询条件：{}", queryDTO);

        // 分页查询
        PageHelper.startPage(queryDTO.getPage(), queryDTO.getPageSize());
        Page<PaymentAccountVO> page = sellerPaymentAccountMapper.selectPage(queryDTO);

        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 管理员根据ID查询收账账户
     */
    @Override
    public PaymentAccountVO adminGetPaymentAccountById(Long id) {
        log.info("管理员查询收账账户，账户ID：{}", id);

        SellerPaymentAccount account = sellerPaymentAccountMapper.selectById(id);
        if (account == null) {
            return null;
        }

        // 查询详细信息
        PaymentAccountQueryDTO queryDTO = new PaymentAccountQueryDTO();
        queryDTO.setSellerId(account.getSellerId());
        PageHelper.startPage(1, 1);
        Page<PaymentAccountVO> page = sellerPaymentAccountMapper.selectPage(queryDTO);

        return page.getResult().stream()
                .filter(vo -> vo.getId().equals(id))
                .findFirst()
                .orElse(null);
    }

    /**
     * 管理员验证收账账户
     */
    @Override
    @Transactional
    public void adminVerifyPaymentAccount(Long id) {
        log.info("管理员验证收账账户，账户ID：{}", id);

        // 验证账户是否存在
        SellerPaymentAccount account = sellerPaymentAccountMapper.selectById(id);
        if (account == null) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NOT_FOUND);
        }

        // 更新验证状态
        sellerPaymentAccountMapper.updateVerificationStatus(id, account.getSellerId(), SellerPaymentAccount.VERIFICATION_STATUS_VERIFIED);
        log.info("管理员验证收账账户成功，账户ID：{}", id);
    }

    /**
     * 管理员启用/禁用收账账户
     */
    @Override
    @Transactional
    public void adminUpdateAccountStatus(Long id, Integer accountStatus) {
        log.info("管理员更新收账账户状态，账户ID：{}，状态：{}", id, accountStatus);

        // 验证账户是否存在
        SellerPaymentAccount account = sellerPaymentAccountMapper.selectById(id);
        if (account == null) {
            throw new BaseException(MessageConstant.PAYMENT_ACCOUNT_NOT_FOUND);
        }

        // 更新状态
        sellerPaymentAccountMapper.updateStatus(id, account.getSellerId(), accountStatus);
        log.info("管理员更新收账账户状态成功，账户ID：{}", id);
    }
}
