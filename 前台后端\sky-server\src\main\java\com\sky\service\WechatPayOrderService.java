package com.sky.service;

import com.sky.dto.WechatPayOrderQueryDTO;
import com.sky.vo.WechatPayOrderQueryVO;

/**
 * 微信支付订单服务接口
 */
public interface WechatPayOrderService {

    /**
     * 查询订单
     * 
     * @param queryDTO 查询参数
     * @return 订单详情
     */
    WechatPayOrderQueryVO queryOrder(WechatPayOrderQueryDTO queryDTO);
    
    /**
     * 根据微信支付订单号查询订单
     * 
     * @param transactionId 微信支付订单号
     * @return 订单详情
     */
    WechatPayOrderQueryVO queryOrderByTransactionId(String transactionId);
    
    /**
     * 根据商户订单号查询订单
     * 
     * @param outTradeNo 商户订单号
     * @return 订单详情
     */
    WechatPayOrderQueryVO queryOrderByOutTradeNo(String outTradeNo);
} 