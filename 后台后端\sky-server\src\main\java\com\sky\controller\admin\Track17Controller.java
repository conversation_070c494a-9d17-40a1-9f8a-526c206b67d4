package com.sky.controller.admin;

import com.sky.dto.RegisterTrackingDTO;
import com.sky.dto.TrackingQueryDTO;
import com.sky.entity.CarrierInfo;
import com.sky.entity.TrackingRecord;
import com.sky.result.Result;
import com.sky.service.CarrierInfoService;
import com.sky.service.Track17Service;
import com.sky.vo.TrackingDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 17TRACK物流跟踪管理控制器
 */
@RestController
@RequestMapping("/admin/tracking")
@Api(tags = "17TRACK物流跟踪管理")
@Slf4j
public class Track17Controller {

    @Autowired
    private Track17Service track17Service;

    @Autowired
    private CarrierInfoService carrierInfoService;

    @PostMapping("/register")
    @ApiOperation("注册物流单号")
    public Result<TrackingRecord> registerTracking(@Valid @RequestBody RegisterTrackingDTO registerDTO) {
        log.info("注册物流单号: {}", registerDTO.getTrackingNumber());
        TrackingRecord record = track17Service.registerTracking(registerDTO);
        return Result.success(record);
    }

    @PostMapping("/register/batch")
    @ApiOperation("批量注册物流单号")
    public Result<List<TrackingRecord>> batchRegisterTracking(@Valid @RequestBody List<RegisterTrackingDTO> registerDTOs) {
        log.info("批量注册物流单号，数量: {}", registerDTOs.size());
        List<TrackingRecord> records = track17Service.batchRegisterTracking(registerDTOs);
        return Result.success(records);
    }

    @GetMapping("/detail")
    @ApiOperation("查询物流详情")
    public Result<TrackingDetailVO> getTrackingDetail(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam Integer carrierCode) {
        log.info("查询物流详情: {}, 运输商: {}", trackingNumber, carrierCode);
        TrackingDetailVO detail = track17Service.getTrackingDetail(trackingNumber, carrierCode);
        return Result.success(detail);
    }

    @GetMapping("/order/{orderId}")
    @ApiOperation("根据订单ID查询物流信息")
    public Result<List<TrackingDetailVO>> getTrackingByOrderId(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId) {
        log.info("根据订单ID查询物流信息: {}", orderId);
        List<TrackingDetailVO> details = track17Service.getTrackingByOrderId(orderId);
        return Result.success(details);
    }

    @PostMapping("/list")
    @ApiOperation("查询跟踪列表")
    public Result<List<TrackingRecord>> getTrackingList(@RequestBody TrackingQueryDTO queryDTO) {
        log.info("查询跟踪列表");
        List<TrackingRecord> records = track17Service.getTrackingList(queryDTO);
        return Result.success(records);
    }

    @PostMapping("/stop")
    @ApiOperation("停止跟踪")
    public Result<String> stopTracking(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam Integer carrierCode) {
        log.info("停止跟踪: {}, 运输商: {}", trackingNumber, carrierCode);
        boolean success = track17Service.stopTracking(trackingNumber, carrierCode);
        return success ? Result.success("停止跟踪成功") : Result.error("停止跟踪失败");
    }

    @PostMapping("/restart")
    @ApiOperation("重启跟踪")
    public Result<String> restartTracking(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam Integer carrierCode) {
        log.info("重启跟踪: {}, 运输商: {}", trackingNumber, carrierCode);
        boolean success = track17Service.restartTracking(trackingNumber, carrierCode);
        return success ? Result.success("重启跟踪成功") : Result.error("重启跟踪失败");
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除跟踪")
    public Result<String> deleteTracking(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam Integer carrierCode) {
        log.info("删除跟踪: {}, 运输商: {}", trackingNumber, carrierCode);
        boolean success = track17Service.deleteTracking(trackingNumber, carrierCode);
        return success ? Result.success("删除跟踪成功") : Result.error("删除跟踪失败");
    }

    @PutMapping("/update")
    @ApiOperation("更新跟踪信息")
    public Result<String> updateTrackingInfo(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam Integer carrierCode,
            @ApiParam(value = "标签") @RequestParam(required = false) String tag,
            @ApiParam(value = "备注") @RequestParam(required = false) String remark) {
        log.info("更新跟踪信息: {}, 标签: {}, 备注: {}", trackingNumber, tag, remark);
        boolean success = track17Service.updateTrackingInfo(trackingNumber, carrierCode, tag, remark);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    @GetMapping("/quota")
    @ApiOperation("获取剩余配额")
    public Result<String> getQuota() {
        log.info("获取剩余配额");
        String quota = track17Service.getQuota();
        return Result.success(quota);
    }

    @PostMapping("/sync")
    @ApiOperation("同步物流状态")
    public Result<String> syncTrackingStatus(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam Integer carrierCode) {
        log.info("同步物流状态: {}, 运输商: {}", trackingNumber, carrierCode);
        boolean success = track17Service.syncTrackingStatus(trackingNumber, carrierCode);
        return success ? Result.success("同步成功") : Result.error("同步失败");
    }

    @GetMapping("/carriers")
    @ApiOperation("获取所有运输商")
    public Result<List<CarrierInfo>> getAllCarriers() {
        log.info("获取所有运输商");
        List<CarrierInfo> carriers = carrierInfoService.getAllActiveCarriers();
        return Result.success(carriers);
    }

    @GetMapping("/carriers/{countryCode}")
    @ApiOperation("根据国家代码获取运输商")
    public Result<List<CarrierInfo>> getCarriersByCountry(
            @ApiParam(value = "国家代码", required = true) @PathVariable String countryCode) {
        log.info("根据国家代码获取运输商: {}", countryCode);
        List<CarrierInfo> carriers = carrierInfoService.getByCountryCode(countryCode);
        return Result.success(carriers);
    }
}
