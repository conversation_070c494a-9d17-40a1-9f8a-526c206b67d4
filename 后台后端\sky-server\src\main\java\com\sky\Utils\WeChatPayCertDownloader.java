package com.sky.Utils;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Signature;
import java.time.Instant;
import java.util.Base64;
import java.util.UUID;

public class WeChatPayCertDownloader {

    // 配置参数（需替换为你的实际值）
    private static final String MERCHANT_ID = "777463044";
    private static final String MERCHANT_SERIAL_NUMBER = "7B90DEE3ABB5B5BCF3048BC24F72C0A767BCE451";
    private static final String API_V3_KEY = "abcdefghigklmnopqrstuvwxyzabzdef";
    private static final String PRIVATE_KEY_PATH = "sky-server/src/main/resources/apiclient_key.pem";
    public static void main(String[] args) {
        try {
            // 1. 生成签名头
            String authorization = generateAuthorizationHeader();

            // 2. 发送请求
            String response = requestCertificates(authorization);
            System.out.println("Response:\n" + response);

            // 3. 解析JSON，提取加密证书
            JSONObject json = new JSONObject(response);
            JSONArray data = json.getJSONArray("data");
            if (data.length() == 0) {
                System.out.println("No certificate found.");
                return;
            }
            JSONObject encryptCertificate = data.getJSONObject(0).getJSONObject("encrypt_certificate");
            String ciphertext = encryptCertificate.getString("ciphertext");
            String nonce = encryptCertificate.getString("nonce");
            String associatedData = encryptCertificate.getString("associated_data");

            // 4. 解密证书
            String certPem = decryptCertificate(ciphertext, nonce, associatedData);

            // 5. 保存为PEM文件
            String pubPath = "sky-server/src/main/resources/apiclient_pub.pem";
            try (FileWriter fw = new FileWriter(pubPath)) {
                fw.write(certPem);
            }
            System.out.println("平台证书已保存为 " + pubPath);

        } catch (Exception e) {
            System.err.println("Error occurred:");
            e.printStackTrace();
        }
    }

    /**
     * 生成微信支付APIv3的Authorization请求头
     */
    private static String generateAuthorizationHeader() throws Exception {
        // 1. 加载商户私钥
        PrivateKey privateKey = loadPrivateKey(PRIVATE_KEY_PATH);
        if (privateKey == null) {
            throw new RuntimeException("Failed to load private key");
        }

        // 2. 准备签名参数
        String method = "GET";
        String url = "/v3/certificates";
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String body = ""; // GET请求必须为空

        // 3. 构建签名串（严格按顺序和格式）
        String message = buildSignMessage(method, url, timestamp, nonce, body);
        System.out.println("Sign Message:\n" + message.replace("\n", "\\n"));

        // 4. 生成签名
        String signature = sign(message, privateKey);
        System.out.println("Generated Signature: " + signature);

        // 5. 组装Authorization头
        return String.format(
                "WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%s\",serial_no=\"%s\",signature=\"%s\"",
                MERCHANT_ID, nonce, timestamp, MERCHANT_SERIAL_NUMBER, signature
        );
    }

    /**
     * 构建签名消息（严格按微信支付要求）
     */
    private static String buildSignMessage(String method, String url,
                                           String timestamp, String nonce,
                                           String body) {
        return String.join("\n", method, url, timestamp, nonce, body) + "\n";
    }

    /**
     * 使用SHA256withRSA签名
     */
    private static String sign(String message, PrivateKey privateKey) throws Exception {
        Signature signer = Signature.getInstance("SHA256withRSA");
        signer.initSign(privateKey);
        signer.update(message.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signer.sign());
    }

    /**
     * 加载PEM格式私钥
     */
    private static PrivateKey loadPrivateKey(String filePath) throws Exception {
        try (PEMParser pemParser = new PEMParser(new FileReader(filePath))) {
            Object obj = pemParser.readObject();
            if (obj instanceof PrivateKeyInfo) {
                return new JcaPEMKeyConverter().getPrivateKey((PrivateKeyInfo) obj);
            }
            throw new IllegalArgumentException("Invalid private key format");
        }
    }

    /**
     * 发送HTTP请求
     */
    private static String requestCertificates(String authorization) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet("https://api.mch.weixin.qq.com/v3/certificates");
            httpGet.setHeader("Authorization", authorization);
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("User-Agent", "Apache-HttpClient/WeChatPay");

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 解密证书（示例方法）
     */
    private static String decryptCertificate(String ciphertext, String nonce,
                                             String associatedData) throws Exception {
        byte[] keyBytes = API_V3_KEY.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
        GCMParameterSpec spec = new GCMParameterSpec(128, nonce.getBytes(StandardCharsets.UTF_8));

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        cipher.init(Cipher.DECRYPT_MODE, key, spec);
        cipher.updateAAD(associatedData.getBytes(StandardCharsets.UTF_8));

        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * 检查平台证书是否存在，不存在则自动下载
     */
    public static void downloadCertIfNotExists() {
        String pubPath = "sky-server/src/main/resources/apiclient_pub.pem";
        File certFile = new File(pubPath);
        if (certFile.exists()) {
            System.out.println("平台证书已存在，无需下载。");
            return;
        }
        System.out.println("平台证书不存在，开始下载...");
        main(null); // 直接调用 main 方法执行下载
    }

    /**
     * 强制下载并覆盖微信平台证书
     */
    public static void downloadCertAlways() {
        System.out.println("定时任务：强制下载微信平台证书...");
        main(null); // 直接调用 main 方法执行下载
    }
}