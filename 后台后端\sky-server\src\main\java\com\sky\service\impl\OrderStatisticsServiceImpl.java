package com.sky.service.impl;

import com.sky.mapper.OrdersMapper;
import com.sky.service.OrderStatisticsService;
import com.sky.vo.OrderStatisticsVO;
import com.sky.entity.Orders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单统计服务实现类
 */
@Service
public class OrderStatisticsServiceImpl implements OrderStatisticsService {
    
    @Autowired
    private OrdersMapper ordersMapper;
    
    @Override
    public OrderStatisticsVO getOrderStatistics() {
        OrderStatisticsVO statistics = new OrderStatisticsVO();
        
        // 统计各状态订单数量
        statistics.setPendingPayment(ordersMapper.countByStatus(Orders.STATUS_PENDING_PAYMENT));
        statistics.setPaid(ordersMapper.countByStatus(Orders.STATUS_PAID));
        statistics.setShipped(ordersMapper.countByStatus(Orders.STATUS_SHIPPED));
        statistics.setCompleted(ordersMapper.countByStatus(Orders.STATUS_COMPLETED));
        statistics.setCancelled(ordersMapper.countByStatus(Orders.STATUS_CANCELLED));

        // 设置处理中和退款状态为0（数据库中没有这些状态）
        statistics.setProcessing(0);
        statistics.setRefunded(0);
        
        // 计算总订单数
        Integer totalOrders = statistics.getPendingPayment() + statistics.getPaid() + 
                             statistics.getProcessing() + statistics.getShipped() + 
                             statistics.getCompleted() + statistics.getCancelled() + 
                             statistics.getRefunded();
        statistics.setTotalOrders(totalOrders);
        
        // 统计销售额（已完成和已发货的订单）
        Double totalSales = ordersMapper.sumAmountByStatus(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED);
        statistics.setTotalSales(totalSales != null ? BigDecimal.valueOf(totalSales) : BigDecimal.ZERO);
        
        // 按状态分组统计
        List<Map<String, Object>> statusGroup = ordersMapper.countGroupByStatus();
        statistics.setStatusDistribution(statusGroup);
        
        // 兼容原有字段
        statistics.setToBeConfirmed(statistics.getPaid()); // 待接单 = 已付款
        statistics.setConfirmed(statistics.getProcessing()); // 待派送 = 处理中
        statistics.setDeliveryInProgress(statistics.getShipped()); // 派送中 = 已发货
        
        return statistics;
    }
    
    @Override
    public OrderStatisticsVO getOrderStatistics(LocalDate beginDate, LocalDate endDate) {
        OrderStatisticsVO statistics = new OrderStatisticsVO();
        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        // 统计指定日期范围内各状态订单数量
        statistics.setPendingPayment(ordersMapper.countByStatusAndDate(Orders.STATUS_PENDING_PAYMENT, beginTime, endTime));
        statistics.setPaid(ordersMapper.countByStatusAndDate(Orders.STATUS_PAID, beginTime, endTime));
        statistics.setShipped(ordersMapper.countByStatusAndDate(Orders.STATUS_SHIPPED, beginTime, endTime));
        statistics.setCompleted(ordersMapper.countByStatusAndDate(Orders.STATUS_COMPLETED, beginTime, endTime));
        statistics.setCancelled(ordersMapper.countByStatusAndDate(Orders.STATUS_CANCELLED, beginTime, endTime));

        // 设置处理中和退款状态为0（数据库中没有这些状态）
        statistics.setProcessing(0);
        statistics.setRefunded(0);

        // 计算总订单数
        Integer totalOrders = statistics.getPendingPayment() + statistics.getPaid() +
                             statistics.getProcessing() + statistics.getShipped() +
                             statistics.getCompleted() + statistics.getCancelled() +
                             statistics.getRefunded();
        statistics.setTotalOrders(totalOrders);

        // 统计指定日期范围内的销售额
        Double totalSales = ordersMapper.sumAmountByStatusAndDate(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        statistics.setTotalSales(totalSales != null ? BigDecimal.valueOf(totalSales) : BigDecimal.ZERO);

        // 按状态分组统计
        List<Map<String, Object>> statusGroup = ordersMapper.countGroupByStatusAndDate(beginTime, endTime);
        statistics.setStatusDistribution(statusGroup);

        // 每日销售统计
        List<Map<String, Object>> dailyStats = ordersMapper.sumDailyAmount(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        statistics.setDailyStatistics(dailyStats);

        // 每月销售统计
        List<Map<String, Object>> monthlyStats = ordersMapper.sumMonthlyAmount(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        statistics.setMonthlyStatistics(monthlyStats);

        return statistics;
    }

    @Override
    public Map<String, Object> getTodayStatistics() {
        LocalDate today = LocalDate.now();
        LocalDateTime beginTime = today.atStartOfDay();
        LocalDateTime endTime = today.atTime(23, 59, 59);

        Map<String, Object> todayStats = new HashMap<>();

        // 今日订单数量
        Integer todayOrders = ordersMapper.countByStatusAndDate(Orders.STATUS_COMPLETED, beginTime, endTime) +
                             ordersMapper.countByStatusAndDate(Orders.STATUS_SHIPPED, beginTime, endTime);
        todayStats.put("todayOrders", todayOrders);

        // 今日销售额
        Double todaySales = ordersMapper.sumAmountByStatusAndDate(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        todayStats.put("todaySales", todaySales != null ? BigDecimal.valueOf(todaySales) : BigDecimal.ZERO);

        return todayStats;
    }

    @Override
    public Map<String, Object> getMonthStatistics() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        LocalDateTime beginTime = firstDayOfMonth.atStartOfDay();
        LocalDateTime endTime = today.atTime(23, 59, 59);

        Map<String, Object> monthStats = new HashMap<>();

        // 本月订单数量
        Integer monthOrders = ordersMapper.countByStatusAndDate(Orders.STATUS_COMPLETED, beginTime, endTime) +
                             ordersMapper.countByStatusAndDate(Orders.STATUS_SHIPPED, beginTime, endTime);
        monthStats.put("monthOrders", monthOrders);

        // 本月销售额
        Double monthSales = ordersMapper.sumAmountByStatusAndDate(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        monthStats.put("monthSales", monthSales != null ? BigDecimal.valueOf(monthSales) : BigDecimal.ZERO);

        return monthStats;
    }
    
    @Override
    public Map<String, Object> getStatusDistribution() {
        Map<String, Object> distribution = new HashMap<>();
        List<Map<String, Object>> statusGroup = ordersMapper.countGroupByStatus();
        distribution.put("statusDistribution", statusGroup);
        return distribution;
    }

    @Override
    public OrderStatisticsVO getEnhancedOrderStatistics(LocalDate beginDate, LocalDate endDate) {
        OrderStatisticsVO statistics = getOrderStatistics(beginDate, endDate);

        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        // 添加增强统计数据
        enrichWithTimeDimension(statistics, beginTime, endTime);
        enrichWithPaymentMethodStats(statistics, beginTime, endTime);
        enrichWithProductStats(statistics, beginTime, endTime);
        enrichWithUserStats(statistics, beginTime, endTime);
        enrichWithConversionRates(statistics, beginTime, endTime);
        enrichWithComparisonData(statistics, beginTime, endTime);
        enrichWithRealtimeStats(statistics);

        return statistics;
    }

    @Override
    public Map<String, Object> getTimeDimensionStatistics() {
        Map<String, Object> timeDimension = new HashMap<>();
        LocalDate today = LocalDate.now();

        // 今日统计
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(23, 59, 59);
        timeDimension.put("today", getStatisticsForPeriod(todayStart, todayEnd));

        // 昨日统计
        LocalDate yesterday = today.minusDays(1);
        LocalDateTime yesterdayStart = yesterday.atStartOfDay();
        LocalDateTime yesterdayEnd = yesterday.atTime(23, 59, 59);
        timeDimension.put("yesterday", getStatisticsForPeriod(yesterdayStart, yesterdayEnd));

        // 本周统计
        LocalDate weekStart = today.minusDays(today.getDayOfWeek().getValue() - 1);
        LocalDateTime weekStartTime = weekStart.atStartOfDay();
        timeDimension.put("thisWeek", getStatisticsForPeriod(weekStartTime, todayEnd));

        // 上周统计
        LocalDate lastWeekStart = weekStart.minusDays(7);
        LocalDate lastWeekEnd = weekStart.minusDays(1);
        LocalDateTime lastWeekStartTime = lastWeekStart.atStartOfDay();
        LocalDateTime lastWeekEndTime = lastWeekEnd.atTime(23, 59, 59);
        timeDimension.put("lastWeek", getStatisticsForPeriod(lastWeekStartTime, lastWeekEndTime));

        // 本月统计
        LocalDate monthStart = today.withDayOfMonth(1);
        LocalDateTime monthStartTime = monthStart.atStartOfDay();
        timeDimension.put("thisMonth", getStatisticsForPeriod(monthStartTime, todayEnd));

        // 上月统计
        LocalDate lastMonthStart = monthStart.minusMonths(1);
        LocalDate lastMonthEnd = monthStart.minusDays(1);
        LocalDateTime lastMonthStartTime = lastMonthStart.atStartOfDay();
        LocalDateTime lastMonthEndTime = lastMonthEnd.atTime(23, 59, 59);
        timeDimension.put("lastMonth", getStatisticsForPeriod(lastMonthStartTime, lastMonthEndTime));

        return timeDimension;
    }

    @Override
    public Map<String, Object> getPaymentMethodStatistics(LocalDate beginDate, LocalDate endDate) {
        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        List<Map<String, Object>> paymentStats = ordersMapper.countByPaymentMethod(
            Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);

        Map<String, Object> result = new HashMap<>();
        result.put("paymentMethodStats", paymentStats);

        // 添加支付方式名称映射
        for (Map<String, Object> stat : paymentStats) {
            Integer payMethod = (Integer) stat.get("pay_method");
            String methodName = getPaymentMethodName(payMethod);
            stat.put("methodName", methodName);
        }

        return result;
    }

    @Override
    public Map<String, Object> getProductStatistics(LocalDate beginDate, LocalDate endDate, Integer topLimit) {
        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        if (topLimit == null) {
            topLimit = 10; // 默认前10名
        }

        List<Map<String, Object>> topProducts = ordersMapper.getTopProducts(
            Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime, topLimit);

        Map<String, Object> result = new HashMap<>();
        result.put("topProducts", topProducts);

        return result;
    }

    @Override
    public Map<String, Object> getUserStatistics(LocalDate beginDate, LocalDate endDate) {
        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        Map<String, Object> result = new HashMap<>();

        // 新老用户订单分布
        List<Map<String, Object>> newOldUserStats = ordersMapper.countNewOldUserOrders(
            Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        result.put("newOldUserStats", newOldUserStats);

        // 用户购买频次分布
        List<Map<String, Object>> userFrequency = ordersMapper.getUserOrderFrequency(
            Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        result.put("userOrderFrequency", userFrequency);

        return result;
    }

    @Override
    public Map<String, Object> getConversionRateStatistics(LocalDate beginDate, LocalDate endDate) {
        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        Map<String, Object> result = new HashMap<>();

        // 总订单数
        Integer totalOrders = ordersMapper.countAllOrdersByDate(beginTime, endTime);
        // 已支付订单数
        Integer paidOrders = ordersMapper.countByStatusAndDate(Orders.STATUS_PAID, beginTime, endTime) +
                           ordersMapper.countByStatusAndDate(Orders.STATUS_SHIPPED, beginTime, endTime) +
                           ordersMapper.countByStatusAndDate(Orders.STATUS_COMPLETED, beginTime, endTime);
        // 已完成订单数
        Integer completedOrders = ordersMapper.countValidOrdersByDate(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        // 已取消订单数
        Integer cancelledOrders = ordersMapper.countByStatusAndDate(Orders.STATUS_CANCELLED, beginTime, endTime);

        // 计算转化率
        Double paymentRate = totalOrders > 0 ? (double) paidOrders / totalOrders * 100 : 0.0;
        Double completionRate = paidOrders > 0 ? (double) completedOrders / paidOrders * 100 : 0.0;
        Double cancellationRate = totalOrders > 0 ? (double) cancelledOrders / totalOrders * 100 : 0.0;

        result.put("totalOrders", totalOrders);
        result.put("paidOrders", paidOrders);
        result.put("completedOrders", completedOrders);
        result.put("cancelledOrders", cancelledOrders);
        result.put("paymentRate", paymentRate);
        result.put("completionRate", completionRate);
        result.put("cancellationRate", cancellationRate);

        return result;
    }

    @Override
    public Map<String, Object> getRealtimeStatistics() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);

        Map<String, Object> realtimeStats = ordersMapper.getRealtimeStats(
            Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, Orders.STATUS_PENDING_PAYMENT, oneHourAgo);

        return realtimeStats;
    }

    @Override
    public Map<String, Object> getComparisonStatistics(LocalDate beginDate, LocalDate endDate) {
        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        // 计算同期数据（去年同期）
        LocalDate lastYearBegin = beginDate.minusYears(1);
        LocalDate lastYearEnd = endDate.minusYears(1);
        LocalDateTime lastYearBeginTime = lastYearBegin.atStartOfDay();
        LocalDateTime lastYearEndTime = lastYearEnd.atTime(23, 59, 59);

        // 计算环比数据（上一个周期）
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(beginDate, endDate) + 1;
        LocalDate lastPeriodBegin = beginDate.minusDays(daysBetween);
        LocalDate lastPeriodEnd = beginDate.minusDays(1);
        LocalDateTime lastPeriodBeginTime = lastPeriodBegin.atStartOfDay();
        LocalDateTime lastPeriodEndTime = lastPeriodEnd.atTime(23, 59, 59);

        Map<String, Object> result = new HashMap<>();

        // 当前期间数据
        Map<String, Object> currentPeriod = getStatisticsForPeriod(beginTime, endTime);
        result.put("currentPeriod", currentPeriod);

        // 同比数据
        Map<String, Object> lastYearPeriod = getStatisticsForPeriod(lastYearBeginTime, lastYearEndTime);
        result.put("lastYearPeriod", lastYearPeriod);

        // 环比数据
        Map<String, Object> lastPeriod = getStatisticsForPeriod(lastPeriodBeginTime, lastPeriodEndTime);
        result.put("lastPeriod", lastPeriod);

        // 计算增长率
        result.put("yearOverYearGrowth", calculateGrowthRate(currentPeriod, lastYearPeriod));
        result.put("periodOverPeriodGrowth", calculateGrowthRate(currentPeriod, lastPeriod));

        return result;
    }

    @Override
    public Map<String, Object> getTrendStatistics(LocalDate beginDate, LocalDate endDate, String granularity) {
        LocalDateTime beginTime = beginDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        Map<String, Object> result = new HashMap<>();

        switch (granularity.toLowerCase()) {
            case "hour":
                List<Map<String, Object>> hourlyStats = ordersMapper.sumHourlyAmount(
                    Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
                result.put("hourlyStatistics", hourlyStats);
                break;
            case "day":
                List<Map<String, Object>> dailyStats = ordersMapper.sumDailyAmount(
                    Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
                result.put("dailyStatistics", dailyStats);
                break;
            case "week":
                List<Map<String, Object>> weeklyStats = ordersMapper.sumWeeklyAmount(
                    Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
                result.put("weeklyStatistics", weeklyStats);
                break;
            case "month":
                List<Map<String, Object>> monthlyStats = ordersMapper.sumMonthlyAmount(
                    Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
                result.put("monthlyStatistics", monthlyStats);
                break;
            default:
                // 默认返回日统计
                List<Map<String, Object>> defaultStats = ordersMapper.sumDailyAmount(
                    Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
                result.put("dailyStatistics", defaultStats);
        }

        return result;
    }

    // 辅助方法：获取指定时间段的统计数据
    private Map<String, Object> getStatisticsForPeriod(LocalDateTime beginTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();

        // 订单数量统计
        Integer totalOrders = ordersMapper.countAllOrdersByDate(beginTime, endTime);
        Integer validOrders = ordersMapper.countValidOrdersByDate(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);

        // 销售额统计
        Double totalSales = ordersMapper.sumAmountByStatusAndDate(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        Double avgOrderAmount = ordersMapper.getAvgOrderAmount(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);

        stats.put("totalOrders", totalOrders);
        stats.put("validOrders", validOrders);
        stats.put("totalSales", totalSales != null ? BigDecimal.valueOf(totalSales) : BigDecimal.ZERO);
        stats.put("avgOrderAmount", avgOrderAmount != null ? BigDecimal.valueOf(avgOrderAmount) : BigDecimal.ZERO);

        return stats;
    }

    // 辅助方法：计算增长率
    private Map<String, Object> calculateGrowthRate(Map<String, Object> current, Map<String, Object> previous) {
        Map<String, Object> growth = new HashMap<>();

        BigDecimal currentSales = (BigDecimal) current.get("totalSales");
        BigDecimal previousSales = (BigDecimal) previous.get("totalSales");
        Integer currentOrders = (Integer) current.get("totalOrders");
        Integer previousOrders = (Integer) previous.get("totalOrders");

        // 销售额增长率
        if (previousSales.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal salesGrowth = currentSales.subtract(previousSales)
                .divide(previousSales, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            growth.put("salesGrowthRate", salesGrowth);
        } else {
            growth.put("salesGrowthRate", BigDecimal.ZERO);
        }

        // 订单数增长率
        if (previousOrders > 0) {
            Double orderGrowth = ((double) (currentOrders - previousOrders) / previousOrders) * 100;
            growth.put("orderGrowthRate", BigDecimal.valueOf(orderGrowth));
        } else {
            growth.put("orderGrowthRate", BigDecimal.ZERO);
        }

        return growth;
    }

    // 辅助方法：获取支付方式名称
    private String getPaymentMethodName(Integer payMethod) {
        if (payMethod == null) return "未知";
        switch (payMethod) {
            case 1: return "微信支付";
            case 2: return "支付宝";
            case 3: return "银行卡";
            default: return "其他";
        }
    }

    // 辅助方法：丰富时间维度数据
    private void enrichWithTimeDimension(OrderStatisticsVO statistics, LocalDateTime beginTime, LocalDateTime endTime) {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);

        // 今日数据
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(23, 59, 59);
        Map<String, Object> todayStats = getStatisticsForPeriod(todayStart, todayEnd);
        statistics.setTodayOrders((Integer) todayStats.get("totalOrders"));
        statistics.setTodaySales((BigDecimal) todayStats.get("totalSales"));

        // 昨日数据
        LocalDateTime yesterdayStart = yesterday.atStartOfDay();
        LocalDateTime yesterdayEnd = yesterday.atTime(23, 59, 59);
        Map<String, Object> yesterdayStats = getStatisticsForPeriod(yesterdayStart, yesterdayEnd);
        statistics.setYesterdayOrders((Integer) yesterdayStats.get("totalOrders"));
        statistics.setYesterdaySales((BigDecimal) yesterdayStats.get("totalSales"));

        // 平均订单金额
        Double avgAmount = ordersMapper.getAvgOrderAmount(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        statistics.setAvgOrderAmount(avgAmount != null ? BigDecimal.valueOf(avgAmount) : BigDecimal.ZERO);

        // 趋势统计
        List<Map<String, Object>> hourlyStats = ordersMapper.sumHourlyAmount(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        List<Map<String, Object>> weeklyStats = ordersMapper.sumWeeklyAmount(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        statistics.setHourlyStatistics(hourlyStats);
        statistics.setWeeklyStatistics(weeklyStats);
    }

    // 辅助方法：丰富支付方式统计
    private void enrichWithPaymentMethodStats(OrderStatisticsVO statistics, LocalDateTime beginTime, LocalDateTime endTime) {
        List<Map<String, Object>> paymentStats = ordersMapper.countByPaymentMethod(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        for (Map<String, Object> stat : paymentStats) {
            Integer payMethod = (Integer) stat.get("pay_method");
            stat.put("methodName", getPaymentMethodName(payMethod));
        }
        statistics.setPaymentMethodStats(paymentStats);
    }

    // 辅助方法：丰富商品统计
    private void enrichWithProductStats(OrderStatisticsVO statistics, LocalDateTime beginTime, LocalDateTime endTime) {
        List<Map<String, Object>> topProducts = ordersMapper.getTopProducts(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime, 10);
        statistics.setTopProducts(topProducts);
    }

    // 辅助方法：丰富用户统计
    private void enrichWithUserStats(OrderStatisticsVO statistics, LocalDateTime beginTime, LocalDateTime endTime) {
        List<Map<String, Object>> newOldUserStats = ordersMapper.countNewOldUserOrders(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        List<Map<String, Object>> userFrequency = ordersMapper.getUserOrderFrequency(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);

        // 设置新老用户订单数
        for (Map<String, Object> stat : newOldUserStats) {
            String userType = (String) stat.get("userType");
            // 修复ClassCastException: COUNT(*)返回Long类型，需要转换为Integer
            Long orderCountLong = (Long) stat.get("orderCount");
            Integer orderCount = orderCountLong != null ? orderCountLong.intValue() : 0;
            if ("new".equals(userType)) {
                statistics.setNewUserOrders(orderCount);
            } else if ("old".equals(userType)) {
                statistics.setOldUserOrders(orderCount);
            }
        }

        statistics.setUserOrderFrequency(userFrequency);
    }

    // 辅助方法：丰富转化率数据
    private void enrichWithConversionRates(OrderStatisticsVO statistics, LocalDateTime beginTime, LocalDateTime endTime) {
        Integer totalOrders = ordersMapper.countAllOrdersByDate(beginTime, endTime);
        Integer paidOrders = ordersMapper.countByStatusAndDate(Orders.STATUS_PAID, beginTime, endTime) +
                           ordersMapper.countByStatusAndDate(Orders.STATUS_SHIPPED, beginTime, endTime) +
                           ordersMapper.countByStatusAndDate(Orders.STATUS_COMPLETED, beginTime, endTime);
        Integer completedOrders = ordersMapper.countValidOrdersByDate(Orders.STATUS_COMPLETED, Orders.STATUS_SHIPPED, beginTime, endTime);
        Integer cancelledOrders = ordersMapper.countByStatusAndDate(Orders.STATUS_CANCELLED, beginTime, endTime);

        Double paymentRate = totalOrders > 0 ? (double) paidOrders / totalOrders : 0.0;
        Double completionRate = paidOrders > 0 ? (double) completedOrders / paidOrders : 0.0;
        Double cancellationRate = totalOrders > 0 ? (double) cancelledOrders / totalOrders : 0.0;

        statistics.setPaymentRate(paymentRate);
        statistics.setCompletionRate(completionRate);
        statistics.setCancellationRate(cancellationRate);
    }

    // 辅助方法：丰富同比环比数据
    private void enrichWithComparisonData(OrderStatisticsVO statistics, LocalDateTime beginTime, LocalDateTime endTime) {
        Map<String, Object> comparisonData = getComparisonStatistics(beginTime.toLocalDate(), endTime.toLocalDate());
        statistics.setComparisonData(comparisonData);
    }

    // 辅助方法：丰富实时统计数据
    private void enrichWithRealtimeStats(OrderStatisticsVO statistics) {
        Map<String, Object> realtimeStats = getRealtimeStatistics();
        statistics.setRealtimeStats(realtimeStats);
    }
}
