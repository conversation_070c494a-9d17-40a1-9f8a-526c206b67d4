package com.sky.dto.track17;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 17TRACK实时查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17RealtimeRequest {
    
    /**
     * 物流单号
     */
    private String trackingNumber;
    
    /**
     * 运输商代码
     */
    private Integer carrierCode;
    
    /**
     * 自动检测运输商
     */
    private Boolean autoDetection;
    
    /**
     * 语言代码
     */
    private String lang;
}
