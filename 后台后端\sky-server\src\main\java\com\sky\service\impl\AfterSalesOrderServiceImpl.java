package com.sky.service.impl;

import com.sky.entity.AfterSalesOrder;
import com.sky.mapper.AfterSalesOrderMapper;
import com.sky.service.AfterSalesOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AfterSalesOrderServiceImpl implements AfterSalesOrderService {
    @Autowired
    private AfterSalesOrderMapper afterSalesOrderMapper;

    @Override
    public AfterSalesOrder getByAfterSalesNo(String afterSalesNo) {
        return afterSalesOrderMapper.findByAfterSalesNo(afterSalesNo);
    }

    @Override
    public AfterSalesOrder getByOrderNo(String orderNo) {
        return afterSalesOrderMapper.findByOrderNo(orderNo);
    }

    @Override
    public AfterSalesOrder getByTransactionNo(String transactionNo) {
        return afterSalesOrderMapper.findByTransactionNo(transactionNo);
    }

    @Override
    public List<AfterSalesOrder> getByStatus(Integer status) {
        return afterSalesOrderMapper.findByStatus(status);
    }
}