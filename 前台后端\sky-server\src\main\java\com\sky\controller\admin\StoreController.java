package com.sky.controller.admin;


import com.sky.dto.StoreDTO;
import com.sky.result.Result;
import com.sky.service.StoreService;
import com.sky.vo.StoreVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 店铺管理
 */
@RestController
@RequestMapping("/store")
@Slf4j
@Api(tags = "店铺相关接口")
public class StoreController {

    @Autowired
    private StoreService storeService;

    /**
     * 新增店铺
     * @param storeDTO
     * @return
     */
    @PostMapping("/create")
    @ApiOperation("新增店铺")
    public Result CreateStore(@RequestBody StoreDTO storeDTO) throws Exception {
        log.info("新增店铺：{}", storeDTO);
        log.info("文件上传：{}", storeDTO.getPhoto());
        storeService.createStore(storeDTO);
        log.info("文件路径：{}", storeDTO.getPhoto());
        return Result.success();
    }

    /**
     * 修改店铺
     * @param storeDTO
     * @return
     */
    @PostMapping("/update")
    @ApiOperation("修改店铺")
    public Result updateStore(@RequestBody StoreDTO storeDTO){
        log.info("修改店铺：{}", storeDTO);
        storeService.updateStore(storeDTO);
        return Result.success();
    }

    /**
     * 根据id查询店铺
     * @param id
     * @return
     */
    @PostMapping("/getById")
    @ApiOperation("根据id查询店铺")
    public Result getById(Long id){
        log.info("根据id查询店铺：{}", id);
        StoreVO storeVO = storeService.getById(id);
        return Result.success(storeVO);
    }

    @DeleteMapping("delete/{id}")
    @ApiOperation("删除店铺")
    public Result delete(@PathVariable Long id){
        log.info("删除店铺：{}", id);
        storeService.delete(id);
        return Result.success();
    }
} 