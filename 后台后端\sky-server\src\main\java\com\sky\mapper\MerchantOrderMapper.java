package com.sky.mapper;

import com.sky.entity.MerchantOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface MerchantOrderMapper {
    @Select("SELECT * FROM merchant_order")
    List<MerchantOrder> findAll();

    @Select("SELECT * FROM merchant_order WHERE status = #{status}")
    List<MerchantOrder> findByStatus(Integer status);

    @Select("SELECT * FROM merchant_order WHERE order_no = #{orderNo}")
    List<MerchantOrder> findByOrderNo(String orderNo);

    @Select("SELECT * FROM merchant_order WHERE transaction_no = #{transactionNo}")
    List<MerchantOrder> findByTransactionNo(String transactionNo);

    @Select("SELECT * FROM merchant_order WHERE tracking_no = #{trackingNo}")
    List<MerchantOrder> findByTrackingNo(String trackingNo);
}