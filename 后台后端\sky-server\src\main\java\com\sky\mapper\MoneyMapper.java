package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.DataRecordsPageDTO;
import com.sky.dto.FundFlowPageDTO;
import com.sky.entity.DataRecords;
import com.sky.entity.FundFlow;
import com.sky.vo.FundFlowVO;
import com.sky.entity.MessageNotifications;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface MoneyMapper {
    Page<MessageNotifications> pageQuery(DataRecordsPageDTO dataRecordsPageDTO);

    @Select("select sum(amount) from data_records where seller_id = #{sellerId} and status='0'")
    BigDecimal getByAmount(Long sellerId);

    List<DataRecords> getStatus(Long id);

    Page<FundFlow> fundFlowPageQuery(FundFlowPageDTO fundFlowPageDTO);

    List<FundFlowVO> listFundFlowData(LocalDateTime of, LocalDateTime of1);
}
