package com.sky.aspect;


import com.sky.annotation.DataScope;
import com.sky.entity.BaseEntity;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

@Aspect
@Component
public class DataScopeAspect {

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private HttpServletResponse response;

    @Pointcut("@annotation(com.sky.annotation.DataScope)")
    public void DataScopePointCut() {
    }

    //前置通知
    @Before("DataScopePointCut()")
    public void doBefore(JoinPoint joinPoint) {
        //1.获取注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        DataScope annotation = method.getAnnotation(DataScope.class);
        if (annotation == null) {
            return;
        }


        //2.获取当前用户
        String userId = request.getHeader("user_id");

        //3.如果不是超级管理员，则过滤数据，给目标方法的第一个参数的实体对象设置属性

        if(!"1".equals(userId)) {
            Object arg = joinPoint.getArgs()[0];
            if( arg != null  && arg instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) arg;
                baseEntity.getParams().put("dataScope", "user_id" + userId);
            }
        }

    }
}
