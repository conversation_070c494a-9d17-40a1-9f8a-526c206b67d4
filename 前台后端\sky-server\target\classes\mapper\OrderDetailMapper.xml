<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrderDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sky.entity.OrderDetail">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="product_image" property="productImage" />
        <result column="product_spec" property="productSpec" />
        <result column="quantity" property="quantity" />
        <result column="unit_price" property="unitPrice" />
        <result column="discount" property="discount" />
        <result column="subtotal" property="subtotal" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 插入订单详情 -->
    <insert id="insert" parameterType="com.sky.entity.OrderDetail">
        INSERT INTO order_detail (
            order_id, product_id, product_name, product_image, product_spec,
            quantity, unit_price, discount, subtotal, create_time, update_time
        ) VALUES (
            #{orderId}, #{productId}, #{productName}, #{productImage}, #{productSpec},
            #{quantity}, #{unitPrice}, #{discount}, #{subtotal}, #{createTime}, #{updateTime}
        )
    </insert>
    
    <!-- 批量插入订单详情 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO order_detail (
            order_id, product_id, product_name, product_image, product_spec,
            quantity, unit_price, discount, subtotal, create_time, update_time
        ) VALUES 
        <foreach collection="orderDetails" item="item" separator=",">
            (
            #{item.orderId}, #{item.productId}, #{item.productName}, #{item.productImage}, #{item.productSpec},
            #{item.quantity}, #{item.unitPrice}, #{item.discount}, #{item.subtotal}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>
    
    <!-- 根据订单ID查询订单详情 -->
    <select id="getByOrderId" resultMap="BaseResultMap">
        SELECT * FROM order_detail WHERE order_id = #{orderId}
    </select>
    
    <!-- 根据订单ID删除订单详情 -->
    <delete id="deleteByOrderId">
        DELETE FROM order_detail WHERE order_id = #{orderId}
    </delete>
</mapper> 