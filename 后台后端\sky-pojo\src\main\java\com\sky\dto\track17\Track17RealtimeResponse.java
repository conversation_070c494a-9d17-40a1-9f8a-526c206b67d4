package com.sky.dto.track17;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 17TRACK实时查询响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17RealtimeResponse {
    
    /**
     * 物流单号
     */
    private String trackingNumber;
    
    /**
     * 运输商代码
     */
    private Integer carrierCode;
    
    /**
     * 运输商名称
     */
    private String carrierName;
    
    /**
     * 物流状态
     */
    private String status;
    
    /**
     * 子状态
     */
    private String subStatus;
    
    /**
     * 状态描述
     */
    private String statusDescription;
    
    /**
     * 最新事件时间
     */
    private LocalDateTime latestEventTime;
    
    /**
     * 最新事件信息
     */
    private String latestEventInfo;
    
    /**
     * 物流事件列表
     */
    private List<TrackingEvent> events;
    
    /**
     * 物流事件
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrackingEvent {
        /**
         * 事件时间
         */
        private LocalDateTime eventTime;
        
        /**
         * 事件描述
         */
        private String eventDescription;
        
        /**
         * 事件位置
         */
        private String location;
        
        /**
         * 事件状态
         */
        private String status;
    }
}
