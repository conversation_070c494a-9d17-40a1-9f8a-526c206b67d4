package com.sky.controller.pay;

import com.sky.dto.PaymentRecordDTO;
import com.sky.entity.PaymentRecord;
import com.sky.result.Result;
import com.sky.service.PaymentRecordService;
import com.stripe.Stripe;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;
import com.stripe.model.Event;
import com.stripe.model.PaymentIntent;
import com.stripe.net.Webhook;
import com.stripe.param.PaymentIntentCreateParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/pay")
@Api(tags = "Stripe银行卡支付接口")
@Slf4j
public class StripePayController {

    private final PaymentRecordService paymentRecordService;

    @Value("${stripe.secret-key}")
    private String stripeSecretKey;

    @Value("${stripe.publishable-key}")
    private String stripePublishableKey;

    public StripePayController(PaymentRecordService paymentRecordService) {
        this.paymentRecordService = paymentRecordService;
    }

    @PostConstruct
    public void init() {
        Stripe.apiKey = stripeSecretKey;
    }

    @PostMapping("/stripePay")
    @ApiOperation("Stripe银行卡支付下单")
    public Result<Map<String, Object>> stripePay(@RequestBody PaymentRecordDTO dto) {
        try {
            // Stripe 金额单位为最小货币单位（如分）
            long amount = dto.getAmount().multiply(new BigDecimal(100)).longValue();
            PaymentIntentCreateParams params = PaymentIntentCreateParams.builder()
                    .setAmount(amount)
                    .setCurrency("usd") // 可根据需要调整币种
                    .setDescription("订单号:" + dto.getOrderId())
                    .putMetadata("orderId", String.valueOf(dto.getOrderId()))
                    .setAutomaticPaymentMethods(
                            PaymentIntentCreateParams.AutomaticPaymentMethods.builder().setEnabled(true).build()
                    )
                    .build();
            PaymentIntent intent = PaymentIntent.create(params);

            // 保存支付记录
            PaymentRecord record = PaymentRecord.builder()
                    .orderId(dto.getOrderId())
                    .paymentMethod(dto.getPaymentMethod())
                    .amount(dto.getAmount())
                    .transactionStatus("PENDING")
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            paymentRecordService.save(record);

            Map<String, Object> result = new HashMap<>();
            result.put("clientSecret", intent.getClientSecret());
            result.put("status",intent.getStatus());
            result.put("paymentIntentId", intent.getId());
            result.put("publishableKey", stripePublishableKey); // 返回前端用的 pk_test_xxx
            return Result.success(result);
        } catch (StripeException e) {
            log.error("Stripe下单异常", e);
            return Result.error("Stripe下单失败: " + e.getMessage());
        }
    }

    @PostMapping("/stripeWebhook")
    @ApiOperation("Stripe支付回调")
    public String stripeWebhook(@RequestBody String payload) throws IOException {
        // 不再校验签名，直接解析事件
        Event event = Event.GSON.fromJson(payload, Event.class);
        if ("payment_intent.succeeded".equals(event.getType())) {
            PaymentIntent intent = (PaymentIntent) event.getDataObjectDeserializer().getObject().orElse(null);
            if (intent != null) {
                String orderId = intent.getMetadata().get("orderId");
                // 更新支付记录状态
                PaymentRecord record = PaymentRecord.builder()
                        .orderId(Long.valueOf(orderId))
                        .transactionStatus("SUCCESS")
                        .transactionTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                paymentRecordService.save(record);
                log.info("订单{}支付成功", orderId);
            }
        } else if ("payment_intent.payment_failed".equals(event.getType())) {
            PaymentIntent intent = (PaymentIntent) event.getDataObjectDeserializer().getObject().orElse(null);
            if (intent != null) {
                String orderId = intent.getMetadata().get("orderId");
                PaymentRecord record = PaymentRecord.builder()
                        .orderId(Long.valueOf(orderId))
                        .transactionStatus("FAILED")
                        .transactionTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                paymentRecordService.save(record);
                log.info("订单{}支付失败", orderId);
            }
        }
        return "";
    }
} 