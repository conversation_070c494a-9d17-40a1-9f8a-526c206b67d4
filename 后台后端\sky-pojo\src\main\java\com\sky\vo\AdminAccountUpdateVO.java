package com.sky.vo;

import com.sky.entity.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminAccountUpdateVO {
    private Long id;
    private String accountName;
    private String email;
    private String phone;
    private Integer accountStatus;
    private LocalDateTime updateTime;
    private String remark;
}
