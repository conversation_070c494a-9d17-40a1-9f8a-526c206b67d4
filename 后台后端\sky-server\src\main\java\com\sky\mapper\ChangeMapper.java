package com.sky.mapper;

import com.sky.entity.Buyer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ChangeMapper {
    @Select("select * from ry_mall.buyer where phone = #{phone}")
    Buyer getBuyerByphone(String phone);

    @Select("select password from ry_mall.buyer where phone = #{phone}")
    String getpassword(String phone);


    void UpdatePassWord(String newPassword,String phone);
}
