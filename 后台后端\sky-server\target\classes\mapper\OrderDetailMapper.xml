<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrderDetailMapper">

    <!-- 批量插入订单明细 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO order_detail (order_id, product_id, product_name, product_image, product_spec,
                                 quantity, unit_price, discount, subtotal, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId}, #{item.productId}, #{item.productName}, #{item.productImage}, #{item.productSpec},
             #{item.quantity}, #{item.unitPrice}, #{item.discount}, #{item.subtotal}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
