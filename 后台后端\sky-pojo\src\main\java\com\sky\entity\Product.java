package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Product implements Serializable {
    private static final long serialVersionUID = 1L;
    // 主键ID
    private Long id;

    // 产品编号
    private String productNumber;

    // 店铺ID
    private Integer storeId;

    // 产品名称
    private String productName;

    // 价格
    private BigDecimal price;

    // 产品介绍
    private String productIntroduction;

    // 产品图片
    private String productImage;

    // 产品视频
    private String productVideo;

    // 产品状态
    private Integer productStatus;

    // 创建时间
    private LocalDateTime createTime;

    // 更新时间
    private LocalDateTime updateTime;

    // 类型
    private String type;

    //分类id
    private String categoryId;
}
