<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.CategoryMapper">

    <select id="pageQuery" resultType="com.sky.entity.Category">
        select * from category
        <where>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
        order by sort asc , create_time desc
    </select>

    <select id="selectAllWithType" resultType="com.sky.entity.Category">
        SELECT id, name, parent_id, level, sort_order
        FROM category
        ORDER BY parent_id ASC, sort_order ASC
    </select>
    <select id="findByCondition" resultType="com.sky.entity.Category">

    </select>

</mapper>
