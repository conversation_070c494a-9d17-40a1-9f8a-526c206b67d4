package com.sky.service;

import com.sky.dto.RegisterTrackingDTO;
import com.sky.dto.TrackingQueryDTO;
import com.sky.vo.TrackingListVO;
import com.sky.vo.TrackingQuotaVO;
import com.sky.vo.RegisterTrackingResponseVO;

import java.util.List;

/**
 * 17TRACK API服务接口
 */
public interface Track17Service {

    /**
     * 注册物流单号到17TRACK
     * @param trackingNumbers 物流单号列表
     * @return 注册结果
     */
    RegisterTrackingResponseVO registerTracking(List<RegisterTrackingDTO> trackingNumbers);

    /**
     * 修改运输商
     * @param trackingNumber 物流单号
     * @param oldCarrier 旧运输商代码
     * @param newCarrier 新运输商代码
     * @return 修改结果
     */
    RegisterTrackingResponseVO changeCarrier(String trackingNumber, Integer oldCarrier, Integer newCarrier);

    /**
     * 修改跟踪信息
     * @param trackingNumber 物流单号
     * @param carrier 运输商代码
     * @param tag 标签
     * @param remark 备注
     * @return 修改结果
     */
    RegisterTrackingResponseVO changeInfo(String trackingNumber, Integer carrier, String tag, String remark);

    /**
     * 停止跟踪
     * @param trackingNumber 物流单号
     * @param carrier 运输商代码
     * @return 停止结果
     */
    RegisterTrackingResponseVO stopTracking(String trackingNumber, Integer carrier);

    /**
     * 重启跟踪
     * @param trackingNumber 物流单号
     * @param carrier 运输商代码
     * @return 重启结果
     */
    RegisterTrackingResponseVO retrack(String trackingNumber, Integer carrier);

    /**
     * 删除物流单号
     * @param trackingNumber 物流单号
     * @param carrier 运输商代码
     * @return 删除结果
     */
    RegisterTrackingResponseVO deleteTracking(String trackingNumber, Integer carrier);

    /**
     * 获取当前剩余单量
     * @return 单量信息
     */
    TrackingQuotaVO getQuota();

    /**
     * 获取跟踪列表
     * @param queryDTO 查询条件
     * @return 跟踪列表
     */
    TrackingListVO getTrackingList(TrackingQueryDTO queryDTO);

    /**
     * 获取支持的运输商列表
     * @return 运输商列表
     */
    List<Object> getSupportedCarriers();
}
