package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 购物车
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCart implements Serializable {

    private static final long serialVersionUID = 1L;

    private int id;

    //用户id
    private Long buyerId;

    //商品id
    private Long productId;

    //商品名称
    private String productName;

    //数量
    private Integer number;

    //金额
    private BigDecimal amount;

    //总金额
    private BigDecimal price;

    //图片
    private String image;
}
