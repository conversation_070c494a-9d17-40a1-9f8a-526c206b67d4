package com.sky.mapper;

import com.sky.entity.*;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 订单详情集成Mapper接口
 */
@Mapper
public interface OrderDetailIntegrationMapper {

    // ========== 物流跟踪相关 ==========

    /**
     * 根据订单ID查询物流跟踪信息
     */
    @Select("SELECT * FROM order_logistics_tracking WHERE order_id = #{orderId}")
    OrderLogisticsTracking getLogisticsTrackingByOrderId(Long orderId);

    /**
     * 根据物流单号查询物流跟踪信息
     */
    @Select("SELECT * FROM order_logistics_tracking WHERE tracking_number = #{trackingNumber}")
    OrderLogisticsTracking getLogisticsTrackingByTrackingNumber(String trackingNumber);

    /**
     * 插入物流跟踪信息
     */
    @Insert("INSERT INTO order_logistics_tracking (order_id, order_number, tracking_number, carrier_code, carrier_name, " +
            "current_status, current_status_desc, latest_location, estimated_delivery_time, actual_delivery_time, " +
            "ship_time, last_update_time) VALUES " +
            "(#{orderId}, #{orderNumber}, #{trackingNumber}, #{carrierCode}, #{carrierName}, " +
            "#{currentStatus}, #{currentStatusDesc}, #{latestLocation}, #{estimatedDeliveryTime}, #{actualDeliveryTime}, " +
            "#{shipTime}, #{lastUpdateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertLogisticsTracking(OrderLogisticsTracking tracking);

    /**
     * 更新物流跟踪信息
     */
    @Update("UPDATE order_logistics_tracking SET current_status = #{currentStatus}, " +
            "current_status_desc = #{currentStatusDesc}, latest_location = #{latestLocation}, " +
            "estimated_delivery_time = #{estimatedDeliveryTime}, actual_delivery_time = #{actualDeliveryTime}, " +
            "last_update_time = #{lastUpdateTime} WHERE id = #{id}")
    void updateLogisticsTracking(OrderLogisticsTracking tracking);

    // ========== 物流轨迹详情相关 ==========

    /**
     * 根据物流跟踪ID查询轨迹详情列表
     */
    @Select("SELECT * FROM logistics_trace_detail WHERE tracking_id = #{trackingId} ORDER BY trace_time DESC")
    List<LogisticsTraceDetail> getLogisticsTracesByTrackingId(Long trackingId);

    /**
     * 根据物流单号查询轨迹详情列表
     */
    @Select("SELECT * FROM logistics_trace_detail WHERE tracking_number = #{trackingNumber} ORDER BY trace_time DESC")
    List<LogisticsTraceDetail> getLogisticsTracesByTrackingNumber(String trackingNumber);

    /**
     * 批量插入物流轨迹详情
     */
    void insertLogisticsTracesBatch(List<LogisticsTraceDetail> traces);

    /**
     * 插入单个物流轨迹详情
     */
    @Insert("INSERT INTO logistics_trace_detail (tracking_id, tracking_number, trace_time, trace_location, " +
            "trace_status, trace_desc, operator, phone, sort_order) VALUES " +
            "(#{trackingId}, #{trackingNumber}, #{traceTime}, #{traceLocation}, " +
            "#{traceStatus}, #{traceDesc}, #{operator}, #{phone}, #{sortOrder})")
    void insertLogisticsTrace(LogisticsTraceDetail trace);

    // ========== 订单地址信息相关 ==========

    /**
     * 根据订单ID和地址类型查询地址信息
     */
    @Select("SELECT * FROM order_address_info WHERE order_id = #{orderId} AND address_type = #{addressType}")
    OrderAddressInfo getOrderAddressByOrderIdAndType(@Param("orderId") Long orderId, @Param("addressType") Integer addressType);

    /**
     * 根据订单ID查询所有地址信息
     */
    @Select("SELECT * FROM order_address_info WHERE order_id = #{orderId}")
    List<OrderAddressInfo> getOrderAddressesByOrderId(Long orderId);

    /**
     * 插入订单地址信息
     */
    @Insert("INSERT INTO order_address_info (order_id, address_type, consignee_name, phone, " +
            "province_code, province_name, city_code, city_name, district_code, district_name, " +
            "detail_address, postal_code, is_default) VALUES " +
            "(#{orderId}, #{addressType}, #{consigneeName}, #{phone}, " +
            "#{provinceCode}, #{provinceName}, #{cityCode}, #{cityName}, #{districtCode}, #{districtName}, " +
            "#{detailAddress}, #{postalCode}, #{isDefault})")
    void insertOrderAddress(OrderAddressInfo address);

    /**
     * 根据地址ID获取用户地址信息（转换为OrderAddressInfo格式）
     */
    @Select("SELECT id, name as consignee_name, phone_number as phone, " +
            "state as province_name, city as city_name, '' as district_name, " +
            "CONCAT(street, ' ', addressDetail) as detail_address, zip_code as postal_code, " +
            "`Default` as is_default, created_at as create_time, created_at as update_time " +
            "FROM user_address WHERE id = #{addressId}")
    OrderAddressInfo getUserAddressById(@Param("addressId") Long addressId);

    // ========== 订单支付详情相关 ==========

    /**
     * 根据订单ID查询支付详情
     */
    @Select("SELECT * FROM order_payment_detail WHERE order_id = #{orderId}")
    OrderPaymentDetail getPaymentDetailByOrderId(Long orderId);

    /**
     * 根据订单号查询支付详情
     */
    @Select("SELECT * FROM order_payment_detail WHERE order_number = #{orderNumber}")
    OrderPaymentDetail getPaymentDetailByOrderNumber(String orderNumber);

    /**
     * 插入支付详情
     */
    @Insert("INSERT INTO order_payment_detail (order_id, order_number, payment_method, payment_channel, " +
            "transaction_id, payment_amount, currency, payment_status, payment_time, " +
            "refund_amount, refund_time, payment_desc) VALUES " +
            "(#{orderId}, #{orderNumber}, #{paymentMethod}, #{paymentChannel}, " +
            "#{transactionId}, #{paymentAmount}, #{currency}, #{paymentStatus}, #{paymentTime}, " +
            "#{refundAmount}, #{refundTime}, #{paymentDesc})")
    void insertPaymentDetail(OrderPaymentDetail paymentDetail);

    /**
     * 更新支付详情
     */
    @Update("UPDATE order_payment_detail SET payment_status = #{paymentStatus}, " +
            "payment_time = #{paymentTime}, refund_amount = #{refundAmount}, " +
            "refund_time = #{refundTime} WHERE order_id = #{orderId}")
    void updatePaymentDetail(OrderPaymentDetail paymentDetail);

    // ========== 订单状态日志相关 ==========

    /**
     * 根据订单ID查询状态变更日志
     */
    @Select("SELECT * FROM order_status_log WHERE order_id = #{orderId} ORDER BY create_time DESC")
    List<OrderStatusLog> getOrderStatusLogsByOrderId(Long orderId);

    /**
     * 插入订单状态日志
     */
    @Insert("INSERT INTO order_status_log (order_id, order_number, old_status, new_status, status_desc, " +
            "operator_type, operator_id, operator_name, remark) VALUES " +
            "(#{orderId}, #{orderNumber}, #{oldStatus}, #{newStatus}, #{statusDesc}, " +
            "#{operatorType}, #{operatorId}, #{operatorName}, #{remark})")
    void insertOrderStatusLog(OrderStatusLog statusLog);
}
