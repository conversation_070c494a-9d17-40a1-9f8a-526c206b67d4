package com.sky.service;

import com.sky.dto.WebhookNotificationDTO;
import com.sky.entity.WebhookLog;

import java.util.List;

/**
 * 17TRACK Webhook服务接口
 */
public interface WebhookService {

    /**
     * 处理Webhook通知
     * @param notification Webhook通知数据
     * @param requestBody 原始请求体
     * @return 处理结果
     */
    boolean processWebhookNotification(WebhookNotificationDTO notification, String requestBody);

    /**
     * 验证Webhook签名
     * @param requestBody 请求体
     * @param signature 签名
     * @return 是否有效
     */
    boolean validateWebhookSignature(String requestBody, String signature);

    /**
     * 记录Webhook日志
     * @param eventType 事件类型
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @param requestBody 请求体
     * @param responseStatus 响应状态
     * @param responseBody 响应体
     * @param processStatus 处理状态
     * @param errorMessage 错误信息
     * @return Webhook日志
     */
    WebhookLog logWebhook(String eventType, String trackingNumber, Integer carrierCode,
                          String requestBody, Integer responseStatus, String responseBody,
                          String processStatus, String errorMessage);

    /**
     * 获取Webhook日志
     * @param trackingNumber 物流单号
     * @return 日志列表
     */
    List<WebhookLog> getWebhookLogs(String trackingNumber);

    /**
     * 重试失败的Webhook处理
     * @param webhookLogId Webhook日志ID
     * @return 是否成功
     */
    boolean retryWebhookProcessing(Long webhookLogId);

    /**
     * 清理过期的Webhook日志
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupExpiredLogs(int days);
}
