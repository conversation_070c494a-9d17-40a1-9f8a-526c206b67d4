package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminAccountUpdateDTO {
    private Long id;
    private String accountName;
    private String email;
    private String phone;
    private String password;
    private Integer status;
    private String remark;
}
