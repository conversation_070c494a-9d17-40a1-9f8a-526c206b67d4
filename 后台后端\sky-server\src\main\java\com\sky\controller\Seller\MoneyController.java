package com.sky.controller.Seller;

import com.sky.dto.DataRecordsPageDTO;

import com.sky.dto.FundFlowPageDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.MoneyService;
import com.sky.service.SellerSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;

@RestController
@RequestMapping("/money")
@Api(tags = "卖家钱包管理相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class MoneyController {

    @Autowired
    private MoneyService moneyService;

    /**
     * 提现记录分类分页查询
     * @param dataRecordsPageDTO
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("提现记录分类分页查询")
    public Result<PageResult> page(DataRecordsPageDTO dataRecordsPageDTO){
        log.info("分页查询：{}", dataRecordsPageDTO);
        PageResult pageResult = moneyService.pageQuery(dataRecordsPageDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据卖家id查询可提现金额
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("根据卖家id查询可提现金额")
    public Result getById(@PathVariable Long id){
        log.info("根据id查询：{}", id);
        BigDecimal byAmount = moneyService.getByAmount(id);
        return Result.success(byAmount);
    }

    /**
     * 流水明细分类分页查询
     * @param fundFlowPageDTO
     * @return
     */
    @GetMapping("/FundFlowPage")
    @ApiOperation("流水明细分类分页查询")
    public Result<PageResult> fundFlowPage(FundFlowPageDTO fundFlowPageDTO){
        log.info("分页查询：{}", fundFlowPageDTO);
        PageResult pageResult = moneyService.fundFlowpageQuery(fundFlowPageDTO);
        return Result.success(pageResult);
    }

    /**
     * 导出流水数据报表
     * @param response
     */
    @GetMapping("/export")
    @ApiOperation("导出流水数据报表")
    public void export(HttpServletResponse response ,
                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate){
        moneyService.exportFlowData(startDate, endDate, response);
    }

}
