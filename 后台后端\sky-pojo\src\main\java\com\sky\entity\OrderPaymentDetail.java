package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单支付详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderPaymentDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付渠道
     */
    private String paymentChannel;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

    /**
     * 支付描述
     */
    private String paymentDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 获取支付方式名称
     */
    public String getPaymentMethodName() {
        if (paymentMethod == null) return paymentDesc;
        switch (paymentMethod) {
            case "wechat": return "微信支付";
            case "alipay": return "支付宝";
            case "bank": return "银行卡";
            default: return paymentDesc != null ? paymentDesc : "未知支付方式";
        }
    }

    /**
     * 获取支付状态名称
     */
    public String getPaymentStatusName() {
        if (paymentStatus == null) return "未知状态";
        switch (paymentStatus) {
            case "success": return "支付成功";
            case "pending": return "待支付";
            case "failed": return "支付失败";
            case "cancelled": return "已取消";
            case "refunded": return "已退款";
            default: return paymentStatus;
        }
    }
}
