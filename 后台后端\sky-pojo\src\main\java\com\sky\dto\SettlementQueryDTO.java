package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 回款查询DTO
 */
@Data
public class SettlementQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;

    /**
     * 商家ID（商家端查询时使用）
     */
    private Long sellerId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 回款状态(0-未到期,1-待回款,2-已回款)
     */
    private Integer settlementStatus;

    /**
     * 账单周期(格式:2025-01)
     */
    private String billingCycle;

    /**
     * 回款开始日期
     */
    private LocalDate settlementStartDate;

    /**
     * 回款结束日期
     */
    private LocalDate settlementEndDate;

    /**
     * 商家名称（平台端查询时使用）
     */
    private String sellerName;
}
