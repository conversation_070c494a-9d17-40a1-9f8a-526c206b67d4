package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sky.dto.SettlementCompleteDTO;
import com.sky.dto.SettlementQueryDTO;
import com.sky.entity.OrderSettlementInfo;
import com.sky.entity.Orders;
import com.sky.entity.SettlementConfig;
import com.sky.exception.BaseException;
import com.sky.mapper.OrderSettlementInfoMapper;
import com.sky.mapper.OrdersMapper;
import com.sky.mapper.SettlementConfigMapper;
import com.sky.service.SettlementService;
import com.sky.vo.SettlementInfoVO;
import com.sky.vo.SettlementSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 回款服务实现类
 */
@Service
@Slf4j
public class SettlementServiceImpl implements SettlementService {

    @Autowired
    private OrderSettlementInfoMapper orderSettlementInfoMapper;

    @Autowired
    private OrdersMapper ordersMapper;

    @Autowired
    private SettlementConfigMapper settlementConfigMapper;

    @Override
    @Transactional
    public void createSettlementInfo(Long orderId) {
        log.info("为订单创建回款信息，订单ID：{}", orderId);

        // 检查是否已存在回款信息
        OrderSettlementInfo existing = orderSettlementInfoMapper.selectByOrderId(orderId);
        if (existing != null) {
            log.warn("订单{}的回款信息已存在，跳过创建", orderId);
            return;
        }

        // 查询订单信息
        Orders order = ordersMapper.getById(orderId);
        if (order == null) {
            throw new BaseException("订单不存在");
        }

        if (order.getPayTime() == null) {
            throw new BaseException("订单未支付，无法创建回款信息");
        }

        // 计算账单日期（支付时间 + 14天）
        LocalDate billingDate = order.getPayTime().toLocalDate().plusDays(14);
        
        // 计算账单周期
        String billingCycle = calculateBillingCycle(billingDate);
        
        // 计算回款日期
        LocalDate settlementDate = calculateSettlementDate(billingDate);

        // 获取商家信息（通过订单详情和商品信息关联）
        // 这里简化处理，实际应该通过订单详情查询商家信息
        Long sellerId = 1L; // 默认商家ID，实际应该从订单详情中获取
        String sellerName = "默认商家"; // 默认商家名称

        // 计算回款金额（扣除平台佣金）
        BigDecimal commissionRate = getCommissionRate();
        BigDecimal settlementAmount = order.getAmount().multiply(BigDecimal.ONE.subtract(commissionRate));

        // 创建回款信息
        OrderSettlementInfo settlementInfo = OrderSettlementInfo.builder()
                .orderId(orderId)
                .orderNumber(order.getNumber())
                .sellerId(sellerId)
                .sellerName(sellerName)
                .payTime(order.getPayTime())
                .billingDate(billingDate)
                .billingCycle(billingCycle)
                .settlementDate(settlementDate)
                .orderAmount(order.getAmount())
                .settlementAmount(settlementAmount)
                .settlementStatus(OrderSettlementInfo.STATUS_NOT_DUE)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        orderSettlementInfoMapper.insert(settlementInfo);
        log.info("订单{}回款信息创建成功，回款日期：{}", orderId, settlementDate);
    }

    @Override
    public PageInfo<SettlementInfoVO> pageQuery(SettlementQueryDTO queryDTO) {
        log.info("分页查询回款信息：{}", queryDTO);
        
        PageHelper.startPage(queryDTO.getPage(), queryDTO.getPageSize());
        Page<SettlementInfoVO> page = orderSettlementInfoMapper.selectPage(queryDTO);
        
        return new PageInfo<>(page);
    }

    @Override
    public SettlementSummaryVO getSummaryBySellerId(Long sellerId) {
        log.info("查询商家回款汇总信息，商家ID：{}", sellerId);
        return orderSettlementInfoMapper.selectSummaryBySellerId(sellerId);
    }

    @Override
    public List<SettlementSummaryVO> getAllSummary() {
        log.info("查询所有商家回款汇总信息");
        return orderSettlementInfoMapper.selectAllSummary();
    }

    @Override
    public List<SettlementInfoVO> getPendingSettlement() {
        log.info("查询待回款订单");
        return orderSettlementInfoMapper.selectPendingSettlement();
    }

    @Override
    @Transactional
    public void completeSettlement(SettlementCompleteDTO completeDTO) {
        log.info("标记回款完成：{}", completeDTO);
        
        OrderSettlementInfo settlementInfo = orderSettlementInfoMapper.selectById(completeDTO.getId());
        if (settlementInfo == null) {
            throw new BaseException("回款记录不存在");
        }

        if (settlementInfo.getSettlementStatus().equals(OrderSettlementInfo.STATUS_COMPLETED)) {
            throw new BaseException("该订单已完成回款");
        }

        orderSettlementInfoMapper.updateSettlementComplete(completeDTO.getId(), completeDTO.getRemark());
        log.info("回款完成标记成功，ID：{}", completeDTO.getId());
    }

    @Override
    @Transactional
    public void updateSettlementStatus() {
        log.info("更新回款状态");
        
        LocalDate today = LocalDate.now();
        
        // 查询今天应该回款的订单
        List<SettlementInfoVO> todaySettlements = orderSettlementInfoMapper.selectBySettlementDate(today);
        
        if (!todaySettlements.isEmpty()) {
            List<Long> ids = todaySettlements.stream()
                    .filter(s -> s.getSettlementStatus().equals(OrderSettlementInfo.STATUS_NOT_DUE))
                    .map(SettlementInfoVO::getId)
                    .toList();
            
            if (!ids.isEmpty()) {
                orderSettlementInfoMapper.batchUpdateStatus(ids, OrderSettlementInfo.STATUS_PENDING);
                log.info("更新{}条记录状态为待回款", ids.size());
            }
        }
    }

    @Override
    public LocalDate calculateSettlementDate(LocalDate billingDate) {
        // 获取账单周期的下个月
        LocalDate nextMonth = billingDate.withDayOfMonth(1).plusMonths(1);
        
        // 下个月10号
        LocalDate settlementDate = nextMonth.withDayOfMonth(10);
        
        // 找到10号后的第一个工作日
        while (!isWorkingDay(settlementDate)) {
            settlementDate = settlementDate.plusDays(1);
        }
        
        return settlementDate;
    }

    @Override
    public String calculateBillingCycle(LocalDate billingDate) {
        return billingDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    @Override
    public boolean isWorkingDay(LocalDate date) {
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        
        // 排除周末
        if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
            return false;
        }
        
        // 这里可以添加节假日判断逻辑
        // 简化处理，只排除周末
        return true;
    }

    /**
     * 获取平台佣金比例
     */
    private BigDecimal getCommissionRate() {
        SettlementConfig config = settlementConfigMapper.selectByKey("platform_commission_rate");
        if (config != null) {
            return new BigDecimal(config.getConfigValue());
        }
        return new BigDecimal("0.05"); // 默认5%
    }
}
