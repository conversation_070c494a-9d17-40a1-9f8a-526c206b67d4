package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款申请DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundApplicationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 申请退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款理由
     */
    private String refundReason;

    /**
     * 退款类型：1-仅退款，2-退货退款
     */
    private Integer refundType;

    /**
     * 退款方式：1-原路退回，2-余额退款
     */
    private Integer refundMethod;
}
