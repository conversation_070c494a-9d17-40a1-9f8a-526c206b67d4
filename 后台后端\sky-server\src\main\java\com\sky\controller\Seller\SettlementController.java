package com.sky.controller.Seller;

import com.github.pagehelper.PageInfo;
import com.sky.dto.SettlementQueryDTO;
import com.sky.result.Result;
import com.sky.service.SettlementService;
import com.sky.vo.SettlementInfoVO;
import com.sky.vo.SettlementSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 商家端回款管理接口
 */
@RestController
@RequestMapping("/merchant/settlement")
@Api(tags = "商家端回款管理接口")
@CrossOrigin(origins = "*")
@Slf4j
public class SettlementController {

    @Autowired
    private SettlementService settlementService;

    /**
     * 分页查询商家订单回款信息
     */
    @GetMapping("/orders")
    @ApiOperation("分页查询商家订单回款信息")
    public Result<PageInfo<SettlementInfoVO>> getSettlementOrders(SettlementQueryDTO queryDTO) {
        log.info("商家查询订单回款信息：{}", queryDTO);
        
        // TODO: 从JWT token中获取当前登录商家ID
        // 这里暂时使用固定值，实际应该从认证信息中获取
        Long currentSellerId = 16L; // 示例商家ID
        queryDTO.setSellerId(currentSellerId);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 查询商家回款汇总信息
     */
    @GetMapping("/summary")
    @ApiOperation("查询商家回款汇总信息")
    public Result<SettlementSummaryVO> getSettlementSummary() {
        log.info("查询商家回款汇总信息");
        
        // TODO: 从JWT token中获取当前登录商家ID
        Long currentSellerId = 16L; // 示例商家ID
        
        SettlementSummaryVO summary = settlementService.getSummaryBySellerId(currentSellerId);
        return Result.success(summary);
    }

    /**
     * 根据回款状态查询订单
     */
    @GetMapping("/orders/status/{status}")
    @ApiOperation("根据回款状态查询订单")
    public Result<PageInfo<SettlementInfoVO>> getOrdersByStatus(
            @PathVariable Integer status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        log.info("根据状态查询回款订单，状态：{}", status);
        
        // TODO: 从JWT token中获取当前登录商家ID
        Long currentSellerId = 16L; // 示例商家ID
        
        SettlementQueryDTO queryDTO = new SettlementQueryDTO();
        queryDTO.setSellerId(currentSellerId);
        queryDTO.setSettlementStatus(status);
        queryDTO.setPage(page);
        queryDTO.setPageSize(pageSize);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 根据账单周期查询订单
     */
    @GetMapping("/orders/cycle/{billingCycle}")
    @ApiOperation("根据账单周期查询订单")
    public Result<PageInfo<SettlementInfoVO>> getOrdersByCycle(
            @PathVariable String billingCycle,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        log.info("根据账单周期查询回款订单，周期：{}", billingCycle);
        
        // TODO: 从JWT token中获取当前登录商家ID
        Long currentSellerId = 16L; // 示例商家ID
        
        SettlementQueryDTO queryDTO = new SettlementQueryDTO();
        queryDTO.setSellerId(currentSellerId);
        queryDTO.setBillingCycle(billingCycle);
        queryDTO.setPage(page);
        queryDTO.setPageSize(pageSize);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        return Result.success(pageInfo);
    }
}
