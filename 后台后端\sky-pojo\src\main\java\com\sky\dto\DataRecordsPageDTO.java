package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class DataRecordsPageDTO implements Serializable {
    //页码
    private int page;

    //每页记录数
    private int pageSize;

    //流水号
    private String serialNumber;

    //起始时间
    private LocalDateTime FirstTime;

    //结束时间
    private LocalDateTime LastTime;

    private String status;
}
