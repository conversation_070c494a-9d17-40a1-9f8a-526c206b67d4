package com.sky.controller.admin;

import com.sky.entity.ShoppingCart;
import com.sky.result.Result;
import com.sky.service.ShoppingCartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@ApiOperation("/cart")
@Api(tags = "C端购物车接口")
@CrossOrigin(origins = "*", allowedHeaders = "*")
@RequestMapping("/cart")
public class ShoppingCartController {

    @Autowired
    private ShoppingCartService shoppingCartService;

    // 添加商品到购物车
    @PostMapping("/add")
    @ApiOperation("添加商品到购物车")
    public Result<Void> addToCart(@RequestBody ShoppingCart shoppingCart) {
        shoppingCartService.addToCart(shoppingCart);
        return Result.success();
    }

    // 获取购物车信息
    @GetMapping("/list/{buyerId}")
    @ApiOperation("获取购物车信息")
    public Result<List<ShoppingCart>> getCartByBuyerId(@PathVariable Long buyerId) {
        List<ShoppingCart> cartList = shoppingCartService.getCartByBuyerId(buyerId);
        return Result.success(cartList);
    }

    // 删除购物车记录
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除购物车记录")
    public Result<Void> deleteCartItem(@PathVariable int id) {
        shoppingCartService.deleteCartItem(id);
        return Result.success();
    }

    @PutMapping("/update")
    public Result update(@RequestBody ShoppingCart shoppingCart) {
        shoppingCartService.update(shoppingCart);
        return Result.success();
    }
}