package com.sky.service.impl;
import com.sky.entity.BusinessException;
import com.sky.entity.ProductReview;
import com.sky.mapper.ProductReviewMapper;
import com.sky.service.ProductReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ProductReviewServiceImpl implements ProductReviewService {

    @Autowired
    private ProductReviewMapper productReviewMapper;

    @Override
    public void addReview(ProductReview productReview) {
        if (productReview.getRating() == null || productReview.getRating() < 1 || productReview.getRating() > 5) {
            throw new BusinessException("评分范围是1-5分！");
        }

        productReview.setCommentTime(LocalDateTime.now());
        productReview.setStatus(0); // 默认状态为正常
        productReviewMapper.insert(productReview);
    }

    @Override
    public List<ProductReview> getReviewsByProductId(Long productId) {
        return productReviewMapper.findByProductId(productId);
    }

    @Override
    public void deleteReview(Long id) {
        productReviewMapper.deleteById(id);
    }
}