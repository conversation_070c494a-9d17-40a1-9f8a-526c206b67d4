package com.sky.mapper;

import com.sky.entity.OrderLogisticsTracking;
import org.apache.ibatis.annotations.*;

/**
 * 订单物流跟踪Mapper
 */
@Mapper
public interface OrderLogisticsTrackingMapper {

    /**
     * 插入物流跟踪信息
     */
    @Insert("INSERT INTO order_logistics_tracking (order_id, order_number, tracking_number, carrier_code, carrier_name, " +
            "current_status, current_status_desc, latest_location, estimated_delivery_time, actual_delivery_time, " +
            "ship_time, last_update_time, create_time, update_time) VALUES " +
            "(#{orderId}, #{orderNumber}, #{trackingNumber}, #{carrierCode}, #{carrierName}, " +
            "#{currentStatus}, #{currentStatusDesc}, #{latestLocation}, #{estimatedDeliveryTime}, #{actualDeliveryTime}, " +
            "#{shipTime}, #{lastUpdateTime}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(OrderLogisticsTracking tracking);

    /**
     * 根据订单ID查询物流跟踪信息
     */
    @Select("SELECT * FROM order_logistics_tracking WHERE order_id = #{orderId}")
    OrderLogisticsTracking getByOrderId(Long orderId);

    /**
     * 根据物流单号查询物流跟踪信息
     */
    @Select("SELECT * FROM order_logistics_tracking WHERE tracking_number = #{trackingNumber}")
    OrderLogisticsTracking getByTrackingNumber(String trackingNumber);

    /**
     * 更新物流跟踪信息
     */
    @Update("UPDATE order_logistics_tracking SET current_status = #{currentStatus}, " +
            "current_status_desc = #{currentStatusDesc}, latest_location = #{latestLocation}, " +
            "estimated_delivery_time = #{estimatedDeliveryTime}, actual_delivery_time = #{actualDeliveryTime}, " +
            "last_update_time = #{lastUpdateTime}, update_time = #{updateTime} WHERE id = #{id}")
    void update(OrderLogisticsTracking tracking);
}
