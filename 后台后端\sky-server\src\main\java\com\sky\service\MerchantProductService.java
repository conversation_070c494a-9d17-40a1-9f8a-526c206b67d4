package com.sky.service;

import com.sky.entity.MerchantProduct;
import java.util.List;

public interface MerchantProductService {
    List<MerchantProduct> findAll();
    List<MerchantProduct> findByProductCode(String productCode);
    List<MerchantProduct> findByProductTitle(String productTitle);
    List<MerchantProduct> findByStatus(String status);

    void publishProduct(MerchantProduct merchantProduct);

}