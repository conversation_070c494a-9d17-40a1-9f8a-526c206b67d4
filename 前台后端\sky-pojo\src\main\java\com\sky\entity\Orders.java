package com.sky.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("orders")
public class Orders implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单状态常量
     */
    public static final Integer STATUS_PENDING_PAYMENT = 1; // 待付款
    public static final Integer STATUS_PAID = 2;           // 已付款
    public static final Integer STATUS_PROCESSING = 3;     // 处理中
    public static final Integer STATUS_SHIPPED = 4;        // 已发货
    public static final Integer STATUS_COMPLETED = 5;      // 已完成
    public static final Integer STATUS_CANCELLED = 6;      // 已取消
    public static final Integer STATUS_REFUNDED = 7;       // 已退款

    /**
     * 支付方式常量
     */
    public static final Integer PAY_METHOD_WECHAT = 1;     // 微信支付
    public static final Integer PAY_METHOD_ALIPAY = 2;     // 支付宝支付
    public static final Integer PAY_METHOD_CREDIT_CARD = 3; // 信用卡支付
    public static final Integer PAY_METHOD_CASH = 4;       // 货到付款

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 订单号
     */
    private String number;
    
    /**
     * 买家ID
     */
    private Long buyerId;
    
    /**
     * 收货地址ID
     */
    private Long addressId;
    
    /**
     * 配送方式ID
     */
    private Integer shippingMethodId;
    
    /**
     * 订单状态
     */
    private Integer status;
    
    /**
     * 订单总金额
     */
    private BigDecimal amount;
    
    /**
     * 下单时间
     */
    private LocalDateTime orderTime;
    
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    
    /**
     * 结账时间
     */
    private LocalDateTime checkoutTime;
    
    /**
     * 发货时间
     */
    private LocalDateTime shipTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;
    
    /**
     * 支付方式
     */
    private Integer payMethod;
    
    /**
     * 支付交易ID
     */
    private String paymentTransactionId;
    
    /**
     * 订单备注
     */
    private String orderRemark;
    
    /**
     * 取消原因
     */
    private String cancelReason;
    
    /**
     * 拒绝原因
     */
    private String rejectionReason;
    
    /**
     * 取消请求
     */
    private String cancelRequest;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 快递公司代码
     */
    private String courierCode;

    /**
     * 快递公司名称
     */
    private String courierName;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;
}