package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BuyerDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;


    private String accountName;

    private String verificationCode;
    private String gender;

    private String photoUrl;


    private String phone;


    private String email;


}

