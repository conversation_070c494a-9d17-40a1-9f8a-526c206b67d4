package com.sky.service;

import com.sky.dto.RegisterTrackingDTO;
import com.sky.entity.Logistics;
import com.sky.vo.LogisticsVO;
import com.sky.vo.RegisterTrackingResponseVO;

import java.util.List;

/**
 * 物流跟踪服务接口
 */
public interface TrackingService {

    /**
     * 创建物流记录
     * @param logistics 物流信息
     * @return 物流记录
     */
    Logistics createLogistics(Logistics logistics);

    /**
     * 注册物流跟踪到17TRACK并保存本地记录
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    RegisterTrackingResponseVO registerAndSaveTracking(RegisterTrackingDTO registerDTO);

    /**
     * 批量注册物流跟踪
     * @param registerDTOs 注册信息列表
     * @return 注册结果
     */
    RegisterTrackingResponseVO batchRegisterTracking(List<RegisterTrackingDTO> registerDTOs);

    /**
     * 根据物流单号查询物流信息
     * @param trackingNumber 物流单号
     * @return 物流信息
     */
    LogisticsVO getLogisticsByTrackingNumber(String trackingNumber);

    /**
     * 根据订单ID查询物流信息
     * @param orderId 订单ID
     * @return 物流信息列表
     */
    List<LogisticsVO> getLogisticsByOrderId(Long orderId);

    /**
     * 更新物流状态
     * @param trackingNumber 物流单号
     * @param status 物流状态
     * @return 更新结果
     */
    boolean updateLogisticsStatus(String trackingNumber, String status);

    /**
     * 同步17TRACK物流状态到本地
     * @param trackingNumber 物流单号
     * @return 同步结果
     */
    boolean syncTrackingStatus(String trackingNumber);

    /**
     * 批量同步物流状态
     * @param trackingNumbers 物流单号列表
     * @return 同步结果
     */
    boolean batchSyncTrackingStatus(List<String> trackingNumbers);

    /**
     * 停止物流跟踪
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 停止结果
     */
    RegisterTrackingResponseVO stopTracking(String trackingNumber, Integer carrierCode);

    /**
     * 重启物流跟踪
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 重启结果
     */
    RegisterTrackingResponseVO restartTracking(String trackingNumber, Integer carrierCode);

    /**
     * 删除物流跟踪
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 删除结果
     */
    RegisterTrackingResponseVO deleteTracking(String trackingNumber, Integer carrierCode);

    /**
     * 获取物流状态统计
     * @return 统计信息
     */
    Object getTrackingStatistics();

    /**
     * 根据用户ID获取物流信息
     * @param userId 用户ID
     * @param page 页码
     * @param pageSize 每页大小
     * @return 物流信息列表
     */
    List<LogisticsVO> getLogisticsByUserId(Long userId, Integer page, Integer pageSize);
}
