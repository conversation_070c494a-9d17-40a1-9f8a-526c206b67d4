package com.sky.mapper;

import com.sky.dto.MerchantDTO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface MerChantMapper {
    void save(MerchantDTO merchantDTO);

    @Select("select *from ry_mall.merchant where mobile = #{mobile}")
    MerchantDTO  selectBymobile(String mobile);

    @Select("select *from ry_mall.merchant where  status = #{status} ")
    List<MerchantDTO> getMerchantDto(String status);


    void MerChantchange(String status,Long ShopId);

    @Select("select *from ry_mall.merchant where status = #{status}")
    List<MerchantDTO> MerChantpass(String status);

    @Delete("delete from ry_mall.merchant where shop_id = #{ShopId}")
    void MerChantdelate(Long ShopId);
}
