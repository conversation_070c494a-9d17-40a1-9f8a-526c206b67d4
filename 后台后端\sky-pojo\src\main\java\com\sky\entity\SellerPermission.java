package com.sky.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("seller_permission")
@Accessors(chain = true)
public class SellerPermission {
    // 逻辑主键
    @TableId(type = IdType.AUTO)
    private Long id;

    // 商家ID（关联seller表）
    private Long sellerId;

    // 权限标识（路由name）
    private String permissionCode;

    // 权限描述
    private String description;
}
