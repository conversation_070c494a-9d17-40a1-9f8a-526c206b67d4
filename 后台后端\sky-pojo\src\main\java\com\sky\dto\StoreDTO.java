package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long sellerId;

    private String storeName;

    private String photo;

    private String storeStatus;

    private String storeDescription;

    private String storeAddress;

}
