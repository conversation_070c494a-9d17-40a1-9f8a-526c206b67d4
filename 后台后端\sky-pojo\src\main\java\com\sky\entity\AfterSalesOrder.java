package com.sky.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AfterSalesOrder {
    private Long id;
    private String afterSalesNo;
    private String orderNo;
    private String transactionNo;
    private String productInfo;
    private BigDecimal refundAmount;
    private LocalDateTime requestTime;
    private Integer status;
    private String reason;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}