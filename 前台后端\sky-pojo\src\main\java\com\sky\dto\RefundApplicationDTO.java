package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款申请DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "退款申请DTO")
public class RefundApplicationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单ID", required = true)
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @ApiModelProperty(value = "申请退款金额", required = true)
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0.01", message = "退款金额必须大于0")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款理由", required = true)
    @NotBlank(message = "退款理由不能为空")
    private String refundReason;

    @ApiModelProperty(value = "退款类型：1-仅退款，2-退货退款", required = true)
    @NotNull(message = "退款类型不能为空")
    private Integer refundType;

    @ApiModelProperty(value = "退款方式：1-原路退回，2-余额退款")
    private Integer refundMethod;
}
