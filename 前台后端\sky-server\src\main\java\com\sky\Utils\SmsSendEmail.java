package com.sky.Utils;

import com.aliyun.tea.*;

public class SmsSendEmail {
    public static com.aliyun.dm20151123.Client createClient() throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId("LTAI5tJS98mpqpmHWXPFmb3h")
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret("******************************");
        // Endpoint 请参考 https://api.aliyun.com/product/Dm
        config.endpoint = "dm.aliyuncs.com";
        return new com.aliyun.dm20151123.Client(config);
    }

    public static void SendEmail(String[] args_,String Mobile,String Address) throws Exception {
        java.util.List<String> args = java.util.Arrays.asList(args_);
        com.aliyun.dm20151123.Client client = SmsSendEmail.createClient();
        com.aliyun.dm20151123.models.SingleSendMailRequest singleSendMailRequest = new com.aliyun.dm20151123.models.SingleSendMailRequest()
                .setAccountName("<EMAIL>")
                .setAddressType(0)
                .setTagName("sharewharf")
                .setReplyToAddress(false)
                .setToAddress(Address)
                .setSubject("sharewharf平台")
                .setFromAlias("sharewharf验证码")
                .setHtmlBody("<div class=\"email-container\">     <div class=\"email-header\">         <h1>             ShareWharf Email Verification         </h1>     </div>     <div class=\"email-body\">         <p>             " +
                        "Hello,         </p>         <p>             Thank you for registering. We need to verify your " +
                        "email address: <strong>{EAddr}</strong>  &nbsp; &nbsp; &nbsp; &nbsp;         </p>         <p>             " +
                        "Please use the verification code below to complete your registration:         </p>        " +
                        " <div class=\"verification-code\">             "+ Mobile + "       </div>         <p>             " +
                        "This code will expire in 10 minutes. If you did not request this verification, please ignore this email.         </p>     </div>     <div class=\"email-footer\">         <p>             " +
                        "If you have any questions, please contact our support team.         </p>         <p>            " +
                        " &amp;copy; 2025 Your Company. All rights reserved.         </p>     </div> </div><style>body {         font-family: Arial, sans-serif;         " +
                        "line-height: 1.6;         color: #333;         max-width: 600px;         margin: 0 auto;         padding: 20px;         background-color: #f9fafb;     }     .email-container {         border: 1px solid #e0e0e0;         border-radius: 10px;         overflow: hidden;         background-color: #ffffff;         box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);     }     .email-header {         background-color: #4f46e5;         color: white;         padding: 25px;         text-align: center;     }     .email-header h1 {         margin: 0;         " +
                        "font-size: 24px;         font-weight: bold;     }     .email-body {         padding: 25px;        " +
                        " background-color: #ffffff;     }     .email-body p {         margin: 0 0 15px;         font-size: 16px;         " +
                        "line-height: 1.5;     }     .email-body strong {         color: #4f46e5;     }     .verification-code {         font-size: 32px;         font-weight: bold;         text-align: center;         margin: 30px 0;         padding: 20px;         background-color: #f3f4f6;         border-radius: 10px;         letter-spacing: 5px;         color: #4f46e5;         box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);     }     .email-footer {         background-color: #f9fafb;         padding: 20px;         text-align: center;         font-size: 12px;         color: #6b7280;         " +
                        "border-top: 1px solid #e0e0e0;     }     .email-footer p {         margin: 5px 0;     }    " +
                        " @media only screen and (max-width: 600px) {         body {             padding: 10px;         }         " +
                        ".verification-code {             font-size: 24px;             letter-spacing: 3px;             padding: 15px;         }         .email-header h1 {             font-size: 20px;         }         .email-body p {             font-size: 14px;         }     }</style>")
                .setTextBody("")
                .setReplyAddress("<EMAIL>")
                .setReplyAddressAlias("")
                .setClickTrace("0")
                .setUnSubscribeLinkType("default")
                .setUnSubscribeFilterLevel("default");
                //.setHeaders("{ \"Message-ID\": \"<<EMAIL>>\", \"X-User-UID1\": \"UID-1-000001\", \"X-User-UID2\": \"UID-2-000001\" }");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.singleSendMailWithOptions(singleSendMailRequest, runtime);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }


}
