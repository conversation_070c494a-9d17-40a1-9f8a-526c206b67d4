package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sky.entity.USAddress;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface USAddressMapper extends BaseMapper<USAddress> {

    @Insert("INSERT INTO user_address (user_id, name, street, city, state, zip_code, phone_number, address_detail) " +
            "VALUES (#{userId}, #{name}, #{street}, #{city}, #{state}, #{zipCode}, #{phoneNumber}, #{addressDetail})")
    void insert1(USAddress address);

    @Select("SELECT * FROM user_address WHERE user_id = #{userId}")
    List<USAddress> findByUserId(Long userId);

//    @Update("<script>" +
//            "UPDATE user_address " +
//            "<set>" +
//            "  <if test='name != null'>name = #{name},</if>" +
//            "  <if test='street != null'>street = #{street},</if>" +
//            "  <if test='city != null'>city = #{city},</if>" +
//            "  <if test='state != null'>state = #{state},</if>" +
//            "  <if test='zipCode != null'>zip_code = #{zipCode},</if>" +
//            "  <if test='phoneNumber != null'>phone_number = #{phoneNumber},</if>" +
//            "  <if test='addressDetail != null'>addressDetail = #{addressDetail},</if>" +
//            "</set>" +
//            "WHERE id = #{id}" +
//            "</script>")
//    void update(USAddress address);

    @Delete("DELETE FROM user_address WHERE id = #{id}")
    void deleteById(Long id);

    @Select("SELECT COUNT(*) FROM us_city_dict WHERE city = #{city} AND state = #{state}")
    int isValidUSCity(String city, String state);
}