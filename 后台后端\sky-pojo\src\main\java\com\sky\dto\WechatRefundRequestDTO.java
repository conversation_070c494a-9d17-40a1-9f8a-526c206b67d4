package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 微信退款请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatRefundRequestDTO implements Serializable {

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 商户退款单号
     */
    private String outRefundNo;

    /**
     * 退款金额（单位：分）
     */
    private Integer refundAmount;

    /**
     * 订单总金额（单位：分）
     */
    private Integer totalAmount;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 退款资金来源
     */
    private String fundsAccount;

    /**
     * 退款结果回调url
     */
    private String notifyUrl;
}
