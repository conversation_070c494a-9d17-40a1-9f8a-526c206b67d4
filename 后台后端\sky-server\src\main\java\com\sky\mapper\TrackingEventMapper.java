package com.sky.mapper;

import com.sky.entity.TrackingEvent;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 物流事件Mapper
 */
@Mapper
public interface TrackingEventMapper {

    /**
     * 插入物流事件
     */
    @Insert("INSERT INTO tracking_event (tracking_record_id, tracking_number, carrier_code, event_time, " +
            "event_time_raw, event_timezone, status, sub_status, sub_status_desc, location, description, " +
            "description_translated, create_time) " +
            "VALUES (#{trackingRecordId}, #{trackingNumber}, #{carrierCode}, #{eventTime}, " +
            "#{eventTimeRaw}, #{eventTimezone}, #{status}, #{subStatus}, #{subStatusDesc}, #{location}, " +
            "#{description}, #{descriptionTranslated}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(TrackingEvent trackingEvent);

    /**
     * 批量插入物流事件
     */
    void insertBatch(List<TrackingEvent> events);

    /**
     * 根据跟踪记录ID查询事件
     */
    @Select("SELECT * FROM tracking_event WHERE tracking_record_id = #{trackingRecordId} ORDER BY event_time DESC")
    List<TrackingEvent> findByTrackingRecordId(Long trackingRecordId);

    /**
     * 根据物流单号查询事件
     */
    @Select("SELECT * FROM tracking_event WHERE tracking_number = #{trackingNumber} " +
            "AND carrier_code = #{carrierCode} ORDER BY event_time DESC")
    List<TrackingEvent> findByTrackingNumberAndCarrier(@Param("trackingNumber") String trackingNumber,
                                                       @Param("carrierCode") Integer carrierCode);

    /**
     * 查询最新事件
     */
    @Select("SELECT * FROM tracking_event WHERE tracking_record_id = #{trackingRecordId} " +
            "ORDER BY event_time DESC LIMIT 1")
    TrackingEvent findLatestByTrackingRecordId(Long trackingRecordId);

    /**
     * 删除跟踪记录的所有事件
     */
    @Delete("DELETE FROM tracking_event WHERE tracking_record_id = #{trackingRecordId}")
    void deleteByTrackingRecordId(Long trackingRecordId);

    /**
     * 检查事件是否已存在
     */
    @Select("SELECT COUNT(*) FROM tracking_event WHERE tracking_record_id = #{trackingRecordId} " +
            "AND event_time = #{eventTime} AND description = #{description}")
    int countByTrackingRecordIdAndEventTimeAndDescription(@Param("trackingRecordId") Long trackingRecordId,
                                                          @Param("eventTime") java.time.LocalDateTime eventTime,
                                                          @Param("description") String description);
}
