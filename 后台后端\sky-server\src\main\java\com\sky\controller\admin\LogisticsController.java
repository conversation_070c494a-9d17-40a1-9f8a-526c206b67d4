package com.sky.controller.admin;


import com.sky.dto.LogisticsInfoDTO;
import com.sky.result.Result;
import com.sky.service.LogisticsService;
import com.sky.vo.LogisticsInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/logistics")
@Api(tags = "物流相关接口")
@Slf4j
public class LogisticsController {

    @Autowired
    private LogisticsService logisticsService;

    @ApiOperation("新增物流信息")
    @PostMapping("/create")
    public Result createLogistics(@RequestBody LogisticsInfoDTO logisticsInfoDTO){
        log.info("新增物流信息：{}",logisticsInfoDTO);
        logisticsService.createLogistics(logisticsInfoDTO);
        return Result.success();
    }

    @ApiOperation("查询物流信息")
    @GetMapping("/{id}")
    public Result getLogisticsInfo(@PathVariable Long id){
        log.info("查询物流信息id:{}",id);
        LogisticsInfoVO logisticsInfoVO = logisticsService.getLogisticsInfo(id);
        return Result.success(logisticsInfoVO);
    }
    @ApiOperation("修改物流信息状态")
    @PutMapping("/update/{id}/{logisticsStatus}")
    public Result updateLogistics(@PathVariable("id") Long id, @PathVariable("logisticsStatus") String logisticsStatus){
    log.info("修改物流信息状态id:{}, logisticsStatus：{}", id, logisticsStatus);
    logisticsService.updateLogistics(id, logisticsStatus);
    return Result.success();
    }
}
