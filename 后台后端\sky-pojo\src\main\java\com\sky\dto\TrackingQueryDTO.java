package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String trackingNumber;          // 物流单号（支持模糊查询）
    private Integer carrierCode;            // 运输商代码
    private String dataOrigin;              // 数据来源：Api/Manual/Import
    private String packageStatus;           // 包裹状态
    private LocalDateTime registerTimeFrom; // 注册起始时间
    private LocalDateTime registerTimeTo;   // 注册结束时间
    private String trackingStatus;          // 跟踪状态：Tracking/Stopped
    private LocalDateTime trackTimeFrom;    // 跟踪起始时间
    private LocalDateTime trackTimeTo;      // 跟踪结束时间
    private LocalDateTime pushTimeFrom;     // 推送起始时间
    private LocalDateTime pushTimeTo;       // 推送结束时间
    private String pushStatus;              // 推送状态：NotPushed/Success/Failure
    private Integer pushStatusCode;         // 推送状态码
    private LocalDateTime stopTrackTimeFrom; // 停止跟踪起始时间
    private LocalDateTime stopTrackTimeTo; // 停止跟踪结束时间
    private Integer pageNo;                 // 页码
    private String orderBy;                 // 排序方式

    // 排序方式常量
    public static final String ORDER_BY_REGISTER_TIME_ASC = "RegisterTimeAsc";
    public static final String ORDER_BY_REGISTER_TIME_DESC = "RegisterTimeDesc";
    public static final String ORDER_BY_PUSH_TIME_ASC = "PushTimeAsc";
    public static final String ORDER_BY_PUSH_TIME_DESC = "PushTimeDesc";
    public static final String ORDER_BY_TRACK_TIME_ASC = "TrackTimeAsc";
    public static final String ORDER_BY_TRACK_TIME_DESC = "TrackTimeDesc";
}
