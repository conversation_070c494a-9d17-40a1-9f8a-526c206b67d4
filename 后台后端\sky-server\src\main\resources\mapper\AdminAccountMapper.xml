<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.AdminAccountMapper">
    <delete id="deletePermissions">
        DELETE FROM seller_permission
        WHERE seller_id = #{id}
        AND permission_code IN
        <foreach item="permission" collection="permissionsToDelete" separator="," open="(" close=")" index="">
            #{permission}
        </foreach>
    </delete>

    <resultMap id="AdminAccountPermissionMap" type="com.sky.vo.AdminAccountVO">
        <id property="id" column="id" />
        <result property="accountName" column="account_name" />
        <result property="email" column="email" />
        <result property="phone" column="phone" />
        <result property="accountStatus" column="account_status" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="lastLoginTime" column="last_login_time" />
        <result
                property="permissions"
                column="permission_codes"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <select id="selectAdminAccounts" resultMap="AdminAccountPermissionMap">
        SELECT
        s.id,
        s.account_name,
        s.email,
        s.phone,
        s.account_status,
        s.create_time,
        s.update_time,
        s.last_login_time,
        CONCAT(
        '[',
        GROUP_CONCAT(DISTINCT CONCAT('"', sp.permission_code, '"') ORDER BY sp.permission_code),
        ']'
        ) AS permission_codes
        FROM seller s
        LEFT JOIN seller_permission sp ON s.id = sp.seller_id
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                AND (account_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR email LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.status != null">
                AND account_status = #{query.status}
            </if>
                and is_sub_account = 1
        </where>
        group by s.id
        <choose>
            <when test="query.sortField == 'accountName'">
                ORDER BY account_name ${query.sortOrder}
            </when>
            <when test="query.sortField == 'createTime'">
                ORDER BY create_time ${query.sortOrder}
            </when>
            <when test="query.sortField == 'lastLoginTime'">
                ORDER BY last_login_time ${query.sortOrder}
            </when>
            <otherwise>
                ORDER BY s.id ${query.sortOrder}
            </otherwise>
        </choose>
        LIMIT ${((query.page != null) ? (query.page - 1) : (1-1)) * ((query.pageSize != null) ? query.pageSize : 10)}, ${(query.pageSize != null) ? query.pageSize : 10}
    </select>

    <select id="countAdminAccounts" resultType="long">
        SELECT COUNT(*) FROM seller
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                AND (account_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR email LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.status != null">
                AND account_status = #{query.status}
            </if>
        </where>
    </select>

    <insert id="insertAdminAccount" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO seller (
            account_name, email, phone, password,
            account_status,created_by, remark,create_time,last_login_time
        ) VALUES (
                     #{accountName}, #{email}, #{phone}, #{password},
                     #{status},#{createdBy}, #{remark}, now(),now()
                 )
    </insert>

    <update id="updateAdminAccountById">
        UPDATE seller
        <set>
            <if test="accountName != null and accountName != ''">
                account_name = #{accountName},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="status != null">
                account_status = #{status},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            update_time = now(),
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateAdminStatus">
        update seller
        set account_status = #{status},update_time = now()
        where id = #{id}
    </update>

    <select id="getAdminLogs" resultType="com.sky.vo.AdminLogVO">
        SELECT
        id,adminId,adminName,actionType,actionDesc,ipAddress,userAgent,createTime
        FROM admin_account_log
        <where>
        <if test="adminId != null ">
            adminId = #{adminId}
        </if>
        <if test="startTime != null and startTime != ''">
            <![CDATA[
            AND createTime >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null and endTime != ''">
            <![CDATA[
            AND createTime <= #{endTime}
            ]]>
        </if>
        <if test="actionType != null and actionType != ''">
            AND actionType = #{actionType}
        </if>
        </where>
        ORDER BY createTime DESC
    </select>
</mapper>
