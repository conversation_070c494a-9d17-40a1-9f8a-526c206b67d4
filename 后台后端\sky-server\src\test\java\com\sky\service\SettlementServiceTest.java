package com.sky.service;

import com.github.pagehelper.PageInfo;
import com.sky.dto.SettlementQueryDTO;
import com.sky.entity.OrderSettlementInfo;
import com.sky.vo.SettlementInfoVO;
import com.sky.vo.SettlementSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.List;

/**
 * 回款服务测试类
 */
@SpringBootTest
@Slf4j
public class SettlementServiceTest {

    @Autowired
    private SettlementService settlementService;

    /**
     * 测试回款日期计算
     */
    @Test
    public void testCalculateSettlementDate() {
        log.info("测试回款日期计算");
        
        // 测试2025年1月的账单日期
        LocalDate billingDate1 = LocalDate.of(2025, 1, 15);
        LocalDate settlementDate1 = settlementService.calculateSettlementDate(billingDate1);
        log.info("账单日期：{}，回款日期：{}", billingDate1, settlementDate1);
        
        // 测试2025年2月的账单日期
        LocalDate billingDate2 = LocalDate.of(2025, 2, 20);
        LocalDate settlementDate2 = settlementService.calculateSettlementDate(billingDate2);
        log.info("账单日期：{}，回款日期：{}", billingDate2, settlementDate2);
        
        // 验证回款日期应该是下个月10号后的第一个工作日
        assert settlementDate1.getMonthValue() == 2; // 2月
        assert settlementDate1.getDayOfMonth() >= 10; // 10号或之后
        
        assert settlementDate2.getMonthValue() == 3; // 3月
        assert settlementDate2.getDayOfMonth() >= 10; // 10号或之后
    }

    /**
     * 测试账单周期计算
     */
    @Test
    public void testCalculateBillingCycle() {
        log.info("测试账单周期计算");
        
        LocalDate billingDate = LocalDate.of(2025, 1, 15);
        String billingCycle = settlementService.calculateBillingCycle(billingDate);
        log.info("账单日期：{}，账单周期：{}", billingDate, billingCycle);
        
        assert "2025-01".equals(billingCycle);
    }

    /**
     * 测试工作日判断
     */
    @Test
    public void testIsWorkingDay() {
        log.info("测试工作日判断");
        
        // 测试周一（工作日）
        LocalDate monday = LocalDate.of(2025, 1, 6);
        boolean isWorkingDay1 = settlementService.isWorkingDay(monday);
        log.info("{}是否为工作日：{}", monday, isWorkingDay1);
        assert isWorkingDay1;
        
        // 测试周六（非工作日）
        LocalDate saturday = LocalDate.of(2025, 1, 4);
        boolean isWorkingDay2 = settlementService.isWorkingDay(saturday);
        log.info("{}是否为工作日：{}", saturday, isWorkingDay2);
        assert !isWorkingDay2;
        
        // 测试周日（非工作日）
        LocalDate sunday = LocalDate.of(2025, 1, 5);
        boolean isWorkingDay3 = settlementService.isWorkingDay(sunday);
        log.info("{}是否为工作日：{}", sunday, isWorkingDay3);
        assert !isWorkingDay3;
    }

    /**
     * 测试分页查询回款信息
     */
    @Test
    public void testPageQuery() {
        log.info("测试分页查询回款信息");
        
        SettlementQueryDTO queryDTO = new SettlementQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        PageInfo<SettlementInfoVO> pageInfo = settlementService.pageQuery(queryDTO);
        log.info("查询结果：总数={}, 当前页={}, 页大小={}", 
                pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());
        
        if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
            SettlementInfoVO first = pageInfo.getList().get(0);
            log.info("第一条记录：订单号={}, 商家={}, 回款状态={}", 
                    first.getOrderNumber(), first.getSellerName(), first.getSettlementStatusDesc());
        }
    }

    /**
     * 测试查询所有商家回款汇总
     */
    @Test
    public void testGetAllSummary() {
        log.info("测试查询所有商家回款汇总");
        
        List<SettlementSummaryVO> summaryList = settlementService.getAllSummary();
        log.info("商家数量：{}", summaryList.size());
        
        for (SettlementSummaryVO summary : summaryList) {
            log.info("商家：{}, 总订单数：{}, 总金额：{}, 待回款订单：{}, 待回款金额：{}", 
                    summary.getSellerName(), 
                    summary.getTotalOrders(), 
                    summary.getTotalSettlementAmount(),
                    summary.getPendingOrders(),
                    summary.getPendingAmount());
        }
    }

    /**
     * 测试查询待回款订单
     */
    @Test
    public void testGetPendingSettlement() {
        log.info("测试查询待回款订单");
        
        List<SettlementInfoVO> pendingList = settlementService.getPendingSettlement();
        log.info("待回款订单数量：{}", pendingList.size());
        
        for (SettlementInfoVO settlement : pendingList) {
            log.info("待回款订单：订单号={}, 商家={}, 回款日期={}, 金额={}", 
                    settlement.getOrderNumber(), 
                    settlement.getSellerName(),
                    settlement.getSettlementDate(),
                    settlement.getSettlementAmount());
        }
    }

    /**
     * 测试更新回款状态
     */
    @Test
    public void testUpdateSettlementStatus() {
        log.info("测试更新回款状态");
        
        try {
            settlementService.updateSettlementStatus();
            log.info("回款状态更新成功");
        } catch (Exception e) {
            log.error("回款状态更新失败", e);
        }
    }

    /**
     * 测试为订单创建回款信息
     */
    @Test
    public void testCreateSettlementInfo() {
        log.info("测试为订单创建回款信息");
        
        // 这里需要一个真实的已支付订单ID进行测试
        // Long orderId = 1L; // 替换为实际的订单ID
        
        // try {
        //     settlementService.createSettlementInfo(orderId);
        //     log.info("回款信息创建成功，订单ID：{}", orderId);
        // } catch (Exception e) {
        //     log.error("回款信息创建失败", e);
        // }
        
        log.info("跳过创建回款信息测试（需要真实订单数据）");
    }
}
