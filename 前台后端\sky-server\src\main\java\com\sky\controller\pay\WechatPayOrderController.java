package com.sky.controller.pay;

import com.sky.dto.WechatPayOrderQueryDTO;
import com.sky.result.Result;
import com.sky.service.WechatPayOrderService;
import com.sky.vo.WechatPayOrderQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 微信支付订单查询控制器
 */
@RestController
@RequestMapping("/pay/wechat/order")
@Api(tags = "微信支付订单相关接口")
@Slf4j
public class WechatPayOrderController {

    @Autowired
    private WechatPayOrderService wechatPayOrderService;

    /**
     * 查询订单
     */
    @PostMapping("/query")
    @ApiOperation("查询订单")
    public Result<WechatPayOrderQueryVO> queryOrder(@RequestBody Map<String, String> requestMap) {
        log.info("查询订单: {}", requestMap);
        
        // 从请求中获取商户订单号
        String outTradeNo = requestMap.get("out_trade_no");
        
        if (outTradeNo == null || outTradeNo.isEmpty()) {
            log.error("商户订单号为空");
            return Result.error("商户订单号不能为空");
        }
        
        log.info("查询订单号: {}", outTradeNo);
        
        // 构建查询DTO
        WechatPayOrderQueryDTO queryDTO = new WechatPayOrderQueryDTO();
        queryDTO.setOutTradeNo(outTradeNo);
        
        log.info("查询订单DTO: {}", queryDTO);
        WechatPayOrderQueryVO orderInfo = wechatPayOrderService.queryOrder(queryDTO);
        return Result.success(orderInfo);
    }

    /**
     * 根据微信支付订单号查询订单
     */
    @GetMapping("/query/transaction/{transactionId}")
    @ApiOperation("根据微信支付订单号查询订单")
    public Result<WechatPayOrderQueryVO> queryOrderByTransactionId(@PathVariable String transactionId) {
        log.info("根据微信支付订单号查询订单: {}", transactionId);
        WechatPayOrderQueryVO orderInfo = wechatPayOrderService.queryOrderByTransactionId(transactionId);
        return Result.success(orderInfo);
    }

    /**
     * 根据商户订单号查询订单
     */
    @GetMapping("/query/out-trade-no/{outTradeNo}")
    @ApiOperation("根据商户订单号查询订单")
    public Result<WechatPayOrderQueryVO> queryOrderByOutTradeNo(@PathVariable String outTradeNo) {
        log.info("根据商户订单号查询订单: {}", outTradeNo);
        WechatPayOrderQueryVO orderInfo = wechatPayOrderService.queryOrderByOutTradeNo(outTradeNo);
        return Result.success(orderInfo);
    }
} 