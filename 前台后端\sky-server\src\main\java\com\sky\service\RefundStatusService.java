package com.sky.service;

import com.sky.vo.RefundApplicationVO;
import com.sky.vo.WechatRefundQueryVO;

/**
 * 退款状态查询服务接口
 */
public interface RefundStatusService {

    /**
     * 查询退款状态并同步微信退款状态
     * @param refundNo 退款申请单号
     * @return 退款申请VO
     */
    RefundApplicationVO queryAndSyncRefundStatus(String refundNo);

    /**
     * 根据退款申请ID查询退款状态
     * @param refundApplicationId 退款申请ID
     * @return 退款申请VO
     */
    RefundApplicationVO queryRefundStatus(Long refundApplicationId);

    /**
     * 同步微信退款状态
     * @param refundNo 退款申请单号
     * @return 是否同步成功
     */
    boolean syncWechatRefundStatus(String refundNo);

    /**
     * 处理微信退款回调通知
     * @param refundNo 退款申请单号
     * @param wechatRefundStatus 微信退款状态
     * @param actualRefundAmount 实际退款金额（单位：分）
     * @return 处理结果
     */
    boolean handleWechatRefundNotify(String refundNo, String wechatRefundStatus, Integer actualRefundAmount);

    /**
     * 批量同步退款状态
     * @return 同步成功的数量
     */
    int batchSyncRefundStatus();

    /**
     * 查询微信退款详情
     * @param refundNo 退款申请单号
     * @return 微信退款查询结果
     */
    WechatRefundQueryVO queryWechatRefundDetail(String refundNo);

    /**
     * 更新本地退款状态
     * @param refundNo 退款申请单号
     * @param applicationStatus 申请状态
     * @param actualRefundAmount 实际退款金额
     * @return 是否更新成功
     */
    boolean updateLocalRefundStatus(String refundNo, Integer applicationStatus, java.math.BigDecimal actualRefundAmount);
}
