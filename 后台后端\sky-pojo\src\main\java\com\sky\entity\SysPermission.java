package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;

import java.io.Serializable;
import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysPermission implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    private Long id;
    private Long parentId;
    private String permName;
    private String permCode;
    private String permType;
    private String path;
    private String component;
    private String method;
    private String icon;
    private Integer orderNum;
    private Boolean isHidden;
    private String status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
