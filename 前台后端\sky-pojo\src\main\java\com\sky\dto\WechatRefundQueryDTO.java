package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 微信退款查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatRefundQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信支付退款订单号（与商户退款单号二选一）
     */
    private String refundId;

    /**
     * 商户退款单号（与微信支付退款订单号二选一）
     */
    private String outRefundNo;

    /**
     * 商户号（直连模式必填）
     */
    private String mchid;

    /**
     * 子商户号（机构模式必填）
     */
    private String subMchid;

    /**
     * 机构商户号（机构模式必填）
     */
    private String spMchid;
}
