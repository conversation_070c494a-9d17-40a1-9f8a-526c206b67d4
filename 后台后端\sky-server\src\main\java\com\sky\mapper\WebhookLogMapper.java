package com.sky.mapper;

import com.sky.entity.WebhookLog;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Webhook日志Mapper
 */
@Mapper
public interface WebhookLogMapper {

    /**
     * 插入Webhook日志
     */
    @Insert("INSERT INTO webhook_log (event_type, tracking_number, carrier_code, request_body, " +
            "response_status, response_body, process_status, error_message, retry_count, " +
            "create_time, process_time) " +
            "VALUES (#{eventType}, #{trackingNumber}, #{carrierCode}, #{requestBody}, " +
            "#{responseStatus}, #{responseBody}, #{processStatus}, #{errorMessage}, #{retryCount}, " +
            "#{createTime}, #{processTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(WebhookLog webhookLog);

    /**
     * 根据ID查询
     */
    @Select("SELECT * FROM webhook_log WHERE id = #{id}")
    WebhookLog findById(Long id);

    /**
     * 根据物流单号查询
     */
    @Select("SELECT * FROM webhook_log WHERE tracking_number = #{trackingNumber} ORDER BY create_time DESC")
    List<WebhookLog> findByTrackingNumber(String trackingNumber);

    /**
     * 查询失败的日志
     */
    @Select("SELECT * FROM webhook_log WHERE process_status = 'failed' AND retry_count < 3 " +
            "ORDER BY create_time ASC")
    List<WebhookLog> findFailedLogs();

    /**
     * 更新处理状态
     */
    @Update("UPDATE webhook_log SET process_status = #{processStatus}, error_message = #{errorMessage}, " +
            "retry_count = #{retryCount}, process_time = #{processTime} WHERE id = #{id}")
    void updateProcessStatus(@Param("id") Long id, @Param("processStatus") String processStatus,
                            @Param("errorMessage") String errorMessage, @Param("retryCount") Integer retryCount,
                            @Param("processTime") LocalDateTime processTime);

    /**
     * 删除过期日志
     */
    @Delete("DELETE FROM webhook_log WHERE create_time < #{expireTime}")
    int deleteExpiredLogs(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 统计日志数量
     */
    @Select("SELECT COUNT(*) FROM webhook_log WHERE process_status = #{processStatus}")
    int countByProcessStatus(String processStatus);
}
