package com.sky.service.impl;

import com.sky.constant.MessageConstant;
import com.sky.dto.MerchantDTO;
import com.sky.exception.AccountNotFoundException;
import com.sky.mapper.MerChantMapper;
import com.sky.service.MerChantService;
import net.bytebuddy.implementation.bytecode.Throw;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
public class MerChantServiceImpl  implements MerChantService {

    @Autowired
    MerChantMapper merChantMapper;

    @Override
    public void MerChantRegister(MerchantDTO merchantDTO) {
        String mobile = merchantDTO.getMobile();
        MerchantDTO merchantDTO1 = merChantMapper.selectBymobile(mobile);
        if (merchantDTO1 != null) {
            throw new AccountNotFoundException("该手机号已存在");
        }
        MerchantDTO merchantDTO2 = new MerchantDTO();
        BeanUtils.copyProperties(merchantDTO, merchantDTO2);
//        String format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//        LocalDateTime now = LocalDateTime.now();
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        String formattedDateTime = now.format(formatter);
        merchantDTO2.setCreateTime(LocalDateTime.now());
        merchantDTO2.setUpdateTime(LocalDateTime.now());
        merchantDTO2.setStatus(0);
        merChantMapper.save(merchantDTO2);
    }

    @Override
    public List<MerchantDTO> MerChantreview() {
        String status = "0";
        List<MerchantDTO> merchantDTOList = merChantMapper.getMerchantDto(status);
        return merchantDTOList;

    }

    @Override
    public void MerChantchange(Long ShopId) {
        String status = "1";
        merChantMapper.MerChantchange(status,ShopId);
    }

    @Override
    public List<MerchantDTO> MerChantpass() {
        String status = "1";
        List<MerchantDTO> merchantDTOList =merChantMapper.MerChantpass(status);
        return merchantDTOList;
    }

    @Override
    public void MerChantDelete(Long ShopId) {
        merChantMapper.MerChantdelate(ShopId);

    }


}
