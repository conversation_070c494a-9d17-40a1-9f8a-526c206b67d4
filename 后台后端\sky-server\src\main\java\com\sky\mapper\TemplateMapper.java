package com.sky.mapper;

import com.sky.entity.Template;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;

import java.util.List;

@Mapper
public interface TemplateMapper {
    @Select("SELECT * FROM template_list")
    List<Template> findAll();

    @Insert("INSERT INTO template_list (template_name, shipping_warehouse_name, return_warehouse_name, type, created_at) " +
            "VALUES (#{templateName}, #{shippingWarehouseName}, #{returnWarehouseName}, #{type}, NOW())")
    void insert(Template template);
}