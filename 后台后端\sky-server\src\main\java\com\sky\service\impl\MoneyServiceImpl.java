package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.dto.DataRecordsPageDTO;
import com.sky.dto.FundFlowPageDTO;
import com.sky.entity.DataRecords;
import com.sky.entity.FundFlow;
import com.sky.vo.FundFlowVO;
import com.sky.entity.MessageNotifications;
import com.sky.exception.BaseException;
import com.sky.mapper.MoneyMapper;
import com.sky.result.PageResult;
import com.sky.service.MoneyService;
import com.sky.vo.BusinessDataVO;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class MoneyServiceImpl implements MoneyService {
    @Autowired
    private MoneyMapper moneyMapper;

    @Override
    public PageResult pageQuery(DataRecordsPageDTO dataRecordsPageDTO) {
        PageHelper.startPage(dataRecordsPageDTO.getPage(),dataRecordsPageDTO.getPageSize());
        //下一条sql进行分页，自动加入limit关键字分页
        Page<MessageNotifications> page = moneyMapper.pageQuery(dataRecordsPageDTO);
        return new PageResult(page.getTotal(), page.getResult());
    }

    //可提现金额
    @Override
    public BigDecimal getByAmount(Long id) {

        //先获取可提现状态数据
//        List<DataRecords> list= moneyMapper.getStatus(id);

        //根据查询到的数据，获取可提现金额
        BigDecimal amount = moneyMapper.getByAmount(id);
        return amount;
    }

    /**
     *流水明细分类分页查询
     * @param fundFlowPageDTO
     * @return
     */
    @Override
    public PageResult fundFlowpageQuery(FundFlowPageDTO fundFlowPageDTO) {
        PageHelper.startPage(fundFlowPageDTO.getPage(),fundFlowPageDTO.getPageSize());
        //下一条sql进行分页，自动加入limit关键字分页
        Page<FundFlow> page = moneyMapper.fundFlowPageQuery(fundFlowPageDTO);
        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 导出流水数据报表
     * @param response
     */
    @Override
    public void exportFlowData(LocalDate startDate, LocalDate endDate, HttpServletResponse response) {
        // 参数校验
        if (startDate == null || endDate == null) {
            throw new BaseException("时间参数不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new BaseException("开始时间不能晚于结束时间");
        }
        // 查询数据
        List<FundFlowVO> flowData = moneyMapper.listFundFlowData(
                LocalDateTime.of(startDate, LocalTime.MIN),
                LocalDateTime.of(endDate, LocalTime.MAX)
        );
        // 导出Excel
        try (InputStream in = this.getClass().getClassLoader().getResourceAsStream("template/资金流水报表模板.xlsx")) {
            if (in == null) {
                throw new RuntimeException("模板文件不存在");
            }

            XSSFWorkbook excel = new XSSFWorkbook(in);
            XSSFSheet sheet = excel.getSheet("Sheet1");

            // 填充标题时间范围
            sheet.getRow(0).getCell(0).setCellValue(
                    String.format("资金流水报表（%s 至 %s）", startDate, endDate)
            );

            // 填充数据行
            int rowNum = 2; // 假设数据从第三行开始
            for (FundFlowVO item : flowData) {
                XSSFRow row = sheet.createRow(rowNum++);
                fillFundFlowRow(row, item);
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode(
                    String.format("资金流水_%s_%s.xlsx", startDate, endDate),
                    "UTF-8"
            ).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + fileName);

            try (ServletOutputStream out = response.getOutputStream()) {
                excel.write(out);
            }
        } catch (IOException e) {

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private void fillFundFlowRow(XSSFRow row, FundFlowVO item) {
        int col = 0;

        // 操作时间
        row.createCell(col++).setCellValue(
                item.getOperationTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );

        // 操作类型
        row.createCell(col++).setCellValue(item.getOperationType());

        // 流水号
        row.createCell(col++).setCellValue(item.getSerialNumber());

        // 关联订单号
        row.createCell(col++).setCellValue(item.getOrderNumber());

        // 资金类型
        row.createCell(col++).setCellValue(item.getFundType());

        // 金额相关字段（使用BigDecimal的字符串表示避免科学计数法）
        row.createCell(col++).setCellValue(item.getOrderAmountPercent().toPlainString());
        row.createCell(col++).setCellValue(item.getPlatformFundPercent().toPlainString());
        row.createCell(col++).setCellValue(item.getFundFlowPercent().toPlainString());
        row.createCell(col++).setCellValue(item.getBalancePercent().toPlainString());

        // 创建时间
        row.createCell(col).setCellValue(
                item.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );
    }

}
