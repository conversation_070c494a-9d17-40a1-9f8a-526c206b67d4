package com.sky.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sky.constant.MessageConstant;
import com.sky.dto.CategoryCreateDTO;
import com.sky.entity.BusinessException;
import com.sky.entity.Category;
import com.sky.exception.AccountNotFoundException;
import com.sky.mapper.CategoryMapper;
import com.sky.service.CategoryService;
import com.sky.vo.CategoryTreeVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 分类业务层
 */
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper,Category> implements CategoryService {
    private final CategoryMapper categoryMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCategory(CategoryCreateDTO dto) {
        // DTO转换实体
        Category category = new Category();
        category.setName(dto.getName());
        category.setParentId(dto.getParentId());
        category.setSortOrder(dto.getSortOrder());
        List<Category> list = lambdaQuery().eq(Category::getParentId, dto.getParentId()).list();
        boolean exists = list.stream()
                .anyMatch(category1 -> category1.getName().equals(dto.getName()));
        if(exists){
            throw new AccountNotFoundException("同一分类下不能存在同名类目");
        }

        // 层级计算逻辑
        if (dto.getParentId() != null && dto.getParentId() != 0L) {
            Category parent = categoryMapper.findById(dto.getParentId());
            if (parent == null) {
                throw new AccountNotFoundException("父分类不存在");
            }
            if (parent.getLevel() >= 2) {
                throw new AccountNotFoundException("分类层级不能超过3级");
            }
            category.setLevel(parent.getLevel() + 1);
        } else {
            category.setLevel(0);
        }

        // 持久化操作
        categoryMapper.insert1(category);
        if(stringRedisTemplate.hasKey("三级类目")){
            stringRedisTemplate.delete("三级类目");
        }
        return category.getId();
    }

    @Override
    public List<CategoryTreeVO> getFullCategoryTree() throws JsonProcessingException {
        String tree = stringRedisTemplate.opsForValue().get("三级类目");

        if (tree != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            List<CategoryTreeVO> categoryTreeVOS = objectMapper.readValue(tree, new TypeReference<List<CategoryTreeVO>>() {
            });
            return categoryTreeVOS;
        }

        // 1. 查询所有分类
        List<Category> allCategories = categoryMapper.selectAllWithType();

        // 2. 过滤出一级分类（parent_id 为 NULL 或 0）
        List<CategoryTreeVO> rootCategories = allCategories.stream()
                .filter(c -> c.getParentId() == null || c.getParentId() == 0)
                .map(c -> new CategoryTreeVO(c.getId(), c.getName(), c.getLevel(), new ArrayList<>()))
                .collect(Collectors.toList());

        // 3. 递归构建二级和三级分类
        rootCategories.forEach(root -> {
            // 构建二级分类
            List<CategoryTreeVO> secondLevel = allCategories.stream()
                    .filter(c -> c.getParentId() != null && c.getParentId().equals(root.getId()))
                    .map(c -> new CategoryTreeVO(c.getId(), c.getName(), c.getLevel(), new ArrayList<>()))
                    .collect(Collectors.toList());

            // 构建三级分类
            secondLevel.forEach(second -> {
                List<CategoryTreeVO> thirdLevel = allCategories.stream()
                        .filter(c -> c.getParentId() != null && c.getParentId().equals(second.getId()))
                        .map(c -> new CategoryTreeVO(c.getId(), c.getName(), c.getLevel(), null))
                        .collect(Collectors.toList());
                second.setChildren(thirdLevel);
            });

            root.setChildren(secondLevel);
        });
        ObjectMapper objectMapper = new ObjectMapper();
        String s = objectMapper.writeValueAsString(rootCategories);
        stringRedisTemplate.opsForValue().set("三级类目",s,30, TimeUnit.MINUTES);
        return rootCategories;
    }

    @Override
    public void deleteCategoryWithSubtree(Long id) throws BusinessException {
//        Long count = lambdaQuery().eq(Category::getParentId, id).count();
//        if(count > 0){}
        Category byId = getById(id);
        if(byId == null){
            throw new AccountNotFoundException("该id下暂无分类");
        }
        removeById(id);
        if(stringRedisTemplate.hasKey("三级类目")){
            stringRedisTemplate.delete("三级类目");
        }



    }

    @Override
    public CategoryTreeVO getCategoryTree(Long rootId) {
        return null;
    }

    private List<CategoryTreeVO> buildCategoryTree(List<Category> categories) {
        // 使用Map加速查找（时间复杂度O(n)）
        Map<Long, CategoryTreeVO> nodeMap = new HashMap<>();

        // 第一轮转换：创建所有节点
        for (Category category : categories) {
            CategoryTreeVO vo = new CategoryTreeVO();
            vo.setId(category.getId());
            vo.setName(category.getName());
            vo.setLevel(category.getLevel());
            vo.setChildren(new ArrayList<>());
            nodeMap.put(category.getId(), vo);
        }

        // 第二轮构建：建立父子关系
        List<CategoryTreeVO> rootNodes = new ArrayList<>();
        for (CategoryTreeVO vo : nodeMap.values()) {
            Category category = categories.stream()
                    .filter(c -> c.getId().equals(vo.getId()))
                    .findFirst()
                    .orElse(null);

            if (category != null) {
                Long parentId = category.getParentId();
                if (parentId == null || parentId == 0L) {
                    rootNodes.add(vo);
                } else {
                    CategoryTreeVO parent = nodeMap.get(parentId);
                    if (parent != null) {
                        parent.getChildren().add(vo);
                    }
                }
            }
        }

        // 第三轮排序：每层按sortOrder排序
//        sortTree(rootNodes);
        return rootNodes;
    }

//    private void sortTree(List<CategoryTreeVO> nodes) {
//        nodes.sort(Comparator.comparingInt(CategoryTreeVO::getSortOrder));
//        nodes.forEach(node -> {
//            if (!node.getChildren().isEmpty()) {
//                sortTree(node.getChildren());
//            }
//        });
//    }
}