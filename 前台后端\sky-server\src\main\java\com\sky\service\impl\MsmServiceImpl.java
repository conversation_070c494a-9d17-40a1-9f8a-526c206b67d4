package com.sky.service.impl;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;

import com.sky.Utils.Sample;
import com.sky.Utils.SmsSendEmail;
import com.sky.service.MsmService;
import io.netty.util.internal.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.filter.CommonsRequestLoggingFilter;


import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import static com.aliyun.teautil.Common.toJSONString;

import java.util.Map;

import static com.aliyun.teautil.Common.toJSONString;

@Service
public class MsmServiceImpl implements MsmService {
    @Override
    public Boolean sendMessage(String address, Integer code) throws Exception {
        String[] args =new String[5];
        String string = Integer.toString(code);
        SmsSendEmail.SendEmail(args,string,address);
        return true;
    }
//    @Override
//    public Boolean sendMessage(String phone,Integer code) {
//        Client client = null;
//        try {
//            client = Sample.createClient();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//        // 构造请求对象，请填入请求参数值
//        SendSmsRequest sendSmsRequest = new SendSmsRequest()
//                .setPhoneNumbers(phone)
//                .setSignName("sharewharf验证")//签名名字
//                .setTemplateCode("SMS_479000396")
//                .setTemplateParam( "{\"code\":\"" + code + "\"}");
//
//        // 获取响应对象
//        SendSmsResponse sendSmsResponse = null;
//        try {
//            sendSmsResponse = client.sendSms(sendSmsRequest);
//            System.out.println(sendSmsResponse.getBody());
//            // 响应包含服务端响应的 body 和 headers
//            System.out.println(toJSONString(sendSmsResponse));
//            Integer statusCode = sendSmsResponse.getStatusCode();
//            if(statusCode.equals(200)) {
//                return true;
//            }
//            return false;
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//    }
//
//    public static Client createClient() throws Exception {
//        Config config = new Config()
//                // 配置 AccessKey ID，请确保代码运行环境设置了环境变量。
//                .setAccessKeyId("LTAI5tEBPiykbMVN68MsMVTi")
//                // 配置 AccessKey Secret，请确保代码运行环境设置了环境变量。
//                .setAccessKeySecret("******************************");
//        // System.getenv()方法表示获取系统环境变量，请配置环境变量后，在此填入环境变量名称，不要直接填入AccessKey信息。
//
//        // 配置 Endpoint
//        config.endpoint = "dysmsapi.aliyuncs.com";
//
//        return new Client(config);
//    }
}
