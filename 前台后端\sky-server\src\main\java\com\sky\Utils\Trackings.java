package com.sky.Utils;

import java.util.List;
import com.tracking51.Tracking51;
import com.tracking51.exception.Tracking51Exception;
import com.tracking51.model.Tracking51Response;
import com.tracking51.model.courier.Courier;
import com.tracking51.model.tracking.*;

import java.io.IOException;
import java.util.List;
public class Trackings {
    public void createTracking(String TrackingNumber){
        try {
            String apiKey = "eaw9fh28-4z0b-jdfj-7i2w-w9eyikpp2g15";
            Tracking51 tracking51 = new Tracking51(apiKey);
            CreateTrackingParams createTrackingParams = new CreateTrackingParams();
            createTrackingParams.setTrackingNumber(TrackingNumber);
            createTrackingParams.setCourierCode("usps");
            Tracking51Response<Tracking> result = tracking51.trackings.CreateTracking(createTrackingParams);
            System.out.println(result.getMeta().getCode());
            if(result.getData() != null){
                Tracking trackings = result.getData();
                System.out.println(trackings);
                System.out.println(trackings.getTrackingNumber());
            }
        } catch (Tracking51Exception e) {
            System.err.println("error：" + e.getMessage());
        } catch (IOException e) {
            System.err.println("error：" + e.getMessage());
        }

    }
    public void getCouriers(){
        try {
            String apiKey = "eaw9fh28-4z0b-jdfj-7i2w-w9eyikpp2g15";
            Tracking51 tracking51 = new Tracking51(apiKey);
            Tracking51Response<List<Courier>> result = tracking51.couriers.getAllCouriers();
            System.out.println(result.getMeta().getCode());
            List<Courier> couriers = result.getData();
            for (Courier courier : couriers) {
                String courierName = courier.getCourierName();
                String courierCode = courier.getCourierCode();
                System.out.println(courierName+"---"+courierCode);
            }
        } catch (Tracking51Exception e) {
            System.err.println("error：" + e.getMessage());
        } catch (IOException e) {
            System.err.println("error：" + e.getMessage());
        }

    }
    public void createTrackings(String TrackingNumbers){
        try {
            String apiKey = "eaw9fh28-4z0b-jdfj-7i2w-w9eyikpp2g15";
            Tracking51 tracking51 = new Tracking51(apiKey);

            GetTrackingResultsParams trackingParams = new GetTrackingResultsParams();
            trackingParams.setTrackingNumbers("92612903029511573030094537,92612903029511573030094531");
            trackingParams.setCourierCode("usps");
            trackingParams.setCreatedDateMin("2023-08-23T06:00:00+00:00");
            trackingParams.setCreatedDateMax("2023-09-18T06:00:00+00:00");
            Tracking51Response<GetResults> result = tracking51.trackings.GetTrackingResults(trackingParams);

            System.out.println(result.getMeta().getCode());
            System.out.println(result.getMeta().getMessage());

            GetResults getResults = result.getData();

            if(getResults != null){
                for (Tracking tracking : getResults.getSuccess()) {
                    String id = tracking.getId();
                    String trackingNumber = tracking.getTrackingNumber();
                    String courierCode = tracking.getCourierCode();
                    System.out.println("id："+id);
                    System.out.println("trackingNumber："+trackingNumber);
                    System.out.println("courierCode："+courierCode);
                }

                for (RejectedItem rejectedItem : getResults.getRejected()) {
                    String trackingNumber = rejectedItem.getTrackingNumber();
                    int rejectedCode = rejectedItem.getRejectedCode();
                    String rejectedMessage = rejectedItem.getRejectedMessage();
                    System.out.println("trackingNumber："+trackingNumber);
                    System.out.println("rejectedCode："+rejectedCode);
                    System.out.println("rejectedMessage："+rejectedMessage);
                }
            }

        } catch (Tracking51Exception e) {
            System.err.println("error：" + e.getMessage());
        } catch (IOException e) {
            System.err.println("error：" + e.getMessage());
        }


    }

}
