package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款审核DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundApprovalDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 退款申请ID
     */
    private Long refundApplicationId;

    /**
     * 审核状态：1-通过，2-拒绝
     */
    private Integer approvalResult;

    /**
     * 审核备注
     */
    private String approvalRemark;

    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 退款方式
     */
    private Integer refundMethod;

    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;
}
