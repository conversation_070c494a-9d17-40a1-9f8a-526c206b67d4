package com.sky.service.impl;

import com.sky.constant.MessageConstant;
import com.sky.context.BaseContext;
import com.sky.entity.*;
import com.sky.exception.BaseException;
import com.sky.mapper.OrderDetailIntegrationMapper;
import com.sky.mapper.OrderDetailMapper;
import com.sky.mapper.OrdersMapper;
import com.sky.service.OrderDetailIntegrationService;
import com.sky.service.Track17ApiService;
import com.sky.vo.OrderDetailIntegrationVO;
import com.sky.dto.track17.Track17RealtimeRequest;
import com.sky.vo.Track17Response;
import com.sky.dto.track17.Track17RealtimeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单详情集成服务实现类
 */
@Service
@Slf4j
public class OrderDetailIntegrationServiceImpl implements OrderDetailIntegrationService {

    @Autowired
    private OrderDetailIntegrationMapper orderDetailIntegrationMapper;

    @Autowired
    private OrdersMapper ordersMapper;

    @Autowired
    private OrderDetailMapper orderDetailMapper;

    @Autowired
    private Track17ApiService track17ApiService;

    @Override
    public OrderDetailIntegrationVO getOrderDetailIntegration(Long orderId, Long userId) {
        log.info("获取订单完整详情，订单ID：{}，用户ID：{}", orderId, userId);

        // 1. 查询基础订单信息
        Orders order = ordersMapper.selectById(orderId);
        if (order == null) {
            throw new BaseException("订单不存在");
        }

        // 验证订单归属
        if (!order.getBuyerId().equals(userId)) {
            throw new BaseException("无权限查看此订单");
        }

        // 2. 构建返回对象
        OrderDetailIntegrationVO result = OrderDetailIntegrationVO.builder()
                .orderId(order.getId())
                .orderNumber(order.getNumber())
                .orderStatus(order.getStatus())
                .orderAmount(order.getAmount())
                .orderTime(order.getOrderTime())
                .userId(order.getBuyerId())
                .remark(order.getOrderRemark())
                .paymentTime(order.getPayTime())
                .shipTime(order.getShipTime())
                .build();

        // 3. 查询订单商品详情
        List<OrderDetail> orderDetails = orderDetailMapper.getByOrderId(orderId);
        result.setOrderDetails(orderDetails);

        // 4. 查询物流信息
        OrderLogisticsTracking logisticsTracking = orderDetailIntegrationMapper.getLogisticsTrackingByOrderId(orderId);

        // 如果关联表没有数据，从orders表中获取物流信息
        if (logisticsTracking == null && order.getTrackingNumber() != null) {
            logisticsTracking = OrderLogisticsTracking.builder()
                    .orderId(orderId)
                    .orderNumber(order.getNumber())
                    .trackingNumber(order.getTrackingNumber())
                    .carrierCode(order.getCourierCode())
                    .carrierName(order.getCourierName())
                    .currentStatus("InTransit")
                    .currentStatusDesc("运输途中")
                    .shipTime(order.getShipTime())
                    .lastUpdateTime(order.getUpdateTime())
                    .build();
        }

        result.setLogisticsTracking(logisticsTracking);

        if (logisticsTracking != null) {
            // 查询物流轨迹
            List<LogisticsTraceDetail> traces = orderDetailIntegrationMapper
                    .getLogisticsTracesByTrackingId(logisticsTracking.getId());
            result.setLogisticsTraces(traces);
            
            result.setEstimatedDeliveryTime(logisticsTracking.getEstimatedDeliveryTime());
            result.setActualDeliveryTime(logisticsTracking.getActualDeliveryTime());
            result.setIsShipped(true);
            result.setIsDelivered("Delivered".equals(logisticsTracking.getCurrentStatus()));
            result.setLogisticsStatusDesc(getLogisticsStatusDescription(logisticsTracking.getCurrentStatus()));
        } else {
            result.setIsShipped(false);
            result.setIsDelivered(false);
            result.setLogisticsStatusDesc("暂无物流信息");
        }

        // 5. 查询地址信息
        OrderAddressInfo deliveryAddress = orderDetailIntegrationMapper
                .getOrderAddressByOrderIdAndType(orderId, 1); // 收货地址
        OrderAddressInfo senderAddress = orderDetailIntegrationMapper
                .getOrderAddressByOrderIdAndType(orderId, 2); // 发货地址

        // 如果没有地址信息，从user_address表获取
        if (deliveryAddress == null && order.getAddressId() != null) {
            deliveryAddress = orderDetailIntegrationMapper.getUserAddressById(order.getAddressId());
            if (deliveryAddress != null) {
                deliveryAddress.setOrderId(orderId);
                deliveryAddress.setAddressType(1); // 收货地址
            }
        }

        result.setDeliveryAddress(deliveryAddress);
        result.setSenderAddress(senderAddress);

        // 6. 查询支付信息
        OrderPaymentDetail paymentDetail = orderDetailIntegrationMapper.getPaymentDetailByOrderId(orderId);

        // 如果没有支付详情，从orders表中获取支付信息
        if (paymentDetail == null) {
            paymentDetail = OrderPaymentDetail.builder()
                    .orderId(orderId)
                    .orderNumber(order.getNumber())
                    .paymentMethod(order.getPayMethod() != null ? getPaymentMethodCode(order.getPayMethod()) : "unknown")
                    .paymentAmount(order.getAmount())
                    .currency("CNY")
                    .paymentStatus(order.getStatus() >= 2 ? "success" : "pending")
                    .paymentTime(order.getPayTime())
                    .paymentDesc(getPaymentMethodName(order.getPayMethod()))
                    .build();
        }

        result.setPaymentDetail(paymentDetail);

        if (paymentDetail != null) {
            result.setIsPaid("success".equals(paymentDetail.getPaymentStatus()));
            result.setPaymentStatusDesc(paymentDetail.getPaymentStatusName());
        } else {
            result.setIsPaid(false);
            result.setPaymentStatusDesc("未支付");
        }

        // 7. 设置扩展信息
        result.setProgressPercentage(result.calculateProgressPercentage());
        result.setCurrentStageDesc(result.getCurrentStageDesc());
        result.setCanCancel(canPerformAction(orderId, userId, "cancel"));
        result.setCanRefund(canPerformAction(orderId, userId, "refund"));
        result.setCanConfirmReceipt(canPerformAction(orderId, userId, "confirm"));
        result.setCustomerServiceContact("400-123-4567");

        // 8. 设置下一步操作提示
        result.setNextActionTip(getNextActionTip(order.getStatus(), logisticsTracking));

        log.info("订单详情查询完成，订单号：{}", result.getOrderNumber());
        return result;
    }

    @Override
    public OrderDetailIntegrationVO getOrderDetailIntegrationByNumber(String orderNumber, Long userId) {
        log.info("根据订单号获取订单详情，订单号：{}，用户ID：{}", orderNumber, userId);
        
        Orders order = ordersMapper.getByNumber(orderNumber);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        
        return getOrderDetailIntegration(order.getId(), userId);
    }

    @Override
    public List<OrderDetailIntegrationVO> getUserOrderList(Long userId, Integer status, Integer pageNum, Integer pageSize) {
        log.info("获取用户订单列表，用户ID：{}，状态：{}，页码：{}，每页大小：{}", userId, status, pageNum, pageSize);
        
        // 这里简化实现，实际应该使用分页查询
        List<Orders> orders = ordersMapper.getByBuyerId(userId);
        
        if (status != null) {
            orders = orders.stream()
                    .filter(order -> order.getStatus().equals(status))
                    .collect(Collectors.toList());
        }
        
        return orders.stream()
                .map(order -> getOrderDetailIntegration(order.getId(), userId))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void updateOrderLogistics(Long orderId, String trackingNumber, String carrierCode, String carrierName) {
        log.info("更新订单物流信息，订单ID：{}，物流单号：{}", orderId, trackingNumber);

        Orders order = ordersMapper.selectById(orderId);
        if (order == null) {
            throw new BaseException("订单不存在");
        }

        // 查询是否已存在物流记录
        OrderLogisticsTracking existing = orderDetailIntegrationMapper.getLogisticsTrackingByOrderId(orderId);

        if (existing == null) {
            // 创建新的物流跟踪记录
            OrderLogisticsTracking tracking = OrderLogisticsTracking.builder()
                    .orderId(orderId)
                    .orderNumber(order.getNumber())
                    .trackingNumber(trackingNumber)
                    .carrierCode(carrierCode)
                    .carrierName(carrierName)
                    .currentStatus("InfoReceived")
                    .currentStatusDesc("物流信息已录入")
                    .shipTime(LocalDateTime.now())
                    .lastUpdateTime(LocalDateTime.now())
                    .build();

            orderDetailIntegrationMapper.insertLogisticsTracking(tracking);

            // 插入初始轨迹记录
            LogisticsTraceDetail initialTrace = LogisticsTraceDetail.builder()
                    .trackingId(tracking.getId())
                    .trackingNumber(trackingNumber)
                    .traceTime(LocalDateTime.now())
                    .traceStatus("InfoReceived")
                    .traceDesc("商家已发货，物流信息已录入系统")
                    .sortOrder(1)
                    .build();

            orderDetailIntegrationMapper.insertLogisticsTrace(initialTrace);
        } else {
            // 更新现有记录
            existing.setTrackingNumber(trackingNumber);
            existing.setCarrierCode(carrierCode);
            existing.setCarrierName(carrierName);
            existing.setLastUpdateTime(LocalDateTime.now());

            orderDetailIntegrationMapper.updateLogisticsTracking(existing);
        }

        log.info("订单物流信息更新完成");
    }

    @Override
    public boolean syncLogisticsTrace(Long orderId) {
        log.info("同步订单物流轨迹，订单ID：{}", orderId);

        try {
            OrderLogisticsTracking tracking = orderDetailIntegrationMapper.getLogisticsTrackingByOrderId(orderId);
            if (tracking == null || tracking.getTrackingNumber() == null) {
                log.warn("订单{}没有物流信息，无法同步", orderId);
                return false;
            }

            // 调用17TRACK API获取实时物流信息
            List<LogisticsTraceDetail> realtimeTraces = getRealtimeLogisticsInfo(
                    tracking.getTrackingNumber(), tracking.getCarrierCode());

            if (realtimeTraces != null && !realtimeTraces.isEmpty()) {
                // 清除旧的轨迹记录（实际项目中可能需要更智能的合并策略）
                // 这里简化处理，直接插入新的轨迹
                for (LogisticsTraceDetail trace : realtimeTraces) {
                    trace.setTrackingId(tracking.getId());
                    orderDetailIntegrationMapper.insertLogisticsTrace(trace);
                }

                // 更新物流跟踪状态
                LogisticsTraceDetail latestTrace = realtimeTraces.get(0);
                tracking.setCurrentStatus(latestTrace.getTraceStatus());
                tracking.setCurrentStatusDesc(latestTrace.getTraceDesc());
                tracking.setLatestLocation(latestTrace.getTraceLocation());
                tracking.setLastUpdateTime(LocalDateTime.now());

                // 如果是已签收状态，设置实际送达时间
                if ("Delivered".equals(latestTrace.getTraceStatus())) {
                    tracking.setActualDeliveryTime(latestTrace.getTraceTime());
                }

                orderDetailIntegrationMapper.updateLogisticsTracking(tracking);

                log.info("订单{}物流轨迹同步成功", orderId);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("同步订单{}物流轨迹失败", orderId, e);
            return false;
        }
    }

    @Override
    public int batchSyncLogisticsTrace(List<Long> orderIds) {
        log.info("批量同步物流轨迹，订单数量：{}", orderIds.size());

        int successCount = 0;
        for (Long orderId : orderIds) {
            if (syncLogisticsTrace(orderId)) {
                successCount++;
            }
        }

        log.info("批量同步完成，成功：{}，失败：{}", successCount, orderIds.size() - successCount);
        return successCount;
    }

    @Override
    public Map<String, Object> getUserOrderStatistics(Long userId) {
        log.info("获取用户订单统计，用户ID：{}", userId);

        List<Orders> orders = ordersMapper.getByBuyerId(userId);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalOrders", orders.size());
        statistics.put("pendingPayment", orders.stream().mapToInt(o -> o.getStatus() == 1 ? 1 : 0).sum());
        statistics.put("paid", orders.stream().mapToInt(o -> o.getStatus() == 2 ? 1 : 0).sum());
        statistics.put("cancelled", orders.stream().mapToInt(o -> o.getStatus() == 3 ? 1 : 0).sum());
        statistics.put("shipped", orders.stream().mapToInt(o -> o.getStatus() == 4 ? 1 : 0).sum());
        statistics.put("completed", orders.stream().mapToInt(o -> o.getStatus() == 5 ? 1 : 0).sum());
        statistics.put("closed", orders.stream().mapToInt(o -> o.getStatus() == 6 ? 1 : 0).sum());

        return statistics;
    }

    @Override
    public List<LogisticsTraceDetail> getRecentLogisticsUpdates(Long userId, Integer limit) {
        log.info("获取用户最近物流更新，用户ID：{}，限制：{}", userId, limit);

        // 这里简化实现，实际应该在Mapper中实现
        List<Orders> orders = ordersMapper.getByBuyerId(userId);
        List<LogisticsTraceDetail> allTraces = new ArrayList<>();

        for (Orders order : orders) {
            if (order.getStatus() == 4 || order.getStatus() == 5) { // 已发货或已完成
                OrderLogisticsTracking tracking = orderDetailIntegrationMapper.getLogisticsTrackingByOrderId(order.getId());
                if (tracking != null) {
                    List<LogisticsTraceDetail> traces = orderDetailIntegrationMapper
                            .getLogisticsTracesByTrackingId(tracking.getId());
                    allTraces.addAll(traces);
                }
            }
        }

        return allTraces.stream()
                .sorted((a, b) -> b.getTraceTime().compareTo(a.getTraceTime()))
                .limit(limit != null ? limit : 10)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void confirmReceipt(Long orderId, Long userId) {
        log.info("确认收货，订单ID：{}，用户ID：{}", orderId, userId);

        Orders order = ordersMapper.selectById(orderId);
        if (order == null || !order.getBuyerId().equals(userId)) {
            throw new BaseException("订单不存在或无权限操作");
        }

        if (order.getStatus() != 4) {
            throw new BaseException("订单状态不允许确认收货");
        }

        // 更新订单状态为已完成
        order.setStatus(5);
        order.setCompleteTime(LocalDateTime.now());
        ordersMapper.updateById(order);

        // 更新物流信息
        OrderLogisticsTracking tracking = orderDetailIntegrationMapper.getLogisticsTrackingByOrderId(orderId);
        if (tracking != null) {
            tracking.setCurrentStatus("Delivered");
            tracking.setCurrentStatusDesc("用户已确认收货");
            tracking.setActualDeliveryTime(LocalDateTime.now());
            tracking.setLastUpdateTime(LocalDateTime.now());
            orderDetailIntegrationMapper.updateLogisticsTracking(tracking);

            // 添加确认收货轨迹
            LogisticsTraceDetail confirmTrace = LogisticsTraceDetail.builder()
                    .trackingId(tracking.getId())
                    .trackingNumber(tracking.getTrackingNumber())
                    .traceTime(LocalDateTime.now())
                    .traceStatus("Delivered")
                    .traceDesc("用户已确认收货，订单完成")
                    .sortOrder(999)
                    .build();

            orderDetailIntegrationMapper.insertLogisticsTrace(confirmTrace);
        }

        log.info("确认收货完成，订单ID：{}", orderId);
    }

    @Override
    @Transactional
    public void applyRefund(Long orderId, Long userId, String reason) {
        log.info("申请退款，订单ID：{}，用户ID：{}，原因：{}", orderId, userId, reason);

        Orders order = ordersMapper.selectById(orderId);
        if (order == null || !order.getBuyerId().equals(userId)) {
            throw new BaseException("订单不存在或无权限操作");
        }

        if (!canPerformAction(orderId, userId, "refund")) {
            throw new BaseException("订单状态不允许申请退款");
        }

        // 更新订单状态为退款中
        order.setRefundStatus(1); // 退款申请中
        ordersMapper.updateById(order);

        // 更新支付状态
        OrderPaymentDetail paymentDetail = orderDetailIntegrationMapper.getPaymentDetailByOrderId(orderId);
        if (paymentDetail != null) {
            paymentDetail.setPaymentStatus("refunding");
            orderDetailIntegrationMapper.updatePaymentDetail(paymentDetail);
        }

        log.info("退款申请提交成功，订单ID：{}", orderId);
    }

    @Override
    @Transactional
    public void cancelOrder(Long orderId, Long userId, String reason) {
        log.info("取消订单，订单ID：{}，用户ID：{}，原因：{}", orderId, userId, reason);

        Orders order = ordersMapper.selectById(orderId);
        if (order == null || !order.getBuyerId().equals(userId)) {
            throw new BaseException("订单不存在或无权限操作");
        }

        if (!canPerformAction(orderId, userId, "cancel")) {
            throw new BaseException("订单状态不允许取消");
        }

        // 更新订单状态为已取消
        order.setStatus(3);
        order.setCancelTime(LocalDateTime.now());
        order.setCancelReason(reason);
        ordersMapper.updateById(order);

        log.info("订单取消成功，订单ID：{}", orderId);
    }

    @Override
    public boolean canPerformAction(Long orderId, Long userId, String action) {
        Orders order = ordersMapper.selectById(orderId);
        if (order == null || !order.getBuyerId().equals(userId)) {
            return false;
        }

        Integer status = order.getStatus();

        switch (action) {
            case "cancel":
                // 只有待支付状态可以取消
                return status == 1;
            case "refund":
                // 已支付、已发货、已完成的订单可以申请退款
                return status == 2 || status == 4 || status == 5;
            case "confirm":
                // 只有已发货状态可以确认收货
                return status == 4;
            default:
                return false;
        }
    }

    @Override
    public String getLogisticsStatusDescription(String status) {
        if (status == null) return "暂无物流信息";

        switch (status) {
            case "NotFound": return "查询不到物流信息";
            case "InfoReceived": return "物流信息已录入";
            case "InTransit": return "运输途中";
            case "Expired": return "运输时间过长";
            case "AvailableForPickup": return "到达待取";
            case "OutForDelivery": return "正在派送";
            case "DeliveryFailure": return "投递失败";
            case "Delivered": return "已签收";
            case "Exception": return "可能异常";
            default: return status;
        }
    }

    @Override
    @Transactional
    public void createOrderAddresses(Long orderId, OrderAddressInfo deliveryAddress, OrderAddressInfo senderAddress) {
        log.info("创建订单地址信息，订单ID：{}", orderId);

        if (deliveryAddress != null) {
            deliveryAddress.setOrderId(orderId);
            deliveryAddress.setAddressType(1); // 收货地址
            orderDetailIntegrationMapper.insertOrderAddress(deliveryAddress);
        }

        if (senderAddress != null) {
            senderAddress.setOrderId(orderId);
            senderAddress.setAddressType(2); // 发货地址
            orderDetailIntegrationMapper.insertOrderAddress(senderAddress);
        }

        log.info("订单地址信息创建完成");
    }

    @Override
    @Transactional
    public void createOrderPayment(OrderPaymentDetail paymentDetail) {
        log.info("创建订单支付信息，订单ID：{}", paymentDetail.getOrderId());

        orderDetailIntegrationMapper.insertPaymentDetail(paymentDetail);

        log.info("订单支付信息创建完成");
    }

    @Override
    public List<LogisticsTraceDetail> getRealtimeLogisticsInfo(String trackingNumber, String carrierCode) {
        log.info("获取实时物流信息，物流单号：{}，运输商：{}", trackingNumber, carrierCode);

        try {
            // 构建17TRACK实时查询请求
            Track17RealtimeRequest request = Track17RealtimeRequest.builder()
                    .trackingNumber(trackingNumber)
                    .carrierCode(carrierCode)
                    .build();

            Track17Response<Track17RealtimeResponse> response = track17ApiService.getRealtimeTracking(request);

            if (response != null && response.getSuccess() && response.getData() != null) {
                Track17RealtimeResponse data = response.getData();

                if (data.getTraces() != null && !data.getTraces().isEmpty()) {
                    List<LogisticsTraceDetail> traces = new ArrayList<>();
                    for (int i = 0; i < data.getTraces().size(); i++) {
                        Track17RealtimeResponse.Track17TraceDetail event = data.getTraces().get(i);

                        LogisticsTraceDetail trace = LogisticsTraceDetail.builder()
                                .trackingNumber(trackingNumber)
                                .traceTime(event.getTraceTime()) // 事件时间
                                .traceLocation(event.getLocation()) // 位置
                                .traceStatus(event.getStatus()) // 状态
                                .traceDesc(event.getDescription()) // 描述
                                .operator(event.getOperator()) // 操作员
                                .phone(event.getPhone()) // 联系电话
                                .sortOrder(i + 1)
                                .build();

                        traces.add(trace);
                    }

                    return traces;
                }
            }

            log.warn("未获取到物流信息，物流单号：{}", trackingNumber);
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("获取实时物流信息失败，物流单号：{}", trackingNumber, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取下一步操作提示
     */
    private String getNextActionTip(Integer orderStatus, OrderLogisticsTracking tracking) {
        if (orderStatus == null) return "";

        switch (orderStatus) {
            case 1: return "请尽快完成支付，避免订单自动取消";
            case 2: return "支付成功，商家正在准备发货，请耐心等待";
            case 3: return "订单已取消";
            case 4:
                if (tracking != null && "OutForDelivery".equals(tracking.getCurrentStatus())) {
                    return "快递员正在派送中，请保持电话畅通";
                } else if (tracking != null && "AvailableForPickup".equals(tracking.getCurrentStatus())) {
                    return "包裹已到达取件点，请及时取件";
                }
                return "商品正在配送中，收到货后请确认收货";
            case 5: return "订单已完成，感谢您的购买";
            case 6: return "订单已关闭";
            default: return "";
        }
    }

    /**
     * 获取支付方式代码
     */
    private String getPaymentMethodCode(Integer payMethod) {
        if (payMethod == null) return "unknown";
        switch (payMethod) {
            case 1: return "wechat";
            case 2: return "alipay";
            case 3: return "bank";
            default: return "unknown";
        }
    }

    /**
     * 获取支付方式名称
     */
    private String getPaymentMethodName(Integer payMethod) {
        if (payMethod == null) return "未知支付方式";
        switch (payMethod) {
            case 1: return "微信支付";
            case 2: return "支付宝";
            case 3: return "银行卡";
            default: return "未知支付方式";
        }
    }
}
