package com.sky.service.impl;

import com.sky.dto.BatchMessageDTO;
import com.sky.dto.MessageDTO;
import com.sky.dto.RecipientQueryDTO;
import com.sky.entity.Message;
import com.sky.entity.MessageRecipient;
import com.sky.mapper.MessageMapper;
import com.sky.service.MessageService;
import com.sky.vo.MessageDetailVO;
import com.sky.vo.MessageItemVO;
import com.sky.vo.MessageListVO;
import com.sky.vo.RecipientVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    @Autowired
    private MessageMapper messageMapper;

    @Override
    public List<RecipientVO> getRecipients(RecipientQueryDTO queryDTO) {
        if ("merchant".equals(queryDTO.getType())) {
            return messageMapper.getSellerList(queryDTO.getKeyword());
        } else if ("user".equals(queryDTO.getType())) {
            return messageMapper.getUserList(queryDTO.getKeyword());
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public String sendMessage(MessageDTO messageDTO) {
        // 验证参数
        validateMessageParams(messageDTO);

        // 生成消息ID
        String msgID = "msg_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);

        // 创建消息
        Message message = Message.builder()
                .msgID(msgID)
                .msgType(messageDTO.getMsgType() != null ? messageDTO.getMsgType() : "用户消息")
                .title(messageDTO.getTitle())
                .content(messageDTO.getContent())
                .senderId(messageDTO.getSenderID())
                .senderName(messageDTO.getType())
                .sendTime(messageDTO.getSendTime() != null ? messageDTO.getSendTime() : LocalDateTime.now())
                .isRead(false)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 插入消息
        messageMapper.insertMessage(message);

        // 处理接收者
        List<MessageRecipient> recipients = new ArrayList<>();

        if (Boolean.TRUE.equals(messageDTO.getSendToAllUsers())) {
            // 发送给全体用户
            List<RecipientVO> users = messageMapper.getUserList(null);
            for (RecipientVO user : users) {
                recipients.add(createMessageRecipient(msgID, "user", user.getId()));
            }
        } else if (Boolean.TRUE.equals(messageDTO.getSendToAllMerchants())) {
            // 发送给全体卖家
            List<RecipientVO> sellers = messageMapper.getSellerList(null);
            for (RecipientVO seller : sellers) {
                recipients.add(createMessageRecipient(msgID, "merchant", seller.getId()));
            }
        } else if (messageDTO.getRecipient() != null) {
            // 发送给指定接收者
            // recipient对象应包含type和id
            Object recipientObj = messageDTO.getRecipient();
            if (recipientObj instanceof java.util.Map) {
                java.util.Map<?,?> map = (java.util.Map<?,?>) recipientObj;
                Object typeObj = map.get("type");
                Object idObj = map.get("id");
                if (typeObj != null && idObj != null) {
                    String recipientType = typeObj.toString();
                    Long recipientId = null;
                    try {
                        recipientId = Long.valueOf(idObj.toString());
                    } catch (Exception e) {
                        throw new IllegalArgumentException("recipient.id 不是有效的Long类型");
                    }
                    recipients.add(createMessageRecipient(msgID, recipientType, recipientId));
                }
            }
        }

        if (!recipients.isEmpty()) {
            messageMapper.batchInsertMessageRecipient(recipients);
        }

        return msgID;
    }

    @Override
    @Transactional
    public String sendAnnouncement(MessageDTO messageDTO) {
        // 验证参数
        if (!StringUtils.hasText(messageDTO.getTitle()) || !StringUtils.hasText(messageDTO.getContent())) {
            throw new IllegalArgumentException("公告标题和内容不能为空");
        }

        // 生成消息ID
        String msgID = "msg_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);

        // 创建系统公告消息
        Message message = Message.builder()
                .msgID(msgID)
                .msgType("系统公告")
                .title(messageDTO.getTitle())
                .content(messageDTO.getContent())
                .senderId(messageDTO.getSenderID())
                .senderName(messageDTO.getType())
                .sendTime(messageDTO.getSendTime() != null ? messageDTO.getSendTime() : LocalDateTime.now())
                .isRead(false)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 插入消息
        messageMapper.insertMessage(message);

        // 发送给所有用户、商家和管理员
        List<MessageRecipient> recipients = new ArrayList<>();

        // 发送给所有用户
        List<RecipientVO> users = messageMapper.getUserList(null);
        for (RecipientVO user : users) {
            recipients.add(createMessageRecipient(msgID, "user", user.getId()));
        }

        // 发送给所有商家
        List<RecipientVO> sellers = messageMapper.getSellerList(null);
        for (RecipientVO seller : sellers) {
            recipients.add(createMessageRecipient(msgID, "merchant", seller.getId()));
        }

        // 发送给所有管理员（这里需要根据实际的管理员表来查询）
        // 暂时使用固定的管理员ID列表，实际应该从数据库查询
        List<Long> adminIds = getAdminIds();
        for (Long adminId : adminIds) {
            recipients.add(createMessageRecipient(msgID, "Administrator", adminId));
        }

        if (!recipients.isEmpty()) {
            messageMapper.batchInsertMessageRecipient(recipients);
        }

        return msgID;
    }

    @Override
    public MessageListVO getMessageList(String type, Long id) {
        List<Message> messages = messageMapper.getMessagesByRecipient(type, id);
        Integer unreadCount = messageMapper.getUnreadCount(type, id);

        List<MessageItemVO> itemList = new ArrayList<>();
        for (Message message : messages) {
            itemList.add(MessageItemVO.builder()
                    .msgID(message.getMsgID())
                    .msgType(message.getMsgType())
                    .title(message.getTitle())
                    .senderName(message.getSenderName())
                    .sendTime(message.getSendTime())
                    .isRead(message.getIsRead())
                    .build());
        }

        return MessageListVO.builder()
                .total(messages.size())
                .unreadCount(unreadCount)
                .list(itemList)
                .build();
    }

    @Override
    public MessageDetailVO getMessageDetail(String msgID, String type, Long id) {
        Message message = messageMapper.getMessageById(msgID, type, id);
        if (message == null) {
            throw new RuntimeException("消息不存在或已被删除");
        }

        // 标记为已读
        messageMapper.markAsRead(msgID, type, id);

        return MessageDetailVO.builder()
                .msgID(message.getMsgID())
                .msgType(message.getMsgType())
                .title(message.getTitle())
                .content(message.getContent())
                .senderId(message.getSenderId())
                .senderName(message.getSenderName())
                .sendTime(message.getSendTime())
                .isRead(message.getIsRead())
                .build();
    }

    @Override
    @Transactional
    public void markAsRead(String msgID, String type, Long id) {
        messageMapper.markAsRead(msgID, type, id);
    }

    @Override
    @Transactional
    public void batchMarkAsRead(BatchMessageDTO batchDTO) {
        if (batchDTO.getMsgIDs() != null && !batchDTO.getMsgIDs().isEmpty()) {
            messageMapper.batchMarkAsRead(batchDTO.getMsgIDs(), batchDTO.getType(), batchDTO.getId());
        }
    }

    @Override
    @Transactional
    public void markAllAsRead(String type, Long id) {
        messageMapper.markAllAsRead(type, id);
    }

    @Override
    public Integer getUnreadCount(String type, Long id) {
        return messageMapper.getUnreadCount(type, id);
    }

    @Override
    @Transactional
    public void deleteMessage(String msgID, String type, Long id) {
        messageMapper.deleteMessage(msgID, type, id);
    }

    @Override
    @Transactional
    public void batchDeleteMessage(BatchMessageDTO batchDTO) {
        if (batchDTO.getMsgIDs() != null && !batchDTO.getMsgIDs().isEmpty()) {
            messageMapper.batchDeleteMessage(batchDTO.getMsgIDs(), batchDTO.getType(), batchDTO.getId());
        }
    }

    // 私有方法
    private void validateMessageParams(MessageDTO messageDTO) {
        if (!StringUtils.hasText(messageDTO.getTitle()) || !StringUtils.hasText(messageDTO.getContent())) {
            throw new IllegalArgumentException("消息标题和内容不能为空");
        }

        if (messageDTO.getSenderID() == null) {
            throw new IllegalArgumentException("发送者ID不能为空");
        }

        // 验证接收者参数
        boolean hasRecipient = Boolean.TRUE.equals(messageDTO.getSendToAllUsers())
                || Boolean.TRUE.equals(messageDTO.getSendToAllSellers())
                || messageDTO.getRecipient() != null;

        if (!hasRecipient) {
            throw new IllegalArgumentException("必须至少传递以下参数之一：recipient、sendToAllUsers 或 sendToAllSellers");
        }
    }

    private MessageRecipient createMessageRecipient(String msgID, String recipientType, Long recipientId) {
        return MessageRecipient.builder()
                .msgID(msgID)
                .recipientType(recipientType)
                .recipientId(recipientId)
                .isRead(false)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }

    private List<Long> getAdminIds() {
        // 这里应该从数据库查询管理员ID列表
        // 暂时返回空列表，实际实现时需要根据管理员表来查询
        return new ArrayList<>();
    }
} 