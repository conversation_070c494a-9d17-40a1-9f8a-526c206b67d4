package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageRecipient implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;                // 主键ID
    private String msgID;           // 消息ID
    private String recipientType;   // 接收者类型：merchant(商家)、user(用户)、Administrator(管理员)
    private Long recipientId;       // 接收者ID
    private Boolean isRead;         // 是否已读
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
} 