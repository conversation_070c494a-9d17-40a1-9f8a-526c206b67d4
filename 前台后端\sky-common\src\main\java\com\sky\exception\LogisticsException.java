package com.sky.exception;

/**
 * 物流业务异常
 */
public class LogisticsException extends BaseException {

    public LogisticsException() {
        super();
    }

    public LogisticsException(String msg) {
        super(msg);
    }

    public LogisticsException(String msg, Throwable cause) {
        super(msg, cause);
    }

    // 物流相关的具体异常类型
    public static class TrackingNumberValidationException extends LogisticsException {
        public TrackingNumberValidationException(String message) {
            super("物流单号验证失败：" + message);
        }
    }

    public static class LogisticsInfoNotFoundException extends LogisticsException {
        public LogisticsInfoNotFoundException() {
            super("物流信息不存在");
        }
        
        public LogisticsInfoNotFoundException(String trackingNumber) {
            super("物流信息不存在：" + trackingNumber);
        }
    }

    public static class LogisticsQueryException extends LogisticsException {
        public LogisticsQueryException(String message) {
            super("物流查询失败：" + message);
        }
        
        public LogisticsQueryException(String message, Throwable cause) {
            super("物流查询失败：" + message, cause);
        }
    }

    public static class CourierCodeException extends LogisticsException {
        public CourierCodeException(String courierCode) {
            super("不支持的快递公司代码：" + courierCode);
        }
    }

    public static class Track17ApiException extends LogisticsException {
        public Track17ApiException(String message) {
            super("17TRACK API调用失败：" + message);
        }
        
        public Track17ApiException(String message, Throwable cause) {
            super("17TRACK API调用失败：" + message, cause);
        }
    }
}
