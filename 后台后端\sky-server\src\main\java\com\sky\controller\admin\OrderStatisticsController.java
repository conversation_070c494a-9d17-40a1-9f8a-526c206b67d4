package com.sky.controller.admin;

import com.sky.result.Result;
import com.sky.service.OrderStatisticsService;
import com.sky.vo.OrderStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;

@RestController
@RequestMapping("/admin/orders/statistics")
@Api(tags = "订单统计管理接口")
public class OrderStatisticsController {
    
    @Autowired
    private OrderStatisticsService orderStatisticsService;
    
    /**
     * 获取订单统计数据
     */
    @GetMapping
    @ApiOperation("获取订单统计数据")
    public Result<OrderStatisticsVO> getOrderStatistics() {
        OrderStatisticsVO statistics = orderStatisticsService.getOrderStatistics();
        return Result.success(statistics);
    }
    
    /**
     * 获取指定日期范围的订单统计
     */
    @GetMapping("/range")
    @ApiOperation("获取指定日期范围的订单统计")
    public Result<OrderStatisticsVO> getOrderStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        OrderStatisticsVO statistics = orderStatisticsService.getOrderStatistics(beginDate, endDate);
        return Result.success(statistics);
    }
    
    /**
     * 获取今日订单统计
     */
    @GetMapping("/today")
    @ApiOperation("获取今日订单统计")
    public Result<Map<String, Object>> getTodayStatistics() {
        Map<String, Object> statistics = orderStatisticsService.getTodayStatistics();
        return Result.success(statistics);
    }
    
    /**
     * 获取本月订单统计
     */
    @GetMapping("/month")
    @ApiOperation("获取本月订单统计")
    public Result<Map<String, Object>> getMonthStatistics() {
        Map<String, Object> statistics = orderStatisticsService.getMonthStatistics();
        return Result.success(statistics);
    }
    
    /**
     * 获取订单状态分布
     */
    @GetMapping("/status-distribution")
    @ApiOperation("获取订单状态分布")
    public Result<Map<String, Object>> getStatusDistribution() {
        Map<String, Object> distribution = orderStatisticsService.getStatusDistribution();
        return Result.success(distribution);
    }

    /**
     * 获取增强版订单统计数据
     */
    @GetMapping("/enhanced")
    @ApiOperation("获取增强版订单统计数据")
    public Result<OrderStatisticsVO> getEnhancedOrderStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        OrderStatisticsVO statistics = orderStatisticsService.getEnhancedOrderStatistics(beginDate, endDate);
        return Result.success(statistics);
    }

    /**
     * 获取时间维度统计
     */
    @GetMapping("/time-dimension")
    @ApiOperation("获取时间维度统计")
    public Result<Map<String, Object>> getTimeDimensionStatistics() {
        Map<String, Object> statistics = orderStatisticsService.getTimeDimensionStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取支付方式统计
     */
    @GetMapping("/payment-method")
    @ApiOperation("获取支付方式统计")
    public Result<Map<String, Object>> getPaymentMethodStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Map<String, Object> statistics = orderStatisticsService.getPaymentMethodStatistics(beginDate, endDate);
        return Result.success(statistics);
    }

    /**
     * 获取商品维度统计
     */
    @GetMapping("/product")
    @ApiOperation("获取商品维度统计")
    public Result<Map<String, Object>> getProductStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(defaultValue = "10") Integer topLimit) {
        Map<String, Object> statistics = orderStatisticsService.getProductStatistics(beginDate, endDate, topLimit);
        return Result.success(statistics);
    }

    /**
     * 获取用户维度统计
     */
    @GetMapping("/user")
    @ApiOperation("获取用户维度统计")
    public Result<Map<String, Object>> getUserStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Map<String, Object> statistics = orderStatisticsService.getUserStatistics(beginDate, endDate);
        return Result.success(statistics);
    }

    /**
     * 获取转化率统计
     */
    @GetMapping("/conversion-rate")
    @ApiOperation("获取转化率统计")
    public Result<Map<String, Object>> getConversionRateStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Map<String, Object> statistics = orderStatisticsService.getConversionRateStatistics(beginDate, endDate);
        return Result.success(statistics);
    }

    /**
     * 获取实时统计数据
     */
    @GetMapping("/realtime")
    @ApiOperation("获取实时统计数据")
    public Result<Map<String, Object>> getRealtimeStatistics() {
        Map<String, Object> statistics = orderStatisticsService.getRealtimeStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取同比环比数据
     */
    @GetMapping("/comparison")
    @ApiOperation("获取同比环比数据")
    public Result<Map<String, Object>> getComparisonStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Map<String, Object> statistics = orderStatisticsService.getComparisonStatistics(beginDate, endDate);
        return Result.success(statistics);
    }

    /**
     * 获取趋势统计数据
     */
    @GetMapping("/trend")
    @ApiOperation("获取趋势统计数据")
    public Result<Map<String, Object>> getTrendStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(defaultValue = "day") String granularity) {
        Map<String, Object> statistics = orderStatisticsService.getTrendStatistics(beginDate, endDate, granularity);
        return Result.success(statistics);
    }
}
