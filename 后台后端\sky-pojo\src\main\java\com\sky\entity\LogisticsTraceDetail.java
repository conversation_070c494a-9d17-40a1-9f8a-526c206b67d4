package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流轨迹详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsTraceDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 物流跟踪ID（关联order_logistics_tracking表）
     */
    private Long trackingId;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 轨迹时间
     */
    private LocalDateTime traceTime;

    /**
     * 轨迹位置
     */
    private String traceLocation;

    /**
     * 轨迹状态
     */
    private String traceStatus;

    /**
     * 轨迹描述
     */
    private String traceDesc;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
