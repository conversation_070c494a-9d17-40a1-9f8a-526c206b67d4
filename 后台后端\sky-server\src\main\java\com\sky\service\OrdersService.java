package com.sky.service;

import com.github.pagehelper.PageInfo;
import com.sky.dto.OrderPageQueryDTO;
import com.sky.dto.ShipOrderDTO;
import com.sky.entity.Orders;
import com.sky.vo.OrderVO;

import java.util.List;
import java.util.Map;

/**
 * 后台订单管理服务接口
 */
public interface OrdersService {

    /**
     * 根据ID获取订单详情
     */
    OrderVO getOrderById(Long id);

    /**
     * 根据订单号获取订单
     */
    OrderVO getOrderByNumber(String number);

    /**
     * 分页查询订单（后台管理）
     */
    PageInfo<OrderVO> pageQuery(OrderPageQueryDTO orderPageQueryDTO);

    /**
     * 发货（旧方法，保持兼容性）
     */
    void shipOrder(Long id);

    /**
     * 发货（新方法，包含物流信息验证）
     */
    void shipOrder(ShipOrderDTO shipOrderDTO);

    /**
     * 完成订单
     */
    void completeOrder(Long id);

    /**
     * 取消订单
     */
    void cancelOrder(Long id, String cancelReason);

    /**
     * 订单统计
     */
    Map<String, Object> getOrderStatistics();

    /**
     * 根据买家ID查询订单列表
     */
    List<OrderVO> getOrdersByBuyerId(Long buyerId);

    /**
     * 查询历史订单
     */
    List<OrderVO> getOrderHistory(Long buyerId);

    /**
     * 编辑订单信息
     */
    void editOrder(Long orderId, String addressInfo, String remark, String editReason);

    /**
     * 退款处理
     */
    void refundOrder(Long orderId, String refundReason, String refundAmount);

    /**
     * 获取订单操作日志
     */
    List<Object> getOrderLogs(Long orderId);



    /**
     * 获取订单统计概览
     */
    Map<String, Object> getOrderOverview();

    /**
     * 导出订单数据
     */
    String exportOrders(List<Long> orderIds, String format);
}

