package com.sky.service;

import com.sky.entity.Seller;

import java.util.List;

public interface SellerService {

    /**
     * 查询所有商家信息
     * @return 商家列表
     */
    List<Seller> findAll();

    /**
     * 根据 ID 查询商家信息
     * @param id 商家 ID
     * @return 商家信息
     */
    Seller findById(Long id);

    /**
     * 更新商家信息
     * @param seller 商家信息
     */
    Boolean update(Seller seller);

    /**
     * 根据 ID 删除商家信息
     * @param id 商家 ID
     */
    Boolean deleteById(Integer id);
}