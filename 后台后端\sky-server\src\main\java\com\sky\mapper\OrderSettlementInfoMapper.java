package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.SettlementQueryDTO;
import com.sky.entity.OrderSettlementInfo;
import com.sky.vo.SettlementInfoVO;
import com.sky.vo.SettlementSummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 订单回款信息Mapper
 */
@Mapper
public interface OrderSettlementInfoMapper {

    /**
     * 插入回款信息
     */
    void insert(OrderSettlementInfo orderSettlementInfo);

    /**
     * 根据ID查询回款信息
     */
    OrderSettlementInfo selectById(Long id);

    /**
     * 根据订单ID查询回款信息
     */
    OrderSettlementInfo selectByOrderId(Long orderId);

    /**
     * 更新回款信息
     */
    void update(OrderSettlementInfo orderSettlementInfo);

    /**
     * 分页查询回款信息
     */
    Page<SettlementInfoVO> selectPage(SettlementQueryDTO queryDTO);

    /**
     * 查询商家回款汇总信息
     */
    SettlementSummaryVO selectSummaryBySellerId(@Param("sellerId") Long sellerId);

    /**
     * 查询所有商家回款汇总信息
     */
    List<SettlementSummaryVO> selectAllSummary();

    /**
     * 查询待回款订单
     */
    List<SettlementInfoVO> selectPendingSettlement();

    /**
     * 根据回款日期查询待回款订单
     */
    List<SettlementInfoVO> selectBySettlementDate(@Param("settlementDate") LocalDate settlementDate);

    /**
     * 批量更新回款状态
     */
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 更新回款完成状态
     */
    void updateSettlementComplete(@Param("id") Long id, @Param("remark") String remark);

    /**
     * 统计商家回款信息
     */
    SettlementSummaryVO countBySellerIdAndStatus(@Param("sellerId") Long sellerId, @Param("status") Integer status);
}
