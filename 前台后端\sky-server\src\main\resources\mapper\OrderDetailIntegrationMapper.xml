<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrderDetailIntegrationMapper">

    <!-- 批量插入物流轨迹详情 -->
    <insert id="insertLogisticsTracesBatch" parameterType="java.util.List">
        INSERT INTO logistics_trace_detail 
        (tracking_id, tracking_number, trace_time, trace_location, trace_status, trace_desc, operator, phone, sort_order)
        VALUES
        <foreach collection="list" item="trace" separator=",">
            (#{trace.trackingId}, #{trace.trackingNumber}, #{trace.traceTime}, #{trace.traceLocation}, 
             #{trace.traceStatus}, #{trace.traceDesc}, #{trace.operator}, #{trace.phone}, #{trace.sortOrder})
        </foreach>
    </insert>

    <!-- 复杂查询：根据用户ID和订单状态查询订单列表 -->
    <select id="getOrdersByUserIdAndStatus" resultType="com.sky.entity.Orders">
        SELECT o.*, 
               olt.tracking_number,
               olt.carrier_name,
               olt.current_status as logistics_status,
               opd.payment_method,
               opd.payment_status
        FROM orders o
        LEFT JOIN order_logistics_tracking olt ON o.id = olt.order_id
        LEFT JOIN order_payment_detail opd ON o.id = opd.order_id
        WHERE o.user_id = #{userId}
        <if test="status != null">
            AND o.status = #{status}
        </if>
        ORDER BY o.order_time DESC
    </select>

    <!-- 复杂查询：获取订单完整信息 -->
    <select id="getOrderFullInfo" resultType="com.sky.vo.OrderDetailIntegrationVO">
        SELECT 
            o.id as order_id,
            o.number as order_number,
            o.status as order_status,
            o.amount as order_amount,
            o.order_time,
            o.user_id,
            o.remark,
            o.pay_time as payment_time,
            o.ship_time,
            
            olt.tracking_number,
            olt.carrier_code,
            olt.carrier_name,
            olt.current_status,
            olt.current_status_desc,
            olt.latest_location,
            olt.estimated_delivery_time,
            olt.actual_delivery_time,
            olt.last_update_time,
            
            opd.payment_method,
            opd.payment_channel,
            opd.transaction_id,
            opd.payment_amount,
            opd.payment_status,
            opd.payment_time as pay_time,
            opd.refund_amount,
            opd.refund_time
            
        FROM orders o
        LEFT JOIN order_logistics_tracking olt ON o.id = olt.order_id
        LEFT JOIN order_payment_detail opd ON o.id = opd.order_id
        WHERE o.id = #{orderId}
        <if test="userId != null">
            AND o.user_id = #{userId}
        </if>
    </select>

    <!-- 统计查询：用户订单统计 -->
    <select id="getUserOrderStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN o.status = 1 THEN 1 END) as pending_payment,
            COUNT(CASE WHEN o.status = 2 THEN 1 END) as pending_shipment,
            COUNT(CASE WHEN o.status = 3 THEN 1 END) as pending_receipt,
            COUNT(CASE WHEN o.status = 4 THEN 1 END) as completed,
            COUNT(CASE WHEN o.status = 5 THEN 1 END) as cancelled,
            SUM(CASE WHEN o.status = 4 THEN o.amount ELSE 0 END) as total_amount
        FROM orders o
        WHERE o.user_id = #{userId}
    </select>

    <!-- 查询用户最近的物流更新 -->
    <select id="getRecentLogisticsUpdates" resultType="com.sky.entity.LogisticsTraceDetail">
        SELECT ltd.*
        FROM logistics_trace_detail ltd
        INNER JOIN order_logistics_tracking olt ON ltd.tracking_id = olt.id
        INNER JOIN orders o ON olt.order_id = o.id
        WHERE o.user_id = #{userId}
        AND o.status IN (3, 4)  -- 待收货和已完成的订单
        ORDER BY ltd.trace_time DESC
        LIMIT #{limit}
    </select>

</mapper>
