<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrdersMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sky.entity.Orders">
        <id column="id" property="id" />
        <result column="number" property="number" />
        <result column="buyer_id" property="buyerId" />
        <result column="address_id" property="addressId" />
        <result column="shipping_method_id" property="shippingMethodId" />
        <result column="status" property="status" />
        <result column="amount" property="amount" />
        <result column="order_time" property="orderTime" />
        <result column="pay_time" property="payTime" />
        <result column="checkout_time" property="checkoutTime" />
        <result column="ship_time" property="shipTime" />
        <result column="complete_time" property="completeTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="pay_method" property="payMethod" />
        <result column="payment_transaction_id" property="paymentTransactionId" />
        <result column="order_remark" property="orderRemark" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="rejection_reason" property="rejectionReason" />
        <result column="cancel_request" property="cancelRequest" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_time" property="refundTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 创建订单 -->
    <insert id="createOrder" useGeneratedKeys="true" keyProperty="id" parameterType="com.sky.entity.Orders">
        INSERT INTO orders (
            number, buyer_id, address_id, shipping_method_id, status, 
            amount, order_time, pay_time, pay_method, payment_transaction_id,
            order_remark, create_time, update_time
        ) VALUES (
            #{number}, #{buyerId}, #{addressId}, #{shippingMethodId}, #{status}, 
            #{amount}, #{orderTime}, #{payTime}, #{payMethod}, #{paymentTransactionId},
            #{orderRemark}, #{createTime}, #{updateTime}
        )
    </insert>
    
    <!-- 根据订单号查询订单 -->
    <select id="getByNumber" resultMap="BaseResultMap">
        SELECT * FROM orders WHERE number = #{number}
    </select>
    
    <!-- 根据买家ID查询订单列表 -->
    <select id="getByBuyerId" resultMap="BaseResultMap">
        SELECT * FROM orders WHERE buyer_id = #{buyerId} ORDER BY create_time DESC
    </select>
    
    <!-- 更新订单状态 -->
    <update id="updateStatus">
        UPDATE orders 
        <set>
            status = #{status},
            <if test="status == 2">
                pay_time = NOW(),
            </if>
            <if test="status == 4">
                ship_time = NOW(),
            </if>
            <if test="status == 5">
                complete_time = NOW(),
            </if>
            <if test="status == 6">
                cancel_time = NOW(),
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 更新支付信息 -->
    <update id="updatePayment">
        UPDATE orders 
        SET pay_method = #{payMethod},
            payment_transaction_id = #{paymentTransactionId},
            status = 2,
            pay_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <!-- 分页查询订单 -->
    <select id="pageQuery" resultMap="BaseResultMap">
        SELECT * FROM orders
        <where>
            <if test="buyerId != null">
                AND buyer_id = #{buyerId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 更新订单退款状态 -->
    <update id="updateRefundStatus">
        UPDATE orders
        SET refund_status = #{refundStatus},
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime},
            </if>
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询订单（包含退款信息） -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM orders WHERE id = #{id}
    </select>
</mapper>