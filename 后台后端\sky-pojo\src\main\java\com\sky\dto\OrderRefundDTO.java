package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单退款数据传输对象
 */
@Data
public class OrderRefundDTO implements Serializable {
    
    private Long orderId;               // 订单ID
    private BigDecimal refundAmount;    // 退款金额
    private String refundReason;        // 退款原因
    private String refundType;          // 退款类型：full-全额退款, partial-部分退款
    private String refundMethod;        // 退款方式：original-原路退回, manual-手动退款
    private String remark;              // 备注信息
}
