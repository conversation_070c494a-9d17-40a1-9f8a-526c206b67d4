package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "订单视图对象")
public class OrderVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单ID")
    private Long id;

    @ApiModelProperty("订单号")
    private String number;

    @ApiModelProperty("订单状态")
    private Integer status;

    @ApiModelProperty("订单状态描述")
    private String statusDesc;

    @ApiModelProperty("订单总金额")
    private BigDecimal amount;

    @ApiModelProperty("下单时间")
    private LocalDateTime orderTime;

    @ApiModelProperty("支付时间")
    private LocalDateTime payTime;

    @ApiModelProperty("支付方式")
    private Integer payMethod;

    @ApiModelProperty("支付方式描述")
    private String payMethodDesc;

    @ApiModelProperty("收货地址信息")
    private AddressVO address;

    @ApiModelProperty("订单备注")
    private String orderRemark;

    @ApiModelProperty("订单详情列表")
    private List<OrderDetailVO> orderDetails;

    @ApiModelProperty("物流信息")
    private LogisticsVO logistics;
} 