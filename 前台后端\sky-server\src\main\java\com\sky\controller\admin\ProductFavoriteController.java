package com.sky.controller.admin;


import com.sky.entity.ProductFavorite;
import com.sky.result.Result;
import com.sky.service.ProductFavoriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/favorites")
@Api(tags = "C端商品收藏接口")
public class ProductFavoriteController {

    @Autowired
    private ProductFavoriteService productFavoriteService;

    // 添加收藏
    @PostMapping("/add")
    @ApiOperation("添加收藏")
    public Result<Void> addFavorite(@RequestBody ProductFavorite productFavorite) {
        productFavoriteService.addFavorite(productFavorite);
        return Result.success();
    }

    // 查询收藏列表
    @GetMapping("/list/{buyerId}")
    @ApiOperation("查询收藏列表")
    public Result<List<ProductFavorite>> getFavoritesByBuyerId(@PathVariable Long buyerId) {
        List<ProductFavorite> favorites = productFavoriteService.getFavoritesByBuyerId(buyerId);
        return Result.success(favorites);
    }

    // 取消收藏
    @DeleteMapping("/delete/{id}")
    @ApiOperation("取消收藏")
    public Result<Void> cancelFavorite(@PathVariable Long id) {
        productFavoriteService.cancelFavorite(id);
        return Result.success();
    }
} 