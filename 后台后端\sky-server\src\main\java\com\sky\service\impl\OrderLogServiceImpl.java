package com.sky.service.impl;

import com.sky.entity.OrderLog;
import com.sky.entity.Orders;
import com.sky.mapper.OrderLogMapper;
import com.sky.service.OrderLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单日志服务实现类
 */
@Service
public class OrderLogServiceImpl implements OrderLogService {
    
    @Autowired
    private OrderLogMapper orderLogMapper;
    
    @Override
    public void recordLog(Long orderId, String orderNumber, String operation, 
                         Integer oldStatus, Integer newStatus, String operatorType, 
                         Long operatorId, String operatorName, String remark) {
        OrderLog log = OrderLog.builder()
                .orderId(orderId)
                .orderNumber(orderNumber)
                .operation(operation)
                .oldStatus(oldStatus)
                .newStatus(newStatus)
                .operatorType(operatorType)
                .operatorId(operatorId)
                .operatorName(operatorName)
                .remark(remark)
                .createTime(LocalDateTime.now())
                .build();
        
        orderLogMapper.insert(log);
    }
    
    @Override
    public void recordCreateLog(Orders order, String operatorName) {
        recordLog(order.getId(), order.getNumber(), "create", 
                 null, order.getStatus(), "admin", 
                 1L, operatorName, "创建订单");
    }
    
    @Override
    public void recordStatusChangeLog(Orders order, Integer oldStatus, String operation, String operatorName, String remark) {
        recordLog(order.getId(), order.getNumber(), operation, 
                 oldStatus, order.getStatus(), "admin", 
                 1L, operatorName, remark);
    }
    
    @Override
    public List<OrderLog> getLogsByOrderId(Long orderId) {
        return orderLogMapper.findByOrderId(orderId);
    }
    
    @Override
    public List<OrderLog> getLogsByOrderNumber(String orderNumber) {
        return orderLogMapper.findByOrderNumber(orderNumber);
    }
}
