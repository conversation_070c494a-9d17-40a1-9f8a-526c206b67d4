package com.sky.service.impl;

import com.sky.entity.Template;
import com.sky.mapper.TemplateMapper;
import com.sky.service.TemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TemplateServiceImpl implements TemplateService {
    @Autowired
    private TemplateMapper templateMapper;

    @Override
    public List<Template> findAll() {
        return templateMapper.findAll();
    }

    @Override
    public void addTemplate(Template template) {
        templateMapper.insert(template);
    }
}