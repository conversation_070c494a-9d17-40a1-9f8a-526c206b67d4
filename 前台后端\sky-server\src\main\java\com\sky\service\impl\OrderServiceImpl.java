package com.sky.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.sky.dto.OrderItemDTO;
import com.sky.dto.OrderSubmitDTO;
import com.sky.entity.Logistics;
import com.sky.entity.OrderDetail;
import com.sky.entity.Orders;
import com.sky.entity.USAddress;
import com.sky.exception.BusinessException;
import com.sky.exception.OrderException;
import com.sky.mapper.LogisticsMapper;
import com.sky.mapper.OrderDetailMapper;
import com.sky.mapper.OrdersMapper;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import com.sky.service.OrderService;
import com.sky.service.USAddressService;
import com.sky.vo.AddressVO;
import com.sky.vo.LogisticsVO;
import com.sky.vo.OrderDetailVO;
import com.sky.vo.OrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 */
@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrdersMapper ordersMapper;
    
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    
    @Autowired
    private LogisticsMapper logisticsMapper;
    
    @Autowired
    private USAddressService addressServiceClient;
    
    @Autowired
    private CategoryService itemServiceClient;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 创建订单（已支付状态）
     * @param orderSubmitDTO 订单提交DTO
     * @param buyerId 买家ID
     * @return 订单视图对象
     */
    @Override
    @Transactional
    public OrderVO createOrder(OrderSubmitDTO orderSubmitDTO, Long buyerId) {
        log.info("创建已支付订单，买家ID：{}，订单信息：{}", buyerId, orderSubmitDTO);
        
        // 将支付时间戳转换为LocalDateTime
        LocalDateTime payTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(orderSubmitDTO.getPayTimeStamp()), 
                ZoneId.systemDefault());
        
        // 1. 构建订单对象（已支付状态）
        Orders orders = Orders.builder()
                .number(generateOrderNumber())
                .buyerId(buyerId)
                .addressId(orderSubmitDTO.getAddressId())
                .shippingMethodId(orderSubmitDTO.getShippingMethodId())
                .status(Orders.STATUS_PAID)  // 设置为已支付状态
                .amount(orderSubmitDTO.getAmount())
                .orderTime(LocalDateTime.now())
                .payTime(payTime)  // 设置支付时间
                .payMethod(orderSubmitDTO.getPayMethod())
                .paymentTransactionId(orderSubmitDTO.getPaymentTransactionId())  // 设置支付交易ID
                .orderRemark(orderSubmitDTO.getOrderRemark())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        // 2. 保存订单
        int result = ordersMapper.createOrder(orders);
        if (result <= 0) {
            log.error("创建订单失败");
            throw new OrderException("创建订单失败");
        }
        
        // 3. 收集所有商品ID
        List<Long> productIds = orderSubmitDTO.getOrderItems().stream()
                .map(OrderItemDTO::getProductId)
                .collect(Collectors.toList());
        
        // 4. 批量获取商品信息
        Map<Long, Map<String, Object>> productInfoMap = new HashMap<>();
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON); // 设置请求内容类型为JSON
            String url = "https://mall.sharewharf.com:8443/products/batch/basic"; // 目标API地址
            HttpEntity<List<Long>> requestEntity = new HttpEntity<>(productIds, httpHeaders); // 封装请求体和请求头

// 2. 使用RestTemplate发送POST请求
// 注意：使用ParameterizedTypeReference来处理复杂的泛型返回类型
            Result<Map<Long, Map<String, Object>>> productResult = restTemplate.exchange(
                    url,                       // 请求URL
                    HttpMethod.POST,           // 请求方法
                    requestEntity,             // 请求实体（包含请求体和请求头）
                    new ParameterizedTypeReference<Result<Map<Long, Map<String, Object>>>>() {} // 定义返回类型
            ).getBody();

// 3. 处理返回结果（保持原有逻辑）
//            Result<Map<Long, Map<String, Object>>> productResult = itemServiceClient.batchGetProductBasicInfo(productIds);
            if (productResult != null && productResult.getCode() == 1 && productResult.getData() != null) {
                productInfoMap = productResult.getData();
            }
        } catch (Exception e) {
            log.error("获取商品信息失败", e);
            throw new OrderException("获取商品信息失败，请稍后重试", e);
        }
        
        // 5. 构建订单详情对象
        List<OrderDetail> orderDetails = new ArrayList<>();
        for (OrderItemDTO item : orderSubmitDTO.getOrderItems()) {
            // 计算小计金额：单价 * 数量
            BigDecimal subtotal = item.getUnitPrice().multiply(new BigDecimal(item.getQuantity()));
            
            // 获取商品信息
            String productName = "";
            String productImage = "";
            String productSpec = "";
            
            Map<String, Object> productInfo = productInfoMap.get(item.getProductId());
            if (productInfo != null) {
                productName = (String) productInfo.getOrDefault("name", "");
                productImage = (String) productInfo.getOrDefault("image", "");
                productSpec = (String) productInfo.getOrDefault("spec", "");
            }
            
            // 如果商品信息获取失败，使用默认值
            if (StringUtils.isEmpty(productName)) {
                productName = "商品#" + item.getProductId();
            }
            
            OrderDetail orderDetail = OrderDetail.builder()
                    .orderId(orders.getId())
                    .productId(item.getProductId())
                    .productName(productName)
                    .productImage(productImage)
                    .productSpec(productSpec)
                    .quantity(item.getQuantity())
                    .unitPrice(item.getUnitPrice())
                    .subtotal(subtotal)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            orderDetails.add(orderDetail);
        }
        
        // 6. 保存订单详情
        if (!orderDetails.isEmpty()) {
            result = orderDetailMapper.batchInsert(orderDetails);
            if (result <= 0) {
                log.error("保存订单详情失败");
                throw new OrderException("保存订单详情失败");
            }
        }
        
        // 7. 返回订单视图对象
        return getOrderById(orders.getId());
    }

    /**
     * 根据订单ID查询订单
     * @param id 订单ID
     * @return 订单视图对象
     */
    @Override
    public OrderVO getOrderById(Long id) {
        // 1. 查询订单
        Orders orders = ordersMapper.selectById(id);
        if (orders == null) {
            log.error("订单不存在，ID：{}", id);
            throw new BusinessException("订单不存在");
        }
        
        // 2. 查询订单详情
        List<OrderDetail> orderDetails = orderDetailMapper.getByOrderId(id);
        
        // 3. 查询物流信息
        LogisticsVO logisticsVO = null;
        if (orders.getStatus() >= Orders.STATUS_SHIPPED) {
            LambdaQueryWrapper<Logistics> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Logistics::getOrderId, id);
            Logistics logistics = logisticsMapper.selectOne(queryWrapper);
            if (logistics != null) {
                logisticsVO = LogisticsVO.builder().build();
                BeanUtils.copyProperties(logistics, logisticsVO);
                
                // 设置物流状态描述
                String statusDesc = "";
                switch (logistics.getLogisticsStatus()) {
                    case "shipped":
                        statusDesc = "已发货";
                        break;
                    case "in_transit":
                        statusDesc = "运输中";
                        break;
                    case "delivered":
                        statusDesc = "已送达";
                        break;
                    default:
                        statusDesc = "未知状态";
                }
                logisticsVO.setLogisticsStatusDesc(statusDesc);
            }
        }
        
        // 4. 查询地址信息
        AddressVO addressVO = null;
        if (orders.getAddressId() != null) {
            // 通过Feign客户端调用用户服务获取地址信息
            try {
                List<USAddress> allAddresses = addressServiceClient.getAllAddresses(orders.getBuyerId());
                Result<List<USAddress>> addressResult = Result.success(allAddresses);
                if (addressResult != null && addressResult.getCode() == 1 && addressResult.getData() != null) {
                    List<USAddress> addresses = addressResult.getData();
                    for (USAddress address : addresses) {
                        if (address.getId().equals(orders.getAddressId())) {
                            addressVO = new AddressVO();
                            // 将USAddress转换为AddressVO
                            addressVO.setId(address.getId());
                            addressVO.setUserId(address.getUserId());
                            addressVO.setConsignee(address.getName());
                            addressVO.setPhone(address.getPhoneNumber());
                            // 设置地址信息
                            addressVO.setProvinceName("");  // USAddress没有省市区信息，这里设置为空
                            addressVO.setCityName(address.getCity());
                            addressVO.setDistrictName("");
                            addressVO.setDetail(address.getStreet() + " " + address.getAddressDetail());
                            addressVO.setLabel("");
                            // 安全处理isDefault字段，避免空指针异常
                            Long isDefault = address.getIsDefault();
                            addressVO.setIsDefault(isDefault != null && isDefault == 1 ? 1 : 0);
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取地址信息失败", e);
            }
        }
        
        // 5. 转换订单详情列表
        List<OrderDetailVO> orderDetailVOList = new ArrayList<>();
        if (orderDetails != null && !orderDetails.isEmpty()) {
            for (OrderDetail detail : orderDetails) {
                OrderDetailVO detailVO = new OrderDetailVO();
                BeanUtils.copyProperties(detail, detailVO);
                orderDetailVOList.add(detailVO);
            }
        }
        
        // 6. 构建订单视图对象
        OrderVO orderVO = OrderVO.builder().build();
        BeanUtils.copyProperties(orders, orderVO);
        
        // 设置订单状态描述
        String statusDesc = "";
        switch (orders.getStatus()) {
            case 1:
                statusDesc = "待付款";
                break;
            case 2:
                statusDesc = "已付款";
                break;
            case 3:
                statusDesc = "处理中";
                break;
            case 4:
                statusDesc = "已发货";
                break;
            case 5:
                statusDesc = "已完成";
                break;
            case 6:
                statusDesc = "已取消";
                break;
            case 7:
                statusDesc = "已退款";
                break;
            default:
                statusDesc = "未知状态";
        }
        orderVO.setStatusDesc(statusDesc);
        
        // 设置支付方式描述
        String payMethodDesc = "";
        if (orders.getPayMethod() != null) {
            switch (orders.getPayMethod()) {
                case 1:
                    payMethodDesc = "微信支付";
                    break;
                case 2:
                    payMethodDesc = "支付宝支付";
                    break;
                case 3:
                    payMethodDesc = "信用卡支付";
                    break;
                case 4:
                    payMethodDesc = "货到付款";
                    break;
                default:
                    payMethodDesc = "其他支付方式";
            }
        }
        orderVO.setPayMethodDesc(payMethodDesc);
        
        // 设置地址信息
        orderVO.setAddress(addressVO);
        
        // 设置订单详情列表
        orderVO.setOrderDetails(orderDetailVOList);
        
        // 设置物流信息
        orderVO.setLogistics(logisticsVO);
        
        return orderVO;
    }

    /**
     * 根据订单号查询订单
     * @param orderNumber 订单号
     * @return 订单视图对象
     */
    @Override
    public OrderVO getOrderByNumber(String orderNumber) {
        // 1. 查询订单
        Orders orders = ordersMapper.getByNumber(orderNumber);
        if (orders == null) {
            log.error("订单不存在，订单号：{}", orderNumber);
            throw new BusinessException("订单不存在");
        }
        
        // 2. 返回订单视图对象
        return getOrderById(orders.getId());
    }

    /**
     * 取消订单
     * @param orderId 订单ID
     * @param cancelReason 取消原因
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean cancelOrder(Long orderId, String cancelReason) {
        // 1. 查询订单
        Orders orders = ordersMapper.selectById(orderId);
        if (orders == null) {
            log.error("订单不存在，ID：{}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 2. 检查订单状态
        if (orders.getStatus() > Orders.STATUS_PROCESSING) {
            log.error("订单状态不正确，当前状态：{}", orders.getStatus());
            throw new BusinessException("订单已发货或已完成，不能取消");
        }
        
        // 3. 更新订单状态
        orders.setStatus(Orders.STATUS_CANCELLED);
        orders.setCancelReason(cancelReason);
        orders.setCancelTime(LocalDateTime.now());
        orders.setUpdateTime(LocalDateTime.now());
        
        int result = ordersMapper.updateById(orders);
        return result > 0;
    }

    /**
     * 发货
     * @param orderId 订单ID
     * @param logisticsCompany 物流公司
     * @param logisticsNumber 物流单号
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean shipOrder(Long orderId, String logisticsCompany, String logisticsNumber) {
        // 1. 查询订单
        Orders orders = ordersMapper.selectById(orderId);
        if (orders == null) {
            log.error("订单不存在，ID：{}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 2. 检查订单状态
        if (orders.getStatus() != Orders.STATUS_PAID && orders.getStatus() != Orders.STATUS_PROCESSING) {
            log.error("订单状态不正确，当前状态：{}", orders.getStatus());
            throw new BusinessException("订单状态不正确，不能发货");
        }
        
        // 3. 创建物流信息
        Logistics logistics = Logistics.builder()
                .orderId(orderId)
                .logisticsNumber(logisticsNumber)
                .logisticsCompany(logisticsCompany)
                .logisticsStatus("shipped")
                .shippingDate(LocalDateTime.now())
                .createTime(LocalDateTime.now())
                .build();
        
        logisticsMapper.insert(logistics);
        
        // 4. 更新订单状态
        int result = ordersMapper.updateStatus(orderId, Orders.STATUS_SHIPPED);
        return result > 0;
    }

    /**
     * 确认收货
     * @param orderId 订单ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean confirmOrder(Long orderId) {
        // 1. 查询订单
        Orders orders = ordersMapper.selectById(orderId);
        if (orders == null) {
            log.error("订单不存在，ID：{}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 2. 检查订单状态
        if (orders.getStatus() != Orders.STATUS_SHIPPED) {
            log.error("订单状态不正确，当前状态：{}", orders.getStatus());
            throw new BusinessException("订单状态不正确，不能确认收货");
        }
        
        // 3. 更新订单状态
        int result = ordersMapper.updateStatus(orderId, Orders.STATUS_COMPLETED);
        
        // 4. 更新物流状态
        if (result > 0) {
            LambdaQueryWrapper<Logistics> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Logistics::getOrderId, orderId);
            Logistics logistics = logisticsMapper.selectOne(queryWrapper);
            if (logistics != null) {
                logisticsMapper.updateLogisticsStatus(logistics.getId(), "delivered");
            }
        }
        
        return result > 0;
    }

    /**
     * 根据买家ID查询订单列表
     * @param buyerId 买家ID
     * @param status 订单状态，可选
     * @return 订单视图对象列表
     */
    @Override
    public List<OrderVO> getOrdersByBuyerId(Long buyerId, Integer status) {
        // 1. 查询订单列表
        List<Orders> ordersList;
        if (status != null) {
            LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Orders::getBuyerId, buyerId)
                    .eq(Orders::getStatus, status)
                    .orderByDesc(Orders::getCreateTime);
            ordersList = ordersMapper.selectList(queryWrapper);
        } else {
            ordersList = ordersMapper.getByBuyerId(buyerId);
        }
        
        // 2. 转换为订单视图对象列表
        List<OrderVO> orderVOList = new ArrayList<>();
        for (Orders order : ordersList) {
            OrderVO orderVO = getOrderById(order.getId());
            orderVOList.add(orderVO);
        }
        return orderVOList;
    }

    /**
     * 分页查询订单
     * @param page 页码
     * @param pageSize 每页大小
     * @param buyerId 买家ID，可选
     * @param status 订单状态，可选
     * @return 订单视图对象分页结果
     */
    @Override
    public Page<OrderVO> pageQuery(int page, int pageSize, Long buyerId, Integer status) {
        // 1. 构建分页对象
        Page<Orders> ordersPage = new Page<>(page, pageSize);
        
        // 2. 查询订单分页
        Page<Orders> resultPage = ordersMapper.pageQuery(ordersPage, buyerId, status);
        
        // 3. 构建订单视图对象分页结果
        Page<OrderVO> orderVOPage = new Page<>(page, pageSize);
        orderVOPage.setTotal(resultPage.getTotal());
        
        // 4. 转换为订单视图对象列表
        List<OrderVO> orderVOList = new ArrayList<>();
        for (Orders order : resultPage.getRecords()) {
            OrderVO orderVO = getOrderById(order.getId());
            orderVOList.add(orderVO);
        }
        
        orderVOPage.setRecords(orderVOList);
        
        return orderVOPage;
    }
    
    /**
     * 生成订单号
     * @return 订单号
     */
    private String generateOrderNumber() {
        // 生成格式：年月日时分秒 + 4位随机数
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomStr = UUID.randomUUID().toString().substring(0, 4);
        return dateStr + randomStr;
    }
} 