package com.sky.controller.admin;

import com.sky.context.BaseContext;
import com.sky.dto.RefundApprovalDTO;
import com.sky.dto.RefundQueryDTO;
import com.sky.exception.RefundException;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.RefundApplicationService;
import com.sky.vo.RefundApplicationVO;
import com.sky.vo.RefundStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 管理端退款管理控制器
 */
@RestController
@RequestMapping("/admin/refund")
@Slf4j
public class RefundManagementController {

    @Autowired
    private RefundApplicationService refundApplicationService;

    /**
     * 分页查询退款申请
     */
    @GetMapping("/page")
    public Result<PageResult> pageQuery(RefundQueryDTO queryDTO) {
        log.info("管理端分页查询退款申请: {}", queryDTO);
        
        List<RefundApplicationVO> records = refundApplicationService.pageQuery(queryDTO);
        Long total = refundApplicationService.countRefundApplications(queryDTO);
        
        return Result.success(new PageResult(total, records));
    }

    /**
     * 查询待审核的退款申请
     */
    @GetMapping("/pending")
    public Result<PageResult> getPendingApprovalApplications(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        log.info("查询待审核的退款申请，page: {}, pageSize: {}", page, pageSize);
        
        List<RefundApplicationVO> records = refundApplicationService.getPendingApprovalApplications(page, pageSize);
        
        // 计算总数
        RefundQueryDTO queryDTO = RefundQueryDTO.builder()
                .needApproval(1)
                .approvalStatus(1) // 待审核
                .build();
        Long total = refundApplicationService.countRefundApplications(queryDTO);
        
        return Result.success(new PageResult(total, records));
    }

    /**
     * 查询退款申请详情
     */
    @GetMapping("/{refundApplicationId}")
    public Result<RefundApplicationVO> getRefundApplication(@PathVariable Long refundApplicationId) {
        log.info("管理端查询退款申请详情: {}", refundApplicationId);
        
        RefundApplicationVO result = refundApplicationService.getRefundApplicationById(refundApplicationId);
        
        return Result.success(result);
    }

    /**
     * 审核退款申请
     */
    @PostMapping("/approve")
    public Result<String> approveRefundApplication(@RequestBody RefundApprovalDTO approvalDTO) {
        log.info("审核退款申请: {}", approvalDTO);
        log.info("approvalResult: {}", approvalDTO.getApprovalResult());

        // 参数验证
        if (approvalDTO.getRefundApplicationId() == null) {
            return Result.error("退款申请ID不能为空");
        }

        if (approvalDTO.getApprovalResult() == null) {
            return Result.error("审核结果不能为空");
        }

        if (!approvalDTO.getApprovalResult().equals(1) && !approvalDTO.getApprovalResult().equals(2)) {
            return Result.error("审核结果无效，1-通过，2-拒绝");
        }

        // 从JWT token中获取当前管理员ID，如果获取失败则使用默认管理员ID
        Long approverId = BaseContext.getCurrentId();
        if (approverId == null) {
            // 使用默认的系统管理员ID
            approverId = 1L;
            log.warn("无法从JWT token获取管理员ID，使用默认管理员ID: {}", approverId);
        }

        try {
            boolean success = refundApplicationService.approveRefundApplication(approvalDTO, approverId);

            if (success) {
                String message = approvalDTO.getApprovalResult().equals(1) ? "审核通过，退款处理成功" : "审核拒绝处理成功";
                return Result.success(message);
            } else {
                return Result.error("审核处理失败");
            }
        } catch (RefundException e) {
            log.error("退款业务异常", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("审核退款申请异常", e);
            String errorMessage = "审核失败";

            // 根据异常类型提供更具体的错误信息
            if (e.getMessage() != null) {
                if (e.getMessage().contains("微信退款API调用失败")) {
                    errorMessage = "微信退款接口调用失败，请检查网络连接或微信支付配置";
                } else if (e.getMessage().contains("私钥文件不存在")) {
                    errorMessage = "微信支付配置错误，私钥文件不存在";
                } else if (e.getMessage().contains("商户号未配置")) {
                    errorMessage = "微信支付配置错误，商户号未配置";
                } else {
                    errorMessage = "审核失败：" + e.getMessage();
                }
            }

            return Result.error(errorMessage);
        }
    }

    /**
     * 手动处理退款
     */
    @PostMapping("/process/{refundApplicationId}")
    public Result<String> processRefund(@PathVariable Long refundApplicationId) {
        log.info("手动处理退款: {}", refundApplicationId);
        
        try {
            boolean success = refundApplicationService.processRefund(refundApplicationId);
            
            if (success) {
                return Result.success("退款处理成功");
            } else {
                return Result.error("退款处理失败");
            }
        } catch (Exception e) {
            log.error("退款处理异常", e);
            return Result.error("退款处理异常：" + e.getMessage());
        }
    }

    /**
     * 根据退款单号查询详情
     */
    @GetMapping("/no/{refundNo}")
    public Result<RefundApplicationVO> getRefundApplicationByNo(@PathVariable String refundNo) {
        log.info("根据退款单号查询详情: {}", refundNo);
        
        RefundApplicationVO result = refundApplicationService.getRefundApplicationByNo(refundNo);
        
        return Result.success(result);
    }

    /**
     * 查询退款统计信息
     */
    @GetMapping("/statistics")
    public Result<RefundStatistics> getRefundStatistics(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime) {
        log.info("查询退款统计信息，startTime: {}, endTime: {}", startTime, endTime);

        try {
            // 解析时间参数
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            if (startTime != null && !startTime.trim().isEmpty()) {
                // 如果只有日期，补充时间为00:00:00
                if (startTime.length() == 10) {
                    startTime += " 00:00:00";
                }
                startDateTime = LocalDateTime.parse(startTime, formatter);
            }

            if (endTime != null && !endTime.trim().isEmpty()) {
                // 如果只有日期，补充时间为23:59:59
                if (endTime.length() == 10) {
                    endTime += " 23:59:59";
                }
                endDateTime = LocalDateTime.parse(endTime, formatter);
            }

            // 调用服务获取统计信息
            RefundStatistics statistics = refundApplicationService.getRefundStatistics(startDateTime, endDateTime);

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("查询退款统计信息失败", e);
            return Result.error("查询退款统计信息失败：" + e.getMessage());
        }
    }
}
