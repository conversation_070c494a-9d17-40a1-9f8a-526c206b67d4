package com.sky.dto.track17;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 17TRACK实时查询响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17RealtimeResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 运输商代码
     */
    private String carrierCode;

    /**
     * 运输商名称
     */
    private String carrierName;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 最新位置
     */
    private String latestLocation;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime actualDeliveryTime;

    /**
     * 发货时间
     */
    private LocalDateTime shipTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 物流轨迹详情列表
     */
    private List<Track17TraceDetail> traces;

    /**
     * 物流轨迹详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Track17TraceDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 轨迹时间
         */
        private LocalDateTime traceTime;

        /**
         * 轨迹位置
         */
        private String location;

        /**
         * 轨迹状态
         */
        private String status;

        /**
         * 轨迹描述
         */
        private String description;

        /**
         * 操作员
         */
        private String operator;

        /**
         * 联系电话
         */
        private String phone;
    }
}
