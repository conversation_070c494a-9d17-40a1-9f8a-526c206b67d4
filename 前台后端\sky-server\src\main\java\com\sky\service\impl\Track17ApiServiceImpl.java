package com.sky.service.impl;

import com.sky.dto.track17.Track17RealtimeRequest;
import com.sky.dto.track17.Track17RealtimeResponse;
import com.sky.service.Track17ApiService;
import com.sky.vo.Track17Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 17TRACK API服务实现类
 */
@Slf4j
@Service
public class Track17ApiServiceImpl implements Track17ApiService {

    @Value("${track17.api.key:}")
    private String apiKey;

    @Value("${track17.api.url:https://api.17track.net/track/v2.2}")
    private String apiUrl;

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public Track17Response<Track17RealtimeResponse> getRealtimeTracking(Track17RealtimeRequest request) {
        log.info("获取实时物流信息，物流单号：{}，运输商：{}", request.getTrackingNumber(), request.getCarrierCode());

        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (apiKey != null && !apiKey.isEmpty()) {
                headers.set("17token", apiKey);
            }

            // 构建请求体
            String requestBody = buildRequestBody(request);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    apiUrl + "/gettrackinfo",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                // 解析响应（这里简化处理，实际应该解析JSON）
                Track17RealtimeResponse trackingResponse = parseResponse(response.getBody(), request);
                return Track17Response.success(trackingResponse);
            } else {
                log.error("17TRACK API调用失败，状态码：{}", response.getStatusCode());
                return Track17Response.error("物流信息查询失败");
            }

        } catch (Exception e) {
            log.error("调用17TRACK API异常", e);
            return Track17Response.error("物流信息查询异常：" + e.getMessage());
        }
    }

    @Override
    public Track17Response<List<Track17RealtimeResponse>> getBatchRealtimeTracking(List<Track17RealtimeRequest> requests) {
        log.info("批量获取实时物流信息，数量：{}", requests.size());

        List<Track17RealtimeResponse> responses = new ArrayList<>();
        for (Track17RealtimeRequest request : requests) {
            Track17Response<Track17RealtimeResponse> response = getRealtimeTracking(request);
            if (response.getSuccess() && response.getData() != null) {
                responses.add(response.getData());
            }
        }

        return Track17Response.success(responses);
    }

    @Override
    public Track17Response<Track17RealtimeResponse> getTrackingByNumber(String trackingNumber, String carrierCode) {
        Track17RealtimeRequest request = Track17RealtimeRequest.builder()
                .trackingNumber(trackingNumber)
                .carrierCode(carrierCode)
                .language("cn")
                .detailed(true)
                .build();

        return getRealtimeTracking(request);
    }

    @Override
    public Track17Response<List<String>> detectCarrier(String trackingNumber) {
        log.info("检测运输商，物流单号：{}", trackingNumber);

        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (apiKey != null && !apiKey.isEmpty()) {
                headers.set("17token", apiKey);
            }

            // 构建请求体
            String requestBody = String.format("[{\"number\":\"%s\"}]", trackingNumber);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    apiUrl + "/detectcarrier",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                // 解析响应（这里简化处理）
                List<String> carriers = parseCarrierResponse(response.getBody());
                return Track17Response.success(carriers);
            } else {
                log.error("17TRACK运输商检测失败，状态码：{}", response.getStatusCode());
                return Track17Response.error("运输商检测失败");
            }

        } catch (Exception e) {
            log.error("调用17TRACK运输商检测API异常", e);
            return Track17Response.error("运输商检测异常：" + e.getMessage());
        }
    }

    @Override
    public Track17Response<List<String>> getSupportedCarriers() {
        log.info("获取支持的运输商列表");

        // 返回常用的运输商列表
        List<String> carriers = Arrays.asList(
                "sf", "ems", "yto", "sto", "zto", "yt", "jt",
                "usps", "ups", "fedex", "dhl", "chinapost"
        );

        return Track17Response.success(carriers);
    }

    /**
     * 构建请求体
     */
    private String buildRequestBody(Track17RealtimeRequest request) {
        return String.format(
                "[{\"number\":\"%s\",\"carrier\":\"%s\",\"lang\":\"%s\"}]",
                request.getTrackingNumber(),
                request.getCarrierCode(),
                request.getLanguage()
        );
    }

    /**
     * 解析响应（简化实现）
     */
    private Track17RealtimeResponse parseResponse(String responseBody, Track17RealtimeRequest request) {
        // 这里应该解析JSON响应，简化处理返回模拟数据
        log.info("解析17TRACK响应：{}", responseBody);

        return Track17RealtimeResponse.builder()
                .trackingNumber(request.getTrackingNumber())
                .carrierCode(request.getCarrierCode())
                .carrierName(getCarrierName(request.getCarrierCode()))
                .status("InTransit")
                .statusDescription("运输途中")
                .latestLocation("分拣中心")
                .lastUpdateTime(LocalDateTime.now())
                .traces(new ArrayList<>())
                .build();
    }

    /**
     * 解析运输商响应
     */
    private List<String> parseCarrierResponse(String responseBody) {
        // 这里应该解析JSON响应，简化处理
        log.info("解析运输商检测响应：{}", responseBody);
        return Arrays.asList("sf", "ems");
    }

    /**
     * 获取运输商名称
     */
    private String getCarrierName(String carrierCode) {
        switch (carrierCode.toLowerCase()) {
            case "sf": return "顺丰速运";
            case "ems": return "中国邮政EMS";
            case "yto": return "圆通速递";
            case "sto": return "申通快递";
            case "zto": return "中通快递";
            case "yt": return "韵达速递";
            case "jt": return "极兔速递";
            case "usps": return "美国邮政";
            case "ups": return "UPS";
            case "fedex": return "联邦快递";
            case "dhl": return "DHL";
            case "chinapost": return "中国邮政";
            default: return carrierCode.toUpperCase();
        }
    }
}
