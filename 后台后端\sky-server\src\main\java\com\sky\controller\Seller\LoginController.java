package com.sky.controller.Seller;

import com.sky.Utils.RandomNumberGenerator;
import com.sky.constant.JwtClaimsConstant;
import com.sky.dto.*;
import com.sky.entity.Buyer;
import com.sky.entity.EVPI;
import com.sky.entity.Seller;
import com.sky.exception.MessageNotFound;
import com.sky.properties.JwtProperties;
import com.sky.result.Result;
import com.sky.service.LoginService;
import com.sky.service.MsmService;
import com.sky.utils.JwtUtil;
import com.sky.vo.BuyerLoginVO;
import com.sky.vo.EVPIVO;
import com.sky.vo.SellerLoginVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/seller")
@Api(tags = "卖家相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class LoginController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private JwtProperties jwtProperties;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MsmService msmService;

    /**
     * 卖家注册
     *
     * @param sellerRegisterDTO
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE, value = "/register")
    @ApiOperation("卖家注册")
    @Transactional(rollbackFor = Exception.class)
    public Result register(@RequestPart SellerRegisterDTO sellerRegisterDTO,
                           @RequestPart("license") MultipartFile licenseFile,
                           @RequestPart("warehouseFiles1") MultipartFile warehouseFiles1,
                           @RequestPart("warehouseFiles2") MultipartFile warehouseFiles2,
                           @RequestPart("warehouseFiles3") MultipartFile warehouseFiles3,
                           @RequestPart("IDCard1") MultipartFile IDCard1,
                           @RequestPart("IDCard2") MultipartFile IDCard2
    ) {
        log.info("卖家注册：{}", sellerRegisterDTO);
        validateFiles(licenseFile, warehouseFiles1, warehouseFiles2, warehouseFiles3, IDCard1, IDCard2);
        loginService.loginRegister(sellerRegisterDTO, licenseFile, warehouseFiles1, warehouseFiles2, warehouseFiles3, IDCard1, IDCard2);
        return Result.success();
    }
    @PostMapping("/register/sendCode")
    @ApiOperation("用户注册发送验证码")
    public Result sendMessage(@RequestBody MsmDTO msmDTO) throws Exception {
//        String phone = msmDTO.getPhone();
//
//        String code = stringRedisTemplate.opsForValue().get(phone);
//        if (!ObjectUtils.isEmpty(code)) {
//            Long expireTime = stringRedisTemplate.getExpire(phone, TimeUnit.MINUTES);
//            if (expireTime != null && expireTime > 0) {
//                //return Result.error("验证码已经发送，请于" + 2 + "分钟后重试");
//            }
//
//        }
//        //自己生成一个验证码
//        code = RandomNumberGenerator.generateRandomNumber();
//        int IntCode = Integer.parseInt(code);
//        //调用发送短信的服务
//        Boolean sentMessage = msmService.sendMessage(phone, IntCode);
//        //存入redis
//        if (sentMessage) {
//            stringRedisTemplate.opsForValue().set(phone, code, 5, TimeUnit.MINUTES);
//            return Result.success("验证码发送成功");
//        }
//        return Result.error("短信发送失败");
        String Address = msmDTO.getAddress();
        String code = stringRedisTemplate.opsForValue().get(Address);
        Long expireTime = stringRedisTemplate.getExpire(Address, TimeUnit.SECONDS);
//        if (!ObjectUtils.isEmpty(code)) {
//            if (expireTime != null && expireTime > 0) {
//                return Result.error("验证码已经发送至邮箱，请于" + expireTime + "秒后重试");
//            }
//        }
        code = RandomNumberGenerator.generateRandomNumber();
        int IntCode = Integer.parseInt(code);
        //调用发送短信的服务
        Boolean sentMessage = msmService.sendMessage(Address, IntCode);
        if (sentMessage) {
            stringRedisTemplate.opsForValue().set(Address, code, 15, TimeUnit.MINUTES);
            return Result.success("验证码发送成功");
        }
        return Result.error("短信发送失败");
    }

    @PutMapping("/change")
    public Result change (@RequestParam Long id){
        Seller seller = loginService.getSellerData(id);
        seller.setAccountStatus(1);
        loginService.updata(seller);
        return Result.success();
    }

    /**
     * 卖家登录
     *
     * @param sellerLoginDTO
     * @return
     */
    @PostMapping("/login")
    @ApiOperation("卖家登录")
    public Result<SellerLoginVO> login(@RequestBody SellerLoginDTO sellerLoginDTO) {
        log.info("卖家登录：{}", sellerLoginDTO);
        Seller seller = loginService.login(sellerLoginDTO);

        //登录成功后，生成jwt令牌
        Map<String, Object> claims = new HashMap<>();
        claims.put(JwtClaimsConstant.EMP_ID, seller.getId());
        String token = JwtUtil.createJWT(
                jwtProperties.getAdminSecretKey(),
                jwtProperties.getAdminTtl(),
                claims);
        SellerLoginVO sellerLoginVO = SellerLoginVO.builder()
                .id(seller.getId())
                .accountName(seller.getAccountName())
                .token(token)
                .Permission(seller.getPermission())
                .build();

        LocalDateTime lastLoginTime = seller.getLastLoginTime();
        Boolean isWithinLast24Hours = isWithinLast24Hours(lastLoginTime);
//        if(isWithinLast24Hours){
//            seller.setLastLoginTime(LocalDateTime.now());
//            loginService.updata(seller);
//            return Result.success(sellerLoginVO);
//        }else {
//            return Result.error("EMAIL");
//        }
        return Result.success(sellerLoginVO);
//        return Result.error("EMAIL");

    }
    public static boolean isWithinLast24Hours(LocalDateTime givenTime) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        // 计算时间差(单位为秒)
        long diffInSeconds = ChronoUnit.SECONDS.between(givenTime, now);
        // 如果时间差小于等于86400秒(24小时)，则返回true
        return diffInSeconds <= 86400 && diffInSeconds >= 0;
    }

    /**
     * 完善信息
     */
    /*    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE, value = "/data")
        public Result<EVPI> createShop(
                @RequestPart EVPIDTO evpidto,
                @RequestPart("license") MultipartFile licenseFile,
                @RequestPart("warehouse") MultipartFile[] warehouseFiles) {

            validateFiles(licenseFile, warehouseFiles);
            loginService.ImpShopData(evpidto, licenseFile, warehouseFiles);
            return Result.success();
        }
*/
    private void validateFiles(MultipartFile license, MultipartFile warehouseFiles1, MultipartFile warehouseFiles2, MultipartFile warehouseFiles3, MultipartFile IDCard1,
                               MultipartFile IDCard2) {
        if (license.isEmpty()) {
            throw new MessageNotFound("必须上传营业执照");
        }
        if (warehouseFiles1.isEmpty() || warehouseFiles2.isEmpty() || warehouseFiles3.isEmpty()) {
            throw new MessageNotFound("仓库照片不能为空");
        }
        if (IDCard1.isEmpty() || IDCard2.isEmpty()) {
            throw new MessageNotFound("身份证正反面不能为空");
        }
    }

    @GetMapping("/{id}")
    public Result<EVPI> getShopData(@PathVariable Long id) {
        return Result.success(loginService.getShopData(id));
    }

    @PostMapping("/logout")
    public Result logout() {
        return Result.success();
    }
    @PostMapping("/PassWord")
    @ApiOperation("更改密码")
    public Result ChangePassWord(@RequestBody PassWordDTO passWordDTO) {
        log.info("passWordDTO:{}", passWordDTO);
        loginService.ChangePassWord(passWordDTO);
        return Result.success();
    }

    @PostMapping("/sendEmail")
    public Result sendEmail(@RequestBody SellerLoginDTO sellerLoginDTO) {
        log.info("开始验证邮箱状态");
        loginService.loginByEmail(sellerLoginDTO);
        return Result.success();
    }

    @PostMapping("/EmailLogin")
    public Result loginByEmail(@RequestBody SellerLoginDTO sellerLoginDTO) {
        log.info("卖家登录：{}", sellerLoginDTO);
        Seller seller = loginService.login1(sellerLoginDTO);

        //登录成功后，生成jwt令牌
        Map<String, Object> claims = new HashMap<>();
        claims.put(JwtClaimsConstant.EMP_ID, seller.getId());
        String token = JwtUtil.createJWT(
                jwtProperties.getAdminSecretKey(),
                jwtProperties.getAdminTtl(),
                claims);
        SellerLoginVO sellerLoginVO = SellerLoginVO.builder()
                .id(seller.getId())
                .accountName(seller.getAccountName())
                .token(token)
                .Permission(seller.getPermission())
                .build();

     return Result.success(sellerLoginVO);

    }




}
