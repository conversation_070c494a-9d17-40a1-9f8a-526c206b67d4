package com.sky.controller.admin;


import com.sky.entity.OrderDetail;
import com.sky.mapper.OrderDetailMapper;
import com.sky.result.Result;
import com.sky.service.OrderService;
import com.sky.vo.OrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单详情控制器
 */
@RestController
@RequestMapping("/orderDetail")
@Api(tags = "订单详情相关接口")
@Slf4j
public class OrderDetailController {

    @Autowired
    private OrderDetailMapper orderDetailMapper;
    
    @Autowired
    private OrderService orderService;

    /**
     * 根据订单ID查询订单详情
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation("根据订单ID查询订单详情")
    public Result<List<OrderDetail>> getByOrderId(@PathVariable("orderId") Long orderId) {
        log.info("根据订单ID查询订单详情：orderId={}", orderId);
        List<OrderDetail> orderDetails = orderDetailMapper.getByOrderId(orderId);
        return Result.success(orderDetails);
    }

    /**
     * 根据订单ID查询完整订单信息（包含商品、订单、地址等详细信息）
     */
    @GetMapping("/{orderId}")
    @ApiOperation("根据订单ID查询完整订单信息（包含商品、订单、地址等详细信息）")
    public Result<OrderVO> getOrderDetailById(@PathVariable("orderId") Long orderId) {
        log.info("根据订单ID查询完整订单信息：orderId={}", orderId);
        // 调用订单服务获取完整订单信息
        OrderVO orderVO = orderService.getOrderById(orderId);
        return Result.success(orderVO);
    }
} 