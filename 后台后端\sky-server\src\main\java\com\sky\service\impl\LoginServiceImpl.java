package com.sky.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.sky.Utils.OssUtils;
import com.sky.Utils.RandomNumberGenerator;
import com.sky.Utils.SmsSendLoginEmail;
import com.sky.constant.MessageConstant;
import com.sky.constant.StatusConstant;
import com.sky.dto.EVPIDTO;
import com.sky.dto.PassWordDTO;
import com.sky.dto.SellerLoginDTO;
import com.sky.dto.SellerRegisterDTO;
import com.sky.entity.*;
import com.sky.enumeration.RoleEnum;
import com.sky.enumeration.StatusEnum;
import com.sky.exception.*;
import com.sky.mapper.LoginMapper;
import com.sky.mapper.RoleMapper;
import com.sky.mapper.SellerAndRoleMapper;
import com.sky.service.LoginService;
import com.sky.service.SellerPermissionService;
import com.sky.vo.EVPIVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class LoginServiceImpl implements LoginService {
    @Autowired
    private LoginMapper loginMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private SellerAndRoleMapper sellerAndRoleMapper;

    @Autowired
    private SellerPermissionService sellerPermissionService;

    @Autowired
    private SmsSendLoginEmail smsSendLoginEmail;

    /**
     * 卖家注册
     * @param sellerRegisterDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public void loginRegister(SellerRegisterDTO sellerRegisterDTO, MultipartFile licenseFile, MultipartFile warehouseFiles1, MultipartFile warehouseFiles2, MultipartFile warehouseFiles3, MultipartFile IDCard1,
                              MultipartFile IDCard2) {
        EVPI evpi = new EVPI();
        BeanUtils.copyProperties(sellerRegisterDTO, evpi);
        if (sellerRegisterDTO == null) {
            throw new RegisterNotNullException(MessageConstant.REGISTER_NOT_NULL);
        }
        if (sellerRegisterDTO.getAccountName() == null || sellerRegisterDTO.getAccountName().isEmpty()) {
            throw new AccountNotFoundException("请先填写账号字段信息");
        }

        Seller sellerList= loginMapper.getByName(sellerRegisterDTO.getAccountName());

        if (sellerList!=null) {
            throw new AccountIsExitException(MessageConstant.ACCOUNT_IS_EXIT);
        }

        String email = sellerRegisterDTO.getEmail();
        String accountName = sellerRegisterDTO.getAccountName();
        Seller buyer02 = loginMapper.getByAccountName(accountName);
        if(buyer02 != null) {
            throw new AccountIsExitException(MessageConstant.ACCOUNT_IS_EXIT);
        }

        Seller buyer01 = loginMapper.getByEmail(email);
        if (buyer01!=null) {
            throw new AccountIsExitException("该邮箱已注册过账户");
        }
        String ver = stringRedisTemplate.opsForValue().get(email);
        String verificationCode = sellerRegisterDTO.getVerificationCode();
        if(ver == null || ver.isEmpty()) {
            throw new AccountIsExitException("请先获取验证码");
        }
        if(!ver.equals(verificationCode)) {
            throw new AccountIsExitException("验证码输入错误");
        }
        Seller seller = new Seller();
        BeanUtils.copyProperties(sellerRegisterDTO, seller);
        seller.setAccountStatus(1);
        seller.setCreateTime(LocalDateTime.now());
        seller.setPhotoUrl("https://hiram.oss-cn-beijing.aliyuncs.com/%E9%BB%98%E8%AE%A4%E5%A4%B4%E5%83%8F/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250302163653.jpg");
        // 校验营业执照唯一性
        if (loginMapper.existsByBusinessImg(evpi.getBusinessImgUrl())) {
            throw new BusinessException("营业执照已存在");
        }

        if (loginMapper.existsByIDCardImg1(evpi.getIDCard1())||loginMapper.existsByIDCardImg2(evpi.getIDCard2()))
        {
            throw new BusinessException("身份证正反照已存在");
        }

        // 上传营业执照
        String licenseUrl = ossUtils.uploadOneFile(licenseFile);
        String warehouseUrls1 = ossUtils.uploadOneFile(warehouseFiles1);
        String warehouseUrls2 = ossUtils.uploadOneFile(warehouseFiles2);
        String warehouseUrls3 = ossUtils.uploadOneFile(warehouseFiles3);
        String IDCardUrls1 = ossUtils.uploadOneFile(IDCard1);
        String IDCardUrls2 = ossUtils.uploadOneFile(IDCard2);

        evpi.setBusinessImgUrl(licenseUrl);
        evpi.setWarehouseImgUrl1(warehouseUrls1);
        evpi.setWarehouseImgUrl2(warehouseUrls2);
        evpi.setWarehouseImgUrl3(warehouseUrls3);
        evpi.setIDCard1(IDCardUrls1);
        evpi.setIDCard2(IDCardUrls2);
        evpi.setCreatedTime(LocalDateTime.now());
        evpi.setLastUpdated(LocalDateTime.now());
        seller.setAccountStatus(0);
        seller.setUserRole("普通用户");
        seller.setLastLoginTime(LocalDateTime.now());
        loginMapper.save(seller);
        Long id = seller.getId();
        evpi.setSellerId(id);
        Set<String> defaultPermissions = new HashSet<>(Arrays.asList(
                "login",
                "dashboard",
                "ProductList",
                "declaration",
                "register-step1",
                "register-step2",
                "main",
                "Product"
        ));
        List<SellerPermission> collect = defaultPermissions.stream().map(s -> (new SellerPermission().setSellerId(id).setPermissionCode(s))).collect(Collectors.toList());
        sellerPermissionService.saveBatch(collect);
        //验证唯一性
        if (loginMapper.selectBySellerId(evpi.getSellerId())>1){
            throw new BusinessException("店铺已存在");
        }
        loginMapper.saveData(evpi);


    }


    @Override
    @Transactional
    public void loginByEmail(SellerLoginDTO sellerLoginDTO) {
        String username = sellerLoginDTO.getAccountName();
        //1、根据用户名查询数据库中的数据
        Seller seller = loginMapper.getByName(username);
        String email = seller.getEmail();
        String loginDTOEmail = sellerLoginDTO.getEmail();
        //2、处理各种异常情况（用户名不存在、密码不对、账号被锁定）

        if(!email.equals(loginDTOEmail)) {
            throw new AccountNotFoundException("输入邮箱与账号绑定邮箱不符，请重试");
        }
        Long expire = stringRedisTemplate.getExpire(loginDTOEmail, TimeUnit.SECONDS);
        if(expire != null && expire > 0){
            throw new AccountNotFoundException("验证码已发送，请于:"+expire+"秒后尝试");
        }


        RandomNumberGenerator randomNumberGenerator = new RandomNumberGenerator();
        String s = randomNumberGenerator.generateRandomNumber();
        Integer code = Integer.valueOf(s);
        try {
            stringRedisTemplate.opsForValue().set(loginDTOEmail,s,5, TimeUnit.MINUTES);
            smsSendLoginEmail.sendEmail(loginDTOEmail,code);
        } catch (Exception e) {
            throw new AccountNotFoundException("验证码发送异常，请稍后重试或联系工作人员");
        }
        //3、返回实体对象
    }

    @Override
    public Seller login1(SellerLoginDTO sellerLoginDTO) {
        String username = sellerLoginDTO.getAccountName();
        //1、根据用户名查询数据库中的数据
        Seller seller = loginMapper.getByName(username);
        String email = sellerLoginDTO.getEmail();
        String s1 = stringRedisTemplate.opsForValue().get(email);
        String code = sellerLoginDTO.getCode();
        if(s1 == null) {
            throw new AccountNotFoundException("验证码信息不存在请重新获取验证码");
        }
        if(!s1.equals(code)) {
            throw new AccountNotFoundException("验证码输入错误");
        }
        //2、处理各种异常情况（用户名不存在、密码不对、账号被锁定）
        seller.setLastLoginTime(LocalDateTime.now());
        List<SellerPermission> list = Db.lambdaQuery(SellerPermission.class).eq(SellerPermission::getSellerId, seller.getId()).list();
        List<String> collect = list.stream().map(s -> s.getPermissionCode()).collect(Collectors.toList());
        seller.setPermission(collect);
        loginMapper.update(seller);
        //3、返回实体对象
        return seller;
    }

    /**
     * 卖家登录
     * @param sellerLoginDTO
     * @return
     */
    @Override
    public Seller login(SellerLoginDTO sellerLoginDTO) {
        String username = sellerLoginDTO.getAccountName();
        String password = sellerLoginDTO.getPassword();
        //1、根据用户名查询数据库中的数据
        Seller seller = loginMapper.getByName(username);

        //2、处理各种异常情况（用户名不存在、密码不对、账号被锁定）
        if (seller == null) {
            //账号不存在
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        //密码比对
        if (!password.equals(seller.getPassword())) {
            //密码错误
            throw new PasswordErrorException(MessageConstant.PASSWORD_ERROR);
        }

        if (seller.getAccountStatus() == StatusConstant.DISABLE) {
            //账号被锁定
            throw new AccountLockedException(MessageConstant.ACCOUNT_LOCKED);
        }
        List<SellerPermission> list = Db.lambdaQuery(SellerPermission.class).eq(SellerPermission::getSellerId, seller.getId()).list();
        List<String> collect = list.stream().map(s -> s.getPermissionCode()).collect(Collectors.toList());
        seller.setPermission(collect);
        //3、返回实体对象
        return seller;
    }



    @Override
    public EVPI getShopData(Long id) {

        return loginMapper.getData(id);
    }

    @Override
    public Seller getSellerData(Long id) {
        Seller seller = loginMapper.getByid(id);
        return seller;
    }

    @Override
    public void updata(Seller seller) {
        loginMapper.update(seller);
    }

    @Override
    public void ChangePassWord(PassWordDTO passWordDTO) {
        String id = passWordDTO.getId();
        Long l = Long.valueOf(id);
        Seller seller = loginMapper.getByid(l);
        String password = seller.getPassword();
        String oldPassword = passWordDTO.getOldPassword();
        if (!oldPassword .equals(password)) {
            throw new AccountNotFoundException("旧密码输入错误");
        }
        seller.setPassword(passWordDTO.getNewPassword());
        loginMapper.update(seller);
    }
}
