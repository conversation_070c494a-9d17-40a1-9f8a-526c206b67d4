package com.sky.entity;

import java.math.BigDecimal;

public class LeaderStats {
    private int totalLeaders;
    private int activeLeaders;
    private int pendingLeaders;
    private int disabledLeaders;
    private int totalTeamMembers;
    private BigDecimal totalCommission;

    // Getters and Setters
    public int getTotalLeaders() {
        return totalLeaders;
    }

    public void setTotalLeaders(int totalLeaders) {
        this.totalLeaders = totalLeaders;
    }

    public int getActiveLeaders() {
        return activeLeaders;
    }

    public void setActiveLeaders(int activeLeaders) {
        this.activeLeaders = activeLeaders;
    }

    public int getPendingLeaders() {
        return pendingLeaders;
    }

    public void setPendingLeaders(int pendingLeaders) {
        this.pendingLeaders = pendingLeaders;
    }

    public int getDisabledLeaders() {
        return disabledLeaders;
    }

    public void setDisabledLeaders(int disabledLeaders) {
        this.disabledLeaders = disabledLeaders;
    }

    public int getTotalTeamMembers() {
        return totalTeamMembers;
    }

    public void setTotalTeamMembers(int totalTeamMembers) {
        this.totalTeamMembers = totalTeamMembers;
    }

    public BigDecimal getTotalCommission() {
        return totalCommission;
    }

    public void setTotalCommission(BigDecimal totalCommission) {
        this.totalCommission = totalCommission;
    }
}


