package com.sky.service.impl;

import com.sky.entity.ProductFavorite;
import com.sky.mapper.ProductFavoriteMapper;
import com.sky.service.ProductFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ProductFavoriteServiceImpl implements ProductFavoriteService {

    @Autowired
    private ProductFavoriteMapper productFavoriteMapper;

    @Override
    public void addFavorite(ProductFavorite productFavorite) {
        productFavorite.setCollectTime(LocalDateTime.now());
        productFavoriteMapper.insert(productFavorite);
    }

    @Override
    public List<ProductFavorite> getFavoritesByBuyerId(Long buyerId) {
        return productFavoriteMapper.findByBuyerId(buyerId);
    }

    @Override
    public void cancelFavorite(Long id) {
        productFavoriteMapper.deleteById(id);
    }
}
