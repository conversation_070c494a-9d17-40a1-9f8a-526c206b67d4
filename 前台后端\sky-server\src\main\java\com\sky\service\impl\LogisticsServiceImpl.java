package com.sky.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.sky.dto.LogisticsInfoDTO;
import com.sky.entity.Logistics;
import com.sky.mapper.LogisticsMapper;
import com.sky.service.LogisticsService;
import com.sky.vo.LogisticsInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 物流服务实现类
 */
@Service
@Slf4j
public class LogisticsServiceImpl extends ServiceImpl<LogisticsMapper, Logistics> implements LogisticsService {

    @Autowired
    private LogisticsMapper logisticsMapper;

    /**
     * 创建物流信息
     * @param logisticsInfoDTO 物流信息DTO
     */
    @Override
    @Transactional
    public void createLogistics(LogisticsInfoDTO logisticsInfoDTO) {
        log.info("创建物流信息: {}", logisticsInfoDTO);
        
        // 构建物流实体
        Logistics logistics = Logistics.builder()
                .logisticsNumber(logisticsInfoDTO.getLogisticsNumber())
                .orderId(logisticsInfoDTO.getOrderId().longValue())
                .logisticsCompany(logisticsInfoDTO.getLogisticsCompany())
                .logisticsStatus(logisticsInfoDTO.getLogisticsStatus())
                .createTime(LocalDateTime.now())
                .build();
        
        // 保存物流信息
        save(logistics);
    }

    /**
     * 获取物流信息
     * @param id 物流ID
     * @return 物流信息VO
     */
    @Override
    public LogisticsInfoVO getLogisticsInfo(Long id) {
        log.info("查询物流信息, id: {}", id);
        
        // 查询物流信息
        Logistics logistics = logisticsMapper.getById(id);
        
        // 转换为VO对象
        LogisticsInfoVO logisticsInfoVO = new LogisticsInfoVO();
        if (logistics != null) {
            BeanUtils.copyProperties(logistics, logisticsInfoVO);
            logisticsInfoVO.setId(logistics.getId().intValue());
            logisticsInfoVO.setOrderId(logistics.getOrderId().intValue());
        }
        
        return logisticsInfoVO;
    }

    /**
     * 更新物流状态
     * @param id 物流ID
     * @param logisticsStatus 物流状态
     */
    @Override
    @Transactional
    public void updateLogistics(Long id, String logisticsStatus) {
        log.info("更新物流状态, id: {}, status: {}", id, logisticsStatus);
        
        // 更新物流状态
        logisticsMapper.updateLogisticsStatus(id, logisticsStatus);
        
        // 如果是已发货状态，更新发货时间
        if ("已发货".equals(logisticsStatus)) {
            Logistics logistics = getById(id);
            if (logistics != null) {
                logistics.setShippingDate(LocalDateTime.now());
                updateById(logistics);
            }
        }
    }
} 