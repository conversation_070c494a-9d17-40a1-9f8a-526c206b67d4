package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.annotation.AutoFill;
import com.sky.dto.ProductPageQueryDTO;
import com.sky.entity.PmsProduct;
import com.sky.entity.Product;
import com.sky.enumeration.OperationType;
import com.sky.vo.ProductVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ProductMapper {

    /**
     * 根据分类id查询产品数量
     *
     * @param categoryId
     * @return
     */
    @Select("select count(id) from product where category_id = #{categoryId}")
    Integer countByCategoryId(Long categoryId);

    /**
     * 插入菜品数据
     *
     * @param product
     */
    @AutoFill(value = OperationType.INSERT)
    void insert(Product product);

    /**
     * 产品分页查询
     *
     * @param productPageQueryDTO
     * @return
     */
    Page<ProductVO> pageQuery(ProductPageQueryDTO productPageQueryDTO);

    /**
     * 根据主键查询菜品
     *
     * @param id
     * @return
     */
    @Select("select * from ry_mall.pms_product where id = #{id}")
    PmsProduct getById(Long id);


    /**
     * 根据id动态修改产品数据
     *
     * @param product
     */
    @AutoFill(value = OperationType.UPDATE)
    void update(Product product);

    /**
     * 根据分类id查询菜品
     *
     * @param categoryId
     * @return
     */
    @Select("select * from ry_mall.pms_product where category_id = #{categoryId}")
    List<PmsProduct> getByCategoryId(Long categoryId);


    @Select("select * from ry_mall.pms_product")
    List<PmsProduct> getByCategoryId1(Long categoryId);
}
