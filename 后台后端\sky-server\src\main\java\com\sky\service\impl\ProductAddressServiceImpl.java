package com.sky.service.impl;

import com.sky.entity.BusinessException;
import com.sky.entity.ProductAddress;
import com.sky.mapper.ProductAddressMapper;
import com.sky.service.ProductAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductAddressServiceImpl implements ProductAddressService {

    @Autowired
    private ProductAddressMapper addressMapper;

    @Override
    public void addAddress(ProductAddress address) {
        // 校验必填字段
        if (address.getBuyerId() == null) {
            throw new BusinessException("买家 ID 未填写");
        }
        if (address.getName() == null || address.getName().isEmpty()) {
            throw new BusinessException("收货人姓名未填写");
        }
        if (address.getPhoneNumber() == null || address.getPhoneNumber().isEmpty()) {
            throw new BusinessException("手机号未填写");
        }
        if (address.getAddressDetail() == null || address.getAddressDetail().isEmpty()) {
            throw new BusinessException("地址详情未填写");
        }
        if (address.getCity() == null || address.getCity().isEmpty()) {
            throw new BusinessException("城市未填写");
        }
        if (address.getProvince() == null || address.getProvince().isEmpty()) {
            throw new BusinessException("省份未填写");
        }
        if (address.getPostalCode() == null || address.getPostalCode().isEmpty()) {
            throw new BusinessException("邮政编码未填写");
        }
        if (address.getCountry() == null || address.getCountry().isEmpty()) {
            throw new BusinessException("国家未填写");
        }

        // 默认设置为非默认地址
        if (address.getIsDefault() == null) {
            address.setIsDefault(0);
        }
        addressMapper.insert(address);
    }

    @Override
    public List<ProductAddress> getAllAddresses(ProductAddress productAddress) {
        Long buyerId = productAddress.getBuyerId();
        if (buyerId == null) {
            throw new BusinessException("用户未登录，无法获取地址列表");
        }
        return addressMapper.findByBuyerId(buyerId);
    }

    @Override
    public void updateAddress(ProductAddress address) {
        if (address.getId() == null) {
            throw new BusinessException("地址 ID 未填写");
        }
        if ((address.getName() == null || address.getName().isEmpty()) ||
                (address.getPhoneNumber() == null || address.getPhoneNumber().isEmpty()) ||
                (address.getAddressDetail() == null || address.getAddressDetail().isEmpty()) ||
                (address.getCity() == null || address.getCity().isEmpty()) ||
                (address.getProvince() == null || address.getProvince().isEmpty()) ||
                (address.getPostalCode() == null || address.getPostalCode().isEmpty()) ||
                (address.getCountry() == null || address.getCountry().isEmpty())) {
            throw new BusinessException("所有字段更新后都不能为空");
        }
        addressMapper.update(address);
    }

    @Override
    public void deleteAddress(Long id) {
        addressMapper.deleteById(id);
    }
}