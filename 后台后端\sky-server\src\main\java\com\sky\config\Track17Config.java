package com.sky.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 17TRACK配置类
 */
@Configuration
@ConfigurationProperties(prefix = "track17")
@Data
public class Track17Config {

    /**
     * API配置
     */
    private Api api = new Api();

    /**
     * Webhook配置
     */
    private Webhook webhook = new Webhook();

    /**
     * 业务配置
     */
    private Business business = new Business();

    @Data
    public static class Api {
        /**
         * API密钥
         */
        private String token;

        /**
         * API基础URL
         */
        private String baseUrl = "https://api.17track.net/track/v2.2";

        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 10000;

        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 30000;

        /**
         * 最大重试次数
         */
        private int maxRetryCount = 3;

        /**
         * 请求频率限制（请求/秒）
         */
        private int rateLimit = 3;
    }

    @Data
    public static class Webhook {
        /**
         * Webhook接收地址
         */
        private String url;

        /**
         * Webhook密钥
         */
        private String secret;

        /**
         * 是否启用Webhook
         */
        private boolean enabled = true;

        /**
         * Webhook重试次数
         */
        private int maxRetryCount = 3;
    }

    @Data
    public static class Business {
        /**
         * 是否自动注册物流单号
         */
        private boolean autoRegister = true;

        /**
         * 默认翻译语言
         */
        private String defaultLang = "zh-hans";

        /**
         * 是否启用翻译
         */
        private boolean enableTranslation = false;

        /**
         * 是否启用邮件通知
         */
        private boolean enableEmailNotification = false;

        /**
         * 默认运输商代码
         */
        private Integer defaultCarrierCode = 3011;

        /**
         * 是否启用运输商自动检测
         */
        private boolean autoDetection = true;

        /**
         * 物流状态同步间隔（小时）
         */
        private int syncInterval = 6;
    }
}
