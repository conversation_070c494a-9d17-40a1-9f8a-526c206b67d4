<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrderSettlementInfoMapper">

    <resultMap id="BaseResultMap" type="com.sky.entity.OrderSettlementInfo">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="seller_id" property="sellerId"/>
        <result column="seller_name" property="sellerName"/>
        <result column="pay_time" property="payTime"/>
        <result column="billing_date" property="billingDate"/>
        <result column="billing_cycle" property="billingCycle"/>
        <result column="settlement_date" property="settlementDate"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="settlement_amount" property="settlementAmount"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="settlement_time" property="settlementTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="SettlementInfoVOMap" type="com.sky.vo.SettlementInfoVO">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="seller_id" property="sellerId"/>
        <result column="seller_name" property="sellerName"/>
        <result column="pay_time" property="payTime"/>
        <result column="billing_date" property="billingDate"/>
        <result column="billing_cycle" property="billingCycle"/>
        <result column="settlement_date" property="settlementDate"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="settlement_amount" property="settlementAmount"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="settlement_status_desc" property="settlementStatusDesc"/>
        <result column="settlement_time" property="settlementTime"/>
        <result column="remark" property="remark"/>
        <result column="is_due" property="isDue"/>
        <result column="days_to_settlement" property="daysToSettlement"/>
    </resultMap>

    <resultMap id="SettlementSummaryVOMap" type="com.sky.vo.SettlementSummaryVO">
        <result column="seller_id" property="sellerId"/>
        <result column="seller_name" property="sellerName"/>
        <result column="total_orders" property="totalOrders"/>
        <result column="total_order_amount" property="totalOrderAmount"/>
        <result column="total_settlement_amount" property="totalSettlementAmount"/>
        <result column="not_due_orders" property="notDueOrders"/>
        <result column="not_due_amount" property="notDueAmount"/>
        <result column="pending_orders" property="pendingOrders"/>
        <result column="pending_amount" property="pendingAmount"/>
        <result column="completed_orders" property="completedOrders"/>
        <result column="completed_amount" property="completedAmount"/>
    </resultMap>

    <insert id="insert" parameterType="com.sky.entity.OrderSettlementInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO order_settlement_info (
            order_id, order_number, seller_id, seller_name, pay_time,
            billing_date, billing_cycle, settlement_date, order_amount, settlement_amount,
            settlement_status, settlement_time, remark, create_time, update_time
        ) VALUES (
            #{orderId}, #{orderNumber}, #{sellerId}, #{sellerName}, #{payTime},
            #{billingDate}, #{billingCycle}, #{settlementDate}, #{orderAmount}, #{settlementAmount},
            #{settlementStatus}, #{settlementTime}, #{remark}, #{createTime}, #{updateTime}
        )
    </insert>

    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        SELECT * FROM order_settlement_info WHERE id = #{id}
    </select>

    <select id="selectByOrderId" parameterType="long" resultMap="BaseResultMap">
        SELECT * FROM order_settlement_info WHERE order_id = #{orderId}
    </select>

    <update id="update" parameterType="com.sky.entity.OrderSettlementInfo">
        UPDATE order_settlement_info
        <set>
            <if test="settlementStatus != null">settlement_status = #{settlementStatus},</if>
            <if test="settlementTime != null">settlement_time = #{settlementTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectPage" parameterType="com.sky.dto.SettlementQueryDTO" resultMap="SettlementInfoVOMap">
        SELECT
            osi.*,
            CASE
                WHEN osi.settlement_status = 0 THEN '未到期'
                WHEN osi.settlement_status = 1 THEN '待回款'
                WHEN osi.settlement_status = 2 THEN '已回款'
                ELSE '未知'
            END as settlement_status_desc,
            CASE
                WHEN CURDATE() >= osi.settlement_date THEN 1
                ELSE 0
            END as is_due,
            DATEDIFF(osi.settlement_date, CURDATE()) as days_to_settlement
        FROM order_settlement_info osi
        <where>
            <if test="sellerId != null">
                AND osi.seller_id = #{sellerId}
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                AND osi.order_number LIKE CONCAT('%', #{orderNumber}, '%')
            </if>
            <if test="settlementStatus != null">
                AND osi.settlement_status = #{settlementStatus}
            </if>
            <if test="billingCycle != null and billingCycle != ''">
                AND osi.billing_cycle = #{billingCycle}
            </if>
            <if test="settlementStartDate != null">
                AND osi.settlement_date >= #{settlementStartDate}
            </if>
            <if test="settlementEndDate != null">
                AND osi.settlement_date &lt;= #{settlementEndDate}
            </if>
            <if test="sellerName != null and sellerName != ''">
                AND osi.seller_name LIKE CONCAT('%', #{sellerName}, '%')
            </if>
        </where>
        ORDER BY osi.settlement_date DESC, osi.create_time DESC
    </select>

    <select id="selectSummaryBySellerId" parameterType="long" resultMap="SettlementSummaryVOMap">
        SELECT
            seller_id,
            seller_name,
            COUNT(*) as total_orders,
            SUM(order_amount) as total_order_amount,
            SUM(settlement_amount) as total_settlement_amount,
            SUM(CASE WHEN settlement_status = 0 THEN 1 ELSE 0 END) as not_due_orders,
            SUM(CASE WHEN settlement_status = 0 THEN settlement_amount ELSE 0 END) as not_due_amount,
            SUM(CASE WHEN settlement_status = 1 THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN settlement_status = 1 THEN settlement_amount ELSE 0 END) as pending_amount,
            SUM(CASE WHEN settlement_status = 2 THEN 1 ELSE 0 END) as completed_orders,
            SUM(CASE WHEN settlement_status = 2 THEN settlement_amount ELSE 0 END) as completed_amount
        FROM order_settlement_info
        WHERE seller_id = #{sellerId}
        GROUP BY seller_id, seller_name
    </select>

    <select id="selectAllSummary" resultMap="SettlementSummaryVOMap">
        SELECT
            seller_id,
            seller_name,
            COUNT(*) as total_orders,
            SUM(order_amount) as total_order_amount,
            SUM(settlement_amount) as total_settlement_amount,
            SUM(CASE WHEN settlement_status = 0 THEN 1 ELSE 0 END) as not_due_orders,
            SUM(CASE WHEN settlement_status = 0 THEN settlement_amount ELSE 0 END) as not_due_amount,
            SUM(CASE WHEN settlement_status = 1 THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN settlement_status = 1 THEN settlement_amount ELSE 0 END) as pending_amount,
            SUM(CASE WHEN settlement_status = 2 THEN 1 ELSE 0 END) as completed_orders,
            SUM(CASE WHEN settlement_status = 2 THEN settlement_amount ELSE 0 END) as completed_amount
        FROM order_settlement_info
        GROUP BY seller_id, seller_name
        ORDER BY seller_name
    </select>

    <select id="selectPendingSettlement" resultMap="SettlementInfoVOMap">
        SELECT
            osi.*,
            '待回款' as settlement_status_desc,
            1 as is_due,
            DATEDIFF(osi.settlement_date, CURDATE()) as days_to_settlement
        FROM order_settlement_info osi
        WHERE osi.settlement_status = 1
          AND CURDATE() >= osi.settlement_date
        ORDER BY osi.settlement_date ASC
    </select>

    <select id="selectBySettlementDate" parameterType="java.time.LocalDate" resultMap="SettlementInfoVOMap">
        SELECT
            osi.*,
            CASE
                WHEN osi.settlement_status = 0 THEN '未到期'
                WHEN osi.settlement_status = 1 THEN '待回款'
                WHEN osi.settlement_status = 2 THEN '已回款'
                ELSE '未知'
            END as settlement_status_desc,
            1 as is_due,
            0 as days_to_settlement
        FROM order_settlement_info osi
        WHERE osi.settlement_date = #{settlementDate}
        ORDER BY osi.seller_id, osi.create_time
    </select>

    <update id="batchUpdateStatus">
        UPDATE order_settlement_info
        SET settlement_status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateSettlementComplete">
        UPDATE order_settlement_info
        SET settlement_status = 2,
            settlement_time = NOW(),
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
