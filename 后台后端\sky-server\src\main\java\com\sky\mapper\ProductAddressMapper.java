package com.sky.mapper;

import com.sky.entity.ProductAddress;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ProductAddressMapper {

    // 插入地址
    @Insert("INSERT INTO product_address (buyer_id, name, phone_number, address_detail, city, province, postal_code, country, is_default) " +
            "VALUES (#{buyerId}, #{name}, #{phoneNumber}, #{addressDetail}, #{city}, #{province}, #{postalCode}, #{country}, #{isDefault})")
    void insert(ProductAddress address);

    // 查询所有地址
    @Select("SELECT * FROM product_address WHERE buyer_id = #{buyerId}")
    List<ProductAddress> findByBuyerId(Long buyerId);

    // 更新地址
    @Update("<script>" +
            "UPDATE product_address " +
            "<set>" +
            "  <if test='name != null'>name = #{name},</if>" +
            "  <if test='phoneNumber != null'>phone_number = #{phoneNumber},</if>" +
            "  <if test='addressDetail != null'>address_detail = #{addressDetail},</if>" +
            "  <if test='city != null'>city = #{city},</if>" +
            "  <if test='province != null'>province = #{province},</if>" +
            "  <if test='postalCode != null'>postal_code = #{postalCode},</if>" +
            "  <if test='country != null'>country = #{country},</if>" +
            "  <if test='isDefault != null'>is_default = #{isDefault},</if>" +
            "</set>" +
            "WHERE id = #{id}" +
            "</script>")
    void update(ProductAddress address);

    // 删除地址
    @Delete("DELETE FROM product_address WHERE id = #{id}")
    void deleteById(Long id);
}
