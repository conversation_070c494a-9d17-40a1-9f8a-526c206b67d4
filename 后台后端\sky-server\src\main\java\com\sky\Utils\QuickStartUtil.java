package com.sky.Utils;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.Amount;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
/** Native 支付下单为例 */
public class QuickStartUtil {

    // 微信支付配置参数

    /** 商户号 */
    @Value("${wechat.merchant-id}")
    public  String merchantId;

    /** 商户证书序列号 */
    @Value("${wechat.merchant-serial-number}")
    private  String merchantSerialNumber;


    /** 商户APIV3密钥 */
    @Value("${wechat.api-v3-key}")
    private  String apiV3Key;
    @Value("${wechat.public-key-path:classpath:apiclient_pub.pem}")
    private  String publicKeyPath ;
    @Value("${wechat.public-key-id}")
    private  String publicKeyId;

    @Value("${wechat.app-id}")
    private  String appId;

    // TODO 回调
    @Value("${wechat.notify-url}")
    private  String notifyUrl;

    // 私钥文件处理
    @Value("${wechat.private-key-path:apiclient_key.pem}")
    private  String privateKeyPath;



    private String resolvedPrivateKeyPath;

    /**
     * 初始化支付配置
     */
    @PostConstruct
    public void init() {
        // 启动时自动下载微信平台证书
        WeChatPayCertDownloader.downloadCertIfNotExists();
        try {
            if (privateKeyPath.startsWith("classpath:")) {
                // 处理类路径资源
                String resourcePath = privateKeyPath.substring("classpath:".length());
                Resource resource = new ClassPathResource(resourcePath);

                // 创建临时文件（解决JAR包内文件访问问题）
                File tempFile = File.createTempFile("apiclient_key", ".pem");
                try (InputStream inputStream = resource.getInputStream()) {
                    Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                }

                resolvedPrivateKeyPath = tempFile.getAbsolutePath();

                log.info("微信支付私钥已从类路径加载到临时文件: {}", resolvedPrivateKeyPath);
            } else {
                // 使用绝对路径
                resolvedPrivateKeyPath = privateKeyPath;
                log.info("使用配置的微信支付私钥路径: {}", resolvedPrivateKeyPath);
            }

            // 验证私钥文件是否存在
            File keyFile = new File(resolvedPrivateKeyPath);
            if (!keyFile.exists() || !keyFile.isFile()) {
                throw new IOException("微信支付私钥文件不存在: " + resolvedPrivateKeyPath);
            }

        } catch (Exception e) {
            log.error("微信支付配置初始化失败", e);
            throw new RuntimeException("支付系统初始化失败，请检查配置", e);
        }
    }




    //商品总金额，金额类（总金额，货币属性），商品标题，商户订单号
    public  String quickStart(Integer total,Amount amount,String title,String outTradeNo) {
        // 使用微信支付公钥的RSA配置
        Config config =
                new RSAPublicKeyConfig.Builder()
                        .merchantId(merchantId)
                        .privateKeyFromPath(privateKeyPath)
                        .publicKeyFromPath(publicKeyPath)
                        .publicKeyId(publicKeyId)
                        .merchantSerialNumber(merchantSerialNumber)
                        .apiV3Key(apiV3Key)
                        .build();
        // 构建service
        NativePayService service = new NativePayService.Builder().config(config).build();
        // request.setXxx(val)设置所需参数，具体参数可见Request定义
        PrepayRequest request = new PrepayRequest();
//            Amount amount = new Amount();
        /** 总金额 说明：订单总金额，单位为分 */
        amount.setTotal(total);
        request.setAmount(amount);

        request.setAppid(appId);//唯一

        request.setMchid(merchantId);//唯一
        //注入商品信息
        request.setDescription(title);
        //对支付结果信息处理,回调
        request.setNotifyUrl(notifyUrl);
        /** 商户订单号 说明：原支付交易对应的商户订单号 */
        request.setOutTradeNo(outTradeNo);
        // 调用下单方法，得到应答
        PrepayResponse response = service.prepay(request);
        // 使用微信扫描 code_url 对应的二维码，即可体验Native支付
        String codeUrl = response.getCodeUrl();
        return codeUrl;
    }
}
