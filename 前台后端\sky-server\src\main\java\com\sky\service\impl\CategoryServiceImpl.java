package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.dto.CategoryCreateDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.dto.CategoryQueryDTO;
import com.sky.entity.BusinessException;
import com.sky.entity.Category;
import com.sky.mapper.CategoryMapper;
import com.sky.mapper.ProductMapper;

import com.sky.result.PageResult;
import com.sky.service.CategoryService;
import com.sky.vo.CategoryTreeVO;
import com.sky.vo.ListCategoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分类业务层
 */
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {
    private final CategoryMapper categoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCategory(CategoryCreateDTO dto) {
        // DTO转换实体
        Category category = new Category();
        category.setName(dto.getName());
        category.setParentId(dto.getParentId());
        category.setSortOrder(dto.getSortOrder());

        // 层级计算逻辑
        if (dto.getParentId() != null && dto.getParentId() != 0L) {
            Category parent = categoryMapper.findById(dto.getParentId());
            if (parent == null) {
                throw new BusinessException("父分类不存在");
            }
            if (parent.getLevel() >= 2) {
                throw new BusinessException("分类层级不能超过3级");
            }
            category.setLevel(parent.getLevel() + 1);
        } else {
            category.setLevel(0);
        }

        // 持久化操作
        categoryMapper.insert(category);
        return category.getId();
    }

    @Override
    public List<CategoryTreeVO> getFullCategoryTree() {
        // 1. 查询所有分类
        List<Category> allCategories = categoryMapper.selectAllWithType();

        // 2. 过滤出一级分类（parent_id 为 NULL 或 0）
        List<CategoryTreeVO> rootCategories = allCategories.stream()
                .filter(c -> c.getParentId() == null || c.getParentId() == 0)
                .map(c -> new CategoryTreeVO(c.getId(), c.getName(), c.getLevel(), new ArrayList<>()))
                .collect(Collectors.toList());

        // 3. 递归构建二级和三级分类
        rootCategories.forEach(root -> {
            // 构建二级分类
            List<CategoryTreeVO> secondLevel = allCategories.stream()
                    .filter(c -> c.getParentId() != null && c.getParentId().equals(root.getId()))
                    .map(c -> new CategoryTreeVO(c.getId(), c.getName(), c.getLevel(), new ArrayList<>()))
                    .collect(Collectors.toList());

            // 构建三级分类
            secondLevel.forEach(second -> {
                List<CategoryTreeVO> thirdLevel = allCategories.stream()
                        .filter(c -> c.getParentId() != null && c.getParentId().equals(second.getId()))
                        .map(c -> new CategoryTreeVO(c.getId(), c.getName(), c.getLevel(), null))
                        .collect(Collectors.toList());
                second.setChildren(thirdLevel);
            });

            root.setChildren(secondLevel);
        });

        return rootCategories;
    }
    @Override
    public void deleteCategoryWithSubtree(Long id) throws BusinessException {

    }

    @Override
    public CategoryTreeVO getCategoryTree(Long rootId) {
        return null;
    }

    private List<CategoryTreeVO> buildCategoryTree(List<Category> categories) {
        // 使用Map加速查找（时间复杂度O(n)）
        Map<Long, CategoryTreeVO> nodeMap = new HashMap<>();

        // 第一轮转换：创建所有节点
        for (Category category : categories) {
            CategoryTreeVO vo = new CategoryTreeVO();
            vo.setId(category.getId());
            vo.setName(category.getName());
            vo.setLevel(category.getLevel());
            vo.setChildren(new ArrayList<>());
            nodeMap.put(category.getId(), vo);
        }

        // 第二轮构建：建立父子关系
        List<CategoryTreeVO> rootNodes = new ArrayList<>();
        for (CategoryTreeVO vo : nodeMap.values()) {
            Category category = categories.stream()
                    .filter(c -> c.getId().equals(vo.getId()))
                    .findFirst()
                    .orElse(null);

            if (category != null) {
                Long parentId = category.getParentId();
                if (parentId == null || parentId == 0L) {
                    rootNodes.add(vo);
                } else {
                    CategoryTreeVO parent = nodeMap.get(parentId);
                    if (parent != null) {
                        parent.getChildren().add(vo);
                    }
                }
            }
        }

        // 第三轮排序：每层按sortOrder排序
//        sortTree(rootNodes);
        return rootNodes;
    }

//    private void sortTree(List<CategoryTreeVO> nodes) {
//        nodes.sort(Comparator.comparingInt(CategoryTreeVO::getSortOrder));
//        nodes.forEach(node -> {
//            if (!node.getChildren().isEmpty()) {
//                sortTree(node.getChildren());
//            }
//        });
//    }
}