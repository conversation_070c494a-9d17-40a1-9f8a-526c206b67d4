package com.sky.service;


import com.sky.dto.StoreDTO;
import com.sky.vo.StoreVO;

public interface StoreService {
    /**
     * 新增店铺
     * @param storeDTO
     */
    void createStore(StoreDTO storeDTO) throws Exception;

    /**
     * 修改店铺
     * @param storeDTO
     */
    void updateStore(StoreDTO storeDTO);


    /**
     * 根据id查询店铺
     * @param id
     * @return
     */
    StoreVO getById(Long id);

    /**
     * 删除店铺
     * @param id
     */
    void delete(Long id);

}
