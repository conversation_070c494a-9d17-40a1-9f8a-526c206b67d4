package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrdersPageQueryDTO implements Serializable {

    private int page;

    private int pageSize;

    private String number;

    private  Long buyerId;

    private  Long addressId;

    private Integer shippingMethodId;

    private Integer status;

    private BigDecimal amount;

    //下单时间
    private LocalDateTime orderTime;

    //支付时间
    private LocalDateTime checkoutTime;

    //订单取消原因
    private String cancelOrderReason;

    //订单拒绝原因
    private String rejectionReason;

    //订单取消时间
    private LocalDateTime cancelOrderTime;

    //预计送达时间
    private LocalDateTime estimatedDeliveryTime;

    //送达时间
    private LocalDateTime deliveryTime;

    //支付方式 1、微信，2、支付宝
    private Integer payMethod;

    //支付状态 0未支付 1已支付 2退款
    private Integer payStatus;

    //备注
    private String orderRemark;
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime beginTime;
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime endTime;
//
//    private Long userId;

}
