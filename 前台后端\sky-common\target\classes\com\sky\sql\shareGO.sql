CREATE TABLE `logistics_info` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `logistics_number` VARCHAR(255) COMMENT '物流单号',
    `order_id` INT COMMENT '订单ID',
    `logistics_company` VARCHAR(255) COMMENT '物流公司',
    `logistics_status` VARCHAR(50) COMMENT '物流状态',
    `create_time` DATETIME COMMENT '创建时间',
    `shipping_date` DATETIME COMMENT '发货日期',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='物流信息';

CREATE TABLE store
(
    id                INT AUTO_INCREMENT PRIMARY KEY,
    seller_id         INT          NULL COMMENT '卖家ID',
    store_name        VARCHAR(255) NULL COMMENT '店铺名称',
    photo            VARCHAR(255) NULL COMMENT '照片（logo）',
    store_status      VARCHAR(50)  NULL COMMENT '店铺状态',
    store_description TEXT         NULL COMMENT '店铺描述',
    store_address     VARCHAR(255) NULL COMMENT '店铺地址（可选填）',
    create_time       DATETIME     NULL COMMENT '创建时间',
    last_login_time   DATETIME     NULL COMMENT '最后登录时间'
)
COMMENT '店铺' ROW_FORMAT = DYNAMIC;