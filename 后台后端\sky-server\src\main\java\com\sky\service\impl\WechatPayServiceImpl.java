package com.sky.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sky.dto.WechatRefundRequestDTO;
import com.sky.properties.WechatPayProperties;
import com.sky.service.WechatPayService;
import com.sky.vo.WechatRefundResponseVO;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.util.Base64;
import java.util.UUID;

/**
 * 微信支付服务实现
 */
@Slf4j
@Service
public class WechatPayServiceImpl implements WechatPayService {

    @Autowired
    private WechatPayProperties wechatPayProperties;

    @Autowired
    private Config wechatPayConfig;

    @Autowired
    private NativePayService nativePayService;

    @Autowired
    @Qualifier("wechatPayObjectMapper")
    private ObjectMapper objectMapper;

    // 微信支付API地址
    private static final String API_URL = "https://apihk.mch.weixin.qq.com/v3/global/transactions/native";
    private static final String REFUND_API_URL = "https://apihk.mch.weixin.qq.com/v3/global/refunds";
    private static final int MAX_OUT_TRADE_NO_LENGTH = 32;



    @Override
    public String createOrder(String outTradeNo, int total, String description, String clientIp) {
        try {
            // 检查微信支付服务是否可用
            if (nativePayService == null) {
                log.warn("微信支付服务未初始化，返回模拟支付链接");
                return "weixin://wxpay/bizpayurl?pr=mock_code_url";
            }

            // 参数验证
            if (total <= 0) throw new IllegalArgumentException("金额必须大于0");
            
            // 处理订单号
            if (outTradeNo == null || outTradeNo.isEmpty()) {
                outTradeNo = generateOutTradeNo();
                log.info("生成新的订单号: {}", outTradeNo);
            } else {
                log.info("使用提供的订单号: {}", outTradeNo);
                if (outTradeNo.length() > MAX_OUT_TRADE_NO_LENGTH) {
                    log.warn("订单号超过{}个字符，将被截断。原订单号: {}", MAX_OUT_TRADE_NO_LENGTH, outTradeNo);
                    outTradeNo = outTradeNo.substring(0, MAX_OUT_TRADE_NO_LENGTH);
                }
            }
            
            // 构建请求体
            JSONObject requestJson = new JSONObject();
            
            // 基础信息
            requestJson.put("mchid", wechatPayProperties.getMchid());
            requestJson.put("appid", wechatPayProperties.getAppid());
            requestJson.put("description", description);
            requestJson.put("out_trade_no", outTradeNo);
            requestJson.put("notify_url", wechatPayProperties.getNotifyUrl());
            requestJson.put("trade_type", "NATIVE");
            
            // 添加商户分类代码（跨境支付必需）
            requestJson.put("merchant_category_code", wechatPayProperties.getCrossBorder().getMerchantCategoryCode());

            // 金额信息
            JSONObject amount = new JSONObject();
            amount.put("total", total);
            amount.put("currency", wechatPayProperties.getCrossBorder().getCurrency());
            requestJson.put("amount", amount);

            // 场景信息
            JSONObject sceneInfo = new JSONObject();
            sceneInfo.put("payer_client_ip", clientIp);
            requestJson.put("scene_info", sceneInfo);

            // 发送请求
            String requestBody = requestJson.toString();
            log.info("微信支付请求参数: {}", requestBody);
            
            String responseStr = httpPost(API_URL, requestBody);
            log.info("微信支付响应: {}", responseStr);

            // 解析响应
            JSONObject responseJson = new JSONObject(responseStr);
            return responseJson.getString("code_url");
        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw new RuntimeException("创建订单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public WechatRefundResponseVO applyRefund(WechatRefundRequestDTO requestDTO) {
        try {
            log.info("申请退款，请求参数: {}", requestDTO);

            // 参数验证
            validateRefundRequest(requestDTO);

            // 检查微信支付配置是否可用
            try {
                validateConfiguration();
            } catch (IllegalStateException e) {
                log.warn("微信支付配置不可用，返回模拟退款响应: {}", e.getMessage());
                return WechatRefundResponseVO.builder()
                        .refundId("mock_refund_" + System.currentTimeMillis())
                        .outRefundNo(requestDTO.getOutRefundNo())
                        .status("SUCCESS")
                        .build();
            }

            // 构建请求体
            JSONObject requestJson = buildRefundRequestJson(requestDTO);

            // 发送退款请求
            log.info("发送退款请求: {}", requestJson.toString());
            String responseStr = httpPost(REFUND_API_URL, requestJson.toString());
            log.info("微信退款响应: {}", responseStr);

            // 解析响应
            WechatRefundResponseVO response = objectMapper.readValue(responseStr, WechatRefundResponseVO.class);

            // 记录关键信息
            log.info("退款处理结果 - 微信退款单号: {}, 商户退款单号: {}, 状态: {}",
                    response.getId(), response.getOutRefundNo(), response.getStatus());

            return response;

        } catch (Exception e) {
            log.error("申请退款失败", e);
            throw new RuntimeException("申请退款失败: " + e.getMessage(), e);
        }
    }

    @Override
    public WechatRefundResponseVO queryRefund(String outRefundNo) {
        try {
            log.info("查询退款状态，退款单号: {}", outRefundNo);

            // 参数验证
            if (outRefundNo == null || outRefundNo.trim().isEmpty()) {
                throw new IllegalArgumentException("商户退款单号不能为空");
            }

            // 检查微信支付配置是否可用
            try {
                validateConfiguration();
            } catch (IllegalStateException e) {
                log.warn("微信支付配置不可用，返回模拟查询响应: {}", e.getMessage());
                return WechatRefundResponseVO.builder()
                        .refundId("mock_refund_" + System.currentTimeMillis())
                        .outRefundNo(outRefundNo)
                        .status("SUCCESS")
                        .build();
            }

            // 构建查询URL
            String queryUrl = buildRefundQueryUrl(outRefundNo);

            // 发送查询请求
            String responseStr = httpGet(queryUrl);
            log.info("微信退款查询响应: {}", responseStr);

            // 解析响应
            return objectMapper.readValue(responseStr, WechatRefundResponseVO.class);

        } catch (Exception e) {
            log.error("查询退款状态失败", e);
            throw new RuntimeException("查询退款状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成商户订单号
     */
    private String generateOutTradeNo() {
        return "ORDER_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }





    /**
     * 验证退款申请请求参数
     */
    private void validateRefundRequest(WechatRefundRequestDTO requestDTO) {
        if (requestDTO == null) {
            throw new IllegalArgumentException("退款请求参数不能为空");
        }

        // 验证商户订单号
        if (requestDTO.getOutTradeNo() == null || requestDTO.getOutTradeNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }

        // 验证退款单号
        if (requestDTO.getOutRefundNo() == null || requestDTO.getOutRefundNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户退款单号不能为空");
        }

        // 验证金额
        if (requestDTO.getRefundAmount() == null || requestDTO.getRefundAmount() <= 0) {
            throw new IllegalArgumentException("退款金额必须大于0");
        }

        if (requestDTO.getTotalAmount() == null || requestDTO.getTotalAmount() <= 0) {
            throw new IllegalArgumentException("原订单金额必须大于0");
        }

        if (requestDTO.getRefundAmount() > requestDTO.getTotalAmount()) {
            throw new IllegalArgumentException("退款金额不能超过原订单金额");
        }
    }

    /**
     * 构建退款申请请求JSON
     */
    private JSONObject buildRefundRequestJson(WechatRefundRequestDTO requestDTO) {
        JSONObject requestJson = new JSONObject();

        // 基础信息
        requestJson.put("mchid", wechatPayProperties.getMchid());
        requestJson.put("appid", wechatPayProperties.getAppid());

        // 商户订单号
        requestJson.put("out_trade_no", requestDTO.getOutTradeNo().trim());

        // 退款单号
        requestJson.put("out_refund_no", requestDTO.getOutRefundNo().trim());

        // 退款原因
        if (requestDTO.getReason() != null && !requestDTO.getReason().trim().isEmpty()) {
            requestJson.put("reason", requestDTO.getReason().trim());
        }

        // 金额信息
        JSONObject amount = new JSONObject();
        amount.put("refund", requestDTO.getRefundAmount());
        amount.put("total", requestDTO.getTotalAmount());
        amount.put("currency", requestDTO.getCurrency() != null ? requestDTO.getCurrency() : "USD");
        requestJson.put("amount", amount);

        // 退款通知地址
        if (requestDTO.getNotifyUrl() != null && !requestDTO.getNotifyUrl().trim().isEmpty()) {
            requestJson.put("notify_url", requestDTO.getNotifyUrl().trim());
        }

        return requestJson;
    }

    /**
     * 构建退款查询URL
     */
    private String buildRefundQueryUrl(String outRefundNo) {
        return REFUND_API_URL + "/out-refund-no/" + outRefundNo.trim() + "?mchid=" + wechatPayProperties.getMchid();
    }

    /**
     * 执行HTTP POST请求
     */
    private String httpPost(String url, String requestBody) throws Exception {
        log.info("发送HTTP POST请求到: {}", url);

        // 验证配置
        validateConfiguration();

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");

            // 添加认证头
            String token = getToken(url, "POST", requestBody);
            httpPost.setHeader("Authorization", token);
            httpPost.setHeader("User-Agent", "WechatPay-Custom/1.0");

            httpPost.setEntity(new StringEntity(requestBody, ContentType.APPLICATION_JSON));

            try (CloseableHttpResponse response = client.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseBody = entity != null ? EntityUtils.toString(entity) : null;

                log.debug("响应码: {}, 响应体: {}", statusCode, responseBody);

                if (statusCode >= 200 && statusCode < 300) {
                    return responseBody;
                } else {
                    log.error("HTTP请求失败: {} - {}", statusCode, responseBody);
                    throw new RuntimeException("HTTP请求失败: " + statusCode + ", 响应: " + responseBody);
                }
            }
        }
    }

    /**
     * 执行HTTP GET请求
     */
    private String httpGet(String url) throws Exception {
        log.info("发送HTTP GET请求到: {}", url);

        // 验证配置
        validateConfiguration();

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Content-Type", "application/json");
            httpGet.setHeader("Accept", "application/json");

            // 添加认证头（GET请求body为空）
            String token = getToken(url, "GET", "");
            httpGet.setHeader("Authorization", token);
            httpGet.setHeader("User-Agent", "WechatPay-Custom/1.0");

            try (CloseableHttpResponse response = client.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseBody = entity != null ? EntityUtils.toString(entity) : null;

                log.debug("响应码: {}, 响应体: {}", statusCode, responseBody);

                if (statusCode >= 200 && statusCode < 300) {
                    return responseBody;
                } else {
                    log.error("HTTP请求失败: {} - {}", statusCode, responseBody);
                    throw new RuntimeException("HTTP请求失败: " + statusCode + ", 响应: " + responseBody);
                }
            }
        }
    }

    /**
     * 生成微信支付认证Token
     */
    private String getToken(String url, String method, String body) throws Exception {
        // 获取当前时间戳
        long timestamp = Instant.now().getEpochSecond();

        // 生成随机字符串
        String nonceStr = UUID.randomUUID().toString().replace("-", "");

        // 构建签名字符串
        String signatureStr = method + "\n" +
                             getUrlPath(url) + "\n" +
                             timestamp + "\n" +
                             nonceStr + "\n" +
                             body + "\n";

        // 使用私钥签名
        String signature = sign(signatureStr);

        // 构建Authorization头
        return String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%d\",serial_no=\"%s\",signature=\"%s\"",
                wechatPayProperties.getMchid(),
                nonceStr,
                timestamp,
                wechatPayProperties.getMchSerialNo(),
                signature);
    }

    /**
     * 从URL中提取路径
     */
    private String getUrlPath(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            String path = urlObj.getPath();
            String query = urlObj.getQuery();
            return query != null ? path + "?" + query : path;
        } catch (Exception e) {
            throw new RuntimeException("解析URL失败: " + url, e);
        }
    }

    /**
     * 使用私钥对字符串进行签名
     */
    private String sign(String data) throws Exception {
        // 读取私钥
        PrivateKey privateKey = loadPrivateKey();

        // 创建签名对象
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));

        // 生成签名
        byte[] signatureBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signatureBytes);
    }

    /**
     * 加载私钥
     */
    private PrivateKey loadPrivateKey() throws Exception {
        String privateKeyPath = "cert/" + wechatPayProperties.getPrivateKeyPath();

        // 尝试从classpath加载资源
        try (java.io.InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(privateKeyPath)) {
            if (inputStream == null) {
                throw new FileNotFoundException("私钥文件不存在: " + privateKeyPath);
            }

            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] data = new byte[1024];
            int nRead;
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            byte[] keyBytes = buffer.toByteArray();

            String privateKeyContent = new String(keyBytes, StandardCharsets.UTF_8)
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");

            byte[] decoded = Base64.getDecoder().decode(privateKeyContent);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decoded);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(keySpec);
        }
    }

    /**
     * 验证微信支付配置
     */
    private void validateConfiguration() {
        if (wechatPayProperties.getMchid() == null || wechatPayProperties.getMchid().trim().isEmpty()) {
            throw new IllegalStateException("商户号未配置");
        }
        if (wechatPayProperties.getMchSerialNo() == null || wechatPayProperties.getMchSerialNo().trim().isEmpty()) {
            throw new IllegalStateException("证书序列号未配置");
        }
        if (wechatPayProperties.getApiV3Key() == null || wechatPayProperties.getApiV3Key().trim().isEmpty()) {
            throw new IllegalStateException("APIv3密钥未配置");
        }
        if (wechatPayProperties.getPrivateKeyPath() == null || wechatPayProperties.getPrivateKeyPath().trim().isEmpty()) {
            throw new IllegalStateException("私钥路径未配置");
        }

        // 检查私钥文件是否存在
        String privateKeyPath = "cert/" + wechatPayProperties.getPrivateKeyPath();
        try (java.io.InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(privateKeyPath)) {
            if (inputStream == null) {
                log.warn("微信支付私钥文件不存在: {}", privateKeyPath);
                throw new IllegalStateException("微信支付私钥文件不存在: " + privateKeyPath);
            }
        } catch (IOException e) {
            log.warn("检查微信支付私钥文件时发生错误: {}", e.getMessage());
            throw new IllegalStateException("检查微信支付私钥文件时发生错误: " + e.getMessage());
        }

        log.debug("微信支付配置验证通过");
    }
}
