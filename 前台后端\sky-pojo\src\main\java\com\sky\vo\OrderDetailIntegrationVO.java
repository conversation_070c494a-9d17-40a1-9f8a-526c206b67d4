package com.sky.vo;

import com.sky.entity.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单详情集成视图对象
 * 包含订单、物流、地址、支付等完整信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetailIntegrationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础订单信息 ==========
    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单状态名称
     */
    private String orderStatusName;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单备注
     */
    private String remark;

    // ========== 订单商品信息 ==========
    /**
     * 订单商品详情列表
     */
    private List<OrderDetail> orderDetails;

    // ========== 物流信息 ==========
    /**
     * 物流跟踪信息
     */
    private OrderLogisticsTracking logisticsTracking;

    /**
     * 物流轨迹详情列表
     */
    private List<LogisticsTraceDetail> logisticsTraces;

    /**
     * 物流状态描述
     */
    private String logisticsStatusDesc;

    /**
     * 是否已发货
     */
    private Boolean isShipped;

    /**
     * 是否已签收
     */
    private Boolean isDelivered;

    // ========== 地址信息 ==========
    /**
     * 收货地址信息
     */
    private OrderAddressInfo deliveryAddress;

    /**
     * 发货地址信息
     */
    private OrderAddressInfo senderAddress;

    // ========== 支付信息 ==========
    /**
     * 支付详情信息
     */
    private OrderPaymentDetail paymentDetail;

    /**
     * 支付状态描述
     */
    private String paymentStatusDesc;

    /**
     * 是否已支付
     */
    private Boolean isPaid;

    // ========== 时间信息 ==========
    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 发货时间
     */
    private LocalDateTime shipTime;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime actualDeliveryTime;

    // ========== 扩展信息 ==========
    /**
     * 订单进度百分比（0-100）
     */
    private Integer progressPercentage;

    /**
     * 当前订单阶段描述
     */
    private String currentStageDesc;

    /**
     * 下一步操作提示
     */
    private String nextActionTip;

    /**
     * 是否可以取消订单
     */
    private Boolean canCancel;

    /**
     * 是否可以申请退款
     */
    private Boolean canRefund;

    /**
     * 是否可以确认收货
     */
    private Boolean canConfirmReceipt;

    /**
     * 客服联系方式
     */
    private String customerServiceContact;

    /**
     * 获取订单状态中文名称
     * 状态：1-待支付，2-已支付，3-已取消，4-已发货，5-已完成，6-已关闭
     */
    public String getOrderStatusName() {
        if (orderStatus == null) return "未知状态";
        switch (orderStatus) {
            case 1: return "待支付";
            case 2: return "已支付";
            case 3: return "已取消";
            case 4: return "已发货";
            case 5: return "已完成";
            case 6: return "已关闭";
            default: return "未知状态";
        }
    }

    /**
     * 计算订单进度百分比
     */
    public Integer calculateProgressPercentage() {
        if (orderStatus == null) return 0;
        switch (orderStatus) {
            case 1: return 10;  // 待支付
            case 2: return 30;  // 已支付
            case 3: return 0;   // 已取消
            case 4: return 70;  // 已发货
            case 5: return 100; // 已完成
            case 6: return 0;   // 已关闭
            default: return 0;
        }
    }

    /**
     * 获取当前阶段描述
     */
    public String getCurrentStageDesc() {
        if (orderStatus == null) return "订单状态异常";

        if (orderStatus == 1) {
            return "等待您完成支付";
        } else if (orderStatus == 2) {
            return "支付成功，商家正在准备发货";
        } else if (orderStatus == 3) {
            return "订单已取消";
        } else if (orderStatus == 4) {
            if (logisticsTracking != null && logisticsTracking.getCurrentStatus() != null) {
                String status = logisticsTracking.getCurrentStatus();
                switch (status) {
                    case "InTransit": return "商品正在运输途中";
                    case "OutForDelivery": return "商品正在派送中，请注意接收";
                    case "AvailableForPickup": return "商品已到达取件点，请及时取件";
                    default: return "商品正在配送中";
                }
            }
            return "商品已发货，正在配送中";
        } else if (orderStatus == 5) {
            return "订单已完成，感谢您的购买";
        } else if (orderStatus == 6) {
            return "订单已关闭";
        }

        return "订单状态异常";
    }
}
