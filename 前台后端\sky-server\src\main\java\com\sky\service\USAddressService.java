package com.sky.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sky.entity.USAddress;

import java.util.List;

public interface USAddressService extends IService<USAddress> {
    void addAddress(USAddress address);
    List<USAddress> getAllAddresses(Long userId);
    void updateAddress(USAddress address);
    void deleteAddress(Long id);
    void validateUSAddress(USAddress address);

    void defaultSet(Long id);
}