package com.sky.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sky.entity.TrackingConfig;
import com.sky.mapper.TrackingConfigMapper;
import com.sky.service.TrackingConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 17TRACK配置管理服务实现
 */
@Service
@Slf4j
public class TrackingConfigServiceImpl implements TrackingConfigService {

    @Autowired
    private TrackingConfigMapper trackingConfigMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 本地缓存
    private final Map<String, String> configCache = new ConcurrentHashMap<>();

    @Override
    @Cacheable(value = "trackingConfig", key = "#configKey")
    public String getConfigValue(String configKey) {
        // 先从缓存获取
        String cachedValue = configCache.get(configKey);
        if (cachedValue != null) {
            return cachedValue;
        }

        // 从数据库获取
        TrackingConfig config = trackingConfigMapper.getByKey(configKey);
        if (config != null && StringUtils.hasText(config.getConfigValue())) {
            configCache.put(configKey, config.getConfigValue());
            return config.getConfigValue();
        }

        return null;
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return StringUtils.hasText(value) ? value : defaultValue;
    }

    @Override
    public boolean getBooleanConfig(String configKey, boolean defaultValue) {
        String value = getConfigValue(configKey);
        if (StringUtils.hasText(value)) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }

    @Override
    public int getIntConfig(String configKey, int defaultValue) {
        String value = getConfigValue(configKey);
        if (StringUtils.hasText(value)) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                log.warn("配置值格式错误: {} = {}", configKey, value);
            }
        }
        return defaultValue;
    }

    @Override
    @CacheEvict(value = "trackingConfig", key = "#configKey")
    public void setConfigValue(String configKey, String configValue) {
        TrackingConfig config = trackingConfigMapper.getByKey(configKey);
        if (config != null) {
            config.setConfigValue(configValue);
            trackingConfigMapper.update(config);
        } else {
            config = new TrackingConfig();
            config.setConfigKey(configKey);
            config.setConfigValue(configValue);
            config.setConfigType("string");
            trackingConfigMapper.insert(config);
        }
        
        // 更新缓存
        configCache.put(configKey, configValue);
        log.info("配置已更新: {} = {}", configKey, configValue);
    }

    @Override
    public void setConfigs(Map<String, String> configs) {
        for (Map.Entry<String, String> entry : configs.entrySet()) {
            setConfigValue(entry.getKey(), entry.getValue());
        }
    }

    @Override
    public List<TrackingConfig> getAllConfigs() {
        return trackingConfigMapper.getAll();
    }

    @Override
    @CacheEvict(value = "trackingConfig", key = "#configKey")
    public void deleteConfig(String configKey) {
        trackingConfigMapper.deleteByKey(configKey);
        configCache.remove(configKey);
        log.info("配置已删除: {}", configKey);
    }

    @Override
    @CacheEvict(value = "trackingConfig", allEntries = true)
    public void refreshCache() {
        configCache.clear();
        log.info("配置缓存已刷新");
    }

    @Override
    public boolean validateApiConfig() {
        String apiKey = getConfigValue("17track.api.key");
        String baseUrl = getConfigValue("17track.api.base_url");
        
        if (!StringUtils.hasText(apiKey)) {
            log.warn("17TRACK API密钥未配置");
            return false;
        }
        
        if (!StringUtils.hasText(baseUrl)) {
            log.warn("17TRACK API基础URL未配置");
            return false;
        }
        
        return true;
    }

    @Override
    public Map<String, Integer> getCarrierMapping() {
        String mappingJson = getConfigValue("17track.carrier.mapping", "{}");
        try {
            return objectMapper.readValue(mappingJson, new TypeReference<Map<String, Integer>>() {});
        } catch (Exception e) {
            log.error("解析运输商映射配置失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public void updateCarrierMapping(Map<String, Integer> mapping) {
        try {
            String mappingJson = objectMapper.writeValueAsString(mapping);
            setConfigValue("17track.carrier.mapping", mappingJson);
            log.info("运输商映射配置已更新");
        } catch (Exception e) {
            log.error("更新运输商映射配置失败: {}", e.getMessage());
            throw new RuntimeException("更新运输商映射配置失败", e);
        }
    }
}
