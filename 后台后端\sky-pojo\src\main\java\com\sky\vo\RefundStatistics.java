package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 退款统计信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总申请数
     */
    private Long totalApplications;

    /**
     * 待审核数
     */
    private Long pendingApprovals;

    /**
     * 已通过数
     */
    private Long approvedApplications;

    /**
     * 已拒绝数
     */
    private Long rejectedApplications;

    /**
     * 已完成退款数
     */
    private Long completedRefunds;

    /**
     * 退款中数量
     */
    private Long refundingCount;

    /**
     * 退款失败数量
     */
    private Long refundFailedCount;

    /**
     * 已取消数量
     */
    private Long cancelledCount;
}
