package com.sky.Utils;

import java.util.Random;

import java.security.SecureRandom;

public class RandomNumberGenerator {
    public static String generateRandomNumber() {
        SecureRandom secureRandom = new SecureRandom();
        // 生成一个0到999999之间的随机数
        int randomNumber = secureRandom.nextInt(900000)+100000; // 0 到 899999
        // 格式化为六位数，确保前导零
        return String.format("%06d", randomNumber);
    }

    public static void main(String[] args) {
        for (int i = 0; i < 100; i++) {
            String randomNumber = generateRandomNumber();
            System.out.println("生成的六位随机数是: " + Integer.valueOf(randomNumber));
        }

    }
}

