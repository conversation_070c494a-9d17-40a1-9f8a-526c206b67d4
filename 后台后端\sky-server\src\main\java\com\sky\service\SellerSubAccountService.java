package com.sky.service;

import com.sky.dto.SellerSubAccountDTO;
import com.sky.vo.BatchDeleteResultVO;
import com.sky.vo.BatchUpdateStatusResultVO;
import com.sky.vo.SellerSubAccountUpdatePermissionsVO;
import com.sky.vo.SellerSubAccountVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SellerSubAccountService {

    /**
     * 创建商家子账户
     */
    SellerSubAccountVO createSubAccount(SellerSubAccountDTO sellerSubAccountDTO);

    /**
     * 查询商家子账户列表
     */
    List<SellerSubAccountVO> getSubAccountList(Long createdBy,Integer accountStatus);

    /**
     * 查询商家子账户
     */
    SellerSubAccountVO getSubAccount(Long id,Long createdBy);

    /**
     * 更新商家子账户
     */
    SellerSubAccountVO updateSellerSubAccount(Long id, SellerSubAccountDTO sellerSubAccountDTO);

    /**
     * 删除商家子账号
     */
    void deleteSellerSubAccount(Long id, Long createdBy);

    /**
     * 更新商家子账户权限
     */
    SellerSubAccountUpdatePermissionsVO updateSellerSubAccountPermission(Long id, Long createdBy,List<String> permissions);

    /**
     * 批量删除商家子账号
     */
    BatchDeleteResultVO batchDeleteSubAccount(List<Long> ids, Long createdBy);

    /**
     * 批量更新商家子账户状态
     */
    BatchUpdateStatusResultVO batchUpdateSubAccountStatus(List<Long> ids,Integer status,Long createdBy);

    /**
     * 重置商家子账户密码
     */
    void resetPassword(Long id, String password, Long createdBy);
}
