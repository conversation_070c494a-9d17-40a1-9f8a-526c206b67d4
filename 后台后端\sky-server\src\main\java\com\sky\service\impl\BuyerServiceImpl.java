package com.sky.service.impl;

import com.sky.Utils.OssUtils;
import com.sky.constant.MessageConstant;
import com.sky.constant.StatusConstant;
import com.sky.dto.BuyerDTO;
import com.sky.dto.BuyerLoginDTO;
import com.sky.dto.BuyerRegisterDTO;
import com.sky.entity.Buyer;
import com.sky.exception.*;
import com.sky.interceptor.JwtTokenAdminInterceptor;
import com.sky.mapper.BuyerMapper;
import com.sky.service.BuyerService;
import com.sky.vo.BuyerInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import java.time.LocalDateTime;
@Service
public class BuyerServiceImpl implements BuyerService {
    @Autowired
    private BuyerMapper buyerMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private JwtTokenAdminInterceptor jwtTokenAdminInterceptor;
    /**
     * 用户注册
     * @param buyerRegisterDTO
     */
    @Override
    public void buyerRegister(BuyerRegisterDTO buyerRegisterDTO) {
        if (buyerRegisterDTO == null) {
            throw new RegisterNotNullException(MessageConstant.REGISTER_NOT_NULL);
        }
        if (buyerRegisterDTO.getAccountName() == null || buyerRegisterDTO.getAccountName().isEmpty()) {
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }
        Buyer buyerList= buyerMapper.getByUsername(buyerRegisterDTO.getAccountName());

        if (buyerList!=null) {
            throw new AccountIsExitException(MessageConstant.ACCOUNT_IS_EXIT);
        }
       // String phone = buyerRegisterDTO.getPhone();
        String email = buyerRegisterDTO.getEmail();
//        Buyer buyer01 = buyerMapper.getByBuyerPhone(phone);
        Buyer buyer01 = buyerMapper.getByBuyeremail(email);
        if (buyer01!=null) {
            throw new AccountIsExitException("该邮箱已注册过账户");
        }
        String ver = stringRedisTemplate.opsForValue().get(email);
        String verificationCode = buyerRegisterDTO.getVerificationCode();
        if(ver == null || ver.isEmpty()) {
            throw new AccountIsExitException("请先获取验证码");
        }
        if(!ver.equals(verificationCode)) {
            throw new AccountIsExitException("验证码输入错误");
        }
        Buyer buyer = new Buyer();
        BeanUtils.copyProperties(buyerRegisterDTO, buyer);
        buyer.setAccountStatus(1);
        buyer.setPhotoUrl("https://hiram.oss-cn-beijing.aliyuncs.com/%E9%BB%98%E8%AE%A4%E5%A4%B4%E5%83%8F/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250302163653.jpg");
        buyerMapper.save(buyer);
    }


    /**
     * 用户登录
     *
     * @param buyerLoginDTO
     * @return
     */
    @Override
    public Buyer login(BuyerLoginDTO buyerLoginDTO) {
        String username = buyerLoginDTO.getAccountName();
        String password = buyerLoginDTO.getPassword();
        //1、根据用户名查询数据库中的数据
        Buyer buyer = buyerMapper.getByUsername(username);

        //2、处理各种异常情况（用户名不存在、密码不对、账号被锁定）
        if (buyer == null) {
            //账号不存在
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        //密码比对
        if (!password.equals(buyer.getPassword())) {
            //密码错误
            throw new PasswordErrorException(MessageConstant.PASSWORD_ERROR);
        }

        if (buyer.getAccountStatus() == StatusConstant.DISABLE) {
            //账号被锁定
            throw new AccountLockedException(MessageConstant.ACCOUNT_LOCKED);
        }
        buyer.setLastLoginTime(LocalDateTime.now());
        buyerMapper.update(buyer);

        //3、返回实体对象
        return buyer;
    }

    /**
     * 根据id查询用户信息
     * @param id
     * @return
     */
    @Override
    public BuyerInfoVO getById(Long id) {
        Buyer buyer = buyerMapper.getByBuyerId(id);
        BuyerInfoVO buyerInfoVO = new BuyerInfoVO();
        BeanUtils.copyProperties(buyer,buyerInfoVO);
        return buyerInfoVO;
    }

    /**
     * 编辑员工信息
     *
     * @param buyerDTO
     */
    @Autowired
    private OssUtils ossUtils;
    public void update(BuyerDTO buyerDTO) {
        if (buyerDTO == null){
            throw new MessageNotFound(MessageConstant.DATA_NOT_FOUND);
        }
        Buyer buyer = new Buyer();

        //String photoUrl = buyerDTO.getPhotoUrl();
        //ossUtils.uploadOneFile(photoUrl);
        BeanUtils.copyProperties(buyerDTO, buyer);

        buyerMapper.update(buyer);
    }
}
