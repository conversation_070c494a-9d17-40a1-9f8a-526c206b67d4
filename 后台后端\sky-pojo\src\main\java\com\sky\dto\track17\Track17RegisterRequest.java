package com.sky.dto.track17;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 17TRACK注册请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17RegisterRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private String number;              // 物流单号
    private String lang;                // 翻译语言代码
    private String email;               // 邮箱
    private String param;               // 附加跟踪参数
    private String order_no;            // 订单编号
    private String order_time;          // 订单时间
    private Integer carrier;            // 运输商代码
    private Integer final_carrier;      // 尾程运输商代码
    private Boolean auto_detection;     // 是否开启运输商自动检测
    private String tag;                 // 自定义标签
    private String remark;              // 备注信息
}
