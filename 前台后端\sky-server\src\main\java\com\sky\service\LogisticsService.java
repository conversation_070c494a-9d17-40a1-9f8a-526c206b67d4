package com.sky.service;

import com.sky.dto.LogisticsInfoDTO;
import com.sky.vo.LogisticsInfoVO;

/**
 * 物流服务接口
 */
public interface LogisticsService {
    
    /**
     * 创建物流信息
     * @param logisticsInfoDTO 物流信息DTO
     */
    void createLogistics(LogisticsInfoDTO logisticsInfoDTO);
    
    /**
     * 获取物流信息
     * @param id 物流ID
     * @return 物流信息VO
     */
    LogisticsInfoVO getLogisticsInfo(Long id);
    
    /**
     * 更新物流状态
     * @param id 物流ID
     * @param logisticsStatus 物流状态
     */
    void updateLogistics(Long id, String logisticsStatus);
} 