package com.sky.controller.admin;

import com.sky.dto.PaymentRecordPageQueryDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.PaymentRecordService;
import com.sky.vo.PaymentRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/payment/record")
@Api(tags = "支付记录相关接口")
@Slf4j
public class PaymentRecordController {

    @Autowired
    private PaymentRecordService paymentRecordService;

    /**
     * 根据id查询支付记录
     * @param id
     * @return
     */
    @ApiOperation("根据id查询支付记录")
    @GetMapping("/{id}")
    public Result<PaymentRecordVO> getPaymentRecord(@PathVariable Long id){
        log.info("根据id查询支付记录:{}",id);
        PaymentRecordVO paymentRecordVO =paymentRecordService.getPaymentRecord(id);
        return Result.success(paymentRecordVO);
    }

    @ApiOperation("支付记录分页查询")
    @GetMapping("/page")
    public Result<PageResult> page(PaymentRecordPageQueryDTO paymentRecordPageQueryDTO){
        log.info("分页查询支付记录:{}",paymentRecordPageQueryDTO);
        PageResult pageResult =paymentRecordService.pageQuery(paymentRecordPageQueryDTO);
        return Result.success(pageResult);
    }
}
