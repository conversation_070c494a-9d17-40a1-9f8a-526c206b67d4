package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 物流跟踪查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "物流跟踪查询DTO")
public class TrackingQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("页码，从1开始")
    private Integer page;

    @ApiModelProperty("每页大小，最大100")
    private Integer pageSize;

    @ApiModelProperty("物流单号")
    private String trackingNumber;

    @ApiModelProperty("运输商代码")
    private Integer carrierCode;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("标签")
    private String tag;

    @ApiModelProperty("物流状态")
    private String status;

    @ApiModelProperty("创建时间开始")
    private String createTimeStart;

    @ApiModelProperty("创建时间结束")
    private String createTimeEnd;

    @ApiModelProperty("更新时间开始")
    private String updateTimeStart;

    @ApiModelProperty("更新时间结束")
    private String updateTimeEnd;

    @ApiModelProperty("目的地国家代码")
    private String destinationCountry;

    @ApiModelProperty("原产地国家代码")
    private String originCountry;

    @ApiModelProperty("客户邮箱")
    private String customerEmail;

    @ApiModelProperty("收件人姓名")
    private String recipientName;

    @ApiModelProperty("收件人邮箱")
    private String recipientEmail;

    @ApiModelProperty("是否已停止跟踪")
    private Boolean stopped;

    @ApiModelProperty("排序字段")
    private String sortBy;

    @ApiModelProperty("排序方向：asc/desc")
    private String sortOrder;

    @ApiModelProperty("物流单号列表（批量查询）")
    private List<String> trackingNumbers;

    @ApiModelProperty("运输商代码列表")
    private List<Integer> carrierCodes;

    @ApiModelProperty("状态列表")
    private List<String> statuses;
}
