package com.sky.dto;

import com.sky.entity.OrderDetail;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class OrdersDTO implements Serializable {

    private Long id;

    //订单号
    private String number;

    //下单用户id
    private Long buyerId;

    //地址id
    private Long addressId;

    //配送状态 id 1立即送出  0选择具体时间
    private Integer shippingMethodId;


    //订单状态 1待付款，2待派送，3已派送，4已完成，5已取消
    private Integer status;

    //实收金额
    private BigDecimal amount;

    //下单时间
    private LocalDateTime orderTime;

    //支付时间
    private LocalDateTime checkoutTime;

    //支付方式 1微信，2支付宝
    private Integer payMethod;

    //备注
    private String orderRemark;

    private List<OrderDetail> orderDetails;

}
