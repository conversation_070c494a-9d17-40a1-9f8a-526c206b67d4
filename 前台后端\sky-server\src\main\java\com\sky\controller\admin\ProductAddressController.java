package com.sky.controller.admin;


import com.sky.entity.ProductAddress;
import com.sky.result.Result;
import com.sky.service.ProductAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/address")
@Api(tags = "C端地址接口")
public class ProductAddressController {

    @Autowired
    private ProductAddressService addressService;

    private static final Logger log = LoggerFactory.getLogger(ProductAddressController.class);

    // 添加收货地址
    @PostMapping("/create")
    @ApiOperation("添加收货地址")
    public Result<Void> addAddress(@RequestBody ProductAddress address) {
        addressService.addAddress(address);
        return Result.success();
    }

    // 查询地址
    @GetMapping("/list/{id}")
    @ApiOperation("查询地址")
    public Result<List<ProductAddress>> getAllAddresses(@PathVariable Long id) {
        ProductAddress productAddress = new ProductAddress();
        productAddress.setBuyerId(id);
        List<ProductAddress> addresses = addressService.getAllAddresses(productAddress);
        return Result.success(addresses);
    }

     //更新收货地址
    @PutMapping("/update/{id}")
    @ApiOperation("更新收货地址")
    public Result<Void> updateAddress(@RequestBody ProductAddress address) {
        addressService.updateAddress(address);
        return Result.success();
    }

    // 删除收货地址
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除收货地址")
    public Result<Void> deleteAddress(@PathVariable Long id) {
        addressService.deleteAddress(id);
        return Result.success();
    }
} 