package com.sky.controller.user;

import com.sky.context.BaseContext;
import com.sky.dto.RefundApplicationDTO;
import com.sky.dto.RefundQueryDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.RefundApplicationService;
import com.sky.vo.RefundApplicationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 前台退款控制器
 */
@RestController
@RequestMapping("/user/refund")
@Api(tags = "前台退款相关接口")
@Slf4j
public class RefundController {

    @Autowired
    private RefundApplicationService refundApplicationService;

    /**
     * 申请退款
     */
    @PostMapping("/apply")
    @ApiOperation("申请退款")
    public Result<RefundApplicationVO> applyRefund( @RequestBody RefundApplicationDTO refundApplicationDTO) {
        log.info("用户申请退款: {}", refundApplicationDTO);
        
        Long buyerId = BaseContext.getCurrentId();
        RefundApplicationVO result = refundApplicationService.applyRefund(refundApplicationDTO, buyerId);
        
        return Result.success(result);
    }

    /**
     * 取消退款申请
     */
    @PutMapping("/cancel/{refundApplicationId}")
    @ApiOperation("取消退款申请")
    public Result<String> cancelRefundApplication(@PathVariable Long refundApplicationId) {
        log.info("取消退款申请: {}", refundApplicationId);
        
        Long buyerId = BaseContext.getCurrentId();
        boolean success = refundApplicationService.cancelRefundApplication(refundApplicationId, buyerId);
        
        if (success) {
            return Result.success("取消成功");
        } else {
            return Result.error("取消失败");
        }
    }

    /**
     * 查询退款申请详情
     */
    @GetMapping("/{refundApplicationId}")
    @ApiOperation("查询退款申请详情")
    public Result<RefundApplicationVO> getRefundApplication(@PathVariable Long refundApplicationId) {
        log.info("查询退款申请详情: {}", refundApplicationId);

        Long buyerId = BaseContext.getCurrentId();
        RefundApplicationVO result = refundApplicationService.getRefundApplicationById(refundApplicationId);

        // 权限检查：只能查看自己的退款申请
        if (result != null && !result.getBuyerId().equals(buyerId)) {
            return Result.error("无权限查看此退款申请");
        }

        return Result.success(result);
    }

    /**
     * 根据退款申请单号查询详情
     */
    @GetMapping("/no/{refundNo}")
    @ApiOperation("根据退款申请单号查询详情")
    public Result<RefundApplicationVO> getRefundApplicationByNo(@PathVariable String refundNo) {
        log.info("根据退款申请单号查询详情: {}", refundNo);

        Long buyerId = BaseContext.getCurrentId();
        RefundApplicationVO result = refundApplicationService.getRefundApplicationByNo(refundNo);

        // 权限检查：只能查看自己的退款申请
        if (result != null && !result.getBuyerId().equals(buyerId)) {
            return Result.error("无权限查看此退款申请");
        }

        return Result.success(result);
    }

    /**
     * 查询用户的退款申请列表
     */
    @GetMapping("/my")
    @ApiOperation("查询我的退款申请列表")
    public Result<PageResult> getMyRefundApplications(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        log.info("查询我的退款申请列表，page: {}, pageSize: {}", page, pageSize);

        Long buyerId = BaseContext.getCurrentId();
        List<RefundApplicationVO> records = refundApplicationService.getUserRefundApplications(buyerId, page, pageSize);

        // 计算总数
        RefundQueryDTO queryDTO = RefundQueryDTO.builder()
                .buyerId(buyerId)
                .build();
        Long total = refundApplicationService.countRefundApplications(queryDTO);

        return Result.success(new PageResult(total, records));
    }

    /**
     * 分页查询退款申请
     */
    @GetMapping("/page")
    @ApiOperation("分页查询退款申请")
    public Result<PageResult> pageQuery(RefundQueryDTO queryDTO) {
        log.info("分页查询退款申请: {}", queryDTO);

        // 限制只能查询当前用户的退款申请
        Long buyerId = BaseContext.getCurrentId();
        queryDTO.setBuyerId(buyerId);

        List<RefundApplicationVO> records = refundApplicationService.pageQuery(queryDTO);
        Long total = refundApplicationService.countRefundApplications(queryDTO);

        return Result.success(new PageResult(total, records));
    }

    /**
     * 查询退款状态
     */
    @GetMapping("/status/{refundNo}")
    @ApiOperation("查询退款状态")
    public Result<RefundApplicationVO> queryRefundStatus(@PathVariable String refundNo) {
        log.info("查询退款状态: {}", refundNo);

        Long buyerId = BaseContext.getCurrentId();
        RefundApplicationVO result = refundApplicationService.getRefundApplicationByNo(refundNo);

        // 权限检查：只能查看自己的退款申请
        if (result != null && !result.getBuyerId().equals(buyerId)) {
            return Result.error("无权限查看此退款申请");
        }

        return Result.success(result);
    }

    /**
     * 检查订单退款条件
     */
    @GetMapping("/check/{orderId}")
    @ApiOperation("检查订单退款条件")
    public Result<RefundApplicationService.RefundCheckResult> checkRefundEligibility(@PathVariable Long orderId) {
        log.info("检查订单退款条件: {}", orderId);

        Long buyerId = BaseContext.getCurrentId();
        RefundApplicationService.RefundCheckResult result = refundApplicationService.checkRefundEligibility(orderId, buyerId);

        return Result.success(result);
    }

}
