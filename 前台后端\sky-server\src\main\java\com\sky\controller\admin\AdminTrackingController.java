package com.sky.controller.admin;

import com.sky.dto.RegisterTrackingDTO;
import com.sky.dto.TrackingQueryDTO;
import com.sky.result.Result;
import com.sky.service.Track17Service;
import com.sky.service.TrackingService;
import com.sky.vo.LogisticsVO;
import com.sky.vo.RegisterTrackingResponseVO;
import com.sky.vo.TrackingListVO;
import com.sky.vo.TrackingQuotaVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理端物流跟踪控制器
 */
@RestController
@RequestMapping("/admin/tracking")
@Api(tags = "管理端物流跟踪接口")
@Slf4j
public class AdminTrackingController {

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private Track17Service track17Service;

    /**
     * 注册物流跟踪
     */
    @PostMapping("/register")
    @ApiOperation("注册物流跟踪")
    public Result<RegisterTrackingResponseVO> registerTracking(@RequestBody RegisterTrackingDTO registerDTO) {
        log.info("注册物流跟踪: {}", registerDTO);
        
        try {
            RegisterTrackingResponseVO result = trackingService.registerAndSaveTracking(registerDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("注册物流跟踪失败", e);
            return Result.error("注册失败: " + e.getMessage());
        }
    }

    /**
     * 批量注册物流跟踪
     */
    @PostMapping("/register/batch")
    @ApiOperation("批量注册物流跟踪")
    public Result<RegisterTrackingResponseVO> batchRegisterTracking(@RequestBody List<RegisterTrackingDTO> registerDTOs) {
        log.info("批量注册物流跟踪: {}", registerDTOs.size());
        
        try {
            if (registerDTOs == null || registerDTOs.isEmpty()) {
                return Result.error("注册列表不能为空");
            }
            
            if (registerDTOs.size() > 40) {
                return Result.error("单次注册数量不能超过40个");
            }
            
            RegisterTrackingResponseVO result = trackingService.batchRegisterTracking(registerDTOs);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量注册物流跟踪失败", e);
            return Result.error("批量注册失败: " + e.getMessage());
        }
    }

    /**
     * 根据物流单号查询物流信息
     */
    @GetMapping("/tracking/{trackingNumber}")
    @ApiOperation("根据物流单号查询物流信息")
    public Result<LogisticsVO> getLogisticsByTrackingNumber(@PathVariable String trackingNumber) {
        log.info("根据物流单号查询物流信息: {}", trackingNumber);
        
        try {
            LogisticsVO result = trackingService.getLogisticsByTrackingNumber(trackingNumber);
            if (result != null) {
                return Result.success(result);
            } else {
                return Result.error("物流信息不存在");
            }
        } catch (Exception e) {
            log.error("查询物流信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 同步物流状态
     */
    @PutMapping("/sync/{trackingNumber}")
    @ApiOperation("同步物流状态")
    public Result<String> syncTrackingStatus(@PathVariable String trackingNumber) {
        log.info("同步物流状态: {}", trackingNumber);
        
        try {
            boolean success = trackingService.syncTrackingStatus(trackingNumber);
            if (success) {
                return Result.success("同步成功");
            } else {
                return Result.error("同步失败");
            }
        } catch (Exception e) {
            log.error("同步物流状态失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 批量同步物流状态
     */
    @PutMapping("/sync/batch")
    @ApiOperation("批量同步物流状态")
    public Result<String> batchSyncTrackingStatus(@RequestBody List<String> trackingNumbers) {
        log.info("批量同步物流状态: {}", trackingNumbers.size());
        
        try {
            if (trackingNumbers == null || trackingNumbers.isEmpty()) {
                return Result.error("物流单号列表不能为空");
            }
            
            if (trackingNumbers.size() > 100) {
                return Result.error("单次同步数量不能超过100个");
            }
            
            boolean success = trackingService.batchSyncTrackingStatus(trackingNumbers);
            if (success) {
                return Result.success("批量同步完成");
            } else {
                return Result.error("批量同步失败");
            }
        } catch (Exception e) {
            log.error("批量同步物流状态失败", e);
            return Result.error("批量同步失败: " + e.getMessage());
        }
    }

    /**
     * 停止物流跟踪
     */
    @PutMapping("/stop/{trackingNumber}")
    @ApiOperation("停止物流跟踪")
    public Result<RegisterTrackingResponseVO> stopTracking(@PathVariable String trackingNumber, @RequestParam Integer carrierCode) {
        log.info("停止物流跟踪: {} {}", trackingNumber, carrierCode);
        
        try {
            RegisterTrackingResponseVO result = trackingService.stopTracking(trackingNumber, carrierCode);
            return Result.success(result);
        } catch (Exception e) {
            log.error("停止物流跟踪失败", e);
            return Result.error("停止失败: " + e.getMessage());
        }
    }

    /**
     * 重启物流跟踪
     */
    @PutMapping("/restart/{trackingNumber}")
    @ApiOperation("重启物流跟踪")
    public Result<RegisterTrackingResponseVO> restartTracking(@PathVariable String trackingNumber, @RequestParam Integer carrierCode) {
        log.info("重启物流跟踪: {} {}", trackingNumber, carrierCode);
        
        try {
            RegisterTrackingResponseVO result = trackingService.restartTracking(trackingNumber, carrierCode);
            return Result.success(result);
        } catch (Exception e) {
            log.error("重启物流跟踪失败", e);
            return Result.error("重启失败: " + e.getMessage());
        }
    }

    /**
     * 删除物流跟踪
     */
    @DeleteMapping("/{trackingNumber}")
    @ApiOperation("删除物流跟踪")
    public Result<RegisterTrackingResponseVO> deleteTracking(@PathVariable String trackingNumber, @RequestParam Integer carrierCode) {
        log.info("删除物流跟踪: {} {}", trackingNumber, carrierCode);
        
        try {
            RegisterTrackingResponseVO result = trackingService.deleteTracking(trackingNumber, carrierCode);
            return Result.success(result);
        } catch (Exception e) {
            log.error("删除物流跟踪失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取17TRACK配额信息
     */
    @GetMapping("/quota")
    @ApiOperation("获取17TRACK配额信息")
    public Result<TrackingQuotaVO> getQuota() {
        log.info("获取17TRACK配额信息");
        
        try {
            TrackingQuotaVO result = track17Service.getQuota();
            if (result != null) {
                return Result.success(result);
            } else {
                return Result.error("获取配额信息失败");
            }
        } catch (Exception e) {
            log.error("获取配额信息失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取跟踪列表
     */
    @PostMapping("/list")
    @ApiOperation("获取跟踪列表")
    public Result<TrackingListVO> getTrackingList(@RequestBody TrackingQueryDTO queryDTO) {
        log.info("获取跟踪列表: {}", queryDTO);
        
        try {
            // 设置默认分页参数
            if (queryDTO.getPage() == null || queryDTO.getPage() < 1) {
                queryDTO.setPage(1);
            }
            if (queryDTO.getPageSize() == null || queryDTO.getPageSize() < 1) {
                queryDTO.setPageSize(10);
            }
            if (queryDTO.getPageSize() > 100) {
                queryDTO.setPageSize(100);
            }
            
            TrackingListVO result = track17Service.getTrackingList(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取跟踪列表失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的运输商列表
     */
    @GetMapping("/carriers")
    @ApiOperation("获取支持的运输商列表")
    public Result<List<Object>> getSupportedCarriers() {
        log.info("获取支持的运输商列表");
        
        try {
            List<Object> carriers = track17Service.getSupportedCarriers();
            return Result.success(carriers);
        } catch (Exception e) {
            log.error("获取运输商列表失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取物流统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取物流统计信息")
    public Result<Object> getTrackingStatistics() {
        log.info("获取物流统计信息");
        
        try {
            Object statistics = trackingService.getTrackingStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取物流统计信息失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询物流信息
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation("根据订单ID查询物流信息")
    public Result<List<LogisticsVO>> getLogisticsByOrderId(@PathVariable Long orderId) {
        log.info("根据订单ID查询物流信息: {}", orderId);
        
        try {
            List<LogisticsVO> result = trackingService.getLogisticsByOrderId(orderId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询物流信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
