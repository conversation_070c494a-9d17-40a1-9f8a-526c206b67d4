package com.sky.mapper;

import com.sky.entity.CommissionSettings;
import com.sky.entity.TeamLeader;
import com.sky.entity.CommissionRecord;
import com.sky.vo.CommissionStats;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface CommissionMapper {
    CommissionSettings getSettings();
    void saveSettings(CommissionSettings settings);
    Long countLeaders(String keyword, String status);
    List<TeamLeader> getLeaders(Integer offset, Integer limit, String keyword, String status, String sortBy, String sortOrder);
    TeamLeader getLeaderDetail(Long id);
    void addLeader(TeamLeader leader);
    void updateLeader(TeamLeader leader);
    void deleteLeader(Long id);
    void updateLeaderStatus(Long id, String status, String reason);
    CommissionStats getStats(String period, String startDate, String endDate);
    Long countRecords(Long leaderId, String status, String orderType, String dateRange, String startDate, String endDate);
    List<CommissionRecord> getRecords(Integer offset, Integer limit, Long leaderId, String status, String orderType, String dateRange, String startDate, String endDate);
} 