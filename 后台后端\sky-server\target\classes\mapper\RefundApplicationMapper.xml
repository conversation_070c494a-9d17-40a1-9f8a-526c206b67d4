<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.RefundApplicationMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sky.entity.RefundApplication">
        <id column="id" property="id" />
        <result column="refund_no" property="refundNo" />
        <result column="order_id" property="orderId" />
        <result column="order_number" property="orderNumber" />
        <result column="buyer_id" property="buyerId" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_reason" property="refundReason" />
        <result column="refund_type" property="refundType" />
        <result column="application_status" property="applicationStatus" />
        <result column="need_approval" property="needApproval" />
        <result column="approval_status" property="approvalStatus" />
        <result column="approver_id" property="approverId" />
        <result column="approval_time" property="approvalTime" />
        <result column="approval_remark" property="approvalRemark" />
        <result column="refund_method" property="refundMethod" />
        <result column="actual_refund_amount" property="actualRefundAmount" />
        <result column="refund_time" property="refundTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 退款申请VO映射结果 -->
    <resultMap id="RefundApplicationVOMap" type="com.sky.vo.RefundApplicationVO">
        <id column="id" property="id" />
        <result column="refund_no" property="refundNo" />
        <result column="order_id" property="orderId" />
        <result column="order_number" property="orderNumber" />
        <result column="order_amount" property="orderAmount" />
        <result column="buyer_id" property="buyerId" />
        <result column="buyer_name" property="buyerName" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_reason" property="refundReason" />
        <result column="refund_type" property="refundType" />
        <result column="application_status" property="applicationStatus" />
        <result column="need_approval" property="needApproval" />
        <result column="approval_status" property="approvalStatus" />
        <result column="approver_id" property="approverId" />
        <result column="approver_name" property="approverName" />
        <result column="approval_time" property="approvalTime" />
        <result column="approval_remark" property="approvalRemark" />
        <result column="refund_method" property="refundMethod" />
        <result column="actual_refund_amount" property="actualRefundAmount" />
        <result column="refund_time" property="refundTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 插入退款申请 -->
    <insert id="insert" parameterType="com.sky.entity.RefundApplication" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO refund_application (
            refund_no, order_id, order_number, buyer_id, refund_amount, refund_reason, refund_type,
            application_status, need_approval, approval_status, refund_method, create_time, update_time
        ) VALUES (
            #{refundNo}, #{orderId}, #{orderNumber}, #{buyerId}, #{refundAmount}, #{refundReason}, #{refundType},
            #{applicationStatus}, #{needApproval}, #{approvalStatus}, #{refundMethod}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM refund_application WHERE id = #{id}
    </select>

    <!-- 根据订单ID查询退款申请 -->
    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT * FROM refund_application WHERE order_id = #{orderId} ORDER BY create_time DESC
    </select>

    <!-- 根据买家ID查询退款申请 -->
    <select id="selectByBuyerId" resultMap="BaseResultMap">
        SELECT * FROM refund_application WHERE buyer_id = #{buyerId} ORDER BY create_time DESC
    </select>

    <!-- 根据退款申请单号查询 -->
    <select id="selectByRefundNo" resultMap="BaseResultMap">
        SELECT * FROM refund_application WHERE refund_no = #{refundNo}
    </select>

    <!-- 查询待审核的退款申请 -->
    <select id="selectPendingApproval" resultMap="BaseResultMap">
        SELECT * FROM refund_application 
        WHERE need_approval = 1 AND approval_status = 1 
        ORDER BY create_time ASC
    </select>

    <!-- 根据状态查询退款申请列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT * FROM refund_application WHERE application_status = #{applicationStatus} ORDER BY create_time DESC
    </select>

    <!-- 分页查询退款申请（带详细信息） -->
    <select id="selectPageWithDetails" resultMap="RefundApplicationVOMap">
        SELECT
            ra.*,
            o.amount as order_amount,
            u.account_name as buyer_name,
            m.account_name as approver_name
        FROM refund_application ra
        LEFT JOIN orders o ON ra.order_id = o.id
        LEFT JOIN buyer u ON ra.buyer_id = u.id
        LEFT JOIN Manager m ON ra.approver_id = m.id
        <where>
            <if test="buyerId != null">
                AND ra.buyer_id = #{buyerId}
            </if>
            <if test="applicationStatus != null">
                AND ra.application_status = #{applicationStatus}
            </if>
            <if test="needApproval != null">
                AND ra.need_approval = #{needApproval}
            </if>
            <if test="approvalStatus != null">
                AND ra.approval_status = #{approvalStatus}
            </if>
            <if test="startTime != null">
                AND ra.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ra.create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY ra.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计退款申请数量 -->
    <select id="countRefundApplications" resultType="java.lang.Long">
        SELECT COUNT(*) FROM refund_application ra
        <where>
            <if test="buyerId != null">
                AND ra.buyer_id = #{buyerId}
            </if>
            <if test="applicationStatus != null">
                AND ra.application_status = #{applicationStatus}
            </if>
            <if test="needApproval != null">
                AND ra.need_approval = #{needApproval}
            </if>
            <if test="approvalStatus != null">
                AND ra.approval_status = #{approvalStatus}
            </if>
            <if test="startTime != null">
                AND ra.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ra.create_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 根据ID查询退款申请详情（包含关联信息） -->
    <select id="selectDetailById" resultMap="RefundApplicationVOMap">
        SELECT
            ra.*,
            o.amount as order_amount,
            u.account_name as buyer_name,
            m.account_name as approver_name
        FROM refund_application ra
        LEFT JOIN orders o ON ra.order_id = o.id
        LEFT JOIN buyer u ON ra.buyer_id = u.id
        LEFT JOIN Manager m ON ra.approver_id = m.id
        WHERE ra.id = #{id}
    </select>

    <!-- 更新申请状态 -->
    <update id="updateApplicationStatus">
        UPDATE refund_application 
        SET application_status = #{applicationStatus},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 更新审核信息 -->
    <update id="updateApprovalInfo">
        UPDATE refund_application 
        SET approval_status = #{approvalStatus},
            approver_id = #{approverId},
            approval_time = #{approvalTime},
            approval_remark = #{approvalRemark},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 更新退款完成信息 -->
    <update id="updateRefundCompleteInfo">
        UPDATE refund_application 
        SET actual_refund_amount = #{actualRefundAmount},
            refund_time = #{refundTime},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>
</mapper>
