package com.sky.mapper;

import com.sky.entity.Message;
import com.sky.entity.MessageRecipient;
import com.sky.vo.RecipientVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MessageMapper {

    /**
     * 插入消息
     */
    void insertMessage(Message message);

    /**
     * 插入消息接收者关联
     */
    void insertMessageRecipient(MessageRecipient messageRecipient);

    /**
     * 批量插入消息接收者关联
     */
    void batchInsertMessageRecipient(@Param("list") List<MessageRecipient> list);

    /**
     * 根据接收者类型和ID查询消息列表
     */
    List<Message> getMessagesByRecipient(@Param("recipientType") String recipientType,
                                         @Param("recipientId") Long recipientId);

    /**
     * 根据消息ID和接收者查询消息详情（带isRead）
     */
    Message getMessageById(@Param("msgID") String msgID,
                          @Param("recipientType") String recipientType,
                          @Param("recipientId") Long recipientId);

    /**
     * 根据接收者类型和ID查询未读消息数量
     */
    Integer getUnreadCount(@Param("recipientType") String recipientType,
                           @Param("recipientId") Long recipientId);

    /**
     * 标记消息为已读
     */
    void markAsRead(@Param("msgID") String msgID,
                    @Param("recipientType") String recipientType,
                    @Param("recipientId") Long recipientId);

    /**
     * 批量标记消息为已读
     */
    void batchMarkAsRead(@Param("msgIDs") List<String> msgIDs,
                         @Param("recipientType") String recipientType,
                         @Param("recipientId") Long recipientId);

    /**
     * 标记所有消息为已读
     */
    void markAllAsRead(@Param("recipientType") String recipientType,
                       @Param("recipientId") Long recipientId);

    /**
     * 删除消息
     */
    void deleteMessage(@Param("msgID") String msgID,
                       @Param("recipientType") String recipientType,
                       @Param("recipientId") Long recipientId);

    /**
     * 批量删除消息
     */
    void batchDeleteMessage(@Param("msgIDs") List<String> msgIDs,
                            @Param("recipientType") String recipientType,
                            @Param("recipientId") Long recipientId);

    /**
     * 查询卖家列表
     */
    List<RecipientVO> getSellerList(@Param("keyword") String keyword);

    /**
     * 查询用户列表
     */
    List<RecipientVO> getUserList(@Param("keyword") String keyword);
} 