package com.sky.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Permission {

    // ================= 核心权限校验字段 =================
    /**
     * 权限ID（对应数据库 id）
     */
    private Long id;

    /**
     * 请求路径（对应数据库 path）
     * 示例：/product, /api/delete
     */
    private String path;

    /**
     * HTTP方法（对应数据库 method）
     * 示例：GET, POST, null（当 permType=MENU 时可为空）
     */
    private String method;

    /**
     * 权限状态（对应数据库 status）
     * 允许值：ENABLED（启用）, DISABLED（禁用）
     */
    private String status;


    // ================= 权限元信息（用于展示/管理） =================
    /**
     * 权限名称（对应数据库 perm_name）
     * 示例：商品管理、删除订单
     */
    private String permName;

    /**
     * 权限唯一标识（对应数据库 perm_code）
     * 示例：product:manage, order:delete
     */
    private String permCode;

    /**
     * 权限类型（对应数据库 perm_type）
     * 允许值：MENU（菜单）, BUTTON（按钮）, API（接口）
     */
    private String permType;

    /**
     * 前端组件路径（对应数据库 component）
     * 示例：@/views/product/List.vue
     */
    private String component;

    /**
     * 权限描述（数据库无此字段，扩展用）
     * 示例："允许管理商品信息"
     */
    private String description;
}