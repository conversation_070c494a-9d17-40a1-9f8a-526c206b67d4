package com.sky.controller.pay;

import com.sky.Utils.WechatPayService;
import com.sky.entity.PaymentRequest;
import com.sky.properties.WechatPayProperties;
import com.sky.result.Result;
import com.sky.service.WechatPayOrderService;
import com.sky.vo.WechatPayOrderQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import com.sky.dto.WechatPayOrderQueryDTO;

/**
 * 微信境外支付控制器
 */
@Slf4j
@RestController
@RequestMapping("/pay/wechat")
public class WechatPayController {

    @Autowired
    private WechatPayService wechatPayService;

    @Autowired
    private WechatPayOrderService wechatPayOrderService;

    @Autowired
    private WechatPayProperties wechatPayProperties;

    /**
     * 创建微信境外支付订单并返回二维码链接
     * @param request 支付请求
     * @param httpServletRequest HTTP请求对象，用于获取客户端IP
     * @return 二维码链接
     */
    @PostMapping("/native/qrcode")
    public Result<Map<String, String>> createGlobalPaymentQRCode(
            @RequestBody PaymentRequest request,
            HttpServletRequest httpServletRequest) {
        try {
            // 如果没有订单号，生成一个
            if (request.getOutTradeNo() == null || request.getOutTradeNo().isEmpty()) {
                request.setOutTradeNo("order_" + System.currentTimeMillis() + "_" + 
                        UUID.randomUUID().toString().substring(0, 8));
                log.info("前端未提供订单号，生成新订单号: {}", request.getOutTradeNo());
            } else {
                log.info("使用前端提供的订单号: {}", request.getOutTradeNo());
            }
            
            // 获取客户端真实IP
            String clientIp = getClientIp(httpServletRequest);
            log.info("接收到微信境外支付请求 - 订单号: {}, 金额: {}, 客户端IP: {}", 
                    request.getOutTradeNo(), request.getTotal(), clientIp);
            
            // 构造商品描述
            String description = request.getDescription();
            if (description == null || description.isEmpty()) {
                description = "购买商品: " + request.getOutTradeNo();
            }
            
            // 调用微信支付服务创建订单
            String codeUrl = wechatPayService.createOrder(
                    request.getOutTradeNo(),
                    request.getTotal(),
                    description,
                    "************"
            );
            
            // 返回二维码链接和原始订单号（不重新生成）
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("codeUrl", codeUrl);
            resultMap.put("orderNo", request.getOutTradeNo());
            
            return Result.success(resultMap);
        } catch (Exception e) {
            log.error("生成境外支付二维码失败", e);
            return Result.error("生成支付二维码失败：" + e.getMessage());
        }
    }
    
    /**
     * 微信支付回调通知接口
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/notify")
    public ResponseEntity<String> payNotify(HttpServletRequest request) {
        log.info("接收到微信支付回调通知");
        
        try {
            // 读取请求体
            StringBuilder sb = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            String body = sb.toString();
            log.info("微信支付回调通知内容: {}", body);
            
            // 验证签名和处理通知（实际应用中需要实现）
            // TODO: 实现签名验证和业务处理逻辑
            
            // 返回成功响应
            Map<String, String> responseMap = new HashMap<>();
            responseMap.put("code", "SUCCESS");
            responseMap.put("message", "成功");
            
            return ResponseEntity
                    .status(HttpStatus.OK)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("{\"code\":\"SUCCESS\",\"message\":\"成功\"}");
        } catch (IOException e) {
            log.error("处理微信支付回调通知失败", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"code\":\"FAIL\",\"message\":\"失败\"}");
        }
    }
    
    /**
     * 查询支付状态
     * @param request HTTP请求
     * @return 支付状态
     */
    @GetMapping("/queryPayStatus")
    public Result<Map<String, String>> queryPayStatus(HttpServletRequest request) {
        String orderNo = request.getParameter("orderNo");
        log.info("查询支付状态，原始订单号: {}", orderNo);
        
        if (orderNo == null || orderNo.isEmpty()) {
            return Result.error("订单号不能为空");
        }
        
        try {
            // 不对订单号进行任何修改，直接使用前端传入的订单号
            log.info("使用原始订单号查询支付状态: {}", orderNo);
            
            // 构建查询DTO
            WechatPayOrderQueryDTO queryDTO = new WechatPayOrderQueryDTO();
            queryDTO.setOutTradeNo(orderNo);
            queryDTO.setDirectMode(true); // 使用直连模式
            
            log.info("查询支付状态DTO: {}", queryDTO);
            
            // 查询订单状态
            WechatPayOrderQueryVO orderInfo = wechatPayOrderService.queryOrder(queryDTO);
            log.info("查询支付状态响应: {}", orderInfo);
            
            // 返回支付状态
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("orderNo", orderNo);
            resultMap.put("payStatus", orderInfo.getTradeState());
            
            // 根据官方文档，SUCCESS表示支付成功
            if ("SUCCESS".equals(orderInfo.getTradeState())) {
                return Result.success(resultMap);
            } else {
                // 如果是NOTPAY状态，返回特定的消息
                if ("NOTPAY".equals(orderInfo.getTradeState())) {
                    return Result.error("订单未支付");
                } else {
                    return Result.error("支付未完成，当前状态: " + orderInfo.getTradeState() + 
                            (orderInfo.getTradeStateDesc() != null ? " - " + orderInfo.getTradeStateDesc() : ""));
                }
            }
        } catch (Exception e) {
            log.error("查询订单失败: {}", e.getMessage(), e);
            return Result.error("查询订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端真实IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果是多级代理，取第一个IP地址
        if (ip != null && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }
}
