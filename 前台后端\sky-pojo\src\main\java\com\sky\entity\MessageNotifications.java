package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageNotifications implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String created_at;

    private String type;

    private String content;

    private String status;

    private String operation;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

    private int seller_id;

}
