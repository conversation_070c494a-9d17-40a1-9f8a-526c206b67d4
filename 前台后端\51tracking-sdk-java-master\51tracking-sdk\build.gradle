/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id 'java-library'
    id 'maven-publish'
}

repositories {
    mavenCentral()
}

dependencies {
    api 'org.projectlombok:lombok:1.16.14'
    annotationProcessor 'org.projectlombok:lombok:1.16.14'
    api 'com.fasterxml.jackson.core:jackson-databind:2.12.5'
    testImplementation 'junit:junit:4.11'
}

group = 'io.github.51tracking'
version = '1.0.3'
description = '51tracking-sdk-java'
java.sourceCompatibility = JavaVersion.VERSION_1_8

publishing {
    publications {
        maven(MavenPublication) {
            from(components.java)
        }
    }
    repositories {
        mavenLocal()
    }
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

tasks.withType(Javadoc) {
    options.encoding = 'UTF-8'
}
