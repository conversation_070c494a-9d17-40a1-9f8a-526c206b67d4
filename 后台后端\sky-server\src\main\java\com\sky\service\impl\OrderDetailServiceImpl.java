package com.sky.service.impl;

import com.sky.entity.BusinessException;
import com.sky.entity.OrderDetail;
import com.sky.mapper.OrderDetailMapper;
import com.sky.service.OrderDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrderDetailServiceImpl implements OrderDetailService {

    @Autowired
    private OrderDetailMapper orderDetailMapper;

    @Override
    public OrderDetail getOrderDetailById(Long id) {
        OrderDetail orderDetail = orderDetailMapper.findById(id);
        if (orderDetail == null) {
            throw new BusinessException("订单明细不存在");
        }
        return orderDetail;
    }

    @Override
    public void updateOrderDetail(OrderDetail orderDetail) {
        if (orderDetail.getId() == null) {
            throw new BusinessException("订单明细 ID 不能为空");
        }
        if (orderDetail.getQuantity() == null && orderDetail.getDiscount() == null) {
            throw new BusinessException("至少提供一个需要修改的字段（数量或折扣）");
        }
        orderDetailMapper.updateOrderDetail(orderDetail);
    }
}
