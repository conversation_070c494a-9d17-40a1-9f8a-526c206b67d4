package com.sky.mapper;

import com.sky.entity.Warehouse;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface WarehouseMapper {
    @Select("SELECT * FROM warehouse_list")
    List<Warehouse> findAll();

    @Insert("INSERT INTO warehouse_list (warehouse_name, address, postal_code, contact_name, contact_phone) " +
            "VALUES (#{warehouseName}, #{address}, #{postalCode}, #{contactName}, #{contactPhone})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Warehouse warehouse);
}