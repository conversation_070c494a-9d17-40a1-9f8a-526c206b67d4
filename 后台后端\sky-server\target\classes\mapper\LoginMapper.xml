<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.LoginMapper">

    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        insert into seller (account_name,password,gender,phone,email,photo_url,user_role,account_status,create_time,last_login_time)
        values (#{accountName},#{password},#{gender},#{phone},#{email},#{photoUrl},#{userRole},#{accountStatus},#{createTime},#{lastLoginTime})
    </insert>
    <insert id="saveData" parameterType="com.sky.entity.EVPI">
        INSERT INTO ry_mall.shop_info (
        <trim suffixOverrides=",">
            <if test="sellerId != null">seller_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="companyName != null">company_name,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="licenseValidity != null">license_validity,</if>
            <if test="companyIntro != null">company_intro,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="addressDetail != null">address_detail,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="lastUpdated != null">last_updated,</if>
            <if test="businessImgUrl != null">business_img_url,</if>
            <if test="warehouseImgUrl1 != null">warehouse_img_url1,</if>
            <if test="warehouseImgUrl2 != null">warehouse_img_url2,</if>
            <if test="warehouseImgUrl3 != null">warehouse_img_url3,</if>
            <if test="IDCard1 != null">id_card1,</if>
            <if test="IDCard2 != null">id_card2,</if>
        </trim>
        )
        VALUES (
        <trim suffixOverrides=",">
            <if test="sellerId != null">#{sellerId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="licenseValidity != null">#{licenseValidity},</if>
            <if test="companyIntro != null">#{companyIntro},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="addressDetail != null">#{addressDetail},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="lastUpdated != null">#{lastUpdated},</if>
            <if test="businessImgUrl != null">#{businessImgUrl},</if>
            <if test="warehouseImgUrl1 != null">#{warehouseImgUrl1},</if>
            <if test="warehouseImgUrl2 != null">#{warehouseImgUrl2},</if>
            <if test="warehouseImgUrl3 != null">#{warehouseImgUrl3},</if>
            <if test="IDCard1 != null">#{IDCard1},</if>
            <if test="IDCard2 != null">#{IDCard2},</if>
        </trim>
        )
    </insert>
    <update id="update" parameterType="com.sky.entity.Seller">
        update seller
        <set>
            <if test="accountName != null">
                account_name = #{accountName},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="gender != null">
                gender = #{gender},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime}
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
