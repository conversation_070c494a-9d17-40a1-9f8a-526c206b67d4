# 订单详情集成功能测试用例

## 测试环境准备

### 1. 数据库准备
- 确保数据库中已创建相关表
- 插入测试数据
- 验证表结构和数据完整性

### 2. 服务启动
```bash
cd 前台后端/sky-server
mvn spring-boot:run
```

### 3. 测试工具
- Postman 或 curl
- 浏览器开发者工具
- 数据库客户端

## API测试用例

### 测试用例1：获取订单完整详情
**接口**：`GET /user/order-detail/{orderId}`

**测试数据**：
```bash
# 正常情况 - 存在的订单
curl -X GET "http://localhost:8443/user/order-detail/1" \
  -H "Authorization: Bearer your-jwt-token"

# 异常情况 - 不存在的订单
curl -X GET "http://localhost:8443/user/order-detail/999" \
  -H "Authorization: Bearer your-jwt-token"

# 异常情况 - 无权限访问的订单
curl -X GET "http://localhost:8443/user/order-detail/1" \
  -H "Authorization: Bearer other-user-token"
```

**预期结果**：
- 正常情况：返回完整的订单详情信息
- 不存在订单：返回404或相应错误信息
- 无权限：返回403权限错误

### 测试用例2：根据订单号查询
**接口**：`GET /user/order-detail/by-number/{orderNumber}`

**测试数据**：
```bash
# 正常情况
curl -X GET "http://localhost:8443/user/order-detail/by-number/202412190001" \
  -H "Authorization: Bearer your-jwt-token"

# 异常情况 - 不存在的订单号
curl -X GET "http://localhost:8443/user/order-detail/by-number/NOTEXIST001" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 正常情况：返回对应订单的详情
- 不存在：返回相应错误信息

### 测试用例3：获取用户订单列表
**接口**：`GET /user/order-detail/list`

**测试数据**：
```bash
# 获取所有订单
curl -X GET "http://localhost:8443/user/order-detail/list" \
  -H "Authorization: Bearer your-jwt-token"

# 按状态筛选
curl -X GET "http://localhost:8443/user/order-detail/list?status=3" \
  -H "Authorization: Bearer your-jwt-token"

# 分页查询
curl -X GET "http://localhost:8443/user/order-detail/list?pageNum=1&pageSize=5" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 返回用户的订单列表
- 状态筛选正确
- 分页功能正常

### 测试用例4：同步物流信息
**接口**：`POST /user/order-detail/{orderId}/sync-logistics`

**测试数据**：
```bash
# 有物流信息的订单
curl -X POST "http://localhost:8443/user/order-detail/1/sync-logistics" \
  -H "Authorization: Bearer your-jwt-token"

# 无物流信息的订单
curl -X POST "http://localhost:8443/user/order-detail/999/sync-logistics" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 有物流信息：同步成功或失败的明确提示
- 无物流信息：返回相应错误提示

### 测试用例5：获取实时物流信息
**接口**：`GET /user/order-detail/{orderId}/realtime-logistics`

**测试数据**：
```bash
# 有物流单号的订单
curl -X GET "http://localhost:8443/user/order-detail/1/realtime-logistics" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 返回实时物流轨迹信息
- 数据格式正确

### 测试用例6：确认收货
**接口**：`POST /user/order-detail/{orderId}/confirm-receipt`

**前置条件**：订单状态为"待收货"(status=3)

**测试数据**：
```bash
# 正常确认收货
curl -X POST "http://localhost:8443/user/order-detail/1/confirm-receipt" \
  -H "Authorization: Bearer your-jwt-token"

# 状态不允许的订单
curl -X POST "http://localhost:8443/user/order-detail/2/confirm-receipt" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 正常情况：确认收货成功，订单状态变为已完成
- 状态不允许：返回相应错误信息

### 测试用例7：申请退款
**接口**：`POST /user/order-detail/{orderId}/apply-refund`

**测试数据**：
```bash
# 正常申请退款
curl -X POST "http://localhost:8443/user/order-detail/1/apply-refund" \
  -H "Authorization: Bearer your-jwt-token" \
  -d "reason=商品质量问题"

# 状态不允许退款的订单
curl -X POST "http://localhost:8443/user/order-detail/999/apply-refund" \
  -H "Authorization: Bearer your-jwt-token" \
  -d "reason=不想要了"
```

**预期结果**：
- 正常情况：退款申请提交成功
- 状态不允许：返回相应错误信息

### 测试用例8：取消订单
**接口**：`POST /user/order-detail/{orderId}/cancel`

**测试数据**：
```bash
# 正常取消订单
curl -X POST "http://localhost:8443/user/order-detail/1/cancel" \
  -H "Authorization: Bearer your-jwt-token" \
  -d "reason=不需要了"

# 状态不允许取消的订单
curl -X POST "http://localhost:8443/user/order-detail/2/cancel" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 正常情况：订单取消成功
- 状态不允许：返回相应错误信息

### 测试用例9：获取订单统计
**接口**：`GET /user/order-detail/statistics`

**测试数据**：
```bash
curl -X GET "http://localhost:8443/user/order-detail/statistics" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 返回用户的订单统计信息
- 包含各状态订单数量、总金额等

### 测试用例10：检查操作权限
**接口**：`GET /user/order-detail/{orderId}/check-action/{action}`

**测试数据**：
```bash
# 检查是否可以取消
curl -X GET "http://localhost:8443/user/order-detail/1/check-action/cancel" \
  -H "Authorization: Bearer your-jwt-token"

# 检查是否可以退款
curl -X GET "http://localhost:8443/user/order-detail/1/check-action/refund" \
  -H "Authorization: Bearer your-jwt-token"

# 检查是否可以确认收货
curl -X GET "http://localhost:8443/user/order-detail/1/check-action/confirm" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 返回true或false
- 权限判断正确

## 数据验证测试

### 测试用例11：数据完整性验证
**目标**：验证返回的订单详情数据完整性

**验证点**：
1. 订单基础信息是否完整
2. 物流信息是否正确关联
3. 地址信息是否完整
4. 支付信息是否正确
5. 时间字段格式是否正确

### 测试用例12：物流状态映射验证
**目标**：验证物流状态的中文描述是否正确

**测试数据**：
```bash
curl -X GET "http://localhost:8443/user/order-detail/logistics-status-desc/InTransit" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 返回"运输途中"
- 各种状态映射正确

## 性能测试

### 测试用例13：并发查询测试
**目标**：测试系统在并发查询下的表现

**测试方法**：
- 使用JMeter或Apache Bench
- 并发用户数：50
- 请求总数：1000
- 测试时长：60秒

**预期结果**：
- 响应时间 < 2秒
- 成功率 > 99%
- 无系统错误

### 测试用例14：大数据量测试
**目标**：测试大量订单数据下的查询性能

**前置条件**：
- 插入10000条订单数据
- 每个订单包含完整的物流、地址、支付信息

**测试方法**：
- 查询用户订单列表
- 查询订单详情
- 统计信息查询

**预期结果**：
- 列表查询 < 1秒
- 详情查询 < 500ms
- 统计查询 < 2秒

## 异常测试

### 测试用例15：网络异常测试
**目标**：测试17TRACK API不可用时的处理

**测试方法**：
- 临时修改API配置为无效地址
- 调用物流同步接口
- 调用实时物流查询接口

**预期结果**：
- 系统不会崩溃
- 返回友好的错误提示
- 降级到本地数据

### 测试用例16：数据库异常测试
**目标**：测试数据库连接异常时的处理

**测试方法**：
- 临时断开数据库连接
- 调用各个查询接口

**预期结果**：
- 返回数据库连接错误
- 系统保持稳定

## 安全测试

### 测试用例17：权限验证测试
**目标**：验证用户只能访问自己的订单

**测试方法**：
- 用户A尝试访问用户B的订单
- 使用无效的JWT token
- 使用过期的JWT token

**预期结果**：
- 返回403权限错误
- 返回401认证错误

### 测试用例18：SQL注入测试
**目标**：验证系统对SQL注入的防护

**测试数据**：
```bash
# 尝试SQL注入
curl -X GET "http://localhost:8443/user/order-detail/by-number/'; DROP TABLE orders; --" \
  -H "Authorization: Bearer your-jwt-token"
```

**预期结果**：
- 系统正常处理，不会执行恶意SQL
- 返回订单不存在的错误

## 测试报告模板

### 测试结果记录
| 测试用例 | 测试结果 | 响应时间 | 错误信息 | 备注 |
|---------|---------|---------|---------|------|
| 获取订单详情 | ✅/❌ | XXXms | - | - |
| 订单列表查询 | ✅/❌ | XXXms | - | - |
| 物流信息同步 | ✅/❌ | XXXms | - | - |
| 确认收货 | ✅/❌ | XXXms | - | - |
| 申请退款 | ✅/❌ | XXXms | - | - |
| 权限验证 | ✅/❌ | XXXms | - | - |

### 问题记录
1. **问题描述**：
2. **重现步骤**：
3. **预期结果**：
4. **实际结果**：
5. **解决方案**：

## 自动化测试脚本

### Postman Collection
创建Postman测试集合，包含所有API测试用例，支持环境变量和自动化测试。

### JUnit测试
```java
@SpringBootTest
@AutoConfigureTestDatabase
class OrderDetailIntegrationServiceTest {
    
    @Test
    void testGetOrderDetail() {
        // 测试获取订单详情
    }
    
    @Test
    void testSyncLogistics() {
        // 测试物流同步
    }
    
    @Test
    void testConfirmReceipt() {
        // 测试确认收货
    }
}
```

## 注意事项

1. **测试数据准备**：确保有足够的测试数据覆盖各种场景
2. **环境隔离**：使用独立的测试环境，避免影响生产数据
3. **API限制**：注意17TRACK API的调用频率限制
4. **数据清理**：测试完成后清理测试数据
5. **日志监控**：关注应用日志中的错误和警告信息
