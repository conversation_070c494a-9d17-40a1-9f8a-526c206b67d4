# 收账账户功能API测试文档

## 测试环境
- 服务器地址：http://localhost:8080
- 数据库：云服务器 ************
- 测试商家ID：16

## 商家端接口测试

### 1. 添加银行卡账户
```bash
curl -X POST "http://localhost:8080/merchant/payment-account" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "accountType": 1,
    "accountName": "张三",
    "accountNumber": "6222021234567890123",
    "bankName": "中国工商银行",
    "bankCode": "ICBC",
    "branchName": "北京分行营业部",
    "idCardNumber": "110101199001011234",
    "phone": "***********",
    "isDefault": 1,
    "remark": "主要收款账户"
  }'
```

### 2. 添加支付宝账户
```bash
curl -X POST "http://localhost:8080/merchant/payment-account" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "accountType": 2,
    "accountName": "李四",
    "accountNumber": "<EMAIL>",
    "platformName": "支付宝",
    "platformAccount": "***********",
    "phone": "***********",
    "isDefault": 0,
    "remark": "备用收款账户"
  }'
```

### 3. 添加微信账户
```bash
curl -X POST "http://localhost:8080/merchant/payment-account" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "accountType": 3,
    "accountName": "王五",
    "accountNumber": "wangwu_wx",
    "platformName": "微信支付",
    "platformAccount": "***********",
    "phone": "***********",
    "isDefault": 0,
    "remark": "微信收款账户"
  }'
```

### 4. 查询所有收账账户
```bash
curl -X GET "http://localhost:8080/merchant/payment-account/list" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 分页查询收账账户
```bash
curl -X GET "http://localhost:8080/merchant/payment-account/page?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 6. 查询默认收账账户
```bash
curl -X GET "http://localhost:8080/merchant/payment-account/default" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 7. 根据ID查询收账账户
```bash
curl -X GET "http://localhost:8080/merchant/payment-account/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 8. 设置默认收账账户
```bash
curl -X PUT "http://localhost:8080/merchant/payment-account/1/default" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 9. 启用收账账户
```bash
curl -X PUT "http://localhost:8080/merchant/payment-account/1/enable" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 10. 禁用收账账户
```bash
curl -X PUT "http://localhost:8080/merchant/payment-account/1/disable" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 11. 更新收账账户
```bash
curl -X PUT "http://localhost:8080/merchant/payment-account" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "id": 1,
    "accountType": 1,
    "accountName": "张三（更新）",
    "accountNumber": "6222021234567890123",
    "bankName": "中国工商银行",
    "bankCode": "ICBC",
    "branchName": "北京分行营业部",
    "idCardNumber": "110101199001011234",
    "phone": "***********",
    "isDefault": 1,
    "remark": "更新后的主要收款账户"
  }'
```

### 12. 删除收账账户
```bash
curl -X DELETE "http://localhost:8080/merchant/payment-account/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 管理员端接口测试

### 1. 分页查询所有收账账户
```bash
curl -X GET "http://localhost:8080/admin/payment-account/page?page=1&pageSize=10" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 2. 按条件查询收账账户
```bash
curl -X GET "http://localhost:8080/admin/payment-account/page?page=1&pageSize=10&accountType=1&accountStatus=1" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3. 根据商家ID查询收账账户
```bash
curl -X GET "http://localhost:8080/admin/payment-account/seller/16?page=1&pageSize=10" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 4. 根据ID查询收账账户详情
```bash
curl -X GET "http://localhost:8080/admin/payment-account/1" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 5. 验证收账账户
```bash
curl -X PUT "http://localhost:8080/admin/payment-account/1/verify" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 6. 启用收账账户
```bash
curl -X PUT "http://localhost:8080/admin/payment-account/1/enable" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 7. 禁用收账账户
```bash
curl -X PUT "http://localhost:8080/admin/payment-account/1/disable" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 8. 查询账户类型统计
```bash
curl -X GET "http://localhost:8080/admin/payment-account/statistics/type" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 9. 查询验证状态统计
```bash
curl -X GET "http://localhost:8080/admin/payment-account/statistics/verification" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

## 数据库初始化

### 1. 执行建表SQL
```sql
-- 在数据库中执行以下文件
source /path/to/settlement_tables.sql;
```

### 2. 验证表结构
```sql
-- 查看表结构
DESCRIBE seller_payment_account;

-- 查看索引
SHOW INDEX FROM seller_payment_account;
```

## 测试数据验证

### 1. 查询测试数据
```sql
-- 查询商家的收账账户
SELECT * FROM seller_payment_account WHERE seller_id = 16;

-- 查询默认账户
SELECT * FROM seller_payment_account WHERE seller_id = 16 AND is_default = 1;

-- 查询已验证账户
SELECT * FROM seller_payment_account WHERE verification_status = 1;
```

### 2. 数据脱敏验证
```sql
-- 验证脱敏效果
SELECT 
    id,
    account_name,
    CONCAT(LEFT(account_number, 4), '****', RIGHT(account_number, 4)) as masked_account_number,
    CASE
        WHEN id_card_number IS NOT NULL AND LENGTH(id_card_number) >= 8
        THEN CONCAT(LEFT(id_card_number, 4), '****', RIGHT(id_card_number, 4))
        ELSE id_card_number
    END as masked_id_card,
    CASE
        WHEN phone IS NOT NULL AND LENGTH(phone) >= 7
        THEN CONCAT(LEFT(phone, 3), '****', RIGHT(phone, 4))
        ELSE phone
    END as masked_phone
FROM seller_payment_account;
```

## 预期响应示例

### 成功响应
```json
{
  "code": 1,
  "msg": "success",
  "data": {
    "id": 1,
    "sellerId": 16,
    "sellerName": "测试商家",
    "accountType": 1,
    "accountTypeDesc": "银行卡",
    "accountName": "张三",
    "accountNumber": "6222****0123",
    "bankName": "中国工商银行",
    "branchName": "北京分行营业部",
    "isDefault": 1,
    "isDefaultDesc": "是",
    "accountStatus": 1,
    "accountStatusDesc": "启用",
    "verificationStatus": 1,
    "verificationStatusDesc": "已验证",
    "createTime": "2025-01-29T10:00:00",
    "updateTime": "2025-01-29T10:00:00"
  }
}
```

### 错误响应
```json
{
  "code": 0,
  "msg": "该账户号码已存在",
  "data": null
}
```

## 注意事项

1. **认证授权**：所有接口都需要有效的JWT Token
2. **数据验证**：银行卡类型必须填写银行名称和开户支行
3. **唯一性约束**：同一商家的同类型账户号码不能重复
4. **默认账户**：每个商家只能有一个默认账户
5. **数据脱敏**：前端显示时账户号码、身份证、手机号会自动脱敏
6. **权限控制**：商家只能操作自己的账户，管理员可以操作所有账户
