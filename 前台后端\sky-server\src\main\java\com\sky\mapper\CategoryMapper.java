package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.annotation.AutoFill;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.dto.CategoryQueryDTO;
import com.sky.entity.Category;
import com.sky.enumeration.OperationType;
import com.sky.vo.ListCategoryVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 分类数据操作接口
 */
@Mapper
public interface CategoryMapper {
    // 精确查询
    @Select("SELECT * FROM category WHERE id = #{id}")
    Category findById(Long id);

    // 条件查询
    List<Category> findByCondition(CategoryQueryDTO dto);

    // 插入记录
    @Insert("INSERT INTO category(name, parent_id, level, sort_order, create_time, update_time) " +
            "VALUES(#{name}, #{parentId}, #{level}, #{sortOrder}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Category category);

    // 递归删除
    @Delete("DELETE FROM category WHERE id IN (" +
            "WITH RECURSIVE cte AS (" +
            "SELECT id FROM category WHERE id = #{id} " +
            "UNION ALL " +
            "SELECT c.id FROM category c INNER JOIN cte ON c.parent_id = cte.id" +
            ") SELECT id FROM cte)")
    int deleteRecursive(Long id);

    List<Category> selectAllWithType();
}