package com.sky.handler;


import com.sky.exception.LogisticsException;
import com.sky.exception.OrderException;
import com.sky.exception.RefundException;
import com.sky.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 业务异常处理器
 */
@RestControllerAdvice
@Slf4j
public class BusinessExceptionHandler {

    /**
     * 处理订单相关异常
     */
    @ExceptionHandler(OrderException.class)
    public Result<String> handleOrderException(OrderException ex) {
        log.error("订单业务异常：{}", ex.getMessage(), ex);
        return Result.error(ex.getMessage());
    }



    /**
     * 处理退款相关异常
     */
    @ExceptionHandler(RefundException.class)
    public Result<String> handleRefundException(RefundException ex) {
        log.error("退款业务异常：{}", ex.getMessage(), ex);
        return Result.error(ex.getMessage());
    }



    /**
     * 处理物流相关异常
     */
    @ExceptionHandler(LogisticsException.class)
    public Result<String> handleLogisticsException(LogisticsException ex) {
        log.error("物流业务异常：{}", ex.getMessage(), ex);
        return Result.error(ex.getMessage());
    }



    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Result<String> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.error("参数验证异常：{}", ex.getMessage(), ex);
        return Result.error("参数错误：" + ex.getMessage());
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public Result<String> handleNullPointerException(NullPointerException ex) {
        log.error("空指针异常：{}", ex.getMessage(), ex);
        return Result.error("系统内部错误，请稍后重试");
    }

    /**
     * 处理数据库异常
     */
    @ExceptionHandler(org.springframework.dao.DataAccessException.class)
    public Result<String> handleDataAccessException(org.springframework.dao.DataAccessException ex) {
        log.error("数据库访问异常：{}", ex.getMessage(), ex);
        return Result.error("数据操作失败，请稍后重试");
    }

    /**
     * 处理其他运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<String> handleRuntimeException(RuntimeException ex) {
        log.error("运行时异常：{}", ex.getMessage(), ex);
        // 如果异常消息明确，返回具体错误信息；否则返回通用错误信息
        String message = ex.getMessage();
        if (message != null && !message.trim().isEmpty()) {
            return Result.error(message);
        }
        return Result.error("系统异常，请稍后重试");
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<String> handleException(Exception ex) {
        log.error("系统异常：{}", ex.getMessage(), ex);
        return Result.error("系统异常，请联系管理员");
    }
}
