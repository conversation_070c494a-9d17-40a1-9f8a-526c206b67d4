package com.sky.entity;

public class BusinessException extends RuntimeException {

    private Integer code;

    // 构造方法，默认错误码
    public BusinessException(String message) {
        super(message);
        this.code = 400;
    }

    // 构造方法，自定义错误码
    public BusinessException(String message, Integer code) {
        super(message);
        this.code = code;
    }

    // 获取错误码
    public Integer getCode() {
        return code;
    }

    // 设置错误码
    public void setCode(Integer code) {
        this.code = code;
    }
}
