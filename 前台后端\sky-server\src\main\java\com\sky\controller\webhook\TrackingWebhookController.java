package com.sky.controller.webhook;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sky.service.TrackingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 17TRACK WebHook控制器
 * 用于接收17TRACK的物流状态推送通知
 */
@RestController
@RequestMapping("/webhook/tracking")
@Slf4j
public class TrackingWebhookController {

    @Autowired
    private TrackingService trackingService;

    /**
     * 接收17TRACK的物流状态推送
     */
    @PostMapping("/17track")
    public ResponseEntity<String> receive17TrackWebhook(
            @RequestBody String payload,
            HttpServletRequest request) {
        
        log.info("收到17TRACK WebHook推送: {}", payload);
        
        try {
            // 解析推送数据
            JSONObject webhookData = JSON.parseObject(payload);
            String event = webhookData.getString("event");
            JSONObject data = webhookData.getJSONObject("data");
            
            if ("TRACKING_UPDATED".equals(event) && data != null) {
                String trackingNumber = data.getString("number");
                String status = data.getString("status");
                String statusDescription = data.getString("status_description");
                
                log.info("物流状态更新: {} -> {} ({})", trackingNumber, status, statusDescription);
                
                // 更新本地物流状态
                if (trackingNumber != null && statusDescription != null) {
                    boolean updated = trackingService.updateLogisticsStatus(trackingNumber, statusDescription);
                    if (updated) {
                        log.info("本地物流状态更新成功: {}", trackingNumber);
                    } else {
                        log.warn("本地物流状态更新失败: {}", trackingNumber);
                    }
                }
                
                // 这里可以添加其他业务逻辑，比如：
                // 1. 发送邮件通知用户
                // 2. 更新订单状态
                // 3. 触发其他业务流程
                
                return ResponseEntity.ok("SUCCESS");
            } else {
                log.warn("未知的WebHook事件类型: {}", event);
                return ResponseEntity.ok("IGNORED");
            }
            
        } catch (Exception e) {
            log.error("处理17TRACK WebHook推送失败", e);
            return ResponseEntity.status(500).body("ERROR");
        }
    }

    /**
     * WebHook健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("OK");
    }

    /**
     * 测试WebHook接收
     */
    @PostMapping("/test")
    public ResponseEntity<String> testWebhook(@RequestBody String payload) {
        log.info("测试WebHook接收: {}", payload);
        return ResponseEntity.ok("TEST_SUCCESS");
    }
}
