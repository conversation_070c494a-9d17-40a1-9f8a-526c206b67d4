<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BeansEndpointTabSettings">
    <option name="showContexts" value="false" />
    <option name="showLibraryBeans" value="false" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="601148a7-622e-43b2-a7bc-34b8f773c04f" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/dto/SellerSubAccountDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/entity/SellerSubAccount.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/vo/SellerSubAccountUpdatePermissionsVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/vo/SellerSubAccountVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/controller/Seller/SubAccountController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/mapper/SellerSubAccountMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/SellerSubAccountService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/SellerSubAccountServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/img.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/img_1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sky-server/src/main/resources/mapper/SellerSubAccountMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-common/src/main/java/com/sky/constant/MessageConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-common/src/main/java/com/sky/constant/MessageConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-common/src/main/java/com/sky/utils/WeChatPayUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-common/src/main/java/com/sky/utils/WeChatPayUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/dto/track17/Track17DeleteTrackRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/dto/track17/Track17DeleteTrackRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/dto/track17/Track17RegisterResponse.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/dto/track17/Track17RegisterResponse.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/entity/PmsProduct.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/entity/PmsProduct.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/vo/WechatRefundResponseVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/vo/WechatRefundResponseVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/config/WebMvcConfiguration.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/config/WebMvcConfiguration.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/controller/Seller/PmsProductController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/controller/Seller/PmsProductController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/controller/admin/RefundManagementController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/controller/admin/RefundManagementController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/OrdersServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/OrdersServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/RefundApplicationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/RefundApplicationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/TrackingServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/TrackingServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/WechatPayServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/java/com/sky/service/impl/WechatPayServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sky-server/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/sky-server/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeStyleSettingsInfer">
    <option name="done" value="true" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="AnnotationType" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../ruanjian/go" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/sky-pojo/src/main/java/com/sky/entity/PmsProduct.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/sky-server/src/main/resources/mapper/MessageMapper.xml" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\Maven-3.9.9\apache-maven-3.9.9-bin\mvn_resp" />
        <option name="showDialogWithAdvancedSettings" value="true" />
        <option name="userSettingsFile" value="D:\Maven-3.9.9\apache-maven-3.9.9-bin\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="1.8" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2omzy6rOjgOUE6xzkx3fVm4zoDw" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.sky-take-out [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sky-take-out [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.SkyApplication.executor&quot;: &quot;Debug&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;grid.search.filter.rows&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/sharewharf/sharewharf/后台后端/sky-server/src/main/java/com/sky/controller/Seller&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;3a5811271dde3cfe220c3af60d527556&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\sharewharf\sharewharf\后台后端\sky-server\src\main\java\com\sky\controller\Seller" />
      <recent name="D:\sharewharf\sharewharf\后台后端\sky-server\src\main\java\com\sky\controller\admin" />
      <recent name="D:\sharewharf\sharewharf\后台后端\sky-server\src\main\java\com\sky\controller" />
      <recent name="D:\sharewharf\sharewharf\后台后端\sky-server\src\main\java\com\sky\mapper" />
      <recent name="D:\sharewharf\sharewharf\后台后端\sky-server\src\main\resources\mapper" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\sky-take-out-new12\sky-server\src\main\resources" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.sky.Utils" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.sky.controller.admin" />
      <recent name="com.sky.Utils" />
      <recent name="com.sky.annotation" />
      <recent name="com.sky.dto" />
      <recent name="com.sky.mapper" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.SkyApplication">
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="sky-take-out" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="FILE" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="sky-take-out" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="DIRECTORY" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
    <configuration name="Server" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.sky.Server.Server" />
      <module name="sky-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.sky.Server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SmsSender" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.SmsSender" />
      <module name="sky-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WeChatPayCertDownloader" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.sky.Utils.WeChatPayCertDownloader" />
      <module name="sky-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.sky.Utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="test" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.test" />
      <module name="sky-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="test" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.test" />
      <module name="sky-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="shareGO.sql" type="DatabaseScript" editBeforeRun="true" temporary="true" nameIsGenerated="true">
      <script-file value="$PROJECT_DIR$/sky-common/src/main/java/com/sky/sql/shareGO.sql" />
      <script-mode>FILE</script-mode>
      <data-source id="944ad715-28a5-4c75-a0c8-435a0e96fe1c" namespace="schema/&quot;ry_mall&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="shareGO.sql" type="DatabaseScript" editBeforeRun="true" temporary="true" nameIsGenerated="true">
      <script-file value="$PROJECT_DIR$/sky-common/src/main/java/com/sky/sql/shareGO.sql" />
      <script-mode>FILE</script-mode>
      <data-source id="944ad715-28a5-4c75-a0c8-435a0e96fe1c" namespace="schema/&quot;ry_mall&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="SkyApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sky-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sky.SkyApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SkyApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sky-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sky.SkyApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23726.103" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23726.103" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="601148a7-622e-43b2-a7bc-34b8f773c04f" name="更改" comment="" />
      <created>1731489792959</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1731489792959</updated>
      <workItem from="1731489794105" duration="2871000" />
      <workItem from="1731494612075" duration="14430000" />
      <workItem from="1731512375675" duration="1229000" />
      <workItem from="1731562766097" duration="11576000" />
      <workItem from="1731588818712" duration="2422000" />
      <workItem from="1731596397648" duration="15000" />
      <workItem from="1731596737114" duration="3617000" />
      <workItem from="1731647552067" duration="9659000" />
      <workItem from="1731660403150" duration="1070000" />
      <workItem from="1731662269141" duration="3364000" />
      <workItem from="1731671937060" duration="28000" />
      <workItem from="1731671994190" duration="13000" />
      <workItem from="1731672019319" duration="4928000" />
      <workItem from="1731760051426" duration="1722000" />
      <workItem from="1731771811273" duration="329000" />
      <workItem from="1731856177898" duration="2380000" />
      <workItem from="1731891313262" duration="121000" />
      <workItem from="1731902557626" duration="234000" />
      <workItem from="1731929947798" duration="3770000" />
      <workItem from="1732023794145" duration="7934000" />
      <workItem from="1732111309304" duration="7384000" />
      <workItem from="1732366092365" duration="735000" />
      <workItem from="1732376712979" duration="602000" />
      <workItem from="1732425905292" duration="2516000" />
      <workItem from="1732441561108" duration="4632000" />
      <workItem from="1732449397011" duration="1221000" />
      <workItem from="1732524619396" duration="2181000" />
      <workItem from="1732532090386" duration="2983000" />
      <workItem from="1732884120082" duration="5673000" />
      <workItem from="1736758645678" duration="1983000" />
      <workItem from="1736767050753" duration="2860000" />
      <workItem from="1736770225251" duration="3685000" />
      <workItem from="1736773926544" duration="1787000" />
      <workItem from="1736820445176" duration="13622000" />
      <workItem from="1736835213268" duration="11453000" />
      <workItem from="1736904351590" duration="3180000" />
      <workItem from="1736910816818" duration="630000" />
      <workItem from="1736913437997" duration="5000" />
      <workItem from="1736925324832" duration="1030000" />
      <workItem from="1736936120450" duration="1366000" />
      <workItem from="1736942584075" duration="5000" />
      <workItem from="1737104418324" duration="12476000" />
      <workItem from="1737118671482" duration="3444000" />
      <workItem from="1737169298489" duration="4184000" />
      <workItem from="1737274763780" duration="1058000" />
      <workItem from="1737277197363" duration="11000" />
      <workItem from="1737277246611" duration="656000" />
      <workItem from="1737282847999" duration="1440000" />
      <workItem from="1737337197721" duration="2190000" />
      <workItem from="1737339694652" duration="148000" />
      <workItem from="1737344155182" duration="5603000" />
      <workItem from="1737371708250" duration="3533000" />
      <workItem from="1737526209822" duration="5739000" />
      <workItem from="1737598571244" duration="1381000" />
      <workItem from="1738316640651" duration="2783000" />
      <workItem from="1738369560912" duration="1284000" />
      <workItem from="1738388328213" duration="1418000" />
      <workItem from="1738843107179" duration="7000" />
      <workItem from="1739008181394" duration="1200000" />
      <workItem from="1740273143618" duration="17961000" />
      <workItem from="1740306422529" duration="6972000" />
      <workItem from="1740357852413" duration="1761000" />
      <workItem from="1740369920388" duration="1212000" />
      <workItem from="1740391284626" duration="10405000" />
      <workItem from="1740458095887" duration="610000" />
      <workItem from="1740460054290" duration="2894000" />
      <workItem from="1740471873310" duration="5701000" />
      <workItem from="1740483127456" duration="7494000" />
      <workItem from="1740558888157" duration="2305000" />
      <workItem from="1740633517539" duration="253000" />
      <workItem from="1740639019303" duration="1374000" />
      <workItem from="1740644065126" duration="10633000" />
      <workItem from="1740707905462" duration="4579000" />
      <workItem from="1740742923353" duration="6462000" />
      <workItem from="1740791596844" duration="7501000" />
      <workItem from="1740804267987" duration="1418000" />
      <workItem from="1740818094267" duration="16563000" />
      <workItem from="1740896428539" duration="14883000" />
      <workItem from="1740924264187" duration="7000" />
      <workItem from="1740984674886" duration="931000" />
      <workItem from="1741260632886" duration="986000" />
      <workItem from="1741261858716" duration="8412000" />
      <workItem from="1741701569332" duration="5563000" />
      <workItem from="1741774485170" duration="10136000" />
      <workItem from="1741848972970" duration="773000" />
      <workItem from="1741870495343" duration="10292000" />
      <workItem from="1741911650116" duration="420000" />
      <workItem from="1741912975317" duration="407000" />
      <workItem from="1741913519960" duration="5375000" />
      <workItem from="1741933760469" duration="11596000" />
      <workItem from="1741946618460" duration="3374000" />
      <workItem from="1742003935792" duration="10330000" />
      <workItem from="1742020996435" duration="651000" />
      <workItem from="1742032396987" duration="357000" />
      <workItem from="1742038484730" duration="690000" />
      <workItem from="1742039206662" duration="80000" />
      <workItem from="1742039301131" duration="6103000" />
      <workItem from="1742046727615" duration="139000" />
      <workItem from="1742092606663" duration="743000" />
      <workItem from="1742093473873" duration="6408000" />
      <workItem from="1742187849717" duration="257000" />
      <workItem from="1742189037471" duration="2243000" />
      <workItem from="1742282211271" duration="4266000" />
      <workItem from="1742288336926" duration="612000" />
      <workItem from="1742295358681" duration="6670000" />
      <workItem from="1742373218693" duration="1967000" />
      <workItem from="1742436758268" duration="5480000" />
      <workItem from="1742442474443" duration="1865000" />
      <workItem from="1742456421056" duration="5589000" />
      <workItem from="1742469414282" duration="5884000" />
      <workItem from="1742607217550" duration="3563000" />
      <workItem from="1742611199744" duration="793000" />
      <workItem from="1742638421297" duration="12174000" />
      <workItem from="1742693091182" duration="30000" />
      <workItem from="1742695434770" duration="22184000" />
      <workItem from="1742720224890" duration="10253000" />
      <workItem from="1742780744329" duration="1393000" />
      <workItem from="1742783151716" duration="14000" />
      <workItem from="1742789667626" duration="778000" />
      <workItem from="1742790529266" duration="4000" />
      <workItem from="1742790613472" duration="106000" />
      <workItem from="1742794267745" duration="2448000" />
      <workItem from="1742797416541" duration="118000" />
      <workItem from="1742803589771" duration="604000" />
      <workItem from="1742815274228" duration="13000" />
      <workItem from="1742816515913" duration="1875000" />
      <workItem from="1742867733807" duration="144000" />
      <workItem from="1742875512281" duration="831000" />
      <workItem from="1742878516966" duration="1898000" />
      <workItem from="1742880736934" duration="170000" />
      <workItem from="1742880911304" duration="4000" />
      <workItem from="1742881466162" duration="68000" />
      <workItem from="1742895254190" duration="30000" />
      <workItem from="1742895369232" duration="48000" />
      <workItem from="1742896583970" duration="3000" />
      <workItem from="1742953971572" duration="5000" />
      <workItem from="1742956969872" duration="91000" />
      <workItem from="1742988441345" duration="3453000" />
      <workItem from="1743041930435" duration="6000" />
      <workItem from="1743043291950" duration="719000" />
      <workItem from="1743079764745" duration="4592000" />
      <workItem from="1743084468514" duration="5000" />
      <workItem from="1743124044198" duration="20000" />
      <workItem from="1743129170212" duration="612000" />
      <workItem from="1743152880522" duration="12000" />
      <workItem from="1743160804246" duration="635000" />
      <workItem from="1743301757431" duration="379000" />
      <workItem from="1743303615420" duration="724000" />
      <workItem from="1743304351128" duration="1314000" />
      <workItem from="1743305728879" duration="426000" />
      <workItem from="1743316750570" duration="2911000" />
      <workItem from="1743322253364" duration="8000" />
      <workItem from="1743322652675" duration="5508000" />
      <workItem from="1743397922075" duration="3697000" />
      <workItem from="1743412318222" duration="87000" />
      <workItem from="1743426690955" duration="213000" />
      <workItem from="1743426964012" duration="28000" />
      <workItem from="1744076937990" duration="3367000" />
      <workItem from="1744099657509" duration="880000" />
      <workItem from="1744101476204" duration="4773000" />
      <workItem from="1744110387730" duration="29000" />
      <workItem from="1744274114324" duration="819000" />
      <workItem from="1744460399956" duration="1484000" />
      <workItem from="1744532881025" duration="593000" />
      <workItem from="1744533868741" duration="53000" />
      <workItem from="1744599967069" duration="2018000" />
      <workItem from="1744602273827" duration="104000" />
      <workItem from="1744613994262" duration="1039000" />
      <workItem from="1744636779609" duration="6015000" />
      <workItem from="1744702817387" duration="140000" />
      <workItem from="1744704014412" duration="613000" />
      <workItem from="1744705767605" duration="4788000" />
      <workItem from="1744810888474" duration="653000" />
      <workItem from="1744852469636" duration="51000" />
      <workItem from="1744853423828" duration="12988000" />
      <workItem from="1744880950121" duration="4617000" />
      <workItem from="1744967050340" duration="715000" />
      <workItem from="1745134766451" duration="1630000" />
      <workItem from="1745140151111" duration="1378000" />
      <workItem from="1745489584713" duration="1261000" />
      <workItem from="1745491052052" duration="1362000" />
      <workItem from="1745629921849" duration="1869000" />
      <workItem from="1745634231555" duration="2767000" />
      <workItem from="1745650844891" duration="161000" />
      <workItem from="1745669205377" duration="1499000" />
      <workItem from="1745671563001" duration="1000" />
      <workItem from="1745716318618" duration="7168000" />
      <workItem from="1745723549693" duration="2313000" />
      <workItem from="1745744751041" duration="6551000" />
      <workItem from="1745819599123" duration="977000" />
      <workItem from="1745821514216" duration="452000" />
      <workItem from="1745839207544" duration="811000" />
      <workItem from="1745842455681" duration="1357000" />
      <workItem from="1745933771194" duration="879000" />
      <workItem from="1746062339325" duration="2067000" />
      <workItem from="1746087504305" duration="5000" />
      <workItem from="1746101213490" duration="626000" />
      <workItem from="1747010724810" duration="7159000" />
      <workItem from="1747028598931" duration="1287000" />
      <workItem from="1747041370618" duration="5859000" />
      <workItem from="1747102467428" duration="4000" />
      <workItem from="1747302425715" duration="4397000" />
      <workItem from="1747312859098" duration="16000" />
      <workItem from="1747481943629" duration="788000" />
      <workItem from="1747634492214" duration="386000" />
      <workItem from="1747727305483" duration="2114000" />
      <workItem from="1747829174020" duration="4000" />
      <workItem from="1748164011965" duration="349000" />
      <workItem from="1748164428143" duration="144000" />
      <workItem from="1748164614643" duration="105000" />
      <workItem from="1748164724999" duration="42000" />
      <workItem from="1748164928538" duration="451000" />
      <workItem from="1748175420149" duration="57000" />
      <workItem from="1748175483237" duration="32000" />
      <workItem from="1748175531871" duration="1899000" />
      <workItem from="1748227465321" duration="309000" />
      <workItem from="1748503113211" duration="15000" />
      <workItem from="1749024208240" duration="5650000" />
      <workItem from="1749114365501" duration="2250000" />
      <workItem from="1749194020506" duration="4955000" />
      <workItem from="1750150476953" duration="908000" />
      <workItem from="1750151470579" duration="20000" />
      <workItem from="1750151712336" duration="2276000" />
      <workItem from="1750162603231" duration="6007000" />
      <workItem from="1750213449046" duration="6645000" />
      <workItem from="1750232087220" duration="1720000" />
      <workItem from="1750315868369" duration="79000" />
      <workItem from="1750315965858" duration="238000" />
      <workItem from="1750316481836" duration="26000" />
      <workItem from="1750316531206" duration="8000" />
      <workItem from="1750316557787" duration="33000" />
      <workItem from="1750318190611" duration="99000" />
      <workItem from="1750318344569" duration="170000" />
      <workItem from="1750318538764" duration="119000" />
      <workItem from="1750318720200" duration="177000" />
      <workItem from="1750320106335" duration="1163000" />
      <workItem from="1750327576225" duration="3971000" />
      <workItem from="1750338869788" duration="43000" />
      <workItem from="1750410112369" duration="1710000" />
      <workItem from="1750412608681" duration="530000" />
      <workItem from="1750413382800" duration="2029000" />
      <workItem from="1750481089799" duration="982000" />
      <workItem from="1750491821727" duration="87000" />
      <workItem from="1750492299573" duration="2495000" />
      <workItem from="1750497715531" duration="301000" />
      <workItem from="1750599493335" duration="6000" />
      <workItem from="1750600707378" duration="790000" />
      <workItem from="1750674964633" duration="1897000" />
      <workItem from="1750685322793" duration="91000" />
      <workItem from="1750685467706" duration="46000" />
      <workItem from="1750757218224" duration="9527000" />
      <workItem from="1750835219079" duration="1486000" />
      <workItem from="1750838504586" duration="166000" />
      <workItem from="1750838800118" duration="1595000" />
      <workItem from="1750852039431" duration="1221000" />
      <workItem from="1750862941969" duration="256000" />
      <workItem from="1750907302688" duration="352000" />
      <workItem from="1750907991421" duration="327000" />
      <workItem from="1750908329707" duration="2059000" />
      <workItem from="1750926432518" duration="2000" />
      <workItem from="1751028379665" duration="1100000" />
      <workItem from="1751035047168" duration="722000" />
      <workItem from="1751036888332" duration="421000" />
      <workItem from="1751037979214" duration="3178000" />
      <workItem from="1751102939337" duration="2605000" />
      <workItem from="1751112422412" duration="9132000" />
      <workItem from="1751158341830" duration="12056000" />
      <workItem from="1751255014751" duration="2443000" />
      <workItem from="1751330321963" duration="9336000" />
      <workItem from="1751442748220" duration="1564000" />
      <workItem from="1751447388293" duration="3290000" />
      <workItem from="1751520619509" duration="1799000" />
      <workItem from="1751957989712" duration="6088000" />
      <workItem from="1752035704944" duration="22139000" />
      <workItem from="1752108924706" duration="32297000" />
      <workItem from="1752225747089" duration="3339000" />
      <workItem from="1752285520215" duration="3661000" />
      <workItem from="1752290605903" duration="564000" />
      <workItem from="1752291486920" duration="485000" />
      <workItem from="1752303079443" duration="7974000" />
      <workItem from="1752333386499" duration="137000" />
      <workItem from="1752376834067" duration="1099000" />
      <workItem from="1752572649309" duration="7319000" />
      <workItem from="1752640367023" duration="3277000" />
      <workItem from="1752655428286" duration="4762000" />
      <workItem from="1752807405235" duration="1950000" />
      <workItem from="1752820558616" duration="6409000" />
      <workItem from="1752829617191" duration="7471000" />
      <workItem from="1752839749911" duration="68000" />
      <workItem from="1752839828385" duration="2771000" />
      <workItem from="1752842803157" duration="16000" />
      <workItem from="1752842875556" duration="528000" />
      <workItem from="1752885082757" duration="346000" />
      <workItem from="1752885436947" duration="288000" />
      <workItem from="1752891163972" duration="7222000" />
      <workItem from="1752916826926" duration="14909000" />
      <workItem from="1752972306717" duration="8711000" />
      <workItem from="1752983314205" duration="1605000" />
      <workItem from="1752995680892" duration="4330000" />
      <workItem from="1753004001049" duration="1582000" />
      <workItem from="1753017087418" duration="2331000" />
      <workItem from="1753060948914" duration="5782000" />
      <workItem from="1753073049391" duration="398000" />
      <workItem from="1753087109029" duration="8000" />
      <workItem from="1753100884032" duration="2692000" />
      <workItem from="1753148889930" duration="23000" />
      <workItem from="1753759993297" duration="3838000" />
      <workItem from="1753773649640" duration="2647000" />
    </task>
    <task id="LOCAL-00001" summary="用户登录注册">
      <option name="closed" value="true" />
      <created>1731503546535</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1731503546535</updated>
    </task>
    <task id="LOCAL-00002" summary="解决jwt问题">
      <option name="closed" value="true" />
      <created>1731568427776</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1731568427776</updated>
    </task>
    <task id="LOCAL-00003" summary="初版">
      <option name="closed" value="true" />
      <created>1732889568812</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1732889568812</updated>
    </task>
    <task id="LOCAL-00004" summary="注册登录(卖家)">
      <option name="closed" value="true" />
      <created>1741272593271</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1741272593271</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="用户登录注册" />
    <MESSAGE value="解决jwt问题" />
    <MESSAGE value="初版" />
    <MESSAGE value="注册登录(卖家)" />
    <option name="LAST_COMMIT_MESSAGE" value="注册登录(卖家)" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/java_shopsharewharf$SkyApplication.ic" NAME="SkyApplication Coverage Results" MODIFIED="1742649601019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
  </component>
</project>