package com.sky.service;

import com.sky.dto.PaymentAccountDTO;
import com.sky.dto.PaymentAccountQueryDTO;
import com.sky.entity.SellerPaymentAccount;
import com.sky.vo.PageResult;
import com.sky.vo.PaymentAccountVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 收账账户服务测试类
 */
@SpringBootTest
@Slf4j
@Transactional
public class PaymentAccountServiceTest {

    @Autowired
    private PaymentAccountService paymentAccountService;

    private static final Long TEST_SELLER_ID = 16L;

    @Test
    public void testAddBankCardAccount() {
        log.info("测试添加银行卡账户");

        PaymentAccountDTO dto = new PaymentAccountDTO();
        dto.setAccountType(SellerPaymentAccount.ACCOUNT_TYPE_BANK_CARD);
        dto.setAccountName("张三");
        dto.setAccountNumber("6222021234567890123");
        dto.setBankName("中国工商银行");
        dto.setBankCode("ICBC");
        dto.setBranchName("北京分行营业部");
        dto.setIdCardNumber("110101199001011234");
        dto.setPhone("***********");
        dto.setIsDefault(SellerPaymentAccount.IS_DEFAULT_YES);
        dto.setRemark("主要收款账户");

        paymentAccountService.addPaymentAccount(dto, TEST_SELLER_ID);
        log.info("银行卡账户添加成功");
    }

    @Test
    public void testAddAlipayAccount() {
        log.info("测试添加支付宝账户");

        PaymentAccountDTO dto = new PaymentAccountDTO();
        dto.setAccountType(SellerPaymentAccount.ACCOUNT_TYPE_ALIPAY);
        dto.setAccountName("李四");
        dto.setAccountNumber("<EMAIL>");
        dto.setPlatformName("支付宝");
        dto.setPlatformAccount("***********");
        dto.setPhone("***********");
        dto.setIsDefault(SellerPaymentAccount.IS_DEFAULT_NO);
        dto.setRemark("备用收款账户");

        paymentAccountService.addPaymentAccount(dto, TEST_SELLER_ID);
        log.info("支付宝账户添加成功");
    }

    @Test
    public void testAddWechatAccount() {
        log.info("测试添加微信账户");

        PaymentAccountDTO dto = new PaymentAccountDTO();
        dto.setAccountType(SellerPaymentAccount.ACCOUNT_TYPE_WECHAT);
        dto.setAccountName("王五");
        dto.setAccountNumber("wangwu_wx");
        dto.setPlatformName("微信支付");
        dto.setPlatformAccount("***********");
        dto.setPhone("***********");
        dto.setIsDefault(SellerPaymentAccount.IS_DEFAULT_NO);
        dto.setRemark("微信收款账户");

        paymentAccountService.addPaymentAccount(dto, TEST_SELLER_ID);
        log.info("微信账户添加成功");
    }

    @Test
    public void testQueryPaymentAccounts() {
        log.info("测试查询收账账户列表");

        List<PaymentAccountVO> accounts = paymentAccountService.getPaymentAccountsBySellerId(TEST_SELLER_ID);
        log.info("查询到 {} 个收账账户", accounts.size());

        for (PaymentAccountVO account : accounts) {
            log.info("账户信息：ID={}, 类型={}, 名称={}, 账号={}, 是否默认={}",
                    account.getId(),
                    account.getAccountTypeDesc(),
                    account.getAccountName(),
                    account.getAccountNumber(),
                    account.getIsDefaultDesc());
        }
    }

    @Test
    public void testPageQuery() {
        log.info("测试分页查询收账账户");

        PaymentAccountQueryDTO queryDTO = new PaymentAccountQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);

        PageResult pageResult = paymentAccountService.pageQuery(queryDTO, TEST_SELLER_ID);
        log.info("分页查询结果：总记录数={}, 当前页数据量={}",
                pageResult.getTotal(), pageResult.getRecords().size());
    }

    @Test
    public void testGetDefaultAccount() {
        log.info("测试查询默认收账账户");

        PaymentAccountVO defaultAccount = paymentAccountService.getDefaultPaymentAccount(TEST_SELLER_ID);
        if (defaultAccount != null) {
            log.info("默认账户：ID={}, 类型={}, 名称={}, 账号={}",
                    defaultAccount.getId(),
                    defaultAccount.getAccountTypeDesc(),
                    defaultAccount.getAccountName(),
                    defaultAccount.getAccountNumber());
        } else {
            log.info("未找到默认收账账户");
        }
    }

    @Test
    public void testSetDefaultAccount() {
        log.info("测试设置默认收账账户");

        // 先查询所有账户
        List<PaymentAccountVO> accounts = paymentAccountService.getPaymentAccountsBySellerId(TEST_SELLER_ID);
        if (!accounts.isEmpty()) {
            Long accountId = accounts.get(0).getId();
            paymentAccountService.setDefaultPaymentAccount(accountId, TEST_SELLER_ID);
            log.info("默认账户设置成功，账户ID：{}", accountId);

            // 验证设置结果
            PaymentAccountVO defaultAccount = paymentAccountService.getDefaultPaymentAccount(TEST_SELLER_ID);
            if (defaultAccount != null && defaultAccount.getId().equals(accountId)) {
                log.info("默认账户设置验证成功");
            } else {
                log.error("默认账户设置验证失败");
            }
        }
    }

    @Test
    public void testUpdateAccountStatus() {
        log.info("测试更新账户状态");

        // 先查询所有账户
        List<PaymentAccountVO> accounts = paymentAccountService.getPaymentAccountsBySellerId(TEST_SELLER_ID);
        if (!accounts.isEmpty()) {
            Long accountId = accounts.get(0).getId();

            // 禁用账户
            paymentAccountService.updateAccountStatus(accountId, TEST_SELLER_ID, SellerPaymentAccount.ACCOUNT_STATUS_DISABLED);
            log.info("账户禁用成功，账户ID：{}", accountId);

            // 启用账户
            paymentAccountService.updateAccountStatus(accountId, TEST_SELLER_ID, SellerPaymentAccount.ACCOUNT_STATUS_ENABLED);
            log.info("账户启用成功，账户ID：{}", accountId);
        }
    }

    @Test
    public void testVerifyAccount() {
        log.info("测试验证账户");

        // 先查询所有账户
        List<PaymentAccountVO> accounts = paymentAccountService.getPaymentAccountsBySellerId(TEST_SELLER_ID);
        if (!accounts.isEmpty()) {
            Long accountId = accounts.get(0).getId();
            paymentAccountService.verifyPaymentAccount(accountId, TEST_SELLER_ID);
            log.info("账户验证成功，账户ID：{}", accountId);
        }
    }

    @Test
    public void testUpdatePaymentAccount() {
        log.info("测试更新收账账户");

        // 先查询所有账户
        List<PaymentAccountVO> accounts = paymentAccountService.getPaymentAccountsBySellerId(TEST_SELLER_ID);
        if (!accounts.isEmpty()) {
            PaymentAccountVO account = accounts.get(0);

            PaymentAccountDTO dto = new PaymentAccountDTO();
            dto.setId(account.getId());
            dto.setAccountType(account.getAccountType());
            dto.setAccountName(account.getAccountName() + "_更新");
            dto.setAccountNumber(account.getFullAccountNumber());
            dto.setBankName(account.getBankName());
            dto.setBranchName(account.getBranchName());
            dto.setPlatformName(account.getPlatformName());
            dto.setPlatformAccount(account.getPlatformAccount());
            dto.setIsDefault(account.getIsDefault());
            dto.setRemark("更新后的备注信息");

            paymentAccountService.updatePaymentAccount(dto, TEST_SELLER_ID);
            log.info("收账账户更新成功，账户ID：{}", account.getId());
        }
    }

    @Test
    public void testDeletePaymentAccount() {
        log.info("测试删除收账账户");

        // 先添加一个测试账户
        PaymentAccountDTO dto = new PaymentAccountDTO();
        dto.setAccountType(SellerPaymentAccount.ACCOUNT_TYPE_OTHER);
        dto.setAccountName("测试账户");
        dto.setAccountNumber("TEST123456789");
        dto.setRemark("用于测试删除的账户");

        paymentAccountService.addPaymentAccount(dto, TEST_SELLER_ID);

        // 查询刚添加的账户
        List<PaymentAccountVO> accounts = paymentAccountService.getPaymentAccountsBySellerId(TEST_SELLER_ID);
        PaymentAccountVO testAccount = accounts.stream()
                .filter(account -> "测试账户".equals(account.getAccountName()))
                .findFirst()
                .orElse(null);

        if (testAccount != null) {
            paymentAccountService.deletePaymentAccount(testAccount.getId(), TEST_SELLER_ID);
            log.info("收账账户删除成功，账户ID：{}", testAccount.getId());
        }
    }

    @Test
    public void testAdminOperations() {
        log.info("测试管理员操作");

        // 管理员分页查询
        PaymentAccountQueryDTO queryDTO = new PaymentAccountQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);

        PageResult pageResult = paymentAccountService.adminPageQuery(queryDTO);
        log.info("管理员分页查询结果：总记录数={}", pageResult.getTotal());

        // 如果有数据，测试管理员验证和状态更新
        if (pageResult.getTotal() > 0) {
            @SuppressWarnings("unchecked")
            List<PaymentAccountVO> accounts = (List<PaymentAccountVO>) pageResult.getRecords();
            if (!accounts.isEmpty()) {
                Long accountId = accounts.get(0).getId();

                // 管理员验证账户
                paymentAccountService.adminVerifyPaymentAccount(accountId);
                log.info("管理员验证账户成功，账户ID：{}", accountId);

                // 管理员更新账户状态
                paymentAccountService.adminUpdateAccountStatus(accountId, SellerPaymentAccount.ACCOUNT_STATUS_ENABLED);
                log.info("管理员更新账户状态成功，账户ID：{}", accountId);
            }
        }
    }
}
