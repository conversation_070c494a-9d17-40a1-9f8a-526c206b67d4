package com.sky.exception;

/**
 * 订单业务异常
 */
public class OrderException extends BaseException {

    public OrderException() {
        super();
    }

    public OrderException(String msg) {
        super(msg);
    }

    public OrderException(String msg, Throwable cause) {
        super(msg);
    }

    // 订单相关的具体异常类型
    public static class OrderNotExistException extends OrderException {
        public OrderNotExistException() {
            super("订单不存在");
        }
        
        public OrderNotExistException(String orderNumber) {
            super("订单不存在：" + orderNumber);
        }
    }

    public static class OrderStatusException extends OrderException {
        public OrderStatusException(String message) {
            super(message);
        }
    }

    public static class OrderAmountException extends OrderException {
        public OrderAmountException(String message) {
            super(message);
        }
    }

    public static class OrderPermissionException extends OrderException {
        public OrderPermissionException() {
            super("无权限操作此订单");
        }
        
        public OrderPermissionException(String message) {
            super(message);
        }
    }

    public static class OrderPaymentException extends OrderException {
        public OrderPaymentException(String message) {
            super(message);
        }
    }

    public static class OrderCancelException extends OrderException {
        public OrderCancelException(String message) {
            super(message);
        }
    }

    public static class OrderDeliveryException extends OrderException {
        public OrderDeliveryException(String message) {
            super(message);
        }
    }
}
