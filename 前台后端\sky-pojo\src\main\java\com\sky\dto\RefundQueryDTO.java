package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 退款查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "退款查询DTO")
public class RefundQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "退款申请单号")
    private String refundNo;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "申请人ID")
    private Long buyerId;

    @ApiModelProperty(value = "申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败")
    private Integer applicationStatus;

    @ApiModelProperty(value = "退款类型：1-仅退款，2-退货退款")
    private Integer refundType;

    @ApiModelProperty(value = "是否需要审核：0-不需要，1-需要")
    private Integer needApproval;

    @ApiModelProperty(value = "审核状态：1-待审核，2-审核通过，3-审核拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;
}
