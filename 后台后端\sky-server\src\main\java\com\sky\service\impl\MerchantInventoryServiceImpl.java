package com.sky.service.impl;

import com.sky.entity.MerchantInventory;
import com.sky.mapper.MerchantInventoryMapper;
import com.sky.service.MerchantInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class MerchantInventoryServiceImpl implements MerchantInventoryService {
    @Autowired
    private MerchantInventoryMapper merchantInventoryMapper;

    @Override
    public List<MerchantInventory> findAll() {
        return merchantInventoryMapper.findAll();
    }

    @Override
    public List<MerchantInventory> findByProductCode(String productCode) {
        return merchantInventoryMapper.findByProductCode(productCode);
    }

    @Override
    public List<MerchantInventory> findByItemCode(String itemCode) {
        return merchantInventoryMapper.findByItemCode(itemCode);
    }

    @Override
    public List<MerchantInventory> findByProductTitle(String productTitle) {
        return merchantInventoryMapper.findByProductTitle(productTitle);
    }

    @Override
    public List<MerchantInventory> findByCreatedAtRange(LocalDateTime startTime, LocalDateTime endTime) {
        return merchantInventoryMapper.findByCreatedAtRange(startTime, endTime);
    }

    @Override
    public void updateSafetyInventory(Long id, Integer safetyInventory) {
        merchantInventoryMapper.updateSafetyInventory(id, safetyInventory);
    }
}