package com.sky.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "wechat.pay")
public class WechatPayProperties {

    private String mchid;
    private String privateKeyPath = "apiclient_key.pem";
    private String mchSerialNo;
    private String apiV3Key;
    private String notifyUrl;
    private String appid;
    
    /**
     * 跨境支付相关配置
     */
    private CrossBorder crossBorder = new CrossBorder();
    
    @Data
    public static class CrossBorder {
        /**
         * 是否启用跨境支付
         */
        private boolean enabled = false;
        
        /**
         * 商户分类代码
         */
        private String merchantCategoryCode = "4111";
        
        /**
         * 交易类型
         */
        private String tradeType = "NATIVE";
        
        /**
         * 货币类型
         */
        private String currency = "USD";
        
        /**
         * 查询订单API基础URL
         */
        private String queryApiBaseUrl = "https://apihk.mch.weixin.qq.com/v3/global/transactions";
    }
    
    // 兼容性方法，保持与原有代码的兼容
    public String getMerchantId() {
        return mchid;
    }
    
    public String getMerchantSerialNumber() {
        return mchSerialNo;
    }
}
