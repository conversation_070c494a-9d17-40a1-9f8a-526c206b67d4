-- 创建商家收账账户信息表
USE ry_mall;

CREATE TABLE seller_payment_account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',

    -- 关联信息
    seller_id BIGINT NOT NULL COMMENT '商家ID',

    -- 账户基本信息
    account_type TINYINT NOT NULL COMMENT '账户类型(1-银行卡,2-支付宝,3-微信,4-其他)',
    account_name VARCHAR(100) NOT NULL COMMENT '账户名称/持卡人姓名',
    account_number VARCHAR(50) NOT NULL COMMENT '账户号码/卡号',

    -- 银行卡专用字段
    bank_name VARCHAR(100) NULL COMMENT '银行名称',
    bank_code VARCHAR(20) NULL COMMENT '银行代码',
    branch_name VARCHAR(200) NULL COMMENT '开户支行',

    -- 第三方支付专用字段
    platform_name VARCHAR(50) NULL COMMENT '平台名称(支付宝/微信等)',
    platform_account VARCHAR(100) NULL COMMENT '平台账号(手机号/邮箱等)',

    -- 状态信息
    is_default TINYINT NOT NULL DEFAULT 0 COMMENT '是否默认账户(0-否,1-是)',
    account_status TINYINT NOT NULL DEFAULT 1 COMMENT '账户状态(0-禁用,1-启用)',
    verification_status TINYINT NOT NULL DEFAULT 0 COMMENT '验证状态(0-未验证,1-已验证)',

    -- 附加信息
    id_card_number VARCHAR(18) NULL COMMENT '身份证号码',
    phone VARCHAR(20) NULL COMMENT '预留手机号',
    remark VARCHAR(500) NULL COMMENT '备注信息',

    -- 系统字段
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_seller_id (seller_id),
    INDEX idx_account_type (account_type),
    INDEX idx_account_status (account_status),
    INDEX idx_is_default (is_default),
    INDEX idx_verification_status (verification_status),
    UNIQUE KEY uk_seller_account (seller_id, account_number, account_type)
) COMMENT '商家收账账户信息表';

-- 插入一些测试数据
INSERT INTO seller_payment_account (
    seller_id, account_type, account_name, account_number, 
    bank_name, bank_code, branch_name, 
    is_default, account_status, verification_status,
    id_card_number, phone, remark
) VALUES 
(1, 1, '张三', '6222021234567890123', '中国工商银行', 'ICBC', '北京朝阳支行', 1, 1, 1, '110101199001011234', '***********', '主要收款账户'),
(1, 2, '张三', '<EMAIL>', NULL, NULL, NULL, 0, 1, 1, '110101199001011234', '***********', '支付宝备用账户'),
(2, 1, '李四', '6222021234567890456', '中国建设银行', 'CCB', '上海浦东支行', 1, 1, 0, '310101199002021234', '***********', '默认收款账户');
