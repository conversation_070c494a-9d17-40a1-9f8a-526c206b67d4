package com.sky.service;

import com.sky.dto.WechatRefundRequestDTO;
import com.sky.vo.WechatRefundResponseVO;

/**
 * 微信支付服务接口
 */
public interface WechatPayService {
    
    /**
     * 创建微信支付订单
     * @param outTradeNo 商户订单号
     * @param total 订单金额（单位：分）
     * @param description 商品描述
     * @param clientIp 客户端IP地址
     * @return 支付二维码链接
     */
    String createOrder(String outTradeNo, int total, String description, String clientIp);
    
    /**
     * 申请退款
     * @param requestDTO 退款申请请求
     * @return 退款响应
     */
    WechatRefundResponseVO applyRefund(WechatRefundRequestDTO requestDTO);
    
    /**
     * 查询退款状态
     * @param outRefundNo 商户退款单号
     * @return 退款查询结果
     */
    WechatRefundResponseVO queryRefund(String outRefundNo);
}
