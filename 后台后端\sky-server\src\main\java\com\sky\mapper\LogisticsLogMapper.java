package com.sky.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 物流日志Mapper
 */
@Mapper
public interface LogisticsLogMapper {
    
    /**
     * 插入物流操作日志
     */
    @Insert("INSERT INTO logistics_log (order_id, tracking_number, old_status, new_status, " +
            "event_info, event_time, location, operator, create_time) " +
            "VALUES (#{orderId}, #{trackingNumber}, #{oldStatus}, #{newStatus}, " +
            "#{eventInfo}, #{eventTime}, #{location}, #{operator}, #{createTime})")
    void insertLogisticsLog(@Param("orderId") Long orderId,
                           @Param("trackingNumber") String trackingNumber,
                           @Param("oldStatus") String oldStatus,
                           @Param("newStatus") String newStatus,
                           @Param("eventInfo") String eventInfo,
                           @Param("eventTime") LocalDateTime eventTime,
                           @Param("location") String location,
                           @Param("operator") String operator,
                           @Param("createTime") LocalDateTime createTime);
}
