package com.sky.controller.Seller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sky.constant.MessageConstant;
import com.sky.entity.Seller;
import com.sky.entity.SellerPermission;
import com.sky.exception.AccountNotFoundException;
import com.sky.result.Result;
import com.sky.service.SellerPermissionService;
import com.sky.service.SellerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ArrayUtil.distinct;

@RestController
@RequestMapping("/SellerPermission")
@CrossOrigin(origins = "*")
@Slf4j
public class SellerPermissionController {

    @Autowired
    private SellerPermissionService sellerPermissionService;

    @Autowired
    private SellerService sellerService;

    @PostMapping("/add")
    public Result PermissionAdd (@RequestBody SellerPermission sellerPermission) {
        Long sellerId = sellerPermission.getSellerId();
        SellerPermission one = sellerPermissionService.lambdaQuery().eq(SellerPermission::getSellerId, sellerId)
                .eq(SellerPermission::getPermissionCode, sellerPermission.getPermissionCode()).one();
        if (one != null) {
            throw new AccountNotFoundException("该权限已经授权给用户");
        }
        sellerPermissionService.save(sellerPermission);
        return Result.success();
    }

    @GetMapping("/getSellerPermission")
    public Result<List<SellerPermission>> getSellerPermission (@RequestParam("sellerId") Long sellerId) {
        List<SellerPermission> list = sellerPermissionService.lambdaQuery().eq(SellerPermission::getSellerId, sellerId).list();
        return Result.success(list);
    }

    @DeleteMapping("/remove")
    public Result PermissionRemove (@RequestBody SellerPermission sellerPermission) {
        SellerPermission one = sellerPermissionService.lambdaQuery().eq(SellerPermission::getSellerId, sellerPermission.getSellerId())
                .eq(SellerPermission::getPermissionCode, sellerPermission.getPermissionCode()).one();
        if(one == null) {
            throw new AccountNotFoundException("该权限用户没有，无法删除");
        }
        LambdaQueryWrapper<SellerPermission> sellerPermissionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sellerPermissionLambdaQueryWrapper.eq(SellerPermission::getSellerId, sellerPermission.getSellerId())
                .eq(SellerPermission::getPermissionCode, sellerPermission.getPermissionCode());
        sellerPermissionService.remove(sellerPermissionLambdaQueryWrapper);
        return Result.success();
    }

    @GetMapping("/getAllPermission")
    public Result<List<Map<String, String>>> getAllPermission () {
        List<SellerPermission> list = sellerPermissionService.lambdaQuery().list();
        List<Map<String, String>> result = list.stream()
                .collect(Collectors.toMap(
                        SellerPermission::getPermissionCode, // 以 permissionCode 作为 key 去重
                        perm -> {
                            Map<String, String> map = new HashMap<>();
                            map.put("permissionCode", perm.getPermissionCode());
                            map.put("description", perm.getDescription());
                            return map;
                        },
                        (oldValue, newValue) -> oldValue // 如果重复，保留旧值
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        return Result.success(result);
    }

//    @GetMapping("/get")
//    public Result<SellerPermission> PermissionGet () {
//
//    }
}
