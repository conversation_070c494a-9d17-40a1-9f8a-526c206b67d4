package com.sky.mapper;

import com.sky.entity.SettlementConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 回款配置Mapper
 */
@Mapper
public interface SettlementConfigMapper {

    /**
     * 根据配置键查询配置
     */
    SettlementConfig selectByKey(@Param("configKey") String configKey);

    /**
     * 查询所有配置
     */
    List<SettlementConfig> selectAll();

    /**
     * 插入配置
     */
    void insert(SettlementConfig settlementConfig);

    /**
     * 更新配置
     */
    void update(SettlementConfig settlementConfig);

    /**
     * 根据配置键删除配置
     */
    void deleteByKey(@Param("configKey") String configKey);
}
