package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 订单日志实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderLog {
    
    private Long id;                    // 主键ID
    private Long orderId;               // 订单ID
    private String orderNumber;         // 订单号
    private String operation;           // 操作类型：create, pay, ship, complete, cancel, refund
    private Integer oldStatus;          // 原状态
    private Integer newStatus;          // 新状态
    private String operatorType;        // 操作者类型：admin, system, user
    private Long operatorId;            // 操作者ID
    private String operatorName;        // 操作者姓名
    private String remark;              // 备注信息
    private String details;             // 详细信息（JSON格式）
    private LocalDateTime createTime;   // 创建时间
}
