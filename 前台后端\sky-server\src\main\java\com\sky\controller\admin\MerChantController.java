package com.sky.controller.admin;

import com.sky.dto.BuyerRegisterDTO;
import com.sky.dto.MerchantDTO;
import com.sky.result.Result;
import com.sky.service.MerChantService;
import com.sky.service.MsmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/MerChant")
@Api(tags = "商家相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class MerChantController {
    @Autowired
    MerChantService merChantService;

    @PostMapping("/register")
    @ApiOperation("商家注册")
    public Result register(@RequestBody MerchantDTO merchantDTO) {
        log.info("商家注册：{}", merchantDTO);
        merChantService.MerChantRegister(merchantDTO);
        return Result.success();
    }

    @GetMapping("/review")
    @ApiOperation("商家资料显示")
    public Result<List<MerchantDTO>> review() {
        List<MerchantDTO> merchantDTOlist = merChantService.MerChantreview();
        return Result.success(merchantDTOlist);
    }

    @PutMapping("/change")
    @ApiOperation("商家信息确认")
    public Result change(@RequestParam Long ShopId) {
        merChantService.MerChantchange(ShopId);
        return Result.success();
    }

    @GetMapping("/pass")
    @ApiOperation("商家资料显示")
    public Result<List<MerchantDTO>> pass() {
        List<MerchantDTO> merchantDTOlist = merChantService.MerChantpass();
        return Result.success(merchantDTOlist);
    }
    @DeleteMapping("/Delete")
    @ApiOperation("商家资料删除")
    public Result Delete(@RequestParam Long ShopId) {
        merChantService.MerChantDelete(ShopId);
        return Result.success();
    }

}
