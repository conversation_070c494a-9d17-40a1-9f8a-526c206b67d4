package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecipientVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;        // 接收者ID
    private String name;    // 接收者名称
    private String phone;   // 接收者电话
} 