package com.sky.service.impl;

import com.sky.entity.Logistics;
import com.sky.entity.Orders;
import com.sky.entity.TrackingRecord;
import com.sky.mapper.LogisticsMapper;
import com.sky.mapper.OrdersMapper;
import com.sky.mapper.TrackingRecordMapper;
import com.sky.service.LogisticsSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流状态同步服务实现
 */
@Service
@Slf4j
public class LogisticsSyncServiceImpl implements LogisticsSyncService {

    @Autowired
    private TrackingRecordMapper trackingRecordMapper;

    @Autowired
    private LogisticsMapper logisticsMapper;

    @Autowired
    private OrdersMapper ordersMapper;

    /**
     * 同步物流状态
     * 每30分钟执行一次
     */
    @Scheduled(cron = "0 */30 * * * ?")
    @Override
    public void syncTrackingStatus() {
        log.info("开始执行物流状态同步任务");
        
        try {
            // 查找需要同步的跟踪记录（正在跟踪且2小时内未更新的）
            List<TrackingRecord> needSyncRecords = trackingRecordMapper.findNeedSyncRecords(2);
            
            for (TrackingRecord record : needSyncRecords) {
                try {
                    syncSingleTrackingRecord(record);
                } catch (Exception e) {
                    log.error("同步物流记录失败，trackingNumber: {}", record.getTrackingNumber(), e);
                }
            }
            
            if (!needSyncRecords.isEmpty()) {
                log.info("物流状态同步任务完成，共处理 {} 条记录", needSyncRecords.size());
            }
            
        } catch (Exception e) {
            log.error("物流状态同步任务执行异常", e);
        }
    }

    /**
     * 同步单个跟踪记录
     */
    @Transactional
    public void syncSingleTrackingRecord(TrackingRecord record) {
        log.info("开始同步物流记录：{}", record.getTrackingNumber());
        
        try {
            // TODO: 调用17TRACK API获取最新状态
            // 这里模拟API调用结果
            TrackingStatusResult result = callTrackingAPI(record);
            
            if (result != null) {
                // 更新跟踪记录
                updateTrackingRecord(record, result);
                
                // 更新物流记录状态
                updateLogisticsStatus(record, result);
                
                // 如果订单状态需要更新，则更新订单状态
                updateOrderStatusIfNeeded(record, result);
                
                log.info("物流记录同步成功：{}，状态：{}", record.getTrackingNumber(), result.getStatus());
            }
            
        } catch (Exception e) {
            log.error("同步物流记录异常：{}", record.getTrackingNumber(), e);
            
            // 更新跟踪时间，避免频繁重试
            record.setTrackTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            trackingRecordMapper.update(record);
        }
    }

    /**
     * 调用17TRACK API获取物流状态（模拟实现）
     */
    private TrackingStatusResult callTrackingAPI(TrackingRecord record) {
        try {
            // TODO: 这里应该调用真实的17TRACK API
            // 目前模拟返回结果
            
            log.info("模拟调用17TRACK API，trackingNumber: {}", record.getTrackingNumber());
            
            // 模拟不同的物流状态
            String[] statuses = {"InfoReceived", "InTransit", "OutForDelivery", "Delivered"};
            String newStatus = statuses[(int) (Math.random() * statuses.length)];
            
            TrackingStatusResult result = new TrackingStatusResult();
            result.setTrackingNumber(record.getTrackingNumber());
            result.setStatus(newStatus);
            result.setSubStatus("Normal");
            result.setSubStatusDesc(getStatusDescription(newStatus));
            result.setLatestEventTime(LocalDateTime.now());
            result.setLatestEventInfo("Package " + newStatus.toLowerCase());
            
            // 如果是已送达状态，设置送达时间
            if ("Delivered".equals(newStatus)) {
                result.setDeliveryTime(LocalDateTime.now());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("调用17TRACK API异常", e);
            return null;
        }
    }

    /**
     * 更新跟踪记录
     */
    private void updateTrackingRecord(TrackingRecord record, TrackingStatusResult result) {
        record.setStatus(result.getStatus());
        record.setSubStatus(result.getSubStatus());
        record.setSubStatusDesc(result.getSubStatusDesc());
        record.setLatestEventTime(result.getLatestEventTime());
        record.setLatestEventInfo(result.getLatestEventInfo());
        record.setTrackTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        
        if (result.getDeliveryTime() != null) {
            record.setDeliveryTime(result.getDeliveryTime());
        }
        
        // 如果已送达，停止跟踪
        if ("Delivered".equals(result.getStatus())) {
            record.setTrackingStatus(TrackingRecord.TRACKING_STATUS_STOPPED);
            record.setStopTrackTime(LocalDateTime.now());
            record.setStopTrackReason("Package delivered");
        }
        
        trackingRecordMapper.update(record);
    }

    /**
     * 更新物流记录状态
     */
    private void updateLogisticsStatus(TrackingRecord record, TrackingStatusResult result) {
        if (record.getOrderId() != null) {
            // 根据17TRACK状态映射到本地物流状态
            String localStatus = mapToLocalStatus(result.getStatus());
            
            // 更新物流记录
            logisticsMapper.updateLogistics(record.getOrderId(), localStatus);
            
            // 如果已送达，更新实际送达时间
            if ("delivered".equals(localStatus) && result.getDeliveryTime() != null) {
                // TODO: 添加更新实际送达时间的方法
                log.info("订单 {} 已送达，送达时间：{}", record.getOrderId(), result.getDeliveryTime());
            }
        }
    }

    /**
     * 根据需要更新订单状态
     */
    private void updateOrderStatusIfNeeded(TrackingRecord record, TrackingStatusResult result) {
        if (record.getOrderId() != null && "Delivered".equals(result.getStatus())) {
            // 查询订单当前状态
            Orders order = ordersMapper.selectById(record.getOrderId());
            
            if (order != null && order.getStatus().equals(Orders.STATUS_SHIPPED)) {
                // 如果订单状态是已发货，且物流显示已送达，则自动确认收货
                ordersMapper.updateStatus(order.getId(), Orders.STATUS_COMPLETED, LocalDateTime.now());
                ordersMapper.updateCompleteTime(order.getId(), result.getDeliveryTime(), LocalDateTime.now());
                
                log.info("订单 {} 根据物流状态自动确认收货", order.getNumber());
            }
        }
    }

    /**
     * 映射17TRACK状态到本地状态
     */
    private String mapToLocalStatus(String trackingStatus) {
        switch (trackingStatus) {
            case "InfoReceived":
                return "shipped";
            case "InTransit":
                return "in_transit";
            case "OutForDelivery":
                return "in_transit";
            case "Delivered":
                return "delivered";
            default:
                return "shipped";
        }
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String status) {
        switch (status) {
            case "InfoReceived":
                return "物流信息已录入";
            case "InTransit":
                return "运输中";
            case "OutForDelivery":
                return "派送中";
            case "Delivered":
                return "已送达";
            default:
                return "未知状态";
        }
    }

    /**
     * 物流状态结果内部类
     */
    private static class TrackingStatusResult {
        private String trackingNumber;
        private String status;
        private String subStatus;
        private String subStatusDesc;
        private LocalDateTime latestEventTime;
        private String latestEventInfo;
        private LocalDateTime deliveryTime;

        // Getters and Setters
        public String getTrackingNumber() { return trackingNumber; }
        public void setTrackingNumber(String trackingNumber) { this.trackingNumber = trackingNumber; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getSubStatus() { return subStatus; }
        public void setSubStatus(String subStatus) { this.subStatus = subStatus; }
        public String getSubStatusDesc() { return subStatusDesc; }
        public void setSubStatusDesc(String subStatusDesc) { this.subStatusDesc = subStatusDesc; }
        public LocalDateTime getLatestEventTime() { return latestEventTime; }
        public void setLatestEventTime(LocalDateTime latestEventTime) { this.latestEventTime = latestEventTime; }
        public String getLatestEventInfo() { return latestEventInfo; }
        public void setLatestEventInfo(String latestEventInfo) { this.latestEventInfo = latestEventInfo; }
        public LocalDateTime getDeliveryTime() { return deliveryTime; }
        public void setDeliveryTime(LocalDateTime deliveryTime) { this.deliveryTime = deliveryTime; }
    }
}
