package com.sky.service;

import com.sky.entity.*;
import com.sky.vo.OrderDetailIntegrationVO;

import java.util.List;
import java.util.Map;

/**
 * 订单详情集成服务接口
 */
public interface OrderDetailIntegrationService {

    /**
     * 获取订单完整详情信息
     * @param orderId 订单ID
     * @param userId 用户ID（用于权限验证）
     * @return 订单完整详情
     */
    OrderDetailIntegrationVO getOrderDetailIntegration(Long orderId, Long userId);

    /**
     * 根据订单号获取订单完整详情信息
     * @param orderNumber 订单号
     * @param userId 用户ID（用于权限验证）
     * @return 订单完整详情
     */
    OrderDetailIntegrationVO getOrderDetailIntegrationByNumber(String orderNumber, Long userId);

    /**
     * 获取用户的订单列表（带基础物流和支付信息）
     * @param userId 用户ID
     * @param status 订单状态（可选）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 订单列表
     */
    List<OrderDetailIntegrationVO> getUserOrderList(Long userId, Integer status, Integer pageNum, Integer pageSize);

    /**
     * 更新订单物流信息
     * @param orderId 订单ID
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @param carrierName 运输商名称
     */
    void updateOrderLogistics(Long orderId, String trackingNumber, String carrierCode, String carrierName);

    /**
     * 同步物流轨迹信息（从17TRACK API获取最新物流信息）
     * @param orderId 订单ID
     * @return 是否同步成功
     */
    boolean syncLogisticsTrace(Long orderId);

    /**
     * 批量同步物流轨迹信息
     * @param orderIds 订单ID列表
     * @return 同步成功的订单数量
     */
    int batchSyncLogisticsTrace(List<Long> orderIds);

    /**
     * 获取用户订单统计信息
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserOrderStatistics(Long userId);

    /**
     * 获取用户最近的物流更新
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近物流更新列表
     */
    List<LogisticsTraceDetail> getRecentLogisticsUpdates(Long userId, Integer limit);

    /**
     * 确认收货
     * @param orderId 订单ID
     * @param userId 用户ID
     */
    void confirmReceipt(Long orderId, Long userId);

    /**
     * 申请退款
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param reason 退款原因
     */
    void applyRefund(Long orderId, Long userId, String reason);

    /**
     * 取消订单
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param reason 取消原因
     */
    void cancelOrder(Long orderId, Long userId, String reason);

    /**
     * 检查订单是否可以执行某个操作
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param action 操作类型：cancel-取消，refund-退款，confirm-确认收货
     * @return 是否可以执行
     */
    boolean canPerformAction(Long orderId, Long userId, String action);

    /**
     * 获取物流状态的中文描述
     * @param status 物流状态
     * @return 中文描述
     */
    String getLogisticsStatusDescription(String status);

    /**
     * 创建订单地址信息
     * @param orderId 订单ID
     * @param deliveryAddress 收货地址
     * @param senderAddress 发货地址（可选）
     */
    void createOrderAddresses(Long orderId, OrderAddressInfo deliveryAddress, OrderAddressInfo senderAddress);

    /**
     * 创建订单支付信息
     * @param paymentDetail 支付详情
     */
    void createOrderPayment(OrderPaymentDetail paymentDetail);

    /**
     * 根据物流单号实时查询物流信息
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 物流轨迹列表
     */
    List<LogisticsTraceDetail> getRealtimeLogisticsInfo(String trackingNumber, String carrierCode);
}
