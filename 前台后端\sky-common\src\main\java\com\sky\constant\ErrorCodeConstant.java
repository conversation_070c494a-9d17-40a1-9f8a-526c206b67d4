package com.sky.constant;

/**
 * 错误码常量
 */
public class ErrorCodeConstant {

    // 通用错误码 (1000-1999)
    public static final String SYSTEM_ERROR = "系统异常，请稍后重试";
    public static final String PARAM_ERROR = "参数错误";
    public static final String DATA_NOT_FOUND = "数据不存在";
    public static final String OPERATION_FAILED = "操作失败";
    public static final String PERMISSION_DENIED = "权限不足";

    // 订单相关错误码 (2000-2999)
    public static final String ORDER_NOT_EXIST = "订单不存在";
    public static final String ORDER_STATUS_ERROR = "订单状态错误";
    public static final String ORDER_PERMISSION_DENIED = "无权限操作此订单";
    public static final String ORDER_AMOUNT_ERROR = "订单金额错误";
    public static final String ORDER_CREATE_FAILED = "创建订单失败";
    public static final String ORDER_UPDATE_FAILED = "更新订单失败";
    public static final String ORDER_CANCEL_FAILED = "取消订单失败";
    public static final String ORDER_SHIP_FAILED = "订单发货失败";
    public static final String ORDER_CONFIRM_FAILED = "确认收货失败";

    // 退款相关错误码 (3000-3999)
    public static final String REFUND_NOT_ALLOWED = "不允许退款";
    public static final String REFUND_AMOUNT_ERROR = "退款金额错误";
    public static final String REFUND_APPLICATION_NOT_EXIST = "退款申请不存在";
    public static final String REFUND_PERMISSION_DENIED = "无权限操作此退款申请";
    public static final String REFUND_STATUS_ERROR = "退款状态错误";
    public static final String REFUND_PROCESS_FAILED = "退款处理失败";
    public static final String WECHAT_REFUND_FAILED = "微信退款失败";
    public static final String REFUND_APPROVAL_FAILED = "退款审核失败";

    // 物流相关错误码 (4000-4999)
    public static final String LOGISTICS_NOT_FOUND = "物流信息不存在";
    public static final String TRACKING_NUMBER_INVALID = "物流单号无效";
    public static final String LOGISTICS_QUERY_FAILED = "物流查询失败";
    public static final String COURIER_CODE_INVALID = "快递公司代码无效";
    public static final String TRACK17_API_ERROR = "17TRACK API调用失败";
    public static final String LOGISTICS_UPDATE_FAILED = "物流信息更新失败";

    // 用户相关错误码 (5000-5999)
    public static final String USER_NOT_EXIST = "用户不存在";
    public static final String USER_NOT_LOGIN = "用户未登录";
    public static final String USER_PERMISSION_DENIED = "用户权限不足";
    public static final String USER_STATUS_ERROR = "用户状态异常";
    public static final String USER_INFO_ERROR = "用户信息错误";

    // 商品相关错误码 (6000-6999)
    public static final String PRODUCT_NOT_EXIST = "商品不存在";
    public static final String PRODUCT_STOCK_INSUFFICIENT = "商品库存不足";
    public static final String PRODUCT_STATUS_ERROR = "商品状态错误";
    public static final String PRODUCT_INFO_ERROR = "商品信息错误";

    // 支付相关错误码 (7000-7999)
    public static final String PAYMENT_FAILED = "支付失败";
    public static final String PAYMENT_AMOUNT_ERROR = "支付金额错误";
    public static final String PAYMENT_STATUS_ERROR = "支付状态错误";
    public static final String WECHAT_PAY_ERROR = "微信支付异常";

    // 数据库相关错误码 (8000-8999)
    public static final String DATABASE_ERROR = "数据库操作失败";
    public static final String DATA_SAVE_FAILED = "数据保存失败";
    public static final String DATA_UPDATE_FAILED = "数据更新失败";
    public static final String DATA_DELETE_FAILED = "数据删除失败";
    public static final String DATA_QUERY_FAILED = "数据查询失败";

    // 文件相关错误码 (9000-9999)
    public static final String FILE_UPLOAD_FAILED = "文件上传失败";
    public static final String FILE_NOT_EXIST = "文件不存在";
    public static final String FILE_FORMAT_ERROR = "文件格式错误";
    public static final String FILE_SIZE_EXCEED = "文件大小超出限制";
}
