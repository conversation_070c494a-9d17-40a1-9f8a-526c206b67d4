package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<String> msgIDs;    // 消息ID列表
    private String type;            // 用户类型：merchant(商家) 或 user(用户) 或 Administrator(管理员)
    private Long id;                // 用户或商家或管理员id
} 