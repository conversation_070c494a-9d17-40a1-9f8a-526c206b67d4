package com.sky.constant;

/**
 * 信息提示常量类
 */
public class MessageConstant {

    public static final String REGISTER_NOT_NULL = "注册信息不能为空";
    public static final String CODE_ERROR = "验证码输入错误";
    public static final String PASSWORD_ERROR = "密码错误";
    public static final String ACCOUNT_IS_EXIT = "账号已存在";
    public static final String ACCOUNT_NOT_FOUND = "账号不存在，请先注册";
    public static final String ACCOUNT_LOCKED = "账号被锁定";
    public static final String UNKNOWN_ERROR = "未知错误";
    public static final String USER_NOT_LOGIN = "用户未登录";
    public static final String CATEGORY_BE_RELATED_BY_SETMEAL = "当前分类关联了套餐,不能删除";
    public static final String CATEGORY_BE_RELATED_BY_DISH = "当前分类关联了产品,不能删除";
    public static final String SHOPPING_CART_IS_NULL = "购物车数据为空，不能下单";
    public static final String ADDRESS_BOOK_IS_NULL = "用户地址为空，不能下单";
    public static final String LOGIN_FAILED = "登录失败";
    public static final String UPLOAD_FAILED = "文件上传失败";
    public static final String SETMEAL_ENABLE_FAILED = "套餐内包含未启售菜品，无法启售";
    public static final String PASSWORD_EDIT_FAILED = "密码修改失败";
    public static final String PRODUCT_ON_SALE = "起售中的产品不能删除";
    public static final String SETMEAL_ON_SALE = "起售中的套餐不能删除";
    public static final String PRODUCT_BE_RELATED_BY_SETSTORE = "当前产品关联了店铺,不能删除";
    public static final String ORDER_STATUS_ERROR = "订单状态错误";
    public static final String ORDER_NOT_FOUND = "订单不存在";
    public static final String DATA_NOT_FOUND = "数据不存在";
    public static final String ID_NOT_FOUND = "ID不存在";



}
