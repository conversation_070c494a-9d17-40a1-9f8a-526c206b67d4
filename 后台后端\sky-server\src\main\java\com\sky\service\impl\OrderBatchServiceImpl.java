package com.sky.service.impl;

import com.sky.dto.OrderBatchOperationDTO;
import com.sky.entity.Orders;
import com.sky.mapper.OrdersMapper;
import com.sky.service.OrderBatchService;
import com.sky.service.OrderStatusService;
import com.sky.vo.OrderBatchOperationResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单批量操作服务实现类
 */
@Service
public class OrderBatchServiceImpl implements OrderBatchService {
    
    @Autowired
    private OrdersMapper ordersMapper;
    
    @Autowired
    private OrderStatusService orderStatusService;
    
    @Override
    @Transactional
    public OrderBatchOperationResultVO batchShip(OrderBatchOperationDTO batchOperationDTO) {
        List<Long> orderIds = batchOperationDTO.getOrderIds();
        List<Orders> orders = ordersMapper.getByIds(orderIds);
        
        OrderBatchOperationResultVO result = new OrderBatchOperationResultVO();
        result.setTotalCount(orderIds.size());
        
        List<String> successOrders = new ArrayList<>();
        List<String> failOrders = new ArrayList<>();
        List<String> failReasons = new ArrayList<>();
        List<Long> validOrderIds = new ArrayList<>();
        
        // 验证订单状态
        for (Orders order : orders) {
            if (orderStatusService.canShip(order)) {
                validOrderIds.add(order.getId());
                successOrders.add(order.getNumber());
            } else {
                failOrders.add(order.getNumber());
                failReasons.add("订单状态不允许发货");
            }
        }
        
        // 批量发货
        if (!validOrderIds.isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            ordersMapper.batchShipOrders(validOrderIds, Orders.STATUS_SHIPPED, now, now);
        }
        
        result.setSuccessCount(successOrders.size());
        result.setFailCount(failOrders.size());
        result.setSuccessOrders(successOrders);
        result.setFailOrders(failOrders);
        result.setFailReasons(failReasons);
        
        return result;
    }
    
    @Override
    @Transactional
    public OrderBatchOperationResultVO batchCancel(OrderBatchOperationDTO batchOperationDTO) {
        List<Long> orderIds = batchOperationDTO.getOrderIds();
        List<Orders> orders = ordersMapper.getByIds(orderIds);
        String cancelReason = batchOperationDTO.getReason();
        
        OrderBatchOperationResultVO result = new OrderBatchOperationResultVO();
        result.setTotalCount(orderIds.size());
        
        List<String> successOrders = new ArrayList<>();
        List<String> failOrders = new ArrayList<>();
        List<String> failReasons = new ArrayList<>();
        List<Long> validOrderIds = new ArrayList<>();
        
        // 验证订单状态
        for (Orders order : orders) {
            if (orderStatusService.canCancel(order)) {
                validOrderIds.add(order.getId());
                successOrders.add(order.getNumber());
            } else {
                failOrders.add(order.getNumber());
                failReasons.add("订单状态不允许取消");
            }
        }
        
        // 批量取消
        if (!validOrderIds.isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            ordersMapper.batchCancelOrders(validOrderIds, Orders.STATUS_CANCELLED, now, cancelReason, now);
        }
        
        result.setSuccessCount(successOrders.size());
        result.setFailCount(failOrders.size());
        result.setSuccessOrders(successOrders);
        result.setFailOrders(failOrders);
        result.setFailReasons(failReasons);
        
        return result;
    }
    
    @Override
    @Transactional
    public OrderBatchOperationResultVO batchComplete(OrderBatchOperationDTO batchOperationDTO) {
        List<Long> orderIds = batchOperationDTO.getOrderIds();
        List<Orders> orders = ordersMapper.getByIds(orderIds);
        
        OrderBatchOperationResultVO result = new OrderBatchOperationResultVO();
        result.setTotalCount(orderIds.size());
        
        List<String> successOrders = new ArrayList<>();
        List<String> failOrders = new ArrayList<>();
        List<String> failReasons = new ArrayList<>();
        List<Long> validOrderIds = new ArrayList<>();
        
        // 验证订单状态
        for (Orders order : orders) {
            if (orderStatusService.canComplete(order)) {
                validOrderIds.add(order.getId());
                successOrders.add(order.getNumber());
            } else {
                failOrders.add(order.getNumber());
                failReasons.add("订单状态不允许完成");
            }
        }
        
        // 批量完成
        if (!validOrderIds.isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            ordersMapper.batchUpdateStatus(validOrderIds, Orders.STATUS_COMPLETED, now);
        }
        
        result.setSuccessCount(successOrders.size());
        result.setFailCount(failOrders.size());
        result.setSuccessOrders(successOrders);
        result.setFailOrders(failOrders);
        result.setFailReasons(failReasons);
        
        return result;
    }
    
    @Override
    public OrderBatchOperationResultVO batchExport(OrderBatchOperationDTO batchOperationDTO) {
        // 这里实现导出逻辑，可以根据需要导出为Excel、CSV等格式
        OrderBatchOperationResultVO result = new OrderBatchOperationResultVO();
        result.setTotalCount(batchOperationDTO.getOrderIds().size());
        result.setSuccessCount(batchOperationDTO.getOrderIds().size());
        result.setFailCount(0);
        result.setDownloadUrl("/api/download/orders_" + System.currentTimeMillis() + ".xlsx");
        
        return result;
    }
}
