<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrdersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sky.entity.Orders">
        <id column="id" property="id" />
        <result column="number" property="number" />
        <result column="buyer_id" property="buyerId" />
        <result column="address_id" property="addressId" />
        <result column="shipping_method_id" property="shippingMethodId" />
        <result column="status" property="status" />
        <result column="amount" property="amount" />
        <result column="order_time" property="orderTime" />
        <result column="pay_time" property="payTime" />
        <result column="checkout_time" property="checkoutTime" />
        <result column="ship_time" property="shipTime" />
        <result column="complete_time" property="completeTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="pay_method" property="payMethod" />
        <result column="payment_transaction_id" property="paymentTransactionId" />
        <result column="order_remark" property="orderRemark" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="rejection_reason" property="rejectionReason" />
        <result column="cancel_request" property="cancelRequest" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_time" property="refundTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 分页查询订单 -->
    <select id="pageQuery" resultType="com.sky.entity.Orders" parameterType="com.sky.dto.OrderPageQueryDTO">
        SELECT DISTINCT o.* FROM orders o
        <if test="productName != null and productName != '' or productId != null">
            LEFT JOIN order_detail od ON o.id = od.order_id
        </if>
        <where>
            <if test="number != null and number != ''">
                AND o.number LIKE CONCAT('%', #{number}, '%')
            </if>
            <if test="buyerId != null">
                AND o.buyer_id = #{buyerId}
            </if>
            <if test="status != null">
                AND o.status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND o.status IN
                <foreach collection="statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="beginTime != null">
                AND o.order_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                AND o.order_time &lt;= #{endTime}
            </if>
            <if test="minAmount != null">
                AND o.amount &gt;= #{minAmount}
            </if>
            <if test="maxAmount != null">
                AND o.amount &lt;= #{maxAmount}
            </if>
            <if test="payMethod != null">
                AND o.pay_method = #{payMethod}
            </if>
            <if test="paymentTransactionId != null and paymentTransactionId != ''">
                AND o.payment_transaction_id LIKE CONCAT('%', #{paymentTransactionId}, '%')
            </if>
            <if test="orderRemark != null and orderRemark != ''">
                AND o.order_remark LIKE CONCAT('%', #{orderRemark}, '%')
            </if>
            <if test="addressId != null">
                AND o.address_id = #{addressId}
            </if>
            <if test="shippingMethodId != null">
                AND o.shipping_method_id = #{shippingMethodId}
            </if>
            <if test="productName != null and productName != ''">
                AND od.product_name LIKE CONCAT('%', #{productName}, '%')
            </if>
            <if test="productId != null">
                AND od.product_id = #{productId}
            </if>
        </where>
        <choose>
            <when test="sortField != null and sortField != '' and sortOrder != null and sortOrder != ''">
                <choose>
                    <when test="sortField == 'orderTime' or sortField == 'order_time'">
                        ORDER BY o.order_time ${sortOrder}
                    </when>
                    <when test="sortField == 'payTime' or sortField == 'pay_time'">
                        ORDER BY o.pay_time ${sortOrder}
                    </when>
                    <when test="sortField == 'shipTime' or sortField == 'ship_time'">
                        ORDER BY o.ship_time ${sortOrder}
                    </when>
                    <when test="sortField == 'completeTime' or sortField == 'complete_time'">
                        ORDER BY o.complete_time ${sortOrder}
                    </when>
                    <when test="sortField == 'cancelTime' or sortField == 'cancel_time'">
                        ORDER BY o.cancel_time ${sortOrder}
                    </when>
                    <when test="sortField == 'createTime' or sortField == 'create_time'">
                        ORDER BY o.create_time ${sortOrder}
                    </when>
                    <when test="sortField == 'updateTime' or sortField == 'update_time'">
                        ORDER BY o.update_time ${sortOrder}
                    </when>
                    <when test="sortField == 'amount'">
                        ORDER BY o.amount ${sortOrder}
                    </when>
                    <when test="sortField == 'status'">
                        ORDER BY o.status ${sortOrder}
                    </when>
                    <when test="sortField == 'buyerId' or sortField == 'buyer_id'">
                        ORDER BY o.buyer_id ${sortOrder}
                    </when>
                    <otherwise>
                        ORDER BY o.order_time DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY o.order_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 批量更新订单状态 -->
    <update id="batchUpdateStatus">
        UPDATE orders SET status = #{status}, update_time = #{updateTime}
        WHERE id IN
        <foreach collection="orderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量发货 -->
    <update id="batchShipOrders">
        UPDATE orders SET status = #{status}, ship_time = #{shipTime}, update_time = #{updateTime}
        WHERE id IN
        <foreach collection="orderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量取消订单 -->
    <update id="batchCancelOrders">
        UPDATE orders SET status = #{status}, cancel_time = #{cancelTime},
               cancel_reason = #{cancelReason}, update_time = #{updateTime}
        WHERE id IN
        <foreach collection="orderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据ID查询订单（包含退款信息） -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM orders WHERE id = #{id}
    </select>

    <!-- 更新订单退款状态 -->
    <update id="updateRefundStatus">
        UPDATE orders
        SET refund_status = #{refundStatus},
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime},
            </if>
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

</mapper>
