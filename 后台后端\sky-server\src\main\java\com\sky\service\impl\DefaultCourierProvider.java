package com.sky.service.impl;

import com.sky.tracking.model.courier.Courier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 默认快递公司数据提供者
 * 当51tracking API不可用时，提供基本的快递公司信息
 */
@Component
public class DefaultCourierProvider {

    /**
     * 获取默认的快递公司列表
     */
    public List<Courier> getDefaultCouriers() {
        List<Courier> couriers = new ArrayList<>();

        // 添加常用的快递公司
        couriers.add(createCourier("fedex", "FedEx", "US"));
        couriers.add(createCourier("ups", "UPS", "US"));
        couriers.add(createCourier("dhl", "DHL", "DE"));
        couriers.add(createCourier("usps", "USPS", "US"));
        couriers.add(createCourier("tnt", "TNT", "NL"));
        couriers.add(createCourier("aramex", "Aramex", "AE"));
        couriers.add(createCourier("dpd", "DPD", "DE"));
        couriers.add(createCourier("gls", "GLS", "DE"));
        couriers.add(createCourier("hermes", "Hermes", "DE"));
        couriers.add(createCourier("royal-mail", "Royal Mail", "GB"));
        couriers.add(createCourier("canada-post", "Canada Post", "CA"));
        couriers.add(createCourier("australia-post", "Australia Post", "AU"));
        couriers.add(createCourier("china-post", "China Post", "CN"));
        couriers.add(createCourier("sf-express", "SF Express", "CN"));
        couriers.add(createCourier("ems", "EMS", "CN"));

        return couriers;
    }

    /**
     * 根据快递公司代码获取快递公司信息
     */
    public Courier getCourierByCode(String courierCode) {
        return getDefaultCouriers().stream()
                .filter(courier -> courierCode.equals(courier.getCourierCode()))
                .findFirst()
                .orElse(null);
    }

    private Courier createCourier(String code, String name, String country) {
        Courier courier = new Courier();
        courier.setCourierCode(code);
        courier.setCourierName(name);
        courier.setCountry(country);
        return courier;
    }
}
