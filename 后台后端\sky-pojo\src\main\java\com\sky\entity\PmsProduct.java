package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PmsProduct {

    private static final long serialVersionUID = 1L;
    private Long id; // 商品ID
    private Long inventory;//库存
    private Long productSnapshotId; // 商品快照ID
    private Long brandId; // 品牌ID
    private Long categoryId; // 分类ID
    private String outProductId; // 商品编码
    private Long creatorUserId;
    private String name; // 商品名称
    private String pic; // 主图
    private String albumPics; // 画册图片
    private Integer publishStatus; // 上架状态：0->下架；1->上架
    private Integer sort; // 排序
    private BigDecimal price; // 价格
    private String unit; // 单位
    private BigDecimal weight; // 商品重量，默认克
    private String detailHtml; // 产品详情网页内容
    private String detailMobileHtml; // 移动端网页详情
    private String brandName; // 品牌名称
    private String productCategoryName; // 商品分类名称
    private Long createBy; // 创建人
    private Date createTime; // 创建时间
    private Long updateBy; // 修改人
    private Date updateTime; // 修改时间
    private String productAttr; // 商品销售属性，json格式
    private Integer status;
    private String introductPics; // 新增字段：图文介绍列表
    private String pdfDocument; // 新增字段：商品介绍 PDF 文档

}
