<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sky.mapper.LogisticsMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sky.entity.Logistics">
        <id column="id" property="id" />
        <result column="logistics_number" property="logisticsNumber" />
        <result column="order_id" property="orderId" />
        <result column="logistics_company" property="logisticsCompany" />
        <result column="logistics_status" property="logisticsStatus" />
        <result column="create_time" property="createTime" />
        <result column="shipping_date" property="shippingDate" />
    </resultMap>
    
    <!-- 根据ID查询物流信息 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT * FROM logistics WHERE id = #{id}
    </select>
    
    <!-- 更新物流状态 -->
    <update id="updateLogisticsStatus">
        UPDATE logistics SET logistics_status = #{logisticsStatus} WHERE id = #{id}
    </update>
</mapper> 