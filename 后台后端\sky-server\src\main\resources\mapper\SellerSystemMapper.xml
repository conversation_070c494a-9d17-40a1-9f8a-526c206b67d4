<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SellerSystemMapper">

    <select id="pageQuery" resultType="com.sky.entity.MessageNotifications"
            parameterType="com.sky.dto.MessageNotificationsDTO">
        select * from message_notifications
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by created_at desc
    </select>
    <select id="pageSystemQuery" resultType="com.sky.entity.SystemPrograms"
            parameterType="com.sky.dto.SystemProgramsDTO">
        select * from system_programs
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by created_at desc
    </select>
</mapper>
