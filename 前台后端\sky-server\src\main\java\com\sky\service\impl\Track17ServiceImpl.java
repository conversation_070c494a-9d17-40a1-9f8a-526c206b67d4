package com.sky.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sky.dto.RegisterTrackingDTO;
import com.sky.dto.TrackingQueryDTO;
import com.sky.service.Track17Service;
import com.sky.vo.RegisterTrackingResponseVO;
import com.sky.vo.TrackingListVO;
import com.sky.vo.TrackingQuotaVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 17TRACK API服务实现
 */
@Service
@Slf4j
public class Track17ServiceImpl implements Track17Service {

    @Value("${track17.api.key}")
    private String apiKey;

    @Value("${track17.api.url:https://api.17track.net/track/v2.2}")
    private String apiUrl;

    private final RestTemplate restTemplate;

    public Track17ServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public RegisterTrackingResponseVO registerTracking(List<RegisterTrackingDTO> trackingNumbers) {
        log.info("注册物流单号到17TRACK: {}", trackingNumbers.size());

        try {
            // 构建请求体
            JSONArray trackingArray = new JSONArray();
            for (RegisterTrackingDTO dto : trackingNumbers) {
                JSONObject trackingObj = new JSONObject();
                trackingObj.put("number", dto.getTrackingNumber());
                trackingObj.put("carrier", dto.getCarrierCode());
                
                if (dto.getTag() != null) {
                    trackingObj.put("tag", dto.getTag());
                }
                if (dto.getRemark() != null) {
                    trackingObj.put("remark", dto.getRemark());
                }
                if (dto.getAutoDetection() != null) {
                    trackingObj.put("auto_detection", dto.getAutoDetection());
                }
                
                // 添加扩展信息
                if (dto.getCustomerName() != null || dto.getCustomerEmail() != null) {
                    JSONObject customerInfo = new JSONObject();
                    if (dto.getCustomerName() != null) {
                        customerInfo.put("name", dto.getCustomerName());
                    }
                    if (dto.getCustomerEmail() != null) {
                        customerInfo.put("email", dto.getCustomerEmail());
                    }
                    trackingObj.put("customer", customerInfo);
                }
                
                trackingArray.add(trackingObj);
            }

            JSONObject requestBody = new JSONObject();
            requestBody.put("data", trackingArray);

            // 发送请求
            String response = sendRequest("/register", requestBody);
            
            // 解析响应
            return parseRegisterResponse(response);

        } catch (Exception e) {
            log.error("注册物流单号失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("注册失败: " + e.getMessage())
                    .successCount(0)
                    .failureCount(trackingNumbers.size())
                    .build();
        }
    }

    @Override
    public RegisterTrackingResponseVO changeCarrier(String trackingNumber, Integer oldCarrier, Integer newCarrier) {
        log.info("修改运输商: {} {} -> {}", trackingNumber, oldCarrier, newCarrier);

        try {
            JSONObject requestBody = new JSONObject();
            JSONArray dataArray = new JSONArray();
            
            JSONObject trackingObj = new JSONObject();
            trackingObj.put("number", trackingNumber);
            trackingObj.put("carrier", oldCarrier);
            trackingObj.put("carrier_new", newCarrier);
            
            dataArray.add(trackingObj);
            requestBody.put("data", dataArray);

            String response = sendRequest("/changecarrier", requestBody);
            return parseRegisterResponse(response);

        } catch (Exception e) {
            log.error("修改运输商失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("修改运输商失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RegisterTrackingResponseVO changeInfo(String trackingNumber, Integer carrier, String tag, String remark) {
        log.info("修改跟踪信息: {} {}", trackingNumber, carrier);

        try {
            JSONObject requestBody = new JSONObject();
            JSONArray dataArray = new JSONArray();
            
            JSONObject trackingObj = new JSONObject();
            trackingObj.put("number", trackingNumber);
            trackingObj.put("carrier", carrier);
            
            if (tag != null) {
                trackingObj.put("tag", tag);
            }
            if (remark != null) {
                trackingObj.put("remark", remark);
            }
            
            dataArray.add(trackingObj);
            requestBody.put("data", dataArray);

            String response = sendRequest("/changeinfo", requestBody);
            return parseRegisterResponse(response);

        } catch (Exception e) {
            log.error("修改跟踪信息失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("修改跟踪信息失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RegisterTrackingResponseVO stopTracking(String trackingNumber, Integer carrier) {
        log.info("停止跟踪: {} {}", trackingNumber, carrier);

        try {
            JSONObject requestBody = new JSONObject();
            JSONArray dataArray = new JSONArray();
            
            JSONObject trackingObj = new JSONObject();
            trackingObj.put("number", trackingNumber);
            trackingObj.put("carrier", carrier);
            
            dataArray.add(trackingObj);
            requestBody.put("data", dataArray);

            String response = sendRequest("/stoptrack", requestBody);
            return parseRegisterResponse(response);

        } catch (Exception e) {
            log.error("停止跟踪失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("停止跟踪失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RegisterTrackingResponseVO retrack(String trackingNumber, Integer carrier) {
        log.info("重启跟踪: {} {}", trackingNumber, carrier);

        try {
            JSONObject requestBody = new JSONObject();
            JSONArray dataArray = new JSONArray();
            
            JSONObject trackingObj = new JSONObject();
            trackingObj.put("number", trackingNumber);
            trackingObj.put("carrier", carrier);
            
            dataArray.add(trackingObj);
            requestBody.put("data", dataArray);

            String response = sendRequest("/retrack", requestBody);
            return parseRegisterResponse(response);

        } catch (Exception e) {
            log.error("重启跟踪失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("重启跟踪失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RegisterTrackingResponseVO deleteTracking(String trackingNumber, Integer carrier) {
        log.info("删除物流单号: {} {}", trackingNumber, carrier);

        try {
            JSONObject requestBody = new JSONObject();
            JSONArray dataArray = new JSONArray();
            
            JSONObject trackingObj = new JSONObject();
            trackingObj.put("number", trackingNumber);
            trackingObj.put("carrier", carrier);
            
            dataArray.add(trackingObj);
            requestBody.put("data", dataArray);

            String response = sendRequest("/deletetrack", requestBody);
            return parseRegisterResponse(response);

        } catch (Exception e) {
            log.error("删除物流单号失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("删除物流单号失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public TrackingQuotaVO getQuota() {
        log.info("获取当前剩余单量");

        try {
            String response = sendRequest("/getquota", new JSONObject());
            JSONObject responseObj = JSON.parseObject(response);
            
            if (responseObj.getInteger("code") == 0) {
                JSONObject data = responseObj.getJSONObject("data");
                return TrackingQuotaVO.builder()
                        .totalQuota(data.getInteger("quota"))
                        .usedQuota(data.getInteger("used"))
                        .remainingQuota(data.getInteger("quota") - data.getInteger("used"))
                        .resetTime(data.getString("reset_time"))
                        .quotaType(data.getString("quota_type"))
                        .quotaPeriod(data.getString("quota_period"))
                        .build();
            } else {
                log.error("获取配额失败: {}", responseObj.getString("message"));
                return null;
            }

        } catch (Exception e) {
            log.error("获取配额失败", e);
            return null;
        }
    }

    @Override
    public TrackingListVO getTrackingList(TrackingQueryDTO queryDTO) {
        log.info("获取跟踪列表: {}", queryDTO);

        try {
            JSONObject requestBody = buildQueryRequest(queryDTO);
            String response = sendRequest("/gettrackinglist", requestBody);
            
            return parseTrackingListResponse(response);

        } catch (Exception e) {
            log.error("获取跟踪列表失败", e);
            return TrackingListVO.builder()
                    .total(0)
                    .page(queryDTO.getPage() != null ? queryDTO.getPage() : 1)
                    .pageSize(queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 10)
                    .totalPages(0)
                    .trackingList(new ArrayList<>())
                    .build();
        }
    }

    @Override
    public List<Object> getSupportedCarriers() {
        log.info("获取支持的运输商列表");

        try {
            String response = sendRequest("/getcarriers", new JSONObject());
            JSONObject responseObj = JSON.parseObject(response);
            
            if (responseObj.getInteger("code") == 0) {
                return responseObj.getJSONArray("data").toJavaList(Object.class);
            } else {
                log.error("获取运输商列表失败: {}", responseObj.getString("message"));
                return new ArrayList<>();
            }

        } catch (Exception e) {
            log.error("获取运输商列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 发送HTTP请求到17TRACK API
     */
    private String sendRequest(String endpoint, JSONObject requestBody) {
        String url = apiUrl + endpoint;
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("17token", apiKey);
        
        HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);
        
        log.debug("发送请求到17TRACK: {} {}", url, requestBody.toJSONString());
        
        ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, String.class);
        
        log.debug("17TRACK响应: {}", response.getBody());
        
        return response.getBody();
    }

    /**
     * 解析注册响应
     */
    private RegisterTrackingResponseVO parseRegisterResponse(String response) {
        // 这里需要根据实际的17TRACK API响应格式来解析
        // 由于没有实际的API文档，这里提供一个基本的解析框架
        try {
            JSONObject responseObj = JSON.parseObject(response);
            
            return RegisterTrackingResponseVO.builder()
                    .code(responseObj.getInteger("code"))
                    .message(responseObj.getString("message"))
                    .successCount(responseObj.getInteger("accepted"))
                    .failureCount(responseObj.getInteger("rejected"))
                    .build();
                    
        } catch (Exception e) {
            log.error("解析注册响应失败", e);
            return RegisterTrackingResponseVO.builder()
                    .code(-1)
                    .message("解析响应失败")
                    .build();
        }
    }

    /**
     * 构建查询请求
     */
    private JSONObject buildQueryRequest(TrackingQueryDTO queryDTO) {
        JSONObject requestBody = new JSONObject();
        
        if (queryDTO.getPage() != null) {
            requestBody.put("page", queryDTO.getPage());
        }
        if (queryDTO.getPageSize() != null) {
            requestBody.put("page_size", queryDTO.getPageSize());
        }
        
        // 添加其他查询条件
        if (queryDTO.getTrackingNumber() != null) {
            requestBody.put("number", queryDTO.getTrackingNumber());
        }
        if (queryDTO.getCarrierCode() != null) {
            requestBody.put("carrier", queryDTO.getCarrierCode());
        }
        
        return requestBody;
    }

    /**
     * 解析跟踪列表响应
     */
    private TrackingListVO parseTrackingListResponse(String response) {
        // 这里需要根据实际的17TRACK API响应格式来解析
        try {
            JSONObject responseObj = JSON.parseObject(response);
            
            if (responseObj.getInteger("code") == 0) {
                JSONObject data = responseObj.getJSONObject("data");
                
                return TrackingListVO.builder()
                        .total(data.getInteger("total"))
                        .page(data.getInteger("page"))
                        .pageSize(data.getInteger("page_size"))
                        .totalPages(data.getInteger("total_pages"))
                        .trackingList(new ArrayList<>()) // 需要进一步解析
                        .build();
            } else {
                log.error("获取跟踪列表失败: {}", responseObj.getString("message"));
                return TrackingListVO.builder()
                        .total(0)
                        .trackingList(new ArrayList<>())
                        .build();
            }
            
        } catch (Exception e) {
            log.error("解析跟踪列表响应失败", e);
            return TrackingListVO.builder()
                    .total(0)
                    .trackingList(new ArrayList<>())
                    .build();
        }
    }
}
