package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 微信支付订单查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatPayOrderQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信支付订单号
     * 微信支付订单号和商户订单号二选一，如果都存在优先使用微信支付订单号
     */
    private String transactionId;

    /**
     * 商户订单号
     * 微信支付订单号和商户订单号二选一，如果都存在优先使用微信支付订单号
     */
    private String outTradeNo;
    
    /**
     * 是否直连模式（默认为true，表示直连模式；false表示机构模式）
     */
    private Boolean directMode = true;
    
    /**
     * 子商户号（机构模式必填）
     */
    private String subMchid;
    
    /**
     * 机构商户号（机构模式必填）
     */
    private String spMchid;
} 