package com.sky.tracking.model.courier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 快递公司信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Courier {

    private String code;        // 快递公司代码
    private String courierCode; // 快递公司代码（别名）
    private String name;        // 快递公司名称
    private String courierName; // 快递公司名称（别名）
    private String nameEn;      // 英文名称
    private String website;     // 官网地址
    private String phone;       // 客服电话
    private String country;     // 国家
    private boolean active;     // 是否启用
}
