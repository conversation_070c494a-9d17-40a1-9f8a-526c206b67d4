package com.sky.dto;

import com.sky.entity.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdminAccountDTO {
    private String accountName;
    private String email;
    private String phone;
    private String password;
    private Integer status; //默认启用
    private List<String> permissions;
    private Long roleId;
    private String remark;
}
