package com.sky.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 微信支付订单查询响应VO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WechatPayOrderQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信支付订单号
     */
    private String id;
    
    /**
     * 微信支付订单号（兼容字段）
     */
    @JsonProperty("transaction_id")
    private String transactionId;

    /**
     * 商户号（直连模式）
     */
    private String mchid;

    /**
     * 商户APPID（直连模式）
     */
    private String appid;

    /**
     * 机构商户号（机构模式）
     */
    @JsonProperty("sp_mchid")
    private String spMchid;

    /**
     * 子商户号（机构模式）
     */
    @JsonProperty("sub_mchid")
    private String subMchid;

    /**
     * 机构APPID（机构模式）
     */
    @JsonProperty("sp_appid")
    private String spAppid;

    /**
     * 子商户APPID（机构模式）
     */
    @JsonProperty("sub_appid")
    private String subAppid;

    /**
     * 商户订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 附加数据
     */
    private String attach;

    /**
     * 交易类型
     */
    @JsonProperty("trade_type")
    private String tradeType;

    /**
     * 付款银行
     */
    @JsonProperty("bank_type")
    private String bankType;

    /**
     * 支付完成时间
     */
    @JsonProperty("success_time")
    private String successTime;

    /**
     * 交易状态
     */
    @JsonProperty("trade_state")
    private String tradeState;

    /**
     * 交易状态描述
     */
    @JsonProperty("trade_state_desc")
    private String tradeStateDesc;

    /**
     * 支付者信息
     */
    private Payer payer;

    /**
     * 订单金额信息
     */
    private Amount amount;

    /**
     * 优惠详情
     */
    @JsonProperty("promotion_detail")
    private List<PromotionDetail> promotionDetail;

    /**
     * 支付者信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Payer {
        /**
         * 用户标识（直连模式）
         */
        private String openid;

        /**
         * 用户标识（机构模式 - 机构）
         */
        @JsonProperty("sp_openid")
        private String spOpenid;

        /**
         * 用户标识（机构模式 - 子商户）
         */
        @JsonProperty("sub_openid")
        private String subOpenid;
    }

    /**
     * 订单金额信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Amount {
        /**
         * 订单总金额
         */
        private Integer total;

        /**
         * 货币类型
         */
        private String currency;

        /**
         * 用户支付金额
         */
        @JsonProperty("payer_total")
        private Integer payerTotal;

        /**
         * 支付货币类型
         */
        @JsonProperty("payer_currency")
        private String payerCurrency;

        /**
         * 汇率信息
         */
        @JsonProperty("exchange_rate")
        private ExchangeRate exchangeRate;
    }

    /**
     * 汇率信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExchangeRate {
        /**
         * 汇率类型
         */
        private String type;

        /**
         * 汇率
         */
        private Long rate;
    }

    /**
     * 优惠详情
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PromotionDetail {
        /**
         * 券ID
         */
        @JsonProperty("promotion_id")
        private String promotionId;

        /**
         * 优惠名称
         */
        private String name;

        /**
         * 优惠范围
         */
        private String scope;

        /**
         * 优惠类型
         */
        private String type;

        /**
         * 优惠券面额
         */
        private Integer amount;

        /**
         * 货币类型
         */
        private String currency;

        /**
         * 活动ID
         */
        @JsonProperty("activity_id")
        private String activityId;

        /**
         * 微信出资
         */
        @JsonProperty("wechatpay_contribute_amount")
        private Integer wechatpayContributeAmount;

        /**
         * 商户出资
         */
        @JsonProperty("merchant_contribute_amount")
        private Integer merchantContributeAmount;

        /**
         * 其他出资
         */
        @JsonProperty("other_contribute_amount")
        private Integer otherContributeAmount;

        /**
         * 单品列表
         */
        @JsonProperty("goods_detail")
        private List<GoodsDetail> goodsDetail;
    }

    /**
     * 单品信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GoodsDetail {
        /**
         * 商品编码
         */
        @JsonProperty("goods_id")
        private String goodsId;
        
        /**
         * 商品备注
         */
        @JsonProperty("goods_remark")
        private String goodsRemark;
        
        /**
         * 商品数量
         */
        private Integer quantity;
        
        /**
         * 商品单价
         */
        private Integer price;
        
        /**
         * 优惠金额
         */
        @JsonProperty("discount_amount")
        private Integer discountAmount;
    }
} 