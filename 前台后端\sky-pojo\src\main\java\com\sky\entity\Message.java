package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Message implements Serializable {
    private static final long serialVersionUID = 1L;

    private String msgID;           // 消息唯一标识
    private String msgType;         // 消息类型：系统公告、商家消息、用户消息
    private String title;           // 消息标题
    private String content;         // 消息内容
    private Long senderId;          // 发送者ID
    private String senderName;      // 发送者名称
    private String senderType;      // 发送者类型：Administrator(管理员)、seller(卖家)
    private String recipientType;   // 接收者类型：seller(卖家)、user(用户)、all(全部)
    private Long recipientId;       // 接收者ID（当recipientType为all时可为空）
    private LocalDateTime sendTime; // 发送时间
    private Boolean isRead;         // 是否已读
    private Boolean isDeleted;      // 是否已删除
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
} 