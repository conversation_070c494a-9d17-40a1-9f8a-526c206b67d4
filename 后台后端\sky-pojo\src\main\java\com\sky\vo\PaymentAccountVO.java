package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 收账账户信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentAccountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商家ID
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 账户类型(1-银行卡,2-支付宝,3-微信,4-其他)
     */
    private Integer accountType;

    /**
     * 账户类型描述
     */
    private String accountTypeDesc;

    /**
     * 账户名称/持卡人姓名
     */
    private String accountName;

    /**
     * 账户号码/卡号（脱敏显示）
     */
    private String accountNumber;

    /**
     * 完整账户号码（管理员可见）
     */
    private String fullAccountNumber;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 开户支行
     */
    private String branchName;

    /**
     * 平台名称(支付宝/微信等)
     */
    private String platformName;

    /**
     * 平台账号(手机号/邮箱等)
     */
    private String platformAccount;

    /**
     * 是否默认账户(0-否,1-是)
     */
    private Integer isDefault;

    /**
     * 是否默认账户描述
     */
    private String isDefaultDesc;

    /**
     * 账户状态(0-禁用,1-启用)
     */
    private Integer accountStatus;

    /**
     * 账户状态描述
     */
    private String accountStatusDesc;

    /**
     * 验证状态(0-未验证,1-已验证)
     */
    private Integer verificationStatus;

    /**
     * 验证状态描述
     */
    private String verificationStatusDesc;

    /**
     * 身份证号码（脱敏显示）
     */
    private String idCardNumber;

    /**
     * 预留手机号（脱敏显示）
     */
    private String phone;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
