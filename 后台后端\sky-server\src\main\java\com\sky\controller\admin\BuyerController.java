package com.sky.controller.admin;


import com.sky.Utils.OssUtils;
import com.sky.Utils.RandomNumberGenerator;
import com.sky.constant.JwtClaimsConstant;
import com.sky.dto.*;
import com.sky.entity.Buyer;
import com.sky.entity.Employee;
import com.sky.properties.JwtProperties;
import com.sky.result.Result;
import com.sky.service.BuyerService;
import com.sky.service.MsmService;
import com.sky.utils.JwtUtil;
import com.sky.vo.BuyerInfoVO;
import com.sky.vo.BuyerLoginVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 员工管理
 */
@RestController
@RequestMapping("/buyer")
@Api(tags = "用户相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class BuyerController {
    @Autowired
    private MsmService msmService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private BuyerService buyerService;
    @Autowired
    private JwtProperties jwtProperties;
    public static final Map<String, VerificationCode> codeStore = new HashMap<>();
    // 定时任务线程池
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    // 验证码过期时间（单位：秒）
    private static final long EXPIRATION_TIME = 5 * 60; // 5分钟
    @Autowired
    private OssUtils ossUtils;

    public BuyerController() {
        // 启动一个定时任务来清理过期验证码
        scheduler.scheduleAtFixedRate(() -> cleanExpiredCodes(), 60, 60, TimeUnit.SECONDS); // 每60秒执行一次清理
    }
    // 定时清理过期验证码
    private void cleanExpiredCodes() {
        long currentTime = System.currentTimeMillis();
        Iterator<Map.Entry<String, VerificationCode>> iterator = codeStore.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, VerificationCode> entry = iterator.next();
            VerificationCode verificationCode = entry.getValue();
            if (verificationCode.getExpirationTime() <= currentTime) {
                iterator.remove();
                System.out.println("验证码已过期并被清除，手机号: " + entry.getKey());
            }
        }
    }
    public static class VerificationCode {
        private final String code;
        private final long expirationTime;

        public VerificationCode(String code, long expirationTime) {
            this.code = code;
            this.expirationTime = expirationTime;
        }

        public String getCode() {
            return code;
        }

        public long getExpirationTime() {
            return expirationTime;
        }
    }
    // 存储验证码
    public void storeVerificationCode(String phone, String code) {
        long expirationTime = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(EXPIRATION_TIME);
        VerificationCode verificationCode = new VerificationCode(code, expirationTime);
        codeStore.put(phone, verificationCode);
        System.out.println("验证码已存储，手机号: " + phone + ", 验证码: " + code + ", 过期时间: " + new Date(expirationTime));
    }



    /**
     * 用户注册
     *
     * @param buyerRegisterDTO
     */
    @PostMapping("/register")
    @ApiOperation("用户注册")
    public Result register(@RequestBody BuyerRegisterDTO buyerRegisterDTO) {
        log.info("用户注册：{}", buyerRegisterDTO);
        buyerService.buyerRegister(buyerRegisterDTO);
        return Result.success();
    }


    /**
     * 用户登录
     *
     * @param buyerLoginDTO
     * @return
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public Result<BuyerLoginVO> login(@RequestBody BuyerLoginDTO buyerLoginDTO) {
        log.info("用户登录：{}", buyerLoginDTO);
        Buyer buyer = buyerService.login(buyerLoginDTO);

        //登录成功后，生成jwt令牌
        Map<String, Object> claims = new HashMap<>();
        claims.put(JwtClaimsConstant.EMP_ID, buyer.getId());
        String token = JwtUtil.createJWT(
                jwtProperties.getAdminSecretKey(),
                jwtProperties.getAdminTtl(),
                claims);
        BuyerLoginVO buyerLoginVO = BuyerLoginVO.builder()
                .id(buyer.getId())
                .accountName(buyer.getAccountName())
                .token(token)
                .build();

        return Result.success(buyerLoginVO);
    }

    /**
     * 根据id查询用户信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("根据id查询用户信息")
    public Result<BuyerInfoVO> getBuyerById(@PathVariable Long id) {
        log.info("根据id查询用户信息：{}", id);
        BuyerInfoVO buyerInfoVO = buyerService.getById(id);
        return Result.success(buyerInfoVO);
    }

    /**
     * 编辑用户信息
     *
     * @param buyerDTO
     * @return
     */
    @PutMapping("/update")
    @ApiOperation("编辑用户信息")
    public Result update(@RequestBody BuyerDTO buyerDTO) {
        log.info("编辑用户信息：{}", buyerDTO);
        buyerService.update(buyerDTO);
        return Result.success();
    }

    @PostMapping("/register/sendCode")
    @ApiOperation("用户注册发送验证码")
    public Result sendMessage(@RequestBody MsmDTO msmDTO) throws Exception {
//        String phone = msmDTO.getPhone();
//
//        String code = stringRedisTemplate.opsForValue().get(phone);
//        if (!ObjectUtils.isEmpty(code)) {
//            Long expireTime = stringRedisTemplate.getExpire(phone, TimeUnit.MINUTES);
//            if (expireTime != null && expireTime > 0) {
//                //return Result.error("验证码已经发送，请于" + 2 + "分钟后重试");
//            }
//
//        }
//        //自己生成一个验证码
//        code = RandomNumberGenerator.generateRandomNumber();
//        int IntCode = Integer.parseInt(code);
//        //调用发送短信的服务
//        Boolean sentMessage = msmService.sendMessage(phone, IntCode);
//        //存入redis
//        if (sentMessage) {
//            stringRedisTemplate.opsForValue().set(phone, code, 5, TimeUnit.MINUTES);
//            return Result.success("验证码发送成功");
//        }
//        return Result.error("短信发送失败");
        String Address = msmDTO.getAddress();
        String code = stringRedisTemplate.opsForValue().get(Address);
        if (!ObjectUtils.isEmpty(code)) {
            Long expireTime = stringRedisTemplate.getExpire(Address, TimeUnit.MINUTES);
            if (expireTime != null && expireTime > 0) {
                return Result.error("验证码已经发送至邮箱，请于" + 2 + "分钟后重试");
            }
        }
        code = RandomNumberGenerator.generateRandomNumber();
        int IntCode = Integer.parseInt(code);
        //调用发送短信的服务
        Boolean sentMessage = msmService.sendMessage(Address, IntCode);
        if (sentMessage) {
            stringRedisTemplate.opsForValue().set(Address, code, 2, TimeUnit.MINUTES);
            return Result.success("验证码发送成功");
        }
        return Result.error("短信发送失败");
    }

    /**
     * 退出
     *
     * @return
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        return Result.success();
    }

    @PostMapping("/file")
    public Result file(@RequestParam("file") MultipartFile file,@RequestParam("id")String id) {
        String url = ossUtils.uploadOneFile(file);
        BuyerDTO buyerDTO = new BuyerDTO();
        Long id1 = Long.valueOf(id);
        buyerDTO.setId(id1);
        buyerDTO.setPhotoUrl(url);
        buyerService.update(buyerDTO);
        return Result.success();
    }

}
