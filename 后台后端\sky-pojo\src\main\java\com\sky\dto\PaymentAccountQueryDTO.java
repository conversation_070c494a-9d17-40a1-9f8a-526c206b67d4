package com.sky.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 收账账户查询DTO
 */
@Data
public class PaymentAccountQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;

    /**
     * 商家ID（管理员查询时使用）
     */
    private Long sellerId;

    /**
     * 账户类型(1-银行卡,2-支付宝,3-微信,4-其他)
     */
    private Integer accountType;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户状态(0-禁用,1-启用)
     */
    private Integer accountStatus;

    /**
     * 验证状态(0-未验证,1-已验证)
     */
    private Integer verificationStatus;

    /**
     * 是否默认账户(0-否,1-是)
     */
    private Integer isDefault;
}
