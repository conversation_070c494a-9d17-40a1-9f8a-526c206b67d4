package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 17TRACK配置实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;                     // 主键ID
    private String configKey;               // 配置键
    private String configValue;             // 配置值
    private String configDesc;              // 配置描述
    private String configType;              // 配置类型：string/number/boolean/json
    private Boolean isEncrypted;            // 是否加密存储
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间

    // 配置类型常量
    public static final String TYPE_STRING = "string";
    public static final String TYPE_NUMBER = "number";
    public static final String TYPE_BOOLEAN = "boolean";
    public static final String TYPE_JSON = "json";

    // 常用配置键常量
    public static final String KEY_API_TOKEN = "17track.api.key";
    public static final String KEY_API_BASE_URL = "17track.api.base_url";
    public static final String KEY_WEBHOOK_URL = "17track.webhook.url";
    public static final String KEY_WEBHOOK_SECRET = "17track.webhook.secret";
    public static final String KEY_AUTO_REGISTER = "17track.auto_register";
    public static final String KEY_DEFAULT_LANG = "17track.default_lang";
    public static final String KEY_MAX_RETRY_COUNT = "17track.max_retry_count";
    public static final String KEY_REQUEST_TIMEOUT = "17track.request_timeout";
    public static final String KEY_CARRIER_MAPPING = "17track.carrier.mapping";
}
