package com.sky.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PaymentRecordPageQueryDTO implements Serializable {

    private int page;

    private int pageSize;

    // 订单ID
    private Long orderId;

    // 支付方式ID
    private Integer paymentMethod;

    // 金额
    private BigDecimal amount;

    // 交易状态
    private String transactionStatus;


}
