# OrderDetailIntegrationController 开发文档

## 概述

`OrderDetailIntegrationController` 是前台用户端的订单详情集成控制器，提供完整的订单信息查看功能，集成了订单基础信息、物流跟踪、地址信息、支付详情等多个维度的数据。

## 功能特性

### 核心功能
- 📋 **订单详情查看**：提供订单的完整信息视图
- 🚚 **物流实时跟踪**：集成17TRACK API，支持2800+运输商
- 📍 **地址信息管理**：收货地址和发货地址完整展示
- 💳 **支付信息展示**：支付方式、状态、交易详情
- 🔄 **订单操作**：确认收货、申请退款、取消订单
- 📊 **统计信息**：用户订单统计和最近物流更新

### 技术特性
- 🔐 **权限控制**：严格的用户权限验证
- 🎯 **状态管理**：动态的操作权限判断
- 📱 **用户体验**：智能的进度提示和操作引导
- 🔄 **实时同步**：支持手动同步最新物流信息

## 数据库设计

### 核心表结构

#### 1. orders（订单主表）
```sql
-- 订单状态：1-待支付，2-已支付，3-已取消，4-已发货，5-已完成，6-已关闭
CREATE TABLE orders (
    id BIGINT PRIMARY KEY,
    number VARCHAR(50) NOT NULL COMMENT '订单号',
    buyer_id BIGINT NOT NULL COMMENT '买家ID',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '订单状态',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    order_time DATETIME NOT NULL COMMENT '下单时间',
    pay_time DATETIME COMMENT '支付时间',
    ship_time DATETIME COMMENT '发货时间',
    complete_time DATETIME COMMENT '完成时间',
    cancel_time DATETIME COMMENT '取消时间',
    -- 其他字段...
);
```

#### 2. order_logistics_tracking（物流跟踪表）
```sql
CREATE TABLE order_logistics_tracking (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_number VARCHAR(50) NOT NULL COMMENT '订单号',
    tracking_number VARCHAR(100) NOT NULL COMMENT '物流单号',
    carrier_code VARCHAR(50) NOT NULL COMMENT '运输商代码',
    carrier_name VARCHAR(100) NOT NULL COMMENT '运输商名称',
    current_status VARCHAR(50) COMMENT '当前物流状态',
    current_status_desc VARCHAR(200) COMMENT '当前状态描述',
    latest_location VARCHAR(200) COMMENT '最新位置',
    estimated_delivery_time DATETIME COMMENT '预计送达时间',
    actual_delivery_time DATETIME COMMENT '实际送达时间',
    ship_time DATETIME COMMENT '发货时间',
    last_update_time DATETIME COMMENT '最后更新时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_order_tracking (order_id, tracking_number)
);
```

#### 3. logistics_trace_detail（物流轨迹详情表）
```sql
CREATE TABLE logistics_trace_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tracking_id BIGINT NOT NULL COMMENT '物流跟踪ID',
    tracking_number VARCHAR(100) NOT NULL COMMENT '物流单号',
    trace_time DATETIME NOT NULL COMMENT '轨迹时间',
    trace_location VARCHAR(200) COMMENT '轨迹位置',
    trace_status VARCHAR(50) COMMENT '轨迹状态',
    trace_desc VARCHAR(500) NOT NULL COMMENT '轨迹描述',
    operator VARCHAR(100) COMMENT '操作员',
    phone VARCHAR(20) COMMENT '联系电话',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. order_address_info（订单地址信息表）
```sql
CREATE TABLE order_address_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    address_type TINYINT NOT NULL COMMENT '地址类型：1-收货地址，2-发货地址',
    consignee_name VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    province_name VARCHAR(50) COMMENT '省份名称',
    city_name VARCHAR(50) COMMENT '城市名称',
    district_name VARCHAR(50) COMMENT '区县名称',
    detail_address VARCHAR(200) NOT NULL COMMENT '详细地址',
    postal_code VARCHAR(10) COMMENT '邮政编码',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 5. order_payment_detail（订单支付详情表）
```sql
CREATE TABLE order_payment_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_number VARCHAR(50) NOT NULL COMMENT '订单号',
    payment_method VARCHAR(20) NOT NULL COMMENT '支付方式：wechat-微信，alipay-支付宝，bank-银行卡',
    payment_channel VARCHAR(50) COMMENT '支付渠道',
    transaction_id VARCHAR(100) COMMENT '第三方交易号',
    payment_amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    payment_status VARCHAR(20) NOT NULL COMMENT '支付状态：pending-待支付，success-成功，failed-失败，refunded-已退款',
    payment_time DATETIME COMMENT '支付时间',
    refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
    refund_time DATETIME COMMENT '退款时间',
    payment_desc VARCHAR(200) COMMENT '支付描述',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_order_payment (order_id)
);
```

### 表关系图
```
orders (订单主表)
├── order_logistics_tracking (物流跟踪) [1:1]
│   └── logistics_trace_detail (物流轨迹) [1:N]
├── order_address_info (地址信息) [1:N]
├── order_payment_detail (支付详情) [1:1]
└── order_detail (订单商品详情) [1:N]
```

## 核心实体类

### 1. OrderDetailIntegrationVO（订单详情集成视图对象）
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetailIntegrationVO implements Serializable {
    
    // ========== 基础订单信息 ==========
    private Long orderId;                    // 订单ID
    private String orderNumber;              // 订单号
    private Integer orderStatus;             // 订单状态
    private String orderStatusName;          // 订单状态名称
    private BigDecimal orderAmount;          // 订单金额
    private LocalDateTime orderTime;         // 下单时间
    private Long userId;                     // 用户ID
    private String remark;                   // 订单备注
    
    // ========== 订单商品信息 ==========
    private List<OrderDetail> orderDetails; // 订单商品详情列表
    
    // ========== 物流信息 ==========
    private OrderLogisticsTracking logisticsTracking;    // 物流跟踪信息
    private List<LogisticsTraceDetail> logisticsTraces;  // 物流轨迹详情列表
    private String logisticsStatusDesc;                  // 物流状态描述
    private Boolean isShipped;                           // 是否已发货
    private Boolean isDelivered;                         // 是否已签收
    
    // ========== 地址信息 ==========
    private OrderAddressInfo deliveryAddress;           // 收货地址信息
    private OrderAddressInfo senderAddress;             // 发货地址信息
    
    // ========== 支付信息 ==========
    private OrderPaymentDetail paymentDetail;           // 支付详情信息
    private String paymentStatusDesc;                   // 支付状态描述
    private Boolean isPaid;                             // 是否已支付
    
    // ========== 时间信息 ==========
    private LocalDateTime paymentTime;                  // 支付时间
    private LocalDateTime shipTime;                     // 发货时间
    private LocalDateTime estimatedDeliveryTime;        // 预计送达时间
    private LocalDateTime actualDeliveryTime;           // 实际送达时间
    
    // ========== 扩展信息 ==========
    private Integer progressPercentage;                 // 订单进度百分比（0-100）
    private String currentStageDesc;                    // 当前订单阶段描述
    private String nextActionTip;                       // 下一步操作提示
    private Boolean canCancel;                          // 是否可以取消订单
    private Boolean canRefund;                          // 是否可以申请退款
    private Boolean canConfirmReceipt;                  // 是否可以确认收货
    private String customerServiceContact;              // 客服联系方式
    
    // ========== 业务方法 ==========
    
    /**
     * 获取订单状态中文名称
     */
    public String getOrderStatusName() {
        if (orderStatus == null) return "未知状态";
        switch (orderStatus) {
            case 1: return "待支付";
            case 2: return "已支付";
            case 3: return "已取消";
            case 4: return "已发货";
            case 5: return "已完成";
            case 6: return "已关闭";
            default: return "未知状态";
        }
    }
    
    /**
     * 计算订单进度百分比
     */
    public Integer calculateProgressPercentage() {
        if (orderStatus == null) return 0;
        switch (orderStatus) {
            case 1: return 10;  // 待支付
            case 2: return 30;  // 已支付
            case 3: return 0;   // 已取消
            case 4: return 70;  // 已发货
            case 5: return 100; // 已完成
            case 6: return 0;   // 已关闭
            default: return 0;
        }
    }
    
    /**
     * 获取当前阶段描述
     */
    public String getCurrentStageDesc() {
        if (orderStatus == null) return "订单状态异常";
        
        if (orderStatus == 1) {
            return "等待您完成支付";
        } else if (orderStatus == 2) {
            return "支付成功，商家正在准备发货";
        } else if (orderStatus == 3) {
            return "订单已取消";
        } else if (orderStatus == 4) {
            if (logisticsTracking != null && logisticsTracking.getCurrentStatus() != null) {
                String status = logisticsTracking.getCurrentStatus();
                switch (status) {
                    case "InTransit": return "商品正在运输途中";
                    case "OutForDelivery": return "商品正在派送中，请注意接收";
                    case "AvailableForPickup": return "商品已到达取件点，请及时取件";
                    default: return "商品正在配送中";
                }
            }
            return "商品已发货，正在配送中";
        } else if (orderStatus == 5) {
            return "订单已完成，感谢您的购买";
        } else if (orderStatus == 6) {
            return "订单已关闭";
        }
        
        return "订单状态异常";
    }
}
```

### 2. OrderLogisticsTracking（物流跟踪实体）
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderLogisticsTracking implements Serializable {
    private Long id;                        // 主键ID
    private Long orderId;                   // 订单ID
    private String orderNumber;             // 订单号
    private String trackingNumber;          // 物流单号
    private String carrierCode;             // 运输商代码
    private String carrierName;             // 运输商名称
    private String currentStatus;           // 当前物流状态
    private String currentStatusDesc;       // 当前状态描述
    private String latestLocation;          // 最新位置
    private LocalDateTime estimatedDeliveryTime;  // 预计送达时间
    private LocalDateTime actualDeliveryTime;     // 实际送达时间
    private LocalDateTime shipTime;         // 发货时间
    private LocalDateTime lastUpdateTime;   // 最后更新时间
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间
}
```

### 3. LogisticsTraceDetail（物流轨迹详情实体）
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsTraceDetail implements Serializable {
    private Long id;                        // 主键ID
    private Long trackingId;                // 物流跟踪ID
    private String trackingNumber;          // 物流单号
    private LocalDateTime traceTime;        // 轨迹时间
    private String traceLocation;           // 轨迹位置
    private String traceStatus;             // 轨迹状态
    private String traceDesc;               // 轨迹描述
    private String operator;                // 操作员
    private String phone;                   // 联系电话
    private Integer sortOrder;              // 排序顺序
    private LocalDateTime createTime;       // 创建时间
}
```

### 4. OrderAddressInfo（订单地址信息实体）
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderAddressInfo implements Serializable {
    private Long id;                        // 主键ID
    private Long orderId;                   // 订单ID
    private Integer addressType;            // 地址类型：1-收货地址，2-发货地址
    private String consigneeName;           // 收货人姓名
    private String phone;                   // 手机号
    private String provinceCode;            // 省份编码
    private String provinceName;            // 省份名称
    private String cityCode;                // 城市编码
    private String cityName;                // 城市名称
    private String districtCode;            // 区县编码
    private String districtName;            // 区县名称
    private String detailAddress;           // 详细地址
    private String postalCode;              // 邮政编码
    private Integer isDefault;              // 是否默认地址：0-否，1-是
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间

    /**
     * 获取完整地址
     */
    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (provinceName != null) sb.append(provinceName);
        if (cityName != null) sb.append(cityName);
        if (districtName != null) sb.append(districtName);
        if (detailAddress != null) sb.append(detailAddress);
        return sb.toString();
    }
}
```

### 5. OrderPaymentDetail（订单支付详情实体）
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderPaymentDetail implements Serializable {
    private Long id;                        // 主键ID
    private Long orderId;                   // 订单ID
    private String orderNumber;             // 订单号
    private String paymentMethod;           // 支付方式：wechat-微信，alipay-支付宝，bank-银行卡
    private String paymentChannel;          // 支付渠道
    private String transactionId;           // 第三方交易号
    private BigDecimal paymentAmount;       // 支付金额
    private String currency;                // 货币类型
    private String paymentStatus;           // 支付状态：pending-待支付，success-成功，failed-失败，refunded-已退款
    private LocalDateTime paymentTime;      // 支付时间
    private BigDecimal refundAmount;        // 退款金额
    private LocalDateTime refundTime;       // 退款时间
    private String paymentDesc;             // 支付描述
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间

    /**
     * 获取支付方式中文名称
     */
    public String getPaymentMethodName() {
        if (paymentMethod == null) return "未知";
        switch (paymentMethod) {
            case "wechat": return "微信支付";
            case "alipay": return "支付宝";
            case "bank": return "银行卡";
            default: return paymentMethod;
        }
    }

    /**
     * 获取支付状态中文名称
     */
    public String getPaymentStatusName() {
        if (paymentStatus == null) return "未知";
        switch (paymentStatus) {
            case "pending": return "待支付";
            case "success": return "支付成功";
            case "failed": return "支付失败";
            case "refunded": return "已退款";
            default: return paymentStatus;
        }
    }
}
```

## API接口详细说明

### 基础路径
```
Base URL: /user/order-detail
```

### 1. 获取订单完整详情
```http
GET /user/order-detail/{orderId}
```

**功能描述**：获取指定订单的完整详情信息，包括订单基础信息、商品详情、物流跟踪、地址信息、支付详情等。

**路径参数**：
- `orderId` (Long, required): 订单ID

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应示例**：
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "orderId": 1,
        "orderNumber": "202412190001",
        "orderStatus": 4,
        "orderStatusName": "已发货",
        "orderAmount": 299.00,
        "orderTime": "2024-12-19T09:30:00",
        "userId": 1001,
        "remark": "请尽快发货",
        "orderDetails": [
            {
                "id": 1,
                "productId": 101,
                "productName": "iPhone 15 Pro",
                "quantity": 1,
                "price": 299.00
            }
        ],
        "logisticsTracking": {
            "id": 1,
            "trackingNumber": "SF1234567890123",
            "carrierCode": "sf",
            "carrierName": "顺丰速运",
            "currentStatus": "InTransit",
            "currentStatusDesc": "运输途中",
            "latestLocation": "北京分拣中心",
            "shipTime": "2024-12-19T10:00:00",
            "lastUpdateTime": "2024-12-19T15:30:00"
        },
        "logisticsTraces": [
            {
                "id": 1,
                "traceTime": "2024-12-19T15:30:00",
                "traceLocation": "北京分拣中心",
                "traceStatus": "InTransit",
                "traceDesc": "快件已到达北京分拣中心，正在分拣",
                "operator": "王五",
                "sortOrder": 3
            },
            {
                "id": 2,
                "traceTime": "2024-12-19T12:30:00",
                "traceLocation": "深圳转运中心",
                "traceStatus": "InTransit",
                "traceDesc": "快件已到达深圳转运中心",
                "operator": "李四",
                "sortOrder": 2
            },
            {
                "id": 3,
                "traceTime": "2024-12-19T10:00:00",
                "traceLocation": "深圳宝安区",
                "traceStatus": "PickedUp",
                "traceDesc": "快件已从寄件网点发出",
                "operator": "张三",
                "sortOrder": 1
            }
        ],
        "deliveryAddress": {
            "id": 1,
            "consigneeName": "张三",
            "phone": "13800138000",
            "provinceName": "北京市",
            "cityName": "北京市",
            "districtName": "朝阳区",
            "detailAddress": "三里屯街道1号院2号楼3单元401室",
            "postalCode": "100000"
        },
        "senderAddress": {
            "id": 2,
            "consigneeName": "发货仓库",
            "phone": "************",
            "provinceName": "广东省",
            "cityName": "深圳市",
            "districtName": "宝安区",
            "detailAddress": "西乡街道物流园区A区1号仓库",
            "postalCode": "518000"
        },
        "paymentDetail": {
            "id": 1,
            "paymentMethod": "wechat",
            "paymentChannel": "wxpay",
            "transactionId": "wx20241219100001",
            "paymentAmount": 299.00,
            "currency": "CNY",
            "paymentStatus": "success",
            "paymentTime": "2024-12-19T09:30:00",
            "paymentDesc": "微信支付"
        },
        "progressPercentage": 70,
        "currentStageDesc": "商品正在运输途中",
        "nextActionTip": "商品正在配送中，收到货后请确认收货",
        "canCancel": false,
        "canRefund": true,
        "canConfirmReceipt": true,
        "customerServiceContact": "************",
        "isShipped": true,
        "isDelivered": false,
        "isPaid": true,
        "logisticsStatusDesc": "运输途中",
        "paymentStatusDesc": "支付成功"
    }
}
```

**错误响应**：
```json
{
    "code": 0,
    "msg": "订单不存在或无权限查看此订单",
    "data": null
}
```

### 2. 根据订单号获取订单详情
```http
GET /user/order-detail/by-number/{orderNumber}
```

**功能描述**：通过订单号查询订单详情，用户可以通过订单号快速查找订单。

**路径参数**：
- `orderNumber` (String, required): 订单号

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应格式**：与获取订单详情接口相同

### 3. 获取用户订单列表
```http
GET /user/order-detail/list?status=4&pageNum=1&pageSize=10
```

**功能描述**：分页查询用户的订单列表，支持按状态筛选。

**查询参数**：
- `status` (Integer, optional): 订单状态筛选
  - 1: 待支付
  - 2: 已支付
  - 3: 已取消
  - 4: 已发货
  - 5: 已完成
  - 6: 已关闭
- `pageNum` (Integer, optional, default=1): 页码
- `pageSize` (Integer, optional, default=10): 每页大小

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应示例**：
```json
{
    "code": 1,
    "msg": "success",
    "data": [
        {
            "orderId": 1,
            "orderNumber": "202412190001",
            "orderStatus": 4,
            "orderStatusName": "已发货",
            "orderAmount": 299.00,
            "orderTime": "2024-12-19T09:30:00",
            "progressPercentage": 70,
            "currentStageDesc": "商品正在运输途中",
            "logisticsTracking": {
                "trackingNumber": "SF1234567890123",
                "carrierName": "顺丰速运",
                "currentStatus": "InTransit",
                "currentStatusDesc": "运输途中"
            },
            "canCancel": false,
            "canRefund": true,
            "canConfirmReceipt": true
        }
    ]
}
```

### 4. 同步物流信息
```http
POST /user/order-detail/{orderId}/sync-logistics
```

**功能描述**：手动同步订单的最新物流信息，调用17TRACK API获取最新物流轨迹。

**路径参数**：
- `orderId` (Long, required): 订单ID

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应示例**：
```json
{
    "code": 1,
    "msg": "物流信息同步成功",
    "data": "物流信息同步成功"
}
```

**错误响应**：
```json
{
    "code": 0,
    "msg": "该订单暂无物流信息",
    "data": null
}
```

### 5. 获取实时物流信息
```http
GET /user/order-detail/{orderId}/realtime-logistics
```

**功能描述**：获取实时物流轨迹信息，直接从17TRACK API获取最新数据。

**路径参数**：
- `orderId` (Long, required): 订单ID

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应示例**：
```json
{
    "code": 1,
    "msg": "success",
    "data": [
        {
            "trackingNumber": "SF1234567890123",
            "traceTime": "2024-12-19T16:00:00",
            "traceLocation": "北京朝阳区",
            "traceStatus": "OutForDelivery",
            "traceDesc": "快件正在派送中，派送员：李师傅，联系电话：138****1234",
            "operator": "李师傅",
            "phone": "138****1234",
            "sortOrder": 4
        }
    ]
}
```

### 6. 确认收货
```http
POST /user/order-detail/{orderId}/confirm-receipt
```

**功能描述**：用户确认收货，订单状态变为已完成。

**路径参数**：
- `orderId` (Long, required): 订单ID

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**前置条件**：
- 订单状态必须为"已发货"(status=4)
- 订单归属于当前用户

**响应示例**：
```json
{
    "code": 1,
    "msg": "确认收货成功",
    "data": "确认收货成功"
}
```

**错误响应**：
```json
{
    "code": 0,
    "msg": "订单状态不允许确认收货",
    "data": null
}
```

### 7. 申请退款
```http
POST /user/order-detail/{orderId}/apply-refund?reason=商品质量问题
```

**功能描述**：用户申请退款。

**路径参数**：
- `orderId` (Long, required): 订单ID

**查询参数**：
- `reason` (String, required): 退款原因

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**前置条件**：
- 订单状态为已支付、已发货或已完成
- 订单归属于当前用户

**响应示例**：
```json
{
    "code": 1,
    "msg": "退款申请提交成功，请等待处理",
    "data": "退款申请提交成功，请等待处理"
}
```

### 8. 取消订单
```http
POST /user/order-detail/{orderId}/cancel?reason=不需要了
```

**功能描述**：取消订单。

**路径参数**：
- `orderId` (Long, required): 订单ID

**查询参数**：
- `reason` (String, optional): 取消原因

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**前置条件**：
- 订单状态必须为"待支付"(status=1)
- 订单归属于当前用户

**响应示例**：
```json
{
    "code": 1,
    "msg": "订单取消成功",
    "data": "订单取消成功"
}
```

### 9. 获取订单统计
```http
GET /user/order-detail/statistics
```

**功能描述**：获取用户的订单统计信息。

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应示例**：
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "totalOrders": 15,
        "pendingPayment": 2,
        "paid": 3,
        "cancelled": 1,
        "shipped": 5,
        "completed": 4,
        "closed": 0
    }
}
```

### 10. 获取最近物流更新
```http
GET /user/order-detail/recent-logistics?limit=10
```

**功能描述**：获取用户最近的物流更新记录。

**查询参数**：
- `limit` (Integer, optional, default=10): 限制数量

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应示例**：
```json
{
    "code": 1,
    "msg": "success",
    "data": [
        {
            "trackingNumber": "SF1234567890123",
            "traceTime": "2024-12-19T16:00:00",
            "traceLocation": "北京朝阳区",
            "traceStatus": "OutForDelivery",
            "traceDesc": "快件正在派送中",
            "operator": "李师傅"
        }
    ]
}
```

### 11. 检查订单操作权限
```http
GET /user/order-detail/{orderId}/check-action/{action}
```

**功能描述**：检查用户是否可以对订单执行某个操作。

**路径参数**：
- `orderId` (Long, required): 订单ID
- `action` (String, required): 操作类型
  - `cancel`: 取消订单
  - `refund`: 申请退款
  - `confirm`: 确认收货

**请求头**：
- `Authorization`: Bearer {JWT_TOKEN}

**响应示例**：
```json
{
    "code": 1,
    "msg": "success",
    "data": true
}
```

### 12. 获取物流状态说明
```http
GET /user/order-detail/logistics-status-desc/{status}
```

**功能描述**：获取物流状态的中文描述。

**路径参数**：
- `status` (String, required): 物流状态

**响应示例**：
```json
{
    "code": 1,
    "msg": "success",
    "data": "运输途中"
}
```

## 业务逻辑说明

### 订单状态流转
```
待支付(1) → 已支付(2) → 已发货(4) → 已完成(5)
    ↓           ↓           ↓
  已取消(3)   已取消(3)   已关闭(6)
```

### 操作权限矩阵
| 订单状态 | 取消订单 | 申请退款 | 确认收货 | 同步物流 |
|---------|---------|---------|---------|---------|
| 待支付(1) | ✅ | ❌ | ❌ | ❌ |
| 已支付(2) | ❌ | ✅ | ❌ | ❌ |
| 已取消(3) | ❌ | ❌ | ❌ | ❌ |
| 已发货(4) | ❌ | ✅ | ✅ | ✅ |
| 已完成(5) | ❌ | ✅ | ❌ | ✅ |
| 已关闭(6) | ❌ | ❌ | ❌ | ❌ |

### 物流状态说明
| 状态代码 | 中文描述 | 说明 |
|---------|---------|------|
| NotFound | 查询不到物流信息 | 物流单号不存在或未录入系统 |
| InfoReceived | 物流信息已录入 | 商家已发货，物流信息已录入 |
| InTransit | 运输途中 | 包裹正在运输过程中 |
| Expired | 运输时间过长 | 运输时间超过预期 |
| AvailableForPickup | 到达待取 | 包裹已到达取件点 |
| OutForDelivery | 正在派送 | 快递员正在派送中 |
| DeliveryFailure | 投递失败 | 派送失败，需要重新派送 |
| Delivered | 已签收 | 包裹已成功签收 |
| Exception | 可能异常 | 物流过程中出现异常 |

### 支付状态说明
| 状态代码 | 中文描述 | 说明 |
|---------|---------|------|
| pending | 待支付 | 订单已创建，等待支付 |
| success | 支付成功 | 支付已完成 |
| failed | 支付失败 | 支付过程中失败 |
| refunded | 已退款 | 已完成退款 |

## 前端集成指南

### 1. 订单详情页面结构建议
```html
<div class="order-detail-page">
    <!-- 订单基础信息区域 -->
    <div class="order-info-section">
        <div class="order-header">
            <h2>订单详情</h2>
            <span class="order-status">{{ orderStatusName }}</span>
        </div>
        <div class="order-progress">
            <div class="progress-bar" :style="{width: progressPercentage + '%'}"></div>
        </div>
        <div class="order-basic-info">
            <p>订单号：{{ orderNumber }}</p>
            <p>下单时间：{{ orderTime }}</p>
            <p>订单金额：¥{{ orderAmount }}</p>
        </div>
        <div class="current-stage">
            <p>{{ currentStageDesc }}</p>
            <p class="next-tip">{{ nextActionTip }}</p>
        </div>
    </div>

    <!-- 商品信息区域 -->
    <div class="product-section">
        <h3>商品信息</h3>
        <div v-for="item in orderDetails" :key="item.id" class="product-item">
            <img :src="item.productImage" :alt="item.productName">
            <div class="product-info">
                <h4>{{ item.productName }}</h4>
                <p>数量：{{ item.quantity }}</p>
                <p>单价：¥{{ item.price }}</p>
            </div>
        </div>
    </div>

    <!-- 物流信息区域 -->
    <div class="logistics-section" v-if="logisticsTracking">
        <div class="logistics-header">
            <h3>物流信息</h3>
            <button @click="syncLogistics" class="sync-btn">同步物流</button>
        </div>
        <div class="logistics-current">
            <p>物流单号：{{ logisticsTracking.trackingNumber }}</p>
            <p>运输商：{{ logisticsTracking.carrierName }}</p>
            <p>当前状态：{{ logisticsTracking.currentStatusDesc }}</p>
            <p>最新位置：{{ logisticsTracking.latestLocation }}</p>
        </div>
        <div class="logistics-timeline">
            <div v-for="trace in logisticsTraces" :key="trace.id" class="timeline-item">
                <div class="timeline-time">{{ formatTime(trace.traceTime) }}</div>
                <div class="timeline-content">
                    <p class="trace-desc">{{ trace.traceDesc }}</p>
                    <p class="trace-location">{{ trace.traceLocation }}</p>
                    <p class="trace-operator" v-if="trace.operator">操作员：{{ trace.operator }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 地址信息区域 -->
    <div class="address-section">
        <div class="delivery-address">
            <h3>收货地址</h3>
            <p>{{ deliveryAddress.consigneeName }} {{ deliveryAddress.phone }}</p>
            <p>{{ deliveryAddress.getFullAddress() }}</p>
        </div>
        <div class="sender-address" v-if="senderAddress">
            <h3>发货地址</h3>
            <p>{{ senderAddress.consigneeName }} {{ senderAddress.phone }}</p>
            <p>{{ senderAddress.getFullAddress() }}</p>
        </div>
    </div>

    <!-- 支付信息区域 -->
    <div class="payment-section" v-if="paymentDetail">
        <h3>支付信息</h3>
        <p>支付方式：{{ paymentDetail.getPaymentMethodName() }}</p>
        <p>支付状态：{{ paymentDetail.getPaymentStatusName() }}</p>
        <p>支付金额：¥{{ paymentDetail.paymentAmount }}</p>
        <p>支付时间：{{ paymentDetail.paymentTime }}</p>
        <p v-if="paymentDetail.transactionId">交易号：{{ paymentDetail.transactionId }}</p>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
        <button v-if="canCancel" @click="cancelOrder" class="cancel-btn">取消订单</button>
        <button v-if="canRefund" @click="applyRefund" class="refund-btn">申请退款</button>
        <button v-if="canConfirmReceipt" @click="confirmReceipt" class="confirm-btn">确认收货</button>
    </div>

    <!-- 客服联系 -->
    <div class="customer-service">
        <p>如有问题，请联系客服：{{ customerServiceContact }}</p>
    </div>
</div>
```

### 2. JavaScript集成示例
```javascript
// Vue.js 组件示例
export default {
    data() {
        return {
            orderId: null,
            orderDetail: null,
            loading: false
        }
    },

    async mounted() {
        this.orderId = this.$route.params.orderId;
        await this.loadOrderDetail();
    },

    methods: {
        // 加载订单详情
        async loadOrderDetail() {
            this.loading = true;
            try {
                const response = await this.$http.get(`/user/order-detail/${this.orderId}`);
                if (response.data.code === 1) {
                    this.orderDetail = response.data.data;
                } else {
                    this.$message.error(response.data.msg);
                }
            } catch (error) {
                this.$message.error('加载订单详情失败');
                console.error(error);
            } finally {
                this.loading = false;
            }
        },

        // 同步物流信息
        async syncLogistics() {
            try {
                const response = await this.$http.post(`/user/order-detail/${this.orderId}/sync-logistics`);
                if (response.data.code === 1) {
                    this.$message.success('物流信息同步成功');
                    await this.loadOrderDetail(); // 重新加载订单详情
                } else {
                    this.$message.error(response.data.msg);
                }
            } catch (error) {
                this.$message.error('同步物流信息失败');
                console.error(error);
            }
        },

        // 确认收货
        async confirmReceipt() {
            try {
                await this.$confirm('确认已收到商品？', '确认收货', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const response = await this.$http.post(`/user/order-detail/${this.orderId}/confirm-receipt`);
                if (response.data.code === 1) {
                    this.$message.success('确认收货成功');
                    await this.loadOrderDetail();
                } else {
                    this.$message.error(response.data.msg);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('确认收货失败');
                    console.error(error);
                }
            }
        },

        // 申请退款
        async applyRefund() {
            try {
                const { value: reason } = await this.$prompt('请输入退款原因', '申请退款', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputValidator: (value) => {
                        if (!value || value.trim() === '') {
                            return '请输入退款原因';
                        }
                        return true;
                    }
                });

                const response = await this.$http.post(`/user/order-detail/${this.orderId}/apply-refund`, null, {
                    params: { reason: reason.trim() }
                });

                if (response.data.code === 1) {
                    this.$message.success('退款申请提交成功');
                    await this.loadOrderDetail();
                } else {
                    this.$message.error(response.data.msg);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('申请退款失败');
                    console.error(error);
                }
            }
        },

        // 取消订单
        async cancelOrder() {
            try {
                const { value: reason } = await this.$prompt('请输入取消原因（可选）', '取消订单', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                });

                const response = await this.$http.post(`/user/order-detail/${this.orderId}/cancel`, null, {
                    params: { reason: reason || '' }
                });

                if (response.data.code === 1) {
                    this.$message.success('订单取消成功');
                    await this.loadOrderDetail();
                } else {
                    this.$message.error(response.data.msg);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('取消订单失败');
                    console.error(error);
                }
            }
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '';
            return new Date(time).toLocaleString('zh-CN');
        }
    },

    computed: {
        // 从订单详情中提取各个部分的数据
        orderNumber() { return this.orderDetail?.orderNumber; },
        orderStatusName() { return this.orderDetail?.orderStatusName; },
        progressPercentage() { return this.orderDetail?.progressPercentage || 0; },
        currentStageDesc() { return this.orderDetail?.currentStageDesc; },
        nextActionTip() { return this.orderDetail?.nextActionTip; },
        orderAmount() { return this.orderDetail?.orderAmount; },
        orderTime() { return this.formatTime(this.orderDetail?.orderTime); },
        orderDetails() { return this.orderDetail?.orderDetails || []; },
        logisticsTracking() { return this.orderDetail?.logisticsTracking; },
        logisticsTraces() { return this.orderDetail?.logisticsTraces || []; },
        deliveryAddress() { return this.orderDetail?.deliveryAddress; },
        senderAddress() { return this.orderDetail?.senderAddress; },
        paymentDetail() { return this.orderDetail?.paymentDetail; },
        canCancel() { return this.orderDetail?.canCancel || false; },
        canRefund() { return this.orderDetail?.canRefund || false; },
        canConfirmReceipt() { return this.orderDetail?.canConfirmReceipt || false; },
        customerServiceContact() { return this.orderDetail?.customerServiceContact; }
    }
}
```

### 3. CSS样式建议
```css
.order-detail-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.order-info-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.order-status {
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}

.order-status.shipped { background: #e6f7ff; color: #1890ff; }
.order-status.completed { background: #f6ffed; color: #52c41a; }
.order-status.cancelled { background: #fff2e8; color: #fa8c16; }

.progress-bar {
    height: 4px;
    background: #1890ff;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.logistics-timeline {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -25px;
    top: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #1890ff;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: -21px;
    top: 8px;
    width: 1px;
    height: calc(100% - 8px);
    background: #e8e8e8;
}

.timeline-item:last-child::after {
    display: none;
}

.action-section {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 30px;
}

.action-section button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.confirm-btn { background: #52c41a; color: white; }
.refund-btn { background: #fa8c16; color: white; }
.cancel-btn { background: #ff4d4f; color: white; }
```

## 技术实现细节

### 1. 权限验证机制
```java
// 在每个接口中都会进行权限验证
Long userId = BaseContext.getCurrentId(); // 从JWT token中获取用户ID
OrderDetailIntegrationVO orderDetail = orderDetailIntegrationService
    .getOrderDetailIntegration(orderId, userId); // 服务层验证订单归属
```

### 2. 17TRACK API集成
```java
// 物流信息同步流程
public boolean syncLogisticsTrace(Long orderId) {
    // 1. 获取订单物流信息
    OrderLogisticsTracking tracking = getLogisticsTrackingByOrderId(orderId);

    // 2. 调用17TRACK API
    List<LogisticsTraceDetail> realtimeTraces = getRealtimeLogisticsInfo(
        tracking.getTrackingNumber(), tracking.getCarrierCode());

    // 3. 更新数据库
    updateLogisticsTraces(realtimeTraces);

    // 4. 更新物流状态
    updateLogisticsStatus(tracking, realtimeTraces);

    return true;
}
```

### 3. 事务管理
```java
@Transactional
public void confirmReceipt(Long orderId, Long userId) {
    // 1. 验证权限和状态
    // 2. 更新订单状态
    // 3. 更新物流信息
    // 4. 记录操作日志
    // 所有操作在一个事务中完成，确保数据一致性
}
```

### 4. 异常处理
```java
// 统一异常处理
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BaseException.class)
    public Result<String> handleBaseException(BaseException ex) {
        return Result.error(ex.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public Result<String> handleException(Exception ex) {
        log.error("系统异常", ex);
        return Result.error("系统繁忙，请稍后重试");
    }
}
```

## 性能优化建议

### 1. 数据库优化
- 为常用查询字段添加索引
- 使用分页查询避免大量数据加载
- 考虑使用缓存减少数据库查询

### 2. API调用优化
- 17TRACK API调用添加缓存机制
- 设置合理的超时时间
- 实现重试机制

### 3. 前端优化
- 使用懒加载减少初始加载时间
- 实现数据缓存避免重复请求
- 添加加载状态提升用户体验

## 安全考虑

### 1. 数据安全
- 所有接口都需要JWT认证
- 严格验证订单归属权限
- 敏感信息脱敏处理

### 2. API安全
- 17TRACK API密钥安全存储
- 实现API调用频率限制
- 添加请求参数验证

### 3. 操作安全
- 重要操作需要二次确认
- 记录所有操作日志
- 实现操作权限控制

## 监控和日志

### 1. 业务监控
- 订单状态变更监控
- 物流同步成功率监控
- 用户操作行为监控

### 2. 技术监控
- API响应时间监控
- 数据库查询性能监控
- 17TRACK API调用监控

### 3. 日志记录
- 详细的业务操作日志
- 异常错误日志
- API调用日志

## 测试建议

### 1. 单元测试
- Service层业务逻辑测试
- 权限验证测试
- 状态流转测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 17TRACK API集成测试

### 3. 用户测试
- 完整业务流程测试
- 异常场景测试
- 性能压力测试

## 部署注意事项

### 1. 环境配置
- 确保17TRACK API密钥正确配置
- 数据库连接配置正确
- 日志级别适当设置

### 2. 数据迁移
- 执行数据库脚本创建新表
- 为现有订单补充地址和支付信息
- 验证数据完整性

### 3. 监控告警
- 设置关键指标监控
- 配置异常告警
- 建立运维响应机制

## 版本更新记录

### v1.0.0 (2024-12-19)
- 初始版本发布
- 实现订单详情集成功能
- 集成17TRACK物流API
- 支持订单操作功能

### 后续规划
- 添加订单评价功能
- 实现消息推送
- 优化物流轨迹展示
- 增加更多运输商支持
```
```
