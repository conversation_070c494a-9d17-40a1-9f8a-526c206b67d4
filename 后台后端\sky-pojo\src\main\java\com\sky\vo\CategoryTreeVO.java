package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分类树节点
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CategoryTreeVO {
    private Long id;          // 分类ID
    private String name;      // 分类名称
    private Integer level;    // 分类层级
    private List<CategoryTreeVO> children;  // 子分类（三级分类时可为空）
}