package com.sky.service;

import com.sky.tracking.model.courier.Courier;
import java.util.List;

/**
 * 物流跟踪服务接口
 */
public interface TrackingService {
    
    /**
     * 验证物流单号有效性
     * @param trackingNumber 物流单号
     * @param courierCode 快递公司代码
     * @return 是否有效
     */
    boolean validateTrackingNumber(String trackingNumber, String courierCode);
    
    /**
     * 获取物流跟踪信息
     * @param trackingNumber 物流单号
     * @param courierCode 快递公司代码
     * @return 跟踪信息
     */
    Object getTrackingInfo(String trackingNumber, String courierCode);

    /**
     * 获取所有支持的快递公司
     * @return 快递公司列表
     */
    List<Courier> getAllCouriers();
}
