package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 17TRACK物流跟踪记录实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;                        // 主键ID
    private String trackingNumber;          // 物流单号
    private Integer carrierCode;            // 运输商代码
    private String carrierName;             // 运输商名称
    private Long orderId;                   // 关联订单ID
    private String orderNumber;             // 订单号
    private String originCountry;           // 发货国家代码
    private String destinationCountry;      // 目的地国家代码
    private String status;                  // 物流主状态
    private String subStatus;               // 物流子状态
    private String subStatusDesc;           // 状态描述
    private String trackingStatus;          // 跟踪状态：Tracking/Stopped
    private LocalDateTime registerTime;     // 注册时间
    private LocalDateTime trackTime;        // 最后跟踪时间
    private LocalDateTime pushTime;         // 最后推送时间
    private String pushStatus;              // 推送状态：Success/Failure/NotPushed
    private LocalDateTime stopTrackTime;    // 停止跟踪时间
    private String stopTrackReason;         // 停止跟踪原因
    private Boolean isRetracked;            // 是否已重新跟踪
    private Integer carrierChangeCount;     // 运输商修改次数
    private String tag;                     // 自定义标签
    private String remark;                  // 备注信息
    private String lang;                    // 翻译语言代码
    private String param;                   // 附加跟踪参数
    private LocalDateTime latestEventTime;  // 最新事件时间
    private String latestEventInfo;         // 最新事件信息
    private LocalDateTime pickupTime;       // 揽收时间
    private LocalDateTime deliveryTime;     // 签收时间
    private Integer daysAfterOrder;         // 下单后天数
    private Integer daysAfterLastUpdate;    // 最后更新后天数
    private Integer daysOfTransit;          // 运输天数
    private Integer daysOfTransitDone;      // 已完成运输天数
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间

    // 物流主状态常量
    public static final String STATUS_NOT_FOUND = "NotFound";
    public static final String STATUS_INFO_RECEIVED = "InfoReceived";
    public static final String STATUS_IN_TRANSIT = "InTransit";
    public static final String STATUS_EXPIRED = "Expired";
    public static final String STATUS_AVAILABLE_FOR_PICKUP = "AvailableForPickup";
    public static final String STATUS_OUT_FOR_DELIVERY = "OutForDelivery";
    public static final String STATUS_DELIVERY_FAILURE = "DeliveryFailure";
    public static final String STATUS_DELIVERED = "Delivered";
    public static final String STATUS_EXCEPTION = "Exception";

    // 跟踪状态常量
    public static final String TRACKING_STATUS_TRACKING = "Tracking";
    public static final String TRACKING_STATUS_STOPPED = "Stopped";

    // 推送状态常量
    public static final String PUSH_STATUS_SUCCESS = "Success";
    public static final String PUSH_STATUS_FAILURE = "Failure";
    public static final String PUSH_STATUS_NOT_PUSHED = "NotPushed";
}
