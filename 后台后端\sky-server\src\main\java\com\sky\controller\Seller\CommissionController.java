package com.sky.controller.Seller;

import com.sky.entity.CommissionSettings;
import com.sky.entity.TeamLeader;
import com.sky.entity.CommissionRecord;
import com.sky.vo.PageResult;
import com.sky.service.CommissionService;
import com.sky.result.Result;
import com.sky.vo.CommissionStats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 佣金控制器，处理与佣金相关的API请求
 */
@RestController
@RequestMapping("/commission")
public class CommissionController {

    @Autowired
    private CommissionService commissionService; // 注入佣金服务

    /**
     * 获取佣金设置
     * @return 包含佣金设置的结果对象
     */
    @GetMapping("/settings")
    public Result<CommissionSettings> getSettings() {
        return Result.success(commissionService.getSettings());
    }

    /**
     * 保存佣金设置
     * @param settings 佣金设置对象
     * @return 成功结果
     */
    @PostMapping("/settings")
    public Result<Void> saveSettings(@RequestBody CommissionSettings settings) {
        commissionService.saveSettings(settings);
        return Result.success();
    }

    /**
     * 获取团队领导列表
     * @param page 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param keyword 关键字查询（可选）
     * @param status 状态过滤（可选）
     * @param sortBy 排序字段（可选）
     * @param sortOrder 排序顺序（可选）
     * @return 包含团队领导分页结果的对象
     */
    @GetMapping("/leaders")
    public Result<PageResult<TeamLeader>> getLeaders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortOrder) {
        return Result.success(commissionService.getLeaders(page, pageSize, keyword, status, sortBy, sortOrder));
    }

    /**
     * 获取指定ID的团队领导详情
     * @param id 团队领导ID
     * @return 包含团队领导详情的结果对象
     */
    @GetMapping("/leaders/{id}")
    public Result<TeamLeader> getLeaderDetail(@PathVariable Long id) {
        return Result.success(commissionService.getLeaderDetail(id));
    }

    /**
     * 添加新的团队领导
     * @param leader 团队领导对象
     * @return 包含新添加的团队领导信息的结果对象
     */
    @PostMapping("/leaders")
    public Result<TeamLeader> addLeader(@RequestBody TeamLeader leader) {
        return Result.success(commissionService.addLeader(leader));
    }

    /**
     * 更新团队领导信息
     * @param id 团队领导ID
     * @param leader 更新后的团队领导对象
     * @return 成功结果
     */
    @PutMapping("/leaders/{id}")
    public Result<Void> updateLeader(@PathVariable Long id, @RequestBody TeamLeader leader) {
        commissionService.updateLeader(id, leader);
        return Result.success();
    }

    /**
     * 删除指定ID的团队领导
     * @param id 团队领导ID
     * @return 成功结果
     */
    @DeleteMapping("/leaders/{id}")
    public Result<Void> deleteLeader(@PathVariable Long id) {
        commissionService.deleteLeader(id);
        return Result.success();
    }

    /**
     * 更新团队领导状态
     * @param id 团队领导ID
     * @param statusUpdate 包含状态和原因的Map对象
     * @return 成功结果
     */
    @PatchMapping("/leaders/{id}/status")
    public Result<Void> updateLeaderStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> statusUpdate) {
        commissionService.updateLeaderStatus(id, statusUpdate.get("status"), statusUpdate.get("reason"));
        return Result.success();
    }

    /**
     * 获取佣金统计信息
     * @param period 时间段（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 包含佣金统计信息的结果对象
     */
    @GetMapping("/stats")
    public Result<CommissionStats> getStats(
            @RequestParam(required = false) String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        return Result.success(commissionService.getStats(period, startDate, endDate));
    }

    /**
     * 获取佣金记录列表
     * @param page 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param leaderId 团队领导ID（可选）
     * @param status 状态过滤（可选）
     * @param orderType 排序类型（可选）
     * @param dateRange 日期范围（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 包含佣金记录分页结果的对象
     */
    @GetMapping("/records")
    public Result<PageResult<CommissionRecord>> getRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Long leaderId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String orderType,
            @RequestParam(required = false) String dateRange,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        return Result.success(commissionService.getRecords(page, pageSize, leaderId, status, orderType, dateRange, startDate, endDate));
    }
}
