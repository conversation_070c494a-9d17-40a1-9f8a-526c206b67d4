package com.sky.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CommissionStats {
    private BigDecimal totalSales;
    private BigDecimal totalCommission;
    private BigDecimal commissionRate;
    private BigDecimal pendingCommission;
    private BigDecimal paidCommission;
    private Integer leadersCount;
    private Integer teamMembersCount;
    private List<CommissionByLevel> commissionByLevels;
    private List<DailyTrend> dailyTrend;
}

@Data
class CommissionByLevel {
    private String level;
    private Integer count;
    private BigDecimal amount;
}

@Data
class DailyTrend {
    private String date;
    private BigDecimal sales;
    private BigDecimal commission;
} 