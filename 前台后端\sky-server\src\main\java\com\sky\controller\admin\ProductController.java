package com.sky.controller.admin;

import com.fasterxml.jackson.core.JsonProcessingException;

import com.sky.dto.ProductPageQueryDTO;
import com.sky.entity.PmsProduct;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.ProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品管理
 */
@RestController
@RequestMapping("/products")
@Api(tags = "产品相关接口")
@Slf4j
public class ProductController {

    @Autowired
    private ProductService productService;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 产品分页查询
     *
     * @param productPageQueryDTO
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("产品分页查询")
    public Result<PageResult> page(ProductPageQueryDTO productPageQueryDTO) {
        log.info("产品分页查询:{}", productPageQueryDTO);
        PageResult pageResult = productService.pageQuery(productPageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据id查询产品
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("根据id查询产品")
    public Result<PmsProduct> getById(@PathVariable Long id) throws JsonProcessingException {
        log.info("根据id查询产品：{}", id);
        PmsProduct pmsProduct = productService.getByProductId(id);
        return Result.success(pmsProduct);
    }

    @ApiOperation("根据分类id查询产品")
    @GetMapping("/category/{id}")
    public Result<List<PmsProduct>> getCategoryId(@PathVariable Long id){
        System.out.println(Thread.currentThread().getId());
        log.info("根据分类id查询产品：{}", id);
        List<PmsProduct> pmsProduct = productService.getByCategoryId(id);
        return Result.success(pmsProduct);
    }

    /**
     * 根据商品ID获取商品基本信息
     * @param productId 商品ID
     * @return 商品基本信息
     */
    @GetMapping("/{productId}/basic")
    @ApiOperation("根据商品ID获取商品基本信息")
    public Result<Map<String, Object>> getProductBasicInfo(@PathVariable Long productId) {
        log.info("获取商品基本信息：{}", productId);
        try {
            PmsProduct product = productService.getByProductId(productId);
            if (product == null) {
                return Result.error("商品不存在");
            }
            
            Map<String, Object> basicInfo = new HashMap<>();
            basicInfo.put("id", product.getId());
            basicInfo.put("name", product.getName());
            basicInfo.put("image", product.getPic());  // 使用pic作为主图
            basicInfo.put("spec", product.getProductAttr());  // 使用productAttr作为规格
            basicInfo.put("price", product.getPrice());
            basicInfo.put("introductPics",product.getIntroductPics());
            basicInfo.put("pdfDocument",product.getPdfDocument());
            
            return Result.success(basicInfo);
        } catch (Exception e) {
            log.error("获取商品基本信息失败", e);
            return Result.error("获取商品信息失败");
        }
    }
    
    /**
     * 批量获取商品基本信息
     * @param productIds 商品ID列表
     * @return 商品基本信息映射
     */
    @PostMapping("/batch/basic")
    @ApiOperation("批量获取商品基本信息")
    public Result<Map<Long, Map<String, Object>>> batchGetProductBasicInfo(@RequestBody List<Long> productIds) {
        log.info("批量获取商品基本信息：{}", productIds);
        try {
            // 由于ProductService中可能没有批量获取方法，这里逐个获取
            Map<Long, Map<String, Object>> resultMap = new HashMap<>();
            
            for (Long productId : productIds) {
                try {
                    PmsProduct product = productService.getByProductId(productId);
                    if (product != null) {
                        Map<String, Object> basicInfo = new HashMap<>();
                        basicInfo.put("name", product.getName());
                        basicInfo.put("image", product.getPic());  // 使用pic作为主图
                        basicInfo.put("spec", product.getProductAttr());  // 使用productAttr作为规格
                        basicInfo.put("price", product.getPrice());
                        basicInfo.put("introductPics",product.getIntroductPics());
                        basicInfo.put("pdfDocument",product.getPdfDocument());

                        resultMap.put(productId, basicInfo);
                    }
                } catch (Exception e) {
                    log.error("获取商品信息失败，ID：{}", productId, e);
                }
            }
            
            return Result.success(resultMap);
        } catch (Exception e) {
            log.error("批量获取商品基本信息失败", e);
            return Result.error("批量获取商品信息失败");
        }
    }
    
    /**
     * 清理缓存数据
     * @param pattern
     */
    private void cleanCache(String pattern){
        Set keys = redisTemplate.keys(pattern);
        redisTemplate.delete(keys);
    }
}
