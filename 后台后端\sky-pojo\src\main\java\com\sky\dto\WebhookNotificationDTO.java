package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 17TRACK Webhook通知DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookNotificationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String event;                   // 事件类型
    private TrackingData data;              // 跟踪数据

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrackingData implements Serializable {
        private String number;              // 物流单号
        private Integer carrier;            // 运输商代码
        private String tag;                 // 自定义标签
        private String remark;              // 备注
        private TrackingInfo tracking;      // 跟踪信息
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrackingInfo implements Serializable {
        private List<ProviderInfo> providers; // 运输商信息列表
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProviderInfo implements Serializable {
        private Integer carrier;            // 运输商代码
        private String service_type;        // 服务类型
        private LatestStatus latest_status; // 最新状态
        private List<EventInfo> events;     // 事件列表
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LatestStatus implements Serializable {
        private String status;              // 主状态
        private String sub_status;          // 子状态
        private String sub_status_descr;    // 状态描述
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventInfo implements Serializable {
        private String time_iso;            // ISO时间格式
        private String time_raw;            // 原始时间
        private String time_zone;           // 时区
        private String description;         // 事件描述
        private String location;            // 地点
        private String status;              // 主状态
        private String sub_status;          // 子状态
        private String sub_status_descr;    // 状态描述
    }
}
