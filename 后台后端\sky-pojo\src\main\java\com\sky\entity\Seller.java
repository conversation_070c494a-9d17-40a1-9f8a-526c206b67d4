package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Seller {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String password;

    private String accountName;

    private String gender;

    private String phone;

    private String email;

    private int accountStatus;

    private String verificationCode;

    private String photoUrl;

    private String userRole;

    private LocalDateTime createTime;

    private LocalDateTime lastLoginTime;

    private List<String> Permission = new ArrayList<>();

}
