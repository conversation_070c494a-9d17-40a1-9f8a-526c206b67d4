package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 回款信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 商家ID
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 账单日期(支付时间+14天)
     */
    private LocalDate billingDate;

    /**
     * 账单周期(格式:2025-01)
     */
    private String billingCycle;

    /**
     * 回款日期
     */
    private LocalDate settlementDate;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 回款金额
     */
    private BigDecimal settlementAmount;

    /**
     * 回款状态(0-未到期,1-待回款,2-已回款)
     */
    private Integer settlementStatus;

    /**
     * 回款状态描述
     */
    private String settlementStatusDesc;

    /**
     * 实际回款时间
     */
    private LocalDateTime settlementTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否已到回款日期
     */
    private Boolean isDue;

    /**
     * 距离回款日期天数
     */
    private Long daysToSettlement;
}
