package com.sky.dto.track17;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 17TRACK实时查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Track17RealtimeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 运输商代码
     */
    private String carrierCode;

    /**
     * 语言代码，默认为中文
     */
    private String language = "cn";

    /**
     * 是否返回详细信息
     */
    private Boolean detailed = true;
}
