package com.sky.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BatchUpdateStatusResultVO {
    private int successCount;
    private int failCount;
    private List<Long> failIds;

    public BatchUpdateStatusResultVO() {
        this.failIds = new ArrayList<>();
    }

    public void incrementSuccess() {
        this.successCount++;
    }

    public void addFailId(Long id) {
        this.failIds.add(id);
    }

    // Getters and Setters
}
