package com.sky.service.impl;



import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.context.BaseContext;
import com.sky.dto.AdminAccountDTO;
import com.sky.dto.AdminAccountPageQueryDTO;
import com.sky.dto.AdminAccountUpdateDTO;
import com.sky.dto.AdminLogQueryDTO;
import com.sky.entity.AdminAccount;
import com.sky.enumeration.CodeEnum;
import com.sky.exception.*;
import com.sky.mapper.AdminAccountMapper;
import com.sky.mapper.SellerPermissionsMapper;
import com.sky.result.PageResult;
import com.sky.service.AdminAccountService;
import com.sky.vo.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AdminAccountServiceImpl implements AdminAccountService {

    @Autowired
    private AdminAccountMapper  adminAccountMapper;
    @Autowired
    private SellerPermissionsMapper sellerPermissionsMapper;

    public PageResult<AdminAccountVO> getAdminAccountList(AdminAccountPageQueryDTO query) {
        // 获取分页数据
        List<AdminAccountVO> adminAccounts = adminAccountMapper.selectAdminAccounts(query);

        // 获取总记录数
        Long total = adminAccountMapper.countAdminAccounts(query);

        return new PageResult<>(total,adminAccounts,query.getPage(), query.getPageSize());
    }

    /**
     * 根据id查询管理员账号信息
     *
     * @param id
     * @return
     */
    @Override
    public AdminAccount getAdminById(Long id) {
        if (id == null){
            throw new IdIsNullException(MessageConstant.ID_NOT_FOUND);
        }

        AdminAccount adminAccount = adminAccountMapper.selectById(id);

        if (adminAccount == null){
            throw new IdIsNullException(MessageConstant.ID_NOT_FOUND);
        }

        List<String> permissions = adminAccountMapper.selectPermissionsByAdminId(id);
        adminAccount.setPermissions(permissions);
        return adminAccount;
    }

    /**
     * 创建管理员账号
     *
     * @param dto
     * @return
     */
    public AdminAccountCreateVO createAdminAccount(AdminAccountDTO dto) {

        AdminAccount adminAccount = adminAccountMapper.selectByEmail(dto.getEmail());
        // 校验邮箱是否重复
        if (adminAccount != null ) {
            throw new EmailIsExistException(MessageConstant.EMAIL_IS_EXIST);
        }

        // 校验密码强度
        if (!isValidPassword(dto.getPassword())) {
            throw new PassWordIsWeakException(MessageConstant.PASSWORD_IS_WEAK);
        }

        // 设置创建者ID
        AdminAccount account = new AdminAccount();
        BeanUtils.copyProperties(dto, account);
        account.setCreatedBy(BaseContext.getCurrentId());


        // 插入管理员账号
        //创建初始权限
        List<String> permissions = new ArrayList<>();
        permissions.add("ProductList");
        permissions.add("register-step2");
        permissions.add("register-step1");
        permissions.add("Product");
        permissions.add("main");
        permissions.add("login");
        permissions.add("declaration");
        permissions.add("dashboard");
        adminAccountMapper.insertAdminAccount(account);
        Long newAdminId = account.getId();
        for (String permission : permissions){
            adminAccountMapper.insertPermission(newAdminId, permission);
        }
        //设置为子账号
        adminAccountMapper.setSubAccount(dto.getAccountName());
        AdminAccountCreateVO createInfoById = adminAccountMapper.getCreateInfoById(newAdminId);
        return createInfoById;// 查询并返回创建结果
    }

    /**
     * 更新管理员账号信息
     *
     * @param dto
     * @return
     */
    @Override
    public AdminAccountUpdateVO updateAdminAccount(AdminAccountUpdateDTO dto) {
        // 校验账号是否存在
        AdminAccount existing = adminAccountMapper.selectById(dto.getId());
        if (existing == null) {
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        // 密码非空时校验强度
        if (dto.getPassword() != null && !isValidPassword(dto.getPassword())) {
            throw new PassWordIsWeakException(MessageConstant.PASSWORD_IS_WEAK);
        }

        // 执行更新
        adminAccountMapper.updateAdminAccountById(dto); // 使用原始 dto

        // 返回更新结果
        return adminAccountMapper.getUpdateInfoById(dto.getId());
    }

    /**
     * 删除管理员账号
     *
     * @param id
     */
    @Override
    public void deleteAdminAccountById(Long id) {
        // 校验账号是否存在
        AdminAccount account = adminAccountMapper.selectById(id);
        if (account == null) {
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        // 禁止删除超级管理员账号（假设 createdBy == null 或 id == 1 表示超级管理员）
        if (account.getId() == 16) {
            throw new CannotDeleteSuperAdminException(CodeEnum.CANNOT_DELETE_ADMIN);
        }

        // 执行删除
        //删除账号前先删除关联的权限
        sellerPermissionsMapper.deletteAllPermissions(id);
        adminAccountMapper.deleteAdminAccountById(id);
    }

    /**
     * 更新管理员权限
     */
    @Override
    public AdminPermissionUpdateVO updatePermissions(Long id, List<String> permissions) {
        // 校验管理员是否存在
        AdminAccount account = adminAccountMapper.selectById(id);
        if (account == null) {
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        // 查询现有权限
        List<String> existingPermissions = adminAccountMapper.selectPermissionsByAdminId(id);

        // 处理权限变更
        if (permissions != null) {
            // 找出需要删除的权限：现有权限中存在但新权限列表中不存在的
            List<String> permissionsToDelete = existingPermissions.stream()
                    .filter(p -> !permissions.contains(p))
                    .collect(Collectors.toList());

            // 找出需要添加的权限：新权限列表中存在但现有权限中不存在的
            List<String> permissionsToAdd = permissions.stream()
                    .filter(p -> !existingPermissions.contains(p))
                    .collect(Collectors.toList());

            // 删除权限
            if (!permissionsToDelete.isEmpty()) {
                adminAccountMapper.deletePermissions(id, permissionsToDelete);
            }

            // 添加新权限
            for (String permission : permissionsToAdd) {
                adminAccountMapper.insertPermission(id, permission);
            }
        } else {
            // 如果permissions为null，删除所有现有权限
            adminAccountMapper.deleteAllPermissions(id);
        }
        // 构造返回结果
        AdminPermissionUpdateVO vo = new AdminPermissionUpdateVO();
        vo.setId(id);
        vo.setAccountName(account.getAccountName());
        vo.setPermissions(permissions);
        vo.setUpdateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return vo;
    }

    /**
     * 批量删除管理员账号
     *
     * @param ids
     * @return
     */
    @Override
    public BatchDeleteResultVO batchDelete(List<Long> ids) {
        BatchDeleteResultVO batchDeleteResultVO = new BatchDeleteResultVO();

        if (ids == null || ids.isEmpty()) {
            batchDeleteResultVO.addFailId(0L, MessageConstant.ID_NOT_FOUND);
            return batchDeleteResultVO;
        }

        for (Long id : ids) {
                // 查询管理员是否存在
                AdminAccount account = adminAccountMapper.selectById(id);
                if (account == null) {
                    batchDeleteResultVO.addFailId(id, MessageConstant.ACCOUNT_NOT_FOUND);
                    continue;
                }

                // 防止删除超级管理员
                if (id == 16) {
                    batchDeleteResultVO.addFailId(id, MessageConstant.CANNOT_DELETE_ADMIN);
                    continue;
                }
                // 删除账号
                //删除账号前将关联的权限删除
                sellerPermissionsMapper.deletteAllPermissions(id);
                adminAccountMapper.deleteAdminAccountById(id);
                batchDeleteResultVO.incrementSuccess();
        }

        return batchDeleteResultVO;
    }

    /**
     * 批量更新管理员账号状态
     *
     * @param ids
     * @param status
     * @return
     */
    @Override
    public BatchUpdateStatusResultVO batchUpdateStatus(List<Long> ids, Integer status) {
        BatchUpdateStatusResultVO result = new BatchUpdateStatusResultVO();

        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException(MessageConstant.ID_NOT_FOUND);
        }

        if (status == null || (status < 0 && status > 2)) {
            throw new IllegalArgumentException(MessageConstant.STATUS_IS_INVALID);
        }

        for (Long id : ids) {
            try {
                // 查询管理员是否存在
                AdminAccount account = adminAccountMapper.selectById(id);
                if (account == null) {
                    result.addFailId(id);
                    continue;
                }

                // 更新状态
                adminAccountMapper.updateAdminStatus(id, status);
                result.incrementSuccess();
            } catch (Exception e) {
                e.printStackTrace();
                result.addFailId(id);
            }
        }

        return result;
    }

    /**
     * 重置管理员密码
     *
     * @param id
     * @param newPassword
     */
    @Override
    public void resetPassword(Long id, String newPassword) {
        // 校验管理员是否存在
        AdminAccount account = adminAccountMapper.selectById(id);
        if (account == null) {
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        // 校验密码强度
        if (!isValidPassword(newPassword)) {
            throw new PassWordIsWeakException(MessageConstant.PASSWORD_IS_WEAK);
        }

        AdminAccountUpdateDTO adminAccountUpdateDTO = new AdminAccountUpdateDTO();
        adminAccountUpdateDTO.setId(id);
        adminAccountUpdateDTO.setPassword(newPassword);

        // 更新密码
        adminAccountMapper.updateAdminAccountById(adminAccountUpdateDTO);
    }

    @Override
    public PageResult<AdminLogVO> getAdminLogs(AdminLogQueryDTO dto) {
        PageHelper.startPage(dto.getPage(), dto.getPageSize());

        List<AdminLogVO> logs = adminAccountMapper.getAdminLogs(
                dto.getAdminId(),
                dto.getStartTime(),
                dto.getEndTime(),
                dto.getActionType()
        );

        return new PageResult<>((long)logs.size(),logs,dto.getPage(),dto.getPageSize());
    }

    private boolean isValidPassword(String password) {
        // 密码至少6位且包含数字和字母
        return password.length() >= 6 && password.matches(".*\\d.*") && password.matches(".*[A-Za-z].*");
    }



}
