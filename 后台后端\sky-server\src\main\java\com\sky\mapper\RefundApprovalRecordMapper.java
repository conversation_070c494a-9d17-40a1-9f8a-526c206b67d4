package com.sky.mapper;

import com.sky.entity.RefundApprovalRecord;
import com.sky.vo.RefundApprovalRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退款审核记录Mapper接口
 */
@Mapper
public interface RefundApprovalRecordMapper {

    /**
     * 插入审核记录
     * @param record 审核记录
     * @return 影响行数
     */
    int insert(RefundApprovalRecord record);

    /**
     * 根据ID查询审核记录
     * @param id 审核记录ID
     * @return 审核记录
     */
    RefundApprovalRecord selectById(@Param("id") Long id);

    /**
     * 根据退款申请ID查询审核记录
     * @param refundApplicationId 退款申请ID
     * @return 审核记录列表
     */
    List<RefundApprovalRecord> selectByRefundApplicationId(@Param("refundApplicationId") Long refundApplicationId);

    /**
     * 根据退款申请单号查询审核记录
     * @param refundNo 退款申请单号
     * @return 审核记录列表
     */
    List<RefundApprovalRecord> selectByRefundNo(@Param("refundNo") String refundNo);

    /**
     * 根据审核人ID查询审核记录
     * @param approverId 审核人ID
     * @return 审核记录列表
     */
    List<RefundApprovalRecord> selectByApproverId(@Param("approverId") Long approverId);

    /**
     * 根据退款申请ID查询审核记录VO
     * @param refundApplicationId 退款申请ID
     * @return 审核记录VO列表
     */
    List<RefundApprovalRecordVO> selectVOByRefundApplicationId(@Param("refundApplicationId") Long refundApplicationId);

    /**
     * 查询最新的审核记录
     * @param refundApplicationId 退款申请ID
     * @return 最新的审核记录
     */
    RefundApprovalRecord selectLatestByRefundApplicationId(@Param("refundApplicationId") Long refundApplicationId);
}
