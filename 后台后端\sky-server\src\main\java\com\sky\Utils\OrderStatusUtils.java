package com.sky.utils;

import com.sky.entity.Orders;

/**
 * 订单状态工具类
 */
public class OrderStatusUtils {

    /**
     * 获取订单状态描述
     */
    public static String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case 1: // Orders.STATUS_PENDING_PAYMENT
                return "待付款";
            case 2: // Orders.STATUS_PAID
                return "已付款";
            case 3: // Orders.STATUS_PROCESSING
                return "处理中";
            case 4: // Orders.STATUS_SHIPPED
                return "已发货";
            case 5: // Orders.STATUS_COMPLETED
                return "已完成";
            case 6: // Orders.STATUS_CANCELLED
                return "已取消";
            case 7: // Orders.STATUS_REFUNDED
                return "已退款";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 检查订单状态是否可以发货
     */
    public static boolean canShip(Integer status) {
        return Orders.STATUS_PAID.equals(status);
    }

    /**
     * 检查订单状态是否可以取消
     */
    public static boolean canCancel(Integer status) {
        return Orders.STATUS_PENDING_PAYMENT.equals(status) || 
               Orders.STATUS_PAID.equals(status);
    }

    /**
     * 检查订单状态是否可以完成
     */
    public static boolean canComplete(Integer status) {
        return Orders.STATUS_SHIPPED.equals(status);
    }
}
