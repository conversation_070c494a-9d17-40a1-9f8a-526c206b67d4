package com.sky.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sky.dto.OrderSubmitDTO;
import com.sky.vo.OrderVO;


import java.util.List;

/**
 * 订单服务接口
 */
public interface OrderService {

    /**
     * 创建订单（已支付状态）
     * @param orderSubmitDTO 订单提交DTO
     * @param buyerId 买家ID
     * @return 订单视图对象
     */
    OrderVO createOrder(OrderSubmitDTO orderSubmitDTO, Long buyerId);
    
    /**
     * 根据订单ID查询订单
     * @param id 订单ID
     * @return 订单视图对象
     */
    OrderVO getOrderById(Long id);
    
    /**
     * 根据订单号查询订单
     * @param orderNumber 订单号
     * @return 订单视图对象
     */
    OrderVO getOrderByNumber(String orderNumber);
    
    /**
     * 取消订单
     * @param orderId 订单ID
     * @param cancelReason 取消原因
     * @return 是否成功
     */
    boolean cancelOrder(Long orderId, String cancelReason);
    
    /**
     * 发货
     * @param orderId 订单ID
     * @param logisticsCompany 物流公司
     * @param logisticsNumber 物流单号
     * @return 是否成功
     */
    boolean shipOrder(Long orderId, String logisticsCompany, String logisticsNumber);
    
    /**
     * 确认收货
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean confirmOrder(Long orderId);
    
    /**
     * 根据买家ID查询订单列表
     * @param buyerId 买家ID
     * @param status 订单状态，可选
     * @return 订单视图对象列表
     */
    List<OrderVO> getOrdersByBuyerId(Long buyerId, Integer status);
    
    /**
     * 分页查询订单
     * @param page 页码
     * @param pageSize 每页大小
     * @param buyerId 买家ID，可选
     * @param status 订单状态，可选
     * @return 订单视图对象分页结果
     */
    Page<OrderVO> pageQuery(int page, int pageSize, Long buyerId, Integer status);
} 