package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecipientQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String type;            // 接收者类型：merchant(商家) 或 user(用户)
    private String keyword;         // 搜索关键词，用于搜索指定商家或用户
} 