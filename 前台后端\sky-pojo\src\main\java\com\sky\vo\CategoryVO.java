package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分类视图对象（用于列表/详情展示）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("分类信息视图对象")
public class CategoryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类ID", example = "123456")
    private Long id;

    @ApiModelProperty(value = "分类名称", example = "电子产品")
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @ApiModelProperty(value = "父分类ID", example = "0")
    private Long parentId;

    @ApiModelProperty(value = "层级（0=一级，1=二级，2=三级）", example = "1")
    private Integer level;

    @ApiModelProperty(value = "显示顺序", example = "1")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间", example = "2023-07-15 10:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-07-15 10:00:00")
    private LocalDateTime updateTime;
}