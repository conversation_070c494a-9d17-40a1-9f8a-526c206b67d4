package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sky.entity.RefundApplication;
import com.sky.vo.RefundApplicationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款申请Mapper接口
 */
@Mapper
public interface RefundApplicationMapper extends BaseMapper<RefundApplication> {

    /**
     * 根据订单ID查询退款申请
     * @param orderId 订单ID
     * @return 退款申请列表
     */
    List<RefundApplication> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据买家ID查询退款申请
     * @param buyerId 买家ID
     * @return 退款申请列表
     */
    List<RefundApplication> selectByBuyerId(@Param("buyerId") Long buyerId);

    /**
     * 根据退款申请单号查询
     * @param refundNo 退款申请单号
     * @return 退款申请
     */
    RefundApplication selectByRefundNo(@Param("refundNo") String refundNo);



    /**
     * 分页查询退款申请（带详细信息）
     * @param buyerId 买家ID（可选）
     * @param applicationStatus 申请状态（可选）
     * @param needApproval 是否需要审核（可选）
     * @param approvalStatus 审核状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 退款申请VO列表
     */
    List<RefundApplicationVO> selectPageWithDetails(
            @Param("buyerId") Long buyerId,
            @Param("applicationStatus") Integer applicationStatus,
            @Param("needApproval") Integer needApproval,
            @Param("approvalStatus") Integer approvalStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 根据ID查询退款申请详情（包含关联信息）
     * @param id 退款申请ID
     * @return 退款申请VO
     */
    RefundApplicationVO selectDetailById(@Param("id") Long id);

    /**
     * 更新申请状态
     * @param id 退款申请ID
     * @param applicationStatus 申请状态
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateApplicationStatus(@Param("id") Long id, 
                               @Param("applicationStatus") Integer applicationStatus,
                               @Param("updateTime") LocalDateTime updateTime);





    /**
     * 统计退款申请数量
     * @param buyerId 买家ID（可选）
     * @param applicationStatus 申请状态（可选）
     * @param needApproval 是否需要审核（可选）
     * @param approvalStatus 审核状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计数量
     */
    Long countRefundApplications(@Param("buyerId") Long buyerId,
                                @Param("applicationStatus") Integer applicationStatus,
                                @Param("needApproval") Integer needApproval,
                                @Param("approvalStatus") Integer approvalStatus,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

}
