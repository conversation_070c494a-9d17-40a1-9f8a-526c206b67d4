package com.sky.vo;

import lombok.Data;

import javax.annotation.sql.DataSourceDefinitions;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BatchDeleteResultVO {
    private int successCount;
    private int failCount;
    private List<Long> failIds;
    private Map<String, String> failReasons;

    // 构造方法、getter/setter
    public BatchDeleteResultVO() {
        this.failIds = new ArrayList<>();
        this.failReasons = new HashMap<>();
    }

    public void incrementSuccess() {
        this.successCount++;
    }

    public void addFailId(Long id, String reason) {
        this.failIds.add(id);
        this.failReasons.put(id.toString(), reason);
    }

}
