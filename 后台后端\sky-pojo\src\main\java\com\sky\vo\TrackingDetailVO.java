package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流详情VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;                        // 主键ID
    private String trackingNumber;          // 物流单号
    private Integer carrierCode;            // 运输商代码
    private String carrierName;             // 运输商名称
    private Long orderId;                   // 关联订单ID
    private String orderNumber;             // 订单号
    private String originCountry;           // 发货国家代码
    private String destinationCountry;      // 目的地国家代码
    private String status;                  // 物流主状态
    private String statusDesc;              // 状态描述
    private String subStatus;               // 物流子状态
    private String subStatusDesc;           // 子状态描述
    private String trackingStatus;          // 跟踪状态
    private LocalDateTime registerTime;     // 注册时间
    private LocalDateTime trackTime;        // 最后跟踪时间
    private LocalDateTime pushTime;         // 最后推送时间
    private String pushStatus;              // 推送状态
    private LocalDateTime stopTrackTime;    // 停止跟踪时间
    private String stopTrackReason;         // 停止跟踪原因
    private Boolean isRetracked;            // 是否已重新跟踪
    private Integer carrierChangeCount;     // 运输商修改次数
    private String tag;                     // 自定义标签
    private String remark;                  // 备注信息
    private String lang;                    // 翻译语言代码
    private LocalDateTime latestEventTime;  // 最新事件时间
    private String latestEventInfo;         // 最新事件信息
    private LocalDateTime pickupTime;       // 揽收时间
    private LocalDateTime deliveryTime;     // 签收时间
    private Integer daysAfterOrder;         // 下单后天数
    private Integer daysAfterLastUpdate;    // 最后更新后天数
    private Integer daysOfTransit;          // 运输天数
    private Integer daysOfTransitDone;      // 已完成运输天数
    private LocalDateTime createTime;       // 创建时间
    private LocalDateTime updateTime;       // 更新时间

    // 物流事件列表
    private List<TrackingEventVO> events;   // 物流事件列表

    // 运输商信息
    private CarrierInfoVO carrierInfo;      // 运输商信息
}
