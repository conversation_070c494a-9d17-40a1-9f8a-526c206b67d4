package com.sky.service;

import com.sky.dto.LogisticsInfoDTO;
import com.sky.vo.LogisticsInfoVO;

public interface LogisticsService {

    /**
     * 创建物流信息
     * @param logisticsInfoDTO
     */
    void createLogistics(LogisticsInfoDTO logisticsInfoDTO);

    /**
     * 根据订单id查询物流信息
     * @param id
     * @return
     */
    LogisticsInfoVO getLogisticsInfo(Long id);

    /**
     * 修改物流信息状态
     * @param id
     * @param logisticsStatus
     */
    void updateLogistics(Long id, String logisticsStatus);

}
