package com.sky.mapper;
import com.sky.entity.ShoppingCart;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ShoppingCartMapper {

     // 插入购物车记录
    @Insert("INSERT INTO shopping_cart (buyer_id, product_id, product_name, number, amount, price, image) " +
            "VALUES (#{buyerId}, #{productId}, #{productName}, #{number}, #{amount}, #{price}, #{image})")
    void insert(ShoppingCart shoppingCart);

    // 根据买家 ID 查询购物车记录
    @Select("SELECT * FROM shopping_cart WHERE buyer_id = #{buyerId}")
    List<ShoppingCart> findByBuyerId(Long buyerId);

    // 根据买家 ID 和商品 ID 查询购物车记录
    @Select("SELECT * FROM shopping_cart WHERE buyer_id = #{buyerId} AND product_id = #{productId}")
    ShoppingCart findByBuyerIdAndProductId(Long buyerId, Long productId);

    // 更新购物车记录
    @Update("UPDATE shopping_cart SET number = #{number} WHERE id = #{id}")
    void update(ShoppingCart shoppingCart);

    // 根据记录 ID 删除购物车记录
    @Delete("DELETE FROM shopping_cart WHERE id = #{id}")
    void deleteById(int id);
}
