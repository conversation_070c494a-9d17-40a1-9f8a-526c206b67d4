package com.sky.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 微信退款响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WechatRefundResponseVO implements Serializable {

    /**
     * 微信退款单号（新版API返回的字段）
     */
    @JsonProperty("id")
    private String id;

    /**
     * 微信退款单号（旧版API返回的字段）
     */
    @JsonProperty("refund_id")
    private String refundId;

    /**
     * 商户退款单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 微信支付订单号
     */
    @JsonProperty("transaction_id")
    private String transactionId;

    /**
     * 商户订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 退款渠道
     */
    @JsonProperty("channel")
    private String channel;

    /**
     * 退款入账账户
     */
    @JsonProperty("user_received_account")
    private String userReceivedAccount;

    /**
     * 退款成功时间
     */
    @JsonProperty("success_time")
    private String successTime;

    /**
     * 退款创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 退款状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 资金账户
     */
    @JsonProperty("funds_account")
    private String fundsAccount;

    /**
     * 金额信息
     */
    @JsonProperty("amount")
    private RefundAmount amount;

    /**
     * 优惠退款信息
     */
    @JsonProperty("promotion_detail")
    private Object promotionDetail;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RefundAmount {
        /**
         * 订单金额（分）
         */
        @JsonProperty("total")
        private Integer total;

        /**
         * 退款金额（分）
         */
        @JsonProperty("refund")
        private Integer refund;

        /**
         * 用户支付金额（分）
         */
        @JsonProperty("payer_total")
        private Integer payerTotal;

        /**
         * 用户退款金额（分）
         */
        @JsonProperty("payer_refund")
        private Integer payerRefund;

        /**
         * 应结订单金额（分）
         */
        @JsonProperty("settlement_total")
        private Integer settlementTotal;

        /**
         * 应结退款金额（分）
         */
        @JsonProperty("settlement_refund")
        private Integer settlementRefund;

        /**
         * 优惠退款金额（分）
         */
        @JsonProperty("discount_refund")
        private Integer discountRefund;

        /**
         * 退款币种
         */
        @JsonProperty("currency")
        private String currency;

        /**
         * 用户支付币种
         */
        @JsonProperty("payer_currency")
        private String payerCurrency;

        /**
         * 汇率信息
         */
        @JsonProperty("exchange_rate")
        private ExchangeRate exchangeRate;
    }

    /**
     * 汇率信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExchangeRate {
        /**
         * 汇率类型
         */
        @JsonProperty("type")
        private String type;

        /**
         * 汇率
         */
        @JsonProperty("rate")
        private Long rate;
    }
}
