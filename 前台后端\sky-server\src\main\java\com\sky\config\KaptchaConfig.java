package com.sky.config;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Properties;

@Configuration
@PropertySource("classpath:kaptcha.properties")  // 加载配置文件
public class KaptchaConfig {

    // 从配置文件中加载各项属性
    @Value("${kaptcha.image.width}")
    private String imageWidth;

    @Value("${kaptcha.image.height}")
    private String imageHeight;

    @Value("${kaptcha.textproducer.font.size}")
    private String fontSize;

    @Value("${kaptcha.textproducer.char.length}")
    private String charLength;

    @Value("${kaptcha.textproducer.font.color}")
    private String fontColor;

    @Value("${kaptcha.background.clear.from}")
    private String backgroundFrom;

    @Value("${kaptcha.background.clear.to}")
    private String backgroundTo;

    @Value("${kaptcha.textproducer.char.string}")
    private String charString;

    @Value("${kaptcha.textproducer.font.names}")
    private String fontNames;

    @Value("${kaptcha.textproducer.char.space}")
    private String charSpace;


    @Value("${kaptcha.noise.color}")
    private String noiseColor;

    @Value("${kaptcha.border}")
    private String border;

    @Value("${kaptcha.border.color}")
    private String borderColor;

    @Value("${kaptcha.border.thickness}")
    private String borderThickness;

    @Value("${kaptcha.textproducer.rotate}")
    private String rotate;

    @Value("${kaptcha.textproducer.rotate.min}")
    private String rotateMin;

    @Value("${kaptcha.textproducer.rotate.max}")
    private String rotateMax;

    @Value("${kaptcha.background.noise.impl}")
    private String backgroundNoiseImpl;

    @Value("${kaptcha.background.noise.color}")
    private String backgroundNoiseColor;

    // 创建验证码生成器
    @Bean
    public DefaultKaptcha captchaProducer() {
        Properties properties = new Properties();
        properties.put("kaptcha.image.width", imageWidth);
        properties.put("kaptcha.image.height", imageHeight);
        properties.put("kaptcha.textproducer.font.size", fontSize);
        properties.put("kaptcha.textproducer.char.length", charLength);
        properties.put("kaptcha.textproducer.font.color", fontColor);
        properties.put("kaptcha.background.clear.from", backgroundFrom);
        properties.put("kaptcha.background.clear.to", backgroundTo);
        properties.put("kaptcha.textproducer.char.string", charString);
        properties.put("kaptcha.textproducer.font.names", fontNames);
        properties.put("kaptcha.textproducer.char.space", charSpace);
        properties.put("kaptcha.noise.color", noiseColor);
        properties.put("kaptcha.border", border);
        properties.put("kaptcha.border.color", borderColor);
        properties.put("kaptcha.border.thickness", borderThickness);
        properties.put("kaptcha.textproducer.rotate", rotate);
        properties.put("kaptcha.textproducer.rotate.min", rotateMin);
        properties.put("kaptcha.textproducer.rotate.max", rotateMax);
        properties.put("kaptcha.background.noise.impl", backgroundNoiseImpl);
        properties.put("kaptcha.background.noise.color", backgroundNoiseColor);

        // 创建 Kaptcha 配置对象
        Config config = new Config(properties);

        // 创建并返回验证码生成器
        DefaultKaptcha captchaProducer = new DefaultKaptcha();
        captchaProducer.setConfig(config);
        return captchaProducer;
    }
}
