package com.sky.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信支付配置属性
 */
@Component
@ConfigurationProperties(prefix = "wechat.pay")
@Data
public class WechatPayProperties {

    /**
     * 商户号
     */
    private String mchid;

    /**
     * 商户API证书序列号
     */
    private String mchSerialNo;

    /**
     * 商户私钥文件路径
     */
    private String privateKeyPath;

    /**
     * APIv3密钥
     */
    private String apiV3Key;

    /**
     * APPID
     */
    private String appid;

    /**
     * 微信支付回调通知地址
     */
    private String notifyUrl;

    /**
     * 跨境支付相关配置
     */
    private CrossBorder crossBorder;

    @Data
    public static class CrossBorder {
        /**
         * 是否启用跨境支付
         */
        private boolean enabled;

        /**
         * 商户分类代码
         */
        private String merchantCategoryCode;

        /**
         * 交易类型
         */
        private String tradeType;

        /**
         * 货币类型
         */
        private String currency;

        /**
         * 查询订单API基础URL
         */
        private String queryApiBaseUrl;
    }
}
