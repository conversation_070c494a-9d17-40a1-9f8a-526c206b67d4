package com.sky.service;

import com.sky.entity.OrderLog;
import com.sky.entity.Orders;

import java.util.List;

/**
 * 订单日志服务接口
 */
public interface OrderLogService {
    
    /**
     * 记录订单操作日志
     */
    void recordLog(Long orderId, String orderNumber, String operation, 
                   Integer oldStatus, Integer newStatus, String operatorType, 
                   Long operatorId, String operatorName, String remark);
    
    /**
     * 记录订单创建日志
     */
    void recordCreateLog(Orders order, String operatorName);
    
    /**
     * 记录订单状态变更日志
     */
    void recordStatusChangeLog(Orders order, Integer oldStatus, String operation, String operatorName, String remark);
    
    /**
     * 根据订单ID查询日志
     */
    List<OrderLog> getLogsByOrderId(Long orderId);
    
    /**
     * 根据订单号查询日志
     */
    List<OrderLog> getLogsByOrderNumber(String orderNumber);
}
