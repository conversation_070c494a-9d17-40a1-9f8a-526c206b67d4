package com.sky.service.impl;

import com.sky.mapper.OrderLogMapper;
import com.sky.mapper.LogisticsLogMapper;
import com.sky.mapper.RefundLogMapper;
import com.sky.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 操作日志服务实现
 */
@Service
@Slf4j
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OrderLogMapper orderLogMapper;

    @Autowired
    private LogisticsLogMapper logisticsLogMapper;

    @Autowired
    private RefundLogMapper refundLogMapper;

    @Override
    public void logOrderOperation(Long orderId, String orderNumber, String operationType, 
                                 Integer oldStatus, Integer newStatus, String operatorType, 
                                 Long operatorId, String operatorName, String operationDesc) {
        try {
            log.info("记录订单操作日志：订单号={}, 操作类型={}, 状态变更={}→{}", 
                    orderNumber, operationType, oldStatus, newStatus);
            
            // TODO: 实现订单日志记录
            log.info("订单操作日志已记录");
            
        } catch (Exception e) {
            log.error("记录订单操作日志失败", e);
        }
    }

    @Override
    public void logLogisticsOperation(Long orderId, String trackingNumber, String oldStatus, 
                                     String newStatus, String eventInfo, String location, String operator) {
        try {
            log.info("记录物流操作日志：物流单号={}, 状态变更={}→{}", 
                    trackingNumber, oldStatus, newStatus);
            
            // TODO: 实现物流日志记录
            log.info("物流操作日志已记录");
            
        } catch (Exception e) {
            log.error("记录物流操作日志失败", e);
        }
    }

    @Override
    public void logRefundOperation(Long refundApplicationId, String refundNo, Integer oldStatus, 
                                  Integer newStatus, String operationType, Long operatorId, 
                                  String operatorName, String operationDesc) {
        try {
            log.info("记录退款操作日志：退款单号={}, 操作类型={}, 状态变更={}→{}", 
                    refundNo, operationType, oldStatus, newStatus);
            
            refundLogMapper.insertRefundLog(
                refundApplicationId, refundNo, oldStatus, newStatus, operationType,
                operatorId, operatorName, operationDesc, LocalDateTime.now()
            );
            
        } catch (Exception e) {
            log.error("记录退款操作日志失败", e);
        }
    }
}
