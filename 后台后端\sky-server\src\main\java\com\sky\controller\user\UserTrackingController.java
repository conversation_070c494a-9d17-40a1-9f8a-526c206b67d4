package com.sky.controller.user;

import com.sky.entity.CarrierInfo;
import com.sky.result.Result;
import com.sky.service.CarrierInfoService;
import com.sky.service.Track17Service;
import com.sky.vo.TrackingDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户端物流查询控制器
 */
@RestController
@RequestMapping("/user/tracking")
@Api(tags = "用户端物流查询")
@Slf4j
public class UserTrackingController {

    @Autowired
    private Track17Service track17Service;

    @Autowired
    private CarrierInfoService carrierInfoService;

    @GetMapping("/detail")
    @ApiOperation("查询物流详情")
    public Result<TrackingDetailVO> getTrackingDetail(
            @ApiParam(value = "物流单号", required = true) @RequestParam String trackingNumber,
            @ApiParam(value = "运输商代码", required = true) @RequestParam Integer carrierCode) {
        log.info("用户查询物流详情: {}, 运输商: {}", trackingNumber, carrierCode);
        TrackingDetailVO detail = track17Service.getTrackingDetail(trackingNumber, carrierCode);
        if (detail == null) {
            return Result.error("未找到物流信息");
        }
        return Result.success(detail);
    }

    @GetMapping("/order/{orderId}")
    @ApiOperation("根据订单ID查询物流信息")
    public Result<List<TrackingDetailVO>> getTrackingByOrderId(
            @ApiParam(value = "订单ID", required = true) @PathVariable Long orderId) {
        log.info("用户根据订单ID查询物流信息: {}", orderId);
        List<TrackingDetailVO> details = track17Service.getTrackingByOrderId(orderId);
        return Result.success(details);
    }

    @GetMapping("/carriers")
    @ApiOperation("获取常用运输商列表")
    public Result<List<CarrierInfo>> getCommonCarriers() {
        log.info("获取常用运输商列表");
        List<CarrierInfo> carriers = carrierInfoService.getAllActiveCarriers();
        return Result.success(carriers);
    }

    @GetMapping("/carriers/search")
    @ApiOperation("搜索运输商")
    public Result<List<CarrierInfo>> searchCarriers(
            @ApiParam(value = "国家代码") @RequestParam(required = false) String countryCode) {
        log.info("搜索运输商，国家代码: {}", countryCode);
        List<CarrierInfo> carriers = carrierInfoService.getByCountryCode(countryCode);
        return Result.success(carriers);
    }
}
