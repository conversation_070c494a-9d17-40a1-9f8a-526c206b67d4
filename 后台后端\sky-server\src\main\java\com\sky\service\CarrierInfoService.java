package com.sky.service;

import com.sky.entity.CarrierInfo;

import java.util.List;

/**
 * 运输商信息服务接口
 */
public interface CarrierInfoService {

    /**
     * 根据运输商代码获取运输商信息
     * @param carrierCode 运输商代码
     * @return 运输商信息
     */
    CarrierInfo getByCarrierCode(Integer carrierCode);

    /**
     * 获取所有启用的运输商
     * @return 运输商列表
     */
    List<CarrierInfo> getAllActiveCarriers();

    /**
     * 根据国家代码获取运输商
     * @param countryCode 国家代码
     * @return 运输商列表
     */
    List<CarrierInfo> getByCountryCode(String countryCode);

    /**
     * 验证运输商代码是否有效
     * @param carrierCode 运输商代码
     * @return 是否有效
     */
    boolean isValidCarrierCode(Integer carrierCode);

    /**
     * 验证运输商代码是否有效（字符串类型）
     * @param carrierCode 运输商代码字符串
     * @return 是否有效
     */
    boolean isValidCarrierCode(String carrierCode);

    /**
     * 将字符串运输商代码转换为17TRACK的数字代码
     * @param carrierCode 字符串运输商代码
     * @return 对应的数字代码，如果不存在则返回null
     */
    Integer getCarrierNumericCode(String carrierCode);

    /**
     * 同步运输商信息（从17TRACK API）
     * @return 同步数量
     */
    int syncCarrierInfo();
}
