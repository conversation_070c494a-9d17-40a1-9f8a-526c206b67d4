<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SellerPaymentAccountMapper">

    <resultMap id="BaseResultMap" type="com.sky.entity.SellerPaymentAccount">
        <id column="id" property="id"/>
        <result column="seller_id" property="sellerId"/>
        <result column="account_type" property="accountType"/>
        <result column="account_name" property="accountName"/>
        <result column="account_number" property="accountNumber"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_code" property="bankCode"/>
        <result column="branch_name" property="branchName"/>
        <result column="platform_name" property="platformName"/>
        <result column="platform_account" property="platformAccount"/>
        <result column="is_default" property="isDefault"/>
        <result column="account_status" property="accountStatus"/>
        <result column="verification_status" property="verificationStatus"/>
        <result column="id_card_number" property="idCardNumber"/>
        <result column="phone" property="phone"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="PaymentAccountVOResultMap" type="com.sky.vo.PaymentAccountVO">
        <id column="id" property="id"/>
        <result column="seller_id" property="sellerId"/>
        <result column="seller_name" property="sellerName"/>
        <result column="account_type" property="accountType"/>
        <result column="account_type_desc" property="accountTypeDesc"/>
        <result column="account_name" property="accountName"/>
        <result column="account_number" property="accountNumber"/>
        <result column="full_account_number" property="fullAccountNumber"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_code" property="bankCode"/>
        <result column="branch_name" property="branchName"/>
        <result column="platform_name" property="platformName"/>
        <result column="platform_account" property="platformAccount"/>
        <result column="is_default" property="isDefault"/>
        <result column="is_default_desc" property="isDefaultDesc"/>
        <result column="account_status" property="accountStatus"/>
        <result column="account_status_desc" property="accountStatusDesc"/>
        <result column="verification_status" property="verificationStatus"/>
        <result column="verification_status_desc" property="verificationStatusDesc"/>
        <result column="id_card_number" property="idCardNumber"/>
        <result column="phone" property="phone"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.sky.entity.SellerPaymentAccount" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO seller_payment_account (
            seller_id, account_type, account_name, account_number, bank_name, bank_code,
            branch_name, platform_name, platform_account, is_default, account_status,
            verification_status, id_card_number, phone, remark, create_time, update_time
        ) VALUES (
            #{sellerId}, #{accountType}, #{accountName}, #{accountNumber}, #{bankName}, #{bankCode},
            #{branchName}, #{platformName}, #{platformAccount}, #{isDefault}, #{accountStatus},
            #{verificationStatus}, #{idCardNumber}, #{phone}, #{remark}, #{createTime}, #{updateTime}
        )
    </insert>

    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        SELECT * FROM seller_payment_account WHERE id = #{id}
    </select>

    <select id="selectByIdAndSellerId" resultMap="BaseResultMap">
        SELECT * FROM seller_payment_account WHERE id = #{id} AND seller_id = #{sellerId}
    </select>

    <update id="update" parameterType="com.sky.entity.SellerPaymentAccount">
        UPDATE seller_payment_account
        SET account_type = #{accountType},
            account_name = #{accountName},
            account_number = #{accountNumber},
            bank_name = #{bankName},
            bank_code = #{bankCode},
            branch_name = #{branchName},
            platform_name = #{platformName},
            platform_account = #{platformAccount},
            is_default = #{isDefault},
            account_status = #{accountStatus},
            verification_status = #{verificationStatus},
            id_card_number = #{idCardNumber},
            phone = #{phone},
            remark = #{remark},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="long">
        DELETE FROM seller_payment_account WHERE id = #{id}
    </delete>

    <delete id="deleteByIdAndSellerId">
        DELETE FROM seller_payment_account WHERE id = #{id} AND seller_id = #{sellerId}
    </delete>

    <select id="selectPage" parameterType="com.sky.dto.PaymentAccountQueryDTO" resultMap="PaymentAccountVOResultMap">
        SELECT
            spa.id,
            spa.seller_id,
            s.account_name as seller_name,
            spa.account_type,
            CASE spa.account_type
                WHEN 1 THEN '银行卡'
                WHEN 2 THEN '支付宝'
                WHEN 3 THEN '微信'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END as account_type_desc,
            spa.account_name,
            CONCAT(LEFT(spa.account_number, 4), '****', RIGHT(spa.account_number, 4)) as account_number,
            spa.account_number as full_account_number,
            spa.bank_name,
            spa.bank_code,
            spa.branch_name,
            spa.platform_name,
            spa.platform_account,
            spa.is_default,
            CASE spa.is_default
                WHEN 0 THEN '否'
                WHEN 1 THEN '是'
                ELSE '未知'
            END as is_default_desc,
            spa.account_status,
            CASE spa.account_status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as account_status_desc,
            spa.verification_status,
            CASE spa.verification_status
                WHEN 0 THEN '未验证'
                WHEN 1 THEN '已验证'
                ELSE '未知'
            END as verification_status_desc,
            CASE
                WHEN spa.id_card_number IS NOT NULL AND LENGTH(spa.id_card_number) >= 8
                THEN CONCAT(LEFT(spa.id_card_number, 4), '****', RIGHT(spa.id_card_number, 4))
                ELSE spa.id_card_number
            END as id_card_number,
            CASE
                WHEN spa.phone IS NOT NULL AND LENGTH(spa.phone) >= 7
                THEN CONCAT(LEFT(spa.phone, 3), '****', RIGHT(spa.phone, 4))
                ELSE spa.phone
            END as phone,
            spa.remark,
            spa.create_time,
            spa.update_time
        FROM seller_payment_account spa
        LEFT JOIN seller s ON spa.seller_id = s.id
        <where>
            <if test="sellerId != null">
                AND spa.seller_id = #{sellerId}
            </if>
            <if test="accountType != null">
                AND spa.account_type = #{accountType}
            </if>
            <if test="accountName != null and accountName != ''">
                AND spa.account_name LIKE CONCAT('%', #{accountName}, '%')
            </if>
            <if test="accountStatus != null">
                AND spa.account_status = #{accountStatus}
            </if>
            <if test="verificationStatus != null">
                AND spa.verification_status = #{verificationStatus}
            </if>
            <if test="isDefault != null">
                AND spa.is_default = #{isDefault}
            </if>
        </where>
        ORDER BY spa.is_default DESC, spa.create_time DESC
    </select>

    <select id="selectBySellerId" parameterType="long" resultMap="PaymentAccountVOResultMap">
        SELECT
            spa.id,
            spa.seller_id,
            s.account_name as seller_name,
            spa.account_type,
            CASE spa.account_type
                WHEN 1 THEN '银行卡'
                WHEN 2 THEN '支付宝'
                WHEN 3 THEN '微信'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END as account_type_desc,
            spa.account_name,
            CONCAT(LEFT(spa.account_number, 4), '****', RIGHT(spa.account_number, 4)) as account_number,
            spa.account_number as full_account_number,
            spa.bank_name,
            spa.bank_code,
            spa.branch_name,
            spa.platform_name,
            spa.platform_account,
            spa.is_default,
            CASE spa.is_default
                WHEN 0 THEN '否'
                WHEN 1 THEN '是'
                ELSE '未知'
            END as is_default_desc,
            spa.account_status,
            CASE spa.account_status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as account_status_desc,
            spa.verification_status,
            CASE spa.verification_status
                WHEN 0 THEN '未验证'
                WHEN 1 THEN '已验证'
                ELSE '未知'
            END as verification_status_desc,
            CASE
                WHEN spa.id_card_number IS NOT NULL AND LENGTH(spa.id_card_number) >= 8
                THEN CONCAT(LEFT(spa.id_card_number, 4), '****', RIGHT(spa.id_card_number, 4))
                ELSE spa.id_card_number
            END as id_card_number,
            CASE
                WHEN spa.phone IS NOT NULL AND LENGTH(spa.phone) >= 7
                THEN CONCAT(LEFT(spa.phone, 3), '****', RIGHT(spa.phone, 4))
                ELSE spa.phone
            END as phone,
            spa.remark,
            spa.create_time,
            spa.update_time
        FROM seller_payment_account spa
        LEFT JOIN seller s ON spa.seller_id = s.id
        WHERE spa.seller_id = #{sellerId}
        ORDER BY spa.is_default DESC, spa.create_time DESC
    </select>

    <select id="selectDefaultBySellerId" parameterType="long" resultMap="PaymentAccountVOResultMap">
        SELECT
            spa.id,
            spa.seller_id,
            s.account_name as seller_name,
            spa.account_type,
            CASE spa.account_type
                WHEN 1 THEN '银行卡'
                WHEN 2 THEN '支付宝'
                WHEN 3 THEN '微信'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END as account_type_desc,
            spa.account_name,
            CONCAT(LEFT(spa.account_number, 4), '****', RIGHT(spa.account_number, 4)) as account_number,
            spa.account_number as full_account_number,
            spa.bank_name,
            spa.bank_code,
            spa.branch_name,
            spa.platform_name,
            spa.platform_account,
            spa.is_default,
            CASE spa.is_default
                WHEN 0 THEN '否'
                WHEN 1 THEN '是'
                ELSE '未知'
            END as is_default_desc,
            spa.account_status,
            CASE spa.account_status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as account_status_desc,
            spa.verification_status,
            CASE spa.verification_status
                WHEN 0 THEN '未验证'
                WHEN 1 THEN '已验证'
                ELSE '未知'
            END as verification_status_desc,
            CASE
                WHEN spa.id_card_number IS NOT NULL AND LENGTH(spa.id_card_number) >= 8
                THEN CONCAT(LEFT(spa.id_card_number, 4), '****', RIGHT(spa.id_card_number, 4))
                ELSE spa.id_card_number
            END as id_card_number,
            CASE
                WHEN spa.phone IS NOT NULL AND LENGTH(spa.phone) >= 7
                THEN CONCAT(LEFT(spa.phone, 3), '****', RIGHT(spa.phone, 4))
                ELSE spa.phone
            END as phone,
            spa.remark,
            spa.create_time,
            spa.update_time
        FROM seller_payment_account spa
        LEFT JOIN seller s ON spa.seller_id = s.id
        WHERE spa.seller_id = #{sellerId} AND spa.is_default = 1 AND spa.account_status = 1
        LIMIT 1
    </select>

    <update id="clearDefaultBySellerId" parameterType="long">
        UPDATE seller_payment_account
        SET is_default = 0, update_time = NOW()
        WHERE seller_id = #{sellerId}
    </update>

    <update id="setDefault">
        UPDATE seller_payment_account
        SET is_default = 1, update_time = NOW()
        WHERE id = #{id} AND seller_id = #{sellerId}
    </update>

    <update id="updateStatus">
        UPDATE seller_payment_account
        SET account_status = #{accountStatus}, update_time = NOW()
        WHERE id = #{id} AND seller_id = #{sellerId}
    </update>

    <update id="updateVerificationStatus">
        UPDATE seller_payment_account
        SET verification_status = #{verificationStatus}, update_time = NOW()
        WHERE id = #{id} AND seller_id = #{sellerId}
    </update>

    <select id="countByAccountNumber" resultType="int">
        SELECT COUNT(*)
        FROM seller_payment_account
        WHERE seller_id = #{sellerId}
          AND account_number = #{accountNumber}
          AND account_type = #{accountType}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="countBySellerId" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM seller_payment_account WHERE seller_id = #{sellerId}
    </select>

    <select id="countVerifiedBySellerId" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM seller_payment_account
        WHERE seller_id = #{sellerId} AND verification_status = 1
    </select>

</mapper>
