package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款申请VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundApplicationVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 退款申请ID
     */
    private Long id;

    /**
     * 退款申请单号
     */
    private String refundNo;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 申请人ID
     */
    private Long buyerId;

    /**
     * 申请人姓名
     */
    private String buyerName;

    /**
     * 申请退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款理由
     */
    private String refundReason;

    /**
     * 退款类型：1-仅退款，2-退货退款
     */
    private Integer refundType;

    /**
     * 退款类型描述
     */
    private String refundTypeDesc;

    /**
     * 申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败
     */
    private Integer applicationStatus;

    /**
     * 申请状态描述
     */
    private String applicationStatusDesc;

    /**
     * 是否需要审核：0-不需要，1-需要
     */
    private Integer needApproval;

    /**
     * 审核状态：1-待审核，2-审核通过，3-审核拒绝
     */
    private Integer approvalStatus;

    /**
     * 审核状态描述
     */
    private String approvalStatusDesc;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 审核时间
     */
    private LocalDateTime approvalTime;

    /**
     * 审核备注
     */
    private String approvalRemark;

    /**
     * 退款方式：1-原路退回，2-余额退款
     */
    private Integer refundMethod;

    /**
     * 退款方式描述
     */
    private String refundMethodDesc;

    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;

    /**
     * 退款完成时间
     */
    private LocalDateTime refundTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 订单信息
     */
    private OrderVO orderInfo;

    /**
     * 审核记录列表
     */
    private List<RefundApprovalRecordVO> approvalRecords;

    /**
     * 获取退款方式描述
     */
    public String getRefundMethodDesc() {
        if (refundMethod == null) {
            return "未设置";
        }
        switch (refundMethod) {
            case 1: return "原路退回";
            case 2: return "余额退款";
            default: return "未知方式";
        }
    }

    /**
     * 判断是否可以取消
     */
    public boolean canCancel() {
        return applicationStatus != null && 
               (applicationStatus == 1 || applicationStatus == 2);
    }

    /**
     * 判断是否需要审核
     */
    public boolean isNeedApproval() {
        return needApproval != null && needApproval == 1;
    }

    /**
     * 判断是否已完成退款
     */
    public boolean isRefundCompleted() {
        return applicationStatus != null && applicationStatus == 6;
    }
}
