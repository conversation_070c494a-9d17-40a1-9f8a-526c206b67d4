package com.sky.service.impl;

import com.sky.dto.WechatRefundQueryDTO;
import com.sky.entity.RefundApplication;
import com.sky.mapper.RefundApplicationMapper;
import com.sky.service.RefundApplicationService;
import com.sky.service.RefundStatusService;
import com.sky.service.WechatRefundService;
import com.sky.vo.RefundApplicationVO;
import com.sky.vo.WechatRefundQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款状态查询服务实现类
 */
@Service
@Slf4j
public class RefundStatusServiceImpl implements RefundStatusService {

    @Autowired
    private RefundApplicationMapper refundApplicationMapper;

    @Autowired
    private RefundApplicationService refundApplicationService;

    @Autowired
    private WechatRefundService wechatRefundService;

    /**
     * 查询退款状态并同步微信退款状态
     */
    @Override
    public RefundApplicationVO queryAndSyncRefundStatus(String refundNo) {
        log.info("查询并同步退款状态，refundNo: {}", refundNo);

        // 1. 查询本地退款申请
        RefundApplicationVO refundApplication = refundApplicationService.getRefundApplicationByNo(refundNo);
        if (refundApplication == null) {
            log.warn("退款申请不存在，refundNo: {}", refundNo);
            return null;
        }

        // 2. 如果退款状态为退款中，尝试同步微信退款状态
        if (refundApplication.getApplicationStatus() != null && 
            refundApplication.getApplicationStatus().equals(RefundApplication.STATUS_REFUNDING)) {
            syncWechatRefundStatus(refundNo);
            // 重新查询最新状态
            refundApplication = refundApplicationService.getRefundApplicationByNo(refundNo);
        }

        return refundApplication;
    }

    /**
     * 根据退款申请ID查询退款状态
     */
    @Override
    public RefundApplicationVO queryRefundStatus(Long refundApplicationId) {
        log.info("查询退款状态，refundApplicationId: {}", refundApplicationId);
        return refundApplicationService.getRefundApplicationById(refundApplicationId);
    }

    /**
     * 同步微信退款状态
     */
    @Override
    @Transactional
    public boolean syncWechatRefundStatus(String refundNo) {
        log.info("同步微信退款状态，refundNo: {}", refundNo);

        try {
            // 1. 查询微信退款状态
            WechatRefundQueryVO wechatRefundQuery = queryWechatRefundDetail(refundNo);
            if (wechatRefundQuery == null) {
                log.warn("微信退款记录不存在，refundNo: {}", refundNo);
                return false;
            }

            // 2. 根据微信退款状态更新本地状态
            Integer localStatus = convertWechatStatusToLocal(wechatRefundQuery.getStatus());
            BigDecimal actualRefundAmount = null;

            if ("SUCCESS".equals(wechatRefundQuery.getStatus())) {
                // 退款成功，计算实际退款金额（从分转换为元）
                if (wechatRefundQuery.getAmount() != null && wechatRefundQuery.getAmount().getRefund() != null) {
                    actualRefundAmount = new BigDecimal(wechatRefundQuery.getAmount().getRefund()).divide(new BigDecimal(100));
                }
            }

            // 3. 更新本地退款状态
            return updateLocalRefundStatus(refundNo, localStatus, actualRefundAmount);

        } catch (Exception e) {
            log.error("同步微信退款状态失败，refundNo: {}", refundNo, e);
            return false;
        }
    }

    /**
     * 处理微信退款回调通知
     */
    @Override
    @Transactional
    public boolean handleWechatRefundNotify(String refundNo, String wechatRefundStatus, Integer actualRefundAmount) {
        log.info("处理微信退款回调通知，refundNo: {}, wechatRefundStatus: {}, actualRefundAmount: {}", 
                refundNo, wechatRefundStatus, actualRefundAmount);

        try {
            // 1. 转换微信状态为本地状态
            Integer localStatus = convertWechatStatusToLocal(wechatRefundStatus);

            // 2. 计算实际退款金额（从分转换为元）
            BigDecimal refundAmount = null;
            if (actualRefundAmount != null && actualRefundAmount > 0) {
                refundAmount = new BigDecimal(actualRefundAmount).divide(new BigDecimal(100));
            }

            // 3. 更新本地退款状态
            return updateLocalRefundStatus(refundNo, localStatus, refundAmount);

        } catch (Exception e) {
            log.error("处理微信退款回调通知失败，refundNo: {}", refundNo, e);
            return false;
        }
    }

    /**
     * 批量同步退款状态
     */
    @Override
    public int batchSyncRefundStatus() {
        log.info("开始批量同步退款状态");

        int successCount = 0;
        try {
            // 查询所有退款中的申请
            List<RefundApplication> refundingApplications = refundApplicationMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<RefundApplication>()
                            .eq(RefundApplication::getApplicationStatus, RefundApplication.STATUS_REFUNDING)
            );

            for (RefundApplication application : refundingApplications) {
                try {
                    if (syncWechatRefundStatus(application.getRefundNo())) {
                        successCount++;
                    }
                    // 避免频繁调用微信接口
                    Thread.sleep(100);
                } catch (Exception e) {
                    log.error("同步退款状态失败，refundNo: {}", application.getRefundNo(), e);
                }
            }

            log.info("批量同步退款状态完成，总数: {}, 成功: {}", refundingApplications.size(), successCount);

        } catch (Exception e) {
            log.error("批量同步退款状态异常", e);
        }

        return successCount;
    }

    /**
     * 查询微信退款详情
     */
    @Override
    public WechatRefundQueryVO queryWechatRefundDetail(String refundNo) {
        log.info("查询微信退款详情，refundNo: {}", refundNo);

        try {
            WechatRefundQueryDTO queryDTO = WechatRefundQueryDTO.builder()
                    .outRefundNo(refundNo)
                    .build();

            return wechatRefundService.queryRefund(queryDTO);

        } catch (Exception e) {
            log.error("查询微信退款详情失败，refundNo: {}", refundNo, e);
            return null;
        }
    }

    /**
     * 更新本地退款状态
     */
    @Override
    @Transactional
    public boolean updateLocalRefundStatus(String refundNo, Integer applicationStatus, BigDecimal actualRefundAmount) {
        log.info("更新本地退款状态，refundNo: {}, applicationStatus: {}, actualRefundAmount: {}", 
                refundNo, applicationStatus, actualRefundAmount);

        try {
            return refundApplicationService.updateRefundStatus(refundNo, applicationStatus, actualRefundAmount);
        } catch (Exception e) {
            log.error("更新本地退款状态失败，refundNo: {}", refundNo, e);
            return false;
        }
    }

    /**
     * 将微信退款状态转换为本地状态
     */
    private Integer convertWechatStatusToLocal(String wechatStatus) {
        if (wechatStatus == null) {
            return RefundApplication.STATUS_REFUND_FAILED;
        }

        switch (wechatStatus) {
            case "SUCCESS":
                return RefundApplication.STATUS_REFUND_SUCCESS;
            case "REFUNDCLOSE":
            case "ABNORMAL":
                return RefundApplication.STATUS_REFUND_FAILED;
            case "PROCESSING":
            default:
                return RefundApplication.STATUS_REFUNDING;
        }
    }
}
