<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sky.mapper.PmsProductMapper">

    <select id="getProductsBySellerId" resultType="com.sky.entity.PmsProduct">
        SELECT * FROM ry_mall.pms_product WHERE creator_user_id = #{sellerId}
    </select>

    <select id="getAllProducts" resultType="com.sky.entity.PmsProduct">
        SELECT * FROM ry_mall.pms_product
    </select>

    <insert id="addProduct" parameterType="com.sky.entity.PmsProduct">
        INSERT INTO ry_mall.pms_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="outProductId != null">out_product_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="creatorUserId != null">creator_user_id,</if>
            <if test="name != null">name,</if>
            <if test="pic != null">pic,</if>
            <if test="albumPics != null">album_pics,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="sort != null">sort,</if>
            <if test="price != null">price,</if>
            <if test="unit != null">unit,</if>
            <if test="weight != null">weight,</if>
            <if test="detailHtml != null">detail_html,</if>
            <if test="detailMobileHtml != null">detail_mobile_html,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="productCategoryName != null">product_category_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="productAttr != null">product_attr,</if>
            <if test="status != null">status</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="outProductId != null">#{outProductId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="creatorUserId != null">#{creatorUserId},</if>
            <if test="name != null">#{name},</if>
            <if test="pic != null">#{pic},</if>
            <if test="albumPics != null">#{albumPics},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="sort != null">#{sort},</if>
            <if test="price != null">#{price},</if>
            <if test="unit != null">#{unit},</if>
            <if test="weight != null">#{weight},</if>
            <if test="detailHtml != null">#{detailHtml},</if>
            <if test="detailMobileHtml != null">#{detailMobileHtml},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="productCategoryName != null">#{productCategoryName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="productAttr != null">#{productAttr},</if>
            <if test="status != null">#{status}</if>
        </trim>
    </insert>

    <delete id="deleteProduct" parameterType="java.lang.Long">
        DELETE FROM ry_mall.pms_product WHERE id = #{productId}
    </delete>

    <update id="updateProduct" parameterType="com.sky.entity.PmsProduct">
        UPDATE ry_mall.pms_product
        <set>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="pic != null">pic = #{pic},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="creatorUserId != null">creator_user_id = #{creatorUserId},</if>
            <if test="outProductId != null">out_product_id = #{outProductId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="albumPics != null">album_pics = #{albumPics},</if>
            <if test="detailHtml != null">detail_html = #{detailHtml},</if>
            <if test="inventory != null">inventory = #{inventory},</if>
            <if test="detailMobileHtml != null">detail_mobile_html = #{detailMobileHtml},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="productCategoryName != null">product_category_name = #{productCategoryName},</if>
            <if test="productAttr != null">product_attr=#{productAttr},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getProductById" resultType="com.sky.entity.PmsProduct">
        SELECT * FROM ry_mall.pms_product WHERE id = #{productId}
    </select>

    <select id="isProductCodeExists" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM ry_mall.pms_product WHERE out_product_id = #{productCode}
    </select>

</mapper>