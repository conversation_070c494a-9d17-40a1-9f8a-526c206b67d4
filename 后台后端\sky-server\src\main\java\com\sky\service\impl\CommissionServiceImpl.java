package com.sky.service.impl;

import com.sky.entity.CommissionSettings;
import com.sky.entity.TeamLeader;
import com.sky.entity.CommissionRecord;
import com.sky.mapper.CommissionMapper;
import com.sky.vo.PageResult;
import com.sky.service.CommissionService;
import com.sky.vo.CommissionStats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class CommissionServiceImpl implements CommissionService {

    @Autowired
    private CommissionMapper commissionMapper;

    @Override
    public CommissionSettings getSettings() {
        return commissionMapper.getSettings();
    }

    @Override
    public void saveSettings(CommissionSettings settings) {
        commissionMapper.saveSettings(settings);
    }

    @Override
    public PageResult<TeamLeader> getLeaders(Integer page, Integer pageSize, String keyword, String status, String sortBy, String sortOrder) {
        PageResult<TeamLeader> pageResult = new PageResult<>();
        Long total = commissionMapper.countLeaders(keyword, status);
        List<TeamLeader> list = commissionMapper.getLeaders((page - 1) * pageSize, pageSize, keyword, status, sortBy, sortOrder);
        pageResult.setTotal(total);
        pageResult.setList(list);
        return pageResult;
    }

    @Override
    public TeamLeader getLeaderDetail(Long id) {
        return commissionMapper.getLeaderDetail(id);
    }

    @Override
    public TeamLeader addLeader(TeamLeader leader) {
        commissionMapper.addLeader(leader);
        return leader;
    }

    @Override
    public void updateLeader(Long id, TeamLeader leader) {
        leader.setId(id);
        commissionMapper.updateLeader(leader);
    }

    @Override
    public void deleteLeader(Long id) {
        commissionMapper.deleteLeader(id);
    }

    @Override
    public void updateLeaderStatus(Long id, String status, String reason) {
        commissionMapper.updateLeaderStatus(id, status, reason);
    }

    @Override
    public CommissionStats getStats(String period, String startDate, String endDate) {
        return commissionMapper.getStats(period, startDate, endDate);
    }

    @Override
    public PageResult<CommissionRecord> getRecords(Integer page, Integer pageSize, Long leaderId, String status, String orderType, String dateRange, String startDate, String endDate) {
        PageResult<CommissionRecord> pageResult = new PageResult<>();
        Long total = commissionMapper.countRecords(leaderId, status, orderType, dateRange, startDate, endDate);
        List<CommissionRecord> list = commissionMapper.getRecords((page - 1) * pageSize, pageSize, leaderId, status, orderType, dateRange, startDate, endDate);
        pageResult.setTotal(total);
        pageResult.setList(list);
        return pageResult;
    }
} 