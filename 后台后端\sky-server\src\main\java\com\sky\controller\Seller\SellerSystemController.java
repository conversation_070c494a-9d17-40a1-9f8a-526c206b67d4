package com.sky.controller.Seller;

import com.sky.dto.CategoryPageQueryDTO;
import com.sky.dto.MessageNotificationsDTO;
import com.sky.dto.SystemProgramsDTO;
import com.sky.entity.MessageNotifications;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.MoneyService;
import com.sky.service.SellerSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/system")
@Api(tags = "卖家系统相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class SellerSystemController {

    @Autowired
    private SellerSystemService sellerSystemService;

    /**
     * 消息通知分类分页查询
     * @param messageNotificationsDTO
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("分类分页查询")
    public Result<PageResult> page(MessageNotificationsDTO messageNotificationsDTO){
        log.info("分页查询：{}", messageNotificationsDTO);
        PageResult pageResult = sellerSystemService.pageQuery(messageNotificationsDTO);
        return Result.success(pageResult);
    }

    /**
     * 消息状态查询
     * @param status
     * @return
     */
    @GetMapping("/status")
    @ApiOperation("消息状态查询")
    public Result<MessageNotifications> status(@RequestParam String status){
        return Result.success(sellerSystemService.getByStatus(status));
    }

    /**
     * 应用市场分类分页查询
     * @param systemProgramsDTO
     * @return
     */
    @GetMapping("/pages")
    @ApiOperation("分类分页查询")
    public Result<PageResult> SysPage(SystemProgramsDTO systemProgramsDTO){
        log.info("分页查询：{}", systemProgramsDTO);
        PageResult pageResult = sellerSystemService.pageSystemQuery(systemProgramsDTO);
        return Result.success(pageResult);
    }


}
