package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sky.entity.Permission;
import com.sky.entity.Seller;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AdminMapper extends BaseMapper <Seller>{

    @Select("-- 通过卖家ID查询对应的权限\n" +
            "SELECT DISTINCT\n" +
            "    p.perm_code, p.perm_name, p.perm_type, p.path, p.method, p.icon\n" +
            "FROM\n" +
            "    ry_mall.seller s\n" +
            "        JOIN ry_mall.seller_role sr ON s.id = sr.seller_id\n" +
            "        JOIN ry_mall.sys_role r ON sr.role_id = r.id\n" +
            "        JOIN ry_mall.sys_role_permission rp ON r.id = rp.role_id\n" +
            "        JOIN ry_mall.sys_permission p ON rp.permission_id = p.id\n" +
            "WHERE\n" +
            "    s.id = #{id} ")
//            "ORDER BY\n" +
//            "    p.order_num;
    List<Permission> findSellerBySellerId(Long id);
}
