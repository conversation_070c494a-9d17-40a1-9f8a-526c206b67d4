package com.sky.service;

import com.sky.entity.CommissionSettings;
import com.sky.entity.TeamLeader;
import com.sky.entity.CommissionRecord;
import com.sky.vo.PageResult;
import com.sky.vo.CommissionStats;

public interface CommissionService {
    CommissionSettings getSettings();
    void saveSettings(CommissionSettings settings);
    PageResult<TeamLeader> getLeaders(Integer page, Integer pageSize, String keyword, String status, String sortBy, String sortOrder);
    TeamLeader getLeaderDetail(Long id);
    TeamLeader addLeader(TeamLeader leader);
    void updateLeader(Long id, TeamLeader leader);
    void deleteLeader(Long id);
    void updateLeaderStatus(Long id, String status, String reason);
    CommissionStats getStats(String period, String startDate, String endDate);
    PageResult<CommissionRecord> getRecords(Integer page, Integer pageSize, Long leaderId, String status, String orderType, String dateRange, String startDate, String endDate);
} 