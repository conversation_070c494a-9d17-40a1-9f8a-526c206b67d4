package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单状态变更日志实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatusLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 原状态
     */
    private Integer oldStatus;

    /**
     * 新状态
     */
    private Integer newStatus;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 操作者类型：user-用户，admin-管理员，system-系统
     */
    private String operatorType;

    /**
     * 操作者ID
     */
    private Long operatorId;

    /**
     * 操作者姓名
     */
    private String operatorName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 获取状态变更描述
     */
    public String getStatusChangeDesc() {
        return String.format("从 %s 变更为 %s", 
                getStatusName(oldStatus), getStatusName(newStatus));
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "待付款";
            case 2: return "待发货";
            case 3: return "待收货";
            case 4: return "已完成";
            case 5: return "已取消";
            case 6: return "退款中";
            case 7: return "已退款";
            default: return "未知状态";
        }
    }
}
