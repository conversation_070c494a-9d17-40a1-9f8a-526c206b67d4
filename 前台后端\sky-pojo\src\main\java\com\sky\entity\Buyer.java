package com.sky.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Buyer implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String accountName;

    private String password;

    private String gender;

    private String photoUrl;

    private String phone;

    private String invitationCode;

    private String email;
    //用户状态，1启用  0停用
    private Integer accountStatus;

    private LocalDateTime createTime;

    private LocalDateTime lastLoginTime;
}
