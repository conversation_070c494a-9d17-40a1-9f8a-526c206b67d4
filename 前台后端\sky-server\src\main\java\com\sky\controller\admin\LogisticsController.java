package com.sky.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.sky.entity.Logistics;
import com.sky.mapper.LogisticsMapper;
import com.sky.result.Result;
import com.sky.vo.LogisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 物流控制器
 */
@RestController
@RequestMapping("/logistics")
@Api(tags = "物流相关接口")
@Slf4j
public class LogisticsController {

    @Autowired
    private LogisticsMapper logisticsMapper;

    /**
     * 根据订单ID查询物流信息
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation("根据订单ID查询物流信息")
    public Result<LogisticsVO> getByOrderId(@PathVariable("orderId") Long orderId) {
        log.info("根据订单ID查询物流信息：orderId={}", orderId);
        LambdaQueryWrapper<Logistics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Logistics::getOrderId, orderId);
        Logistics logistics = logisticsMapper.selectOne(queryWrapper);
        
        if (logistics == null) {
            return Result.error("物流信息不存在");
        }
        
        LogisticsVO logisticsVO = LogisticsVO.builder().build();
        BeanUtils.copyProperties(logistics, logisticsVO);
        
        // 设置物流状态描述
        String statusDesc = "";
        switch (logistics.getLogisticsStatus()) {
            case "shipped":
                statusDesc = "已发货";
                break;
            case "in_transit":
                statusDesc = "运输中";
                break;
            case "delivered":
                statusDesc = "已送达";
                break;
            default:
                statusDesc = "未知状态";
        }
        logisticsVO.setLogisticsStatusDesc(statusDesc);
        
        return Result.success(logisticsVO);
    }

    /**
     * 更新物流状态
     */
    @PutMapping("/{id}/status")
    @ApiOperation("更新物流状态")
    public Result<Boolean> updateStatus(
            @PathVariable("id") Long id,
            @RequestParam("status") String status) {
        log.info("更新物流状态：id={}, status={}", id, status);
        int result = logisticsMapper.updateLogisticsStatus(id, status);
        return Result.success(result > 0);
    }
} 