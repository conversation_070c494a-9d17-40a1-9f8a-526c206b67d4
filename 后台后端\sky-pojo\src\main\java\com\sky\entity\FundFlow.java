package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FundFlow implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long sellerId;
//操作时间
    private LocalDateTime operationTime;

//操作类型
    private String operationType;

//流水号
    private String serialNumber;

//关联订单号
    private String orderNumber;

//资金类型
    private String fundType;

//订单金额
    private BigDecimal orderAmountPercent;

//平台佣金
    private BigDecimal platformFundPercent;

//资金变动
    private BigDecimal fundFlowPercent;

//余额
    private BigDecimal balancePercent;

    private LocalDateTime createdTime;
}