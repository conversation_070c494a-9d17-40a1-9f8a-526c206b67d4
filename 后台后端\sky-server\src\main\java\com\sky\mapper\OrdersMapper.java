package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.OrderPageQueryDTO;
import com.sky.entity.Orders;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface OrdersMapper {

    /**
     * 创建订单
     */
    @Insert("INSERT INTO orders (number, buyer_id, address_id, shipping_method_id, status, amount, order_time, " +
            "pay_time, checkout_time, pay_method, payment_transaction_id, order_remark, create_time, update_time) " +
            "VALUES (#{number}, #{buyerId}, #{addressId}, #{shippingMethodId}, #{status}, #{amount}, #{orderTime}, " +
            "#{payTime}, #{checkoutTime}, #{payMethod}, #{paymentTransactionId}, #{orderRemark}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void createOrder(Orders order);

    /**
     * 根据ID获取订单
     */
    @Select("SELECT * FROM orders WHERE id = #{id}")
    Orders findById(Long id);

    /**
     * 根据订单号获取订单
     */
    @Select("SELECT * FROM orders WHERE number = #{number}")
    Orders getByNumber(String number);

    /**
     * 查询历史订单
     */
    @Select("SELECT * FROM orders WHERE buyer_id = #{buyerId} ORDER BY order_time DESC")
    List<Orders> findHistoryByBuyerId(Long buyerId);

    /**
     * 分页查询订单
     */
    Page<Orders> pageQuery(OrderPageQueryDTO orderPageQueryDTO);

    /**
     * 更新订单状态
     */
    @Update("UPDATE orders SET status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    void updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新支付信息
     */
    @Update("UPDATE orders SET pay_time = #{payTime}, payment_transaction_id = #{paymentTransactionId}, " +
            "status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    void updatePayment(@Param("id") Long id, @Param("payTime") LocalDateTime payTime,
                      @Param("paymentTransactionId") String paymentTransactionId,
                      @Param("status") Integer status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 发货
     */
    @Update("UPDATE orders SET status = #{status}, ship_time = #{shipTime}, update_time = #{updateTime} WHERE id = #{id}")
    void shipOrder(@Param("id") Long id, @Param("status") Integer status,
                   @Param("shipTime") LocalDateTime shipTime, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 发货（包含物流信息）
     */
    @Update("UPDATE orders SET status = #{status}, ship_time = #{shipTime}, update_time = #{updateTime}, " +
            "tracking_number = #{trackingNumber}, courier_code = #{courierCode}, courier_name = #{courierName} " +
            "WHERE id = #{id}")
    void shipOrderWithTracking(@Param("id") Long id, @Param("status") Integer status,
                              @Param("shipTime") LocalDateTime shipTime, @Param("updateTime") LocalDateTime updateTime,
                              @Param("trackingNumber") String trackingNumber, @Param("courierCode") String courierCode,
                              @Param("courierName") String courierName);

    /**
     * 完成订单
     */
    @Update("UPDATE orders SET status = #{status}, complete_time = #{completeTime}, update_time = #{updateTime} WHERE id = #{id}")
    void completeOrder(@Param("id") Long id, @Param("status") Integer status,
                       @Param("completeTime") LocalDateTime completeTime, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 取消订单
     */
    @Update("UPDATE orders SET status = #{status}, cancel_time = #{cancelTime}, cancel_reason = #{cancelReason}, " +
            "update_time = #{updateTime} WHERE id = #{id}")
    void cancelOrder(@Param("id") Long id, @Param("status") Integer status,
                     @Param("cancelTime") LocalDateTime cancelTime, @Param("cancelReason") String cancelReason,
                     @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据买家ID查询订单
     */
    @Select("SELECT * FROM orders WHERE buyer_id = #{buyerId}")
    List<Orders> getByBuyerId(Long buyerId);

    /**
     * 批量更新订单状态
     */
    void batchUpdateStatus(@Param("orderIds") List<Long> orderIds,
                          @Param("status") Integer status,
                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量发货
     */
    void batchShipOrders(@Param("orderIds") List<Long> orderIds,
                        @Param("status") Integer status,
                        @Param("shipTime") LocalDateTime shipTime,
                        @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量取消订单
     */
    void batchCancelOrders(@Param("orderIds") List<Long> orderIds,
                          @Param("status") Integer status,
                          @Param("cancelTime") LocalDateTime cancelTime,
                          @Param("cancelReason") String cancelReason,
                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据ID列表查询订单
     */
    @Select("<script>" +
            "SELECT * FROM orders WHERE id IN " +
            "<foreach collection='orderIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<Orders> getByIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 统计订单数量
     */
    @Select("SELECT COUNT(*) FROM orders WHERE status = #{status}")
    Integer countByStatus(Integer status);

    /**
     * 统计指定日期范围内的订单数量
     */
    @Select("SELECT COUNT(*) FROM orders WHERE status = #{status} AND order_time BETWEEN #{beginTime} AND #{endTime}")
    Integer countByStatusAndDate(@Param("status") Integer status,
                                @Param("beginTime") LocalDateTime beginTime,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 统计订单数量（按状态分组）
     */
    @Select("SELECT status, COUNT(*) as count FROM orders GROUP BY status")
    List<Map<String, Object>> countGroupByStatus();

    /**
     * 统计指定日期范围内的订单数量（按状态分组）
     */
    @Select("SELECT status, COUNT(*) as count FROM orders WHERE order_time BETWEEN #{beginTime} AND #{endTime} GROUP BY status")
    List<Map<String, Object>> countGroupByStatusAndDate(@Param("beginTime") LocalDateTime beginTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计销售额
     */
    @Select("SELECT SUM(amount) FROM orders WHERE status IN (#{status1}, #{status2})")
    Double sumAmountByStatus(@Param("status1") Integer status1, @Param("status2") Integer status2);

    /**
     * 统计指定日期范围内的销售额
     */
    @Select("SELECT SUM(amount) FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime}")
    Double sumAmountByStatusAndDate(@Param("status1") Integer status1,
                                   @Param("status2") Integer status2,
                                   @Param("beginTime") LocalDateTime beginTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每日销售额
     */
    @Select("SELECT DATE_FORMAT(order_time, '%Y-%m-%d') as date, SUM(amount) as amount " +
            "FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY DATE_FORMAT(order_time, '%Y-%m-%d') ORDER BY date")
    List<Map<String, Object>> sumDailyAmount(@Param("status1") Integer status1,
                                           @Param("status2") Integer status2,
                                           @Param("beginTime") LocalDateTime beginTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每月销售额
     */
    @Select("SELECT DATE_FORMAT(order_time, '%Y-%m') as month, SUM(amount) as amount " +
            "FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY DATE_FORMAT(order_time, '%Y-%m') ORDER BY month")
    List<Map<String, Object>> sumMonthlyAmount(@Param("status1") Integer status1,
                                             @Param("status2") Integer status2,
                                             @Param("beginTime") LocalDateTime beginTime,
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每周销售额
     */
    @Select("SELECT YEARWEEK(order_time) as week, SUM(amount) as amount, COUNT(*) as orderCount " +
            "FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY YEARWEEK(order_time) ORDER BY week")
    List<Map<String, Object>> sumWeeklyAmount(@Param("status1") Integer status1,
                                            @Param("status2") Integer status2,
                                            @Param("beginTime") LocalDateTime beginTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每小时销售额
     */
    @Select("SELECT HOUR(order_time) as hour, SUM(amount) as amount, COUNT(*) as orderCount " +
            "FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY HOUR(order_time) ORDER BY hour")
    List<Map<String, Object>> sumHourlyAmount(@Param("status1") Integer status1,
                                            @Param("status2") Integer status2,
                                            @Param("beginTime") LocalDateTime beginTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计支付方式分布
     */
    @Select("SELECT pay_method, COUNT(*) as orderCount, SUM(amount) as totalAmount " +
            "FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY pay_method")
    List<Map<String, Object>> countByPaymentMethod(@Param("status1") Integer status1,
                                                 @Param("status2") Integer status2,
                                                 @Param("beginTime") LocalDateTime beginTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 统计平均订单金额
     */
    @Select("SELECT AVG(amount) FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime}")
    Double getAvgOrderAmount(@Param("status1") Integer status1,
                           @Param("status2") Integer status2,
                           @Param("beginTime") LocalDateTime beginTime,
                           @Param("endTime") LocalDateTime endTime);

    /**
     * 统计新老用户订单分布
     */
    @Select("SELECT " +
            "CASE WHEN buyer_first_order.buyer_id IS NULL THEN 'old' ELSE 'new' END as userType, " +
            "COUNT(*) as orderCount, SUM(o.amount) as totalAmount " +
            "FROM orders o " +
            "LEFT JOIN (SELECT buyer_id, MIN(order_time) as first_order_time FROM orders GROUP BY buyer_id) buyer_first_order " +
            "ON o.buyer_id = buyer_first_order.buyer_id AND DATE(o.order_time) = DATE(buyer_first_order.first_order_time) " +
            "WHERE o.status IN (#{status1}, #{status2}) AND o.order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY CASE WHEN buyer_first_order.buyer_id IS NULL THEN 'old' ELSE 'new' END")
    List<Map<String, Object>> countNewOldUserOrders(@Param("status1") Integer status1,
                                                   @Param("status2") Integer status2,
                                                   @Param("beginTime") LocalDateTime beginTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户购买频次分布
     */
    @Select("SELECT frequency_range, COUNT(*) as userCount FROM (" +
            "SELECT buyer_id, " +
            "CASE " +
            "WHEN order_count = 1 THEN '1次' " +
            "WHEN order_count BETWEEN 2 AND 3 THEN '2-3次' " +
            "WHEN order_count BETWEEN 4 AND 10 THEN '4-10次' " +
            "ELSE '10次以上' " +
            "END as frequency_range " +
            "FROM (SELECT buyer_id, COUNT(*) as order_count FROM orders " +
            "WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY buyer_id) user_orders" +
            ") frequency_stats GROUP BY frequency_range")
    List<Map<String, Object>> getUserOrderFrequency(@Param("status1") Integer status1,
                                                   @Param("status2") Integer status2,
                                                   @Param("beginTime") LocalDateTime beginTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 获取热销商品排行（需要关联订单详情表）
     */
    @Select("SELECT od.product_name, SUM(od.quantity) as totalQuantity, SUM(od.subtotal) as totalAmount, COUNT(DISTINCT o.id) as orderCount " +
            "FROM orders o JOIN order_detail od ON o.id = od.order_id " +
            "WHERE o.status IN (#{status1}, #{status2}) AND o.order_time BETWEEN #{beginTime} AND #{endTime} " +
            "GROUP BY od.product_id, od.product_name " +
            "ORDER BY totalQuantity DESC LIMIT #{limit}")
    List<Map<String, Object>> getTopProducts(@Param("status1") Integer status1,
                                           @Param("status2") Integer status2,
                                           @Param("beginTime") LocalDateTime beginTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("limit") Integer limit);

    /**
     * 统计订单总数（不限状态）
     */
    @Select("SELECT COUNT(*) FROM orders WHERE order_time BETWEEN #{beginTime} AND #{endTime}")
    Integer countAllOrdersByDate(@Param("beginTime") LocalDateTime beginTime,
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 统计有效订单数（已完成+已发货）
     */
    @Select("SELECT COUNT(*) FROM orders WHERE status IN (#{status1}, #{status2}) AND order_time BETWEEN #{beginTime} AND #{endTime}")
    Integer countValidOrdersByDate(@Param("status1") Integer status1,
                                 @Param("status2") Integer status2,
                                 @Param("beginTime") LocalDateTime beginTime,
                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 获取实时统计数据（最近1小时）
     */
    @Select("SELECT " +
            "COUNT(*) as totalOrders, " +
            "SUM(CASE WHEN status IN (#{completedStatus}, #{shippedStatus}) THEN amount ELSE 0 END) as totalSales, " +
            "COUNT(CASE WHEN status = #{pendingStatus} THEN 1 END) as pendingOrders, " +
            "COUNT(CASE WHEN status IN (#{completedStatus}, #{shippedStatus}) THEN 1 END) as completedOrders " +
            "FROM orders WHERE order_time >= #{beginTime}")
    Map<String, Object> getRealtimeStats(@Param("completedStatus") Integer completedStatus,
                                       @Param("shippedStatus") Integer shippedStatus,
                                       @Param("pendingStatus") Integer pendingStatus,
                                       @Param("beginTime") LocalDateTime beginTime);

    /**
     * 更新订单退款状态
     */
    @Update("UPDATE orders SET refund_status = #{refundStatus}, " +
            "refund_amount = #{refundAmount}, " +
            "refund_time = #{refundTime}, " +
            "update_time = #{updateTime} WHERE id = #{id}")
    int updateRefundStatus(@Param("id") Long id,
                          @Param("refundStatus") Integer refundStatus,
                          @Param("refundAmount") java.math.BigDecimal refundAmount,
                          @Param("refundTime") LocalDateTime refundTime,
                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据ID查询订单（包含退款信息）
     */
    @Select("SELECT * FROM orders WHERE id = #{id}")
    Orders selectById(@Param("id") Long id);

    /**
     * 查找超时未支付的订单
     */
    @Select("SELECT * FROM orders WHERE status = 1 AND create_time < #{timeoutTime}")
    List<Orders> findTimeoutPendingPaymentOrders(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 查找长时间已发货但未确认收货的订单
     */
    @Select("SELECT * FROM orders WHERE status = 4 AND ship_time < #{autoConfirmTime}")
    List<Orders> findLongTimeShippedOrders(@Param("autoConfirmTime") LocalDateTime autoConfirmTime);

    /**
     * 更新订单取消信息
     */
    @Update("UPDATE orders SET cancel_reason = #{cancelReason}, cancel_time = #{cancelTime}, " +
            "update_time = #{updateTime} WHERE id = #{orderId}")
    void updateCancelInfo(@Param("orderId") Long orderId,
                         @Param("cancelReason") String cancelReason,
                         @Param("cancelTime") LocalDateTime cancelTime,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新订单完成时间
     */
    @Update("UPDATE orders SET complete_time = #{completeTime}, update_time = #{updateTime} WHERE id = #{orderId}")
    void updateCompleteTime(@Param("orderId") Long orderId,
                           @Param("completeTime") LocalDateTime completeTime,
                           @Param("updateTime") LocalDateTime updateTime);
}
