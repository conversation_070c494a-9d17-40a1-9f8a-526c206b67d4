package com.sky.mapper;

import com.sky.entity.Seller;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface SellerMapper {

    /**
     * 查询所有商家信息
     * @return 商家列表
     */
    @Select("SELECT * FROM seller")
    List<Seller> findAll();

    /**
     * 根据 ID 查询商家信息
     * @param id 商家 ID
     * @return 商家信息
     */
    @Select("SELECT * FROM seller WHERE id = #{id}")
    Seller findById(Long id);

    /**
     * 更新商家信息
     * @param seller 商家信息
     */

    void update(Seller seller);


    /**
     * 根据 ID 删除商家信息
     * @param id 商家 ID
     */
    @Delete("DELETE FROM seller WHERE id = #{id}")
    void deleteById(Integer id);
}