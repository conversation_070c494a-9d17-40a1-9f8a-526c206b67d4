package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单物流跟踪实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderLogisticsTracking implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 运输商代码
     */
    private String carrierCode;

    /**
     * 运输商名称
     */
    private String carrierName;

    /**
     * 当前物流状态
     */
    private String currentStatus;

    /**
     * 当前状态描述
     */
    private String currentStatusDesc;

    /**
     * 最新位置
     */
    private String latestLocation;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime actualDeliveryTime;

    /**
     * 发货时间
     */
    private LocalDateTime shipTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
