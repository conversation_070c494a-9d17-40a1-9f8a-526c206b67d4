{"groups": [{"name": "track17", "type": "com.sky.config.Track17Config", "sourceType": "com.sky.config.Track17Config"}, {"name": "track17.api", "type": "com.sky.config.Track17Config$Api", "sourceType": "com.sky.config.Track17Config", "sourceMethod": "getApi()"}, {"name": "track17.business", "type": "com.sky.config.Track17Config$Business", "sourceType": "com.sky.config.Track17Config", "sourceMethod": "getBusiness()"}, {"name": "track17.webhook", "type": "com.sky.config.Track17Config$Webhook", "sourceType": "com.sky.config.Track17Config", "sourceMethod": "getWebhook()"}, {"name": "wechat.pay", "type": "com.sky.properties.WechatPayProperties", "sourceType": "com.sky.properties.WechatPayProperties"}, {"name": "wechat.pay.cross-border", "type": "com.sky.properties.WechatPayProperties$CrossBorder", "sourceType": "com.sky.properties.WechatPayProperties", "sourceMethod": "getCrossBorder()"}], "properties": [{"name": "track17.api.base-url", "type": "java.lang.String", "description": "API基础URL", "sourceType": "com.sky.config.Track17Config$Api", "defaultValue": "https://api.17track.net/track/v2.2"}, {"name": "track17.api.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "com.sky.config.Track17Config$Api", "defaultValue": 10000}, {"name": "track17.api.max-retry-count", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "com.sky.config.Track17Config$Api", "defaultValue": 3}, {"name": "track17.api.rate-limit", "type": "java.lang.Integer", "description": "请求频率限制（请求/秒）", "sourceType": "com.sky.config.Track17Config$Api", "defaultValue": 3}, {"name": "track17.api.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（毫秒）", "sourceType": "com.sky.config.Track17Config$Api", "defaultValue": 30000}, {"name": "track17.api.token", "type": "java.lang.String", "description": "API密钥", "sourceType": "com.sky.config.Track17Config$Api"}, {"name": "track17.business.auto-detection", "type": "java.lang.Bo<PERSON>an", "description": "是否启用运输商自动检测", "sourceType": "com.sky.config.Track17Config$Business", "defaultValue": true}, {"name": "track17.business.auto-register", "type": "java.lang.Bo<PERSON>an", "description": "是否自动注册物流单号", "sourceType": "com.sky.config.Track17Config$Business", "defaultValue": true}, {"name": "track17.business.default-carrier-code", "type": "java.lang.Integer", "description": "默认运输商代码", "sourceType": "com.sky.config.Track17Config$Business", "defaultValue": 3011}, {"name": "track17.business.default-lang", "type": "java.lang.String", "description": "默认翻译语言", "sourceType": "com.sky.config.Track17Config$Business", "defaultValue": "zh-hans"}, {"name": "track17.business.enable-email-notification", "type": "java.lang.Bo<PERSON>an", "description": "是否启用邮件通知", "sourceType": "com.sky.config.Track17Config$Business", "defaultValue": false}, {"name": "track17.business.enable-translation", "type": "java.lang.Bo<PERSON>an", "description": "是否启用翻译", "sourceType": "com.sky.config.Track17Config$Business", "defaultValue": false}, {"name": "track17.business.sync-interval", "type": "java.lang.Integer", "description": "物流状态同步间隔（小时）", "sourceType": "com.sky.config.Track17Config$Business", "defaultValue": 6}, {"name": "track17.webhook.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Webhook", "sourceType": "com.sky.config.Track17Config$Webhook", "defaultValue": true}, {"name": "track17.webhook.max-retry-count", "type": "java.lang.Integer", "description": "Webhook重试次数", "sourceType": "com.sky.config.Track17Config$Webhook", "defaultValue": 3}, {"name": "track17.webhook.secret", "type": "java.lang.String", "description": "Webhook密钥", "sourceType": "com.sky.config.Track17Config$Webhook"}, {"name": "track17.webhook.url", "type": "java.lang.String", "description": "Webhook接收地址", "sourceType": "com.sky.config.Track17Config$Webhook"}, {"name": "wechat.pay.api-v3-key", "type": "java.lang.String", "description": "APIv3密钥", "sourceType": "com.sky.properties.WechatPayProperties"}, {"name": "wechat.pay.appid", "type": "java.lang.String", "description": "APPID", "sourceType": "com.sky.properties.WechatPayProperties"}, {"name": "wechat.pay.cross-border.currency", "type": "java.lang.String", "description": "货币类型", "sourceType": "com.sky.properties.WechatPayProperties$CrossBorder"}, {"name": "wechat.pay.cross-border.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用跨境支付", "sourceType": "com.sky.properties.WechatPayProperties$CrossBorder", "defaultValue": false}, {"name": "wechat.pay.cross-border.merchant-category-code", "type": "java.lang.String", "description": "商户分类代码", "sourceType": "com.sky.properties.WechatPayProperties$CrossBorder"}, {"name": "wechat.pay.cross-border.query-api-base-url", "type": "java.lang.String", "description": "查询订单API基础URL", "sourceType": "com.sky.properties.WechatPayProperties$CrossBorder"}, {"name": "wechat.pay.cross-border.trade-type", "type": "java.lang.String", "description": "交易类型", "sourceType": "com.sky.properties.WechatPayProperties$CrossBorder"}, {"name": "wechat.pay.mch-serial-no", "type": "java.lang.String", "description": "商户API证书序列号", "sourceType": "com.sky.properties.WechatPayProperties"}, {"name": "wechat.pay.mchid", "type": "java.lang.String", "description": "商户号", "sourceType": "com.sky.properties.WechatPayProperties"}, {"name": "wechat.pay.notify-url", "type": "java.lang.String", "description": "微信支付回调通知地址", "sourceType": "com.sky.properties.WechatPayProperties"}, {"name": "wechat.pay.private-key-path", "type": "java.lang.String", "description": "商户私钥文件路径", "sourceType": "com.sky.properties.WechatPayProperties"}], "hints": []}