package com.sky.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.sky.dto.CategoryCreateDTO;
import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.dto.CategoryQueryDTO;
import com.sky.entity.BusinessException;
import com.sky.entity.Category;
import com.sky.result.PageResult;
import com.sky.vo.CategoryTreeVO;
import com.sky.vo.CategoryVO;
import com.sky.vo.ListCategoryVO;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * 分类服务接口
 */
public interface CategoryService extends IService<Category> {
    // 创建分类（包含层级校验）
    Long createCategory(CategoryCreateDTO dto) throws BusinessException;

    // 获取分类树（内存构建）
    List<CategoryTreeVO> getFullCategoryTree() throws JsonProcessingException;

    // 级联删除
    void deleteCategoryWithSubtree(Long id) throws BusinessException;

    CategoryTreeVO getCategoryTree(@Min(1) Long rootId);
}