package com.sky.mapper;

import com.sky.entity.OrderAddressInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 订单地址信息Mapper
 */
@Mapper
public interface OrderAddressInfoMapper {

    /**
     * 插入订单地址信息
     */
    @Insert("INSERT INTO order_address_info (order_id, address_type, consignee_name, phone, " +
            "province_code, province_name, city_code, city_name, district_code, district_name, " +
            "detail_address, postal_code, is_default, create_time, update_time) VALUES " +
            "(#{orderId}, #{addressType}, #{consigneeName}, #{phone}, " +
            "#{provinceCode}, #{provinceName}, #{cityCode}, #{cityName}, #{districtCode}, #{districtName}, " +
            "#{detailAddress}, #{postalCode}, #{isDefault}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(OrderAddressInfo addressInfo);

    /**
     * 根据订单ID和地址类型查询地址信息
     */
    @Select("SELECT * FROM order_address_info WHERE order_id = #{orderId} AND address_type = #{addressType}")
    OrderAddressInfo getByOrderIdAndType(@Param("orderId") Long orderId, @Param("addressType") Integer addressType);

    /**
     * 根据订单ID查询所有地址信息
     */
    @Select("SELECT * FROM order_address_info WHERE order_id = #{orderId}")
    List<OrderAddressInfo> getByOrderId(Long orderId);
}
