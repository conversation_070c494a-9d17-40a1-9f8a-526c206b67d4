package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 发货请求DTO
 */
@Data
@ApiModel(description = "发货请求数据传输对象")
public class ShipOrderDTO {

    @NotNull(message = "订单ID不能为空")
    @ApiModelProperty(value = "订单ID", required = true)
    private Long orderId;

    @NotBlank(message = "物流单号不能为空")
    @ApiModelProperty(value = "物流单号", required = true, example = "1234567890")
    private String trackingNumber;

    @NotBlank(message = "快递公司代码不能为空")
    @ApiModelProperty(value = "快递公司代码", required = true, example = "fedex")
    private String courierCode;

    @ApiModelProperty(value = "快递公司名称", example = "FedEx")
    private String courierName;

    @ApiModelProperty(value = "发货备注")
    private String shipNote;
}
