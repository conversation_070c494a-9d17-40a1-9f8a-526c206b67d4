//package com.sky.controller.admin;
//
//
//import com.sky.Utils.RandomNumberGenerator;
//import com.sky.dto.BuyerDTO;
//import com.sky.dto.MsmDTO;
//import com.sky.dto.SendCodeDTO;
//import com.sky.result.Result;
//import com.sky.service.MsmService;
//import io.netty.util.internal.ObjectUtil;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.poi.util.StringUtil;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.util.ObjectUtils;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.HashMap;
//import java.util.concurrent.TimeUnit;
//
//@RestController
//@RequestMapping("/buyer")
//@Api(tags = "发送验证码相关接口")
//@CrossOrigin()
//@Slf4j
//public class MsmController {
//
//    @Autowired
//    private MsmService msmService;
//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//
//    @PostMapping("/register/sendcode")
//    @ApiOperation("用户注册发送验证码")
//    public Result sendMessage(@RequestBody SendCodeDTO sendCodeDTO) {
//        String phone = sendCodeDTO.getPhone();
//        String code = stringRedisTemplate.opsForValue().get(phone);
//        if(!ObjectUtils.isEmpty(code)){
//            return Result.success("验证码发送成功");
//        }
//        //自己生成一个验证码
//        code = RandomNumberGenerator.RandomNumber();
//        //调用发送短信的服务
//        HashMap<String, Object> param = new HashMap<>();
//        param.put("code", code);
//        Boolean sentMessage = msmService.sendMessage(phone);
//        //存入redis
//        if(sentMessage){
//            stringRedisTemplate.opsForValue().set(phone,code,5, TimeUnit.MINUTES);
//            return Result.success("验证码发送成功");
//        }
//        return Result.error("短信发送失败");
//
//    }
//}
