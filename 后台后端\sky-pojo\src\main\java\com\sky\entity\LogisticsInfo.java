package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 物流信息
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer id;
    private String logisticsNumber;
    private Integer orderId;
    private String logisticsCompany;
    private String logisticsStatus;
    private LocalDateTime createTime;
    private LocalDateTime shippingDate;
}
