package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.MessageNotificationsDTO;
import com.sky.dto.SystemProgramsDTO;
import com.sky.entity.MessageNotifications;
import com.sky.entity.SystemPrograms;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SellerSystemMapper {

    Page<MessageNotifications> pageQuery(MessageNotificationsDTO messageNotificationsDTO);

    @Select("select seller_id from message_notifications where id = #{id}")
    int getById(Long id);

    @Select("select * from message_notifications where seller_id = #{sellerId} and status = #{status}")
    MessageNotifications getByStatus(int sellerId, String status);

    Page<SystemPrograms> pageSystemQuery(SystemProgramsDTO systemProgramsDTO);
}
