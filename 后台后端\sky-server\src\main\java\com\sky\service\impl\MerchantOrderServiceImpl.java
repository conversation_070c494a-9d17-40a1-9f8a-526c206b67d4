package com.sky.service.impl;

import com.sky.entity.MerchantOrder;
import com.sky.mapper.MerchantOrderMapper;
import com.sky.service.MerchantOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MerchantOrderServiceImpl implements MerchantOrderService {
    @Autowired
    private MerchantOrderMapper merchantOrderMapper;

    @Override
    public List<MerchantOrder> findAll() {
        return merchantOrderMapper.findAll();
    }

    @Override
    public List<MerchantOrder> findByStatus(Integer status) {
        return merchantOrderMapper.findByStatus(status);
    }

    @Override
    public List<MerchantOrder> findByOrderNo(String orderNo) {
        return merchantOrderMapper.findByOrderNo(orderNo);
    }

    @Override
    public List<MerchantOrder> findByTransactionNo(String transactionNo) {
        return merchantOrderMapper.findByTransactionNo(transactionNo);
    }

    @Override
    public List<MerchantOrder> findByTrackingNo(String trackingNo) {
        return merchantOrderMapper.findByTrackingNo(trackingNo);
    }
}