package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.PaymentRecordPageQueryDTO;
import com.sky.entity.PaymentRecord;
import com.sky.vo.PaymentRecordVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface PaymentRecordMapper {

    /**
     * 根据id查询支付记录
     * @param id
     * @return
     */
    @Select("select * from payment_record where id = #{id}")
    PaymentRecord getById(Long id);

    /**
     * 支付记录分页查询
     * @param paymentRecordPageQueryDTO
     * @return
     */
    Page<PaymentRecordVO> pageQuery(PaymentRecordPageQueryDTO paymentRecordPageQueryDTO);

    /**
     * 插入支付记录
     * @param paymentRecord 支付记录对象
     */
    @Insert("INSERT INTO payment_record (order_id, payment_method, amount, transaction_status, transaction_time, create_time, update_time) VALUES (#{orderId}, #{paymentMethod}, #{amount}, #{transactionStatus}, #{transactionTime}, #{createTime}, #{updateTime})")
    void insert(com.sky.entity.PaymentRecord paymentRecord);

}
