package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 物流跟踪配额VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "物流跟踪配额VO")
public class TrackingQuotaVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总配额")
    private Integer totalQuota;

    @ApiModelProperty("已使用配额")
    private Integer usedQuota;

    @ApiModelProperty("剩余配额")
    private Integer remainingQuota;

    @ApiModelProperty("配额重置时间")
    private String resetTime;

    @ApiModelProperty("配额类型")
    private String quotaType;

    @ApiModelProperty("配额周期")
    private String quotaPeriod;
}
