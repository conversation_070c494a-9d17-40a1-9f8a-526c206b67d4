package com.sky.service;

import com.sky.vo.OrderStatisticsVO;

import java.time.LocalDate;
import java.util.Map;

/**
 * 订单统计服务接口
 */
public interface OrderStatisticsService {

    /**
     * 获取订单统计数据
     */
    OrderStatisticsVO getOrderStatistics();

    /**
     * 获取指定日期范围的订单统计
     */
    OrderStatisticsVO getOrderStatistics(LocalDate beginDate, LocalDate endDate);

    /**
     * 获取今日订单统计
     */
    Map<String, Object> getTodayStatistics();

    /**
     * 获取本月订单统计
     */
    Map<String, Object> getMonthStatistics();

    /**
     * 获取订单状态分布
     */
    Map<String, Object> getStatusDistribution();

    /**
     * 获取增强版订单统计数据（包含所有维度）
     */
    OrderStatisticsVO getEnhancedOrderStatistics(LocalDate beginDate, LocalDate endDate);

    /**
     * 获取时间维度统计（今日、昨日、本周、上周、本月、上月）
     */
    Map<String, Object> getTimeDimensionStatistics();

    /**
     * 获取支付方式统计
     */
    Map<String, Object> getPaymentMethodStatistics(LocalDate beginDate, LocalDate endDate);

    /**
     * 获取商品维度统计
     */
    Map<String, Object> getProductStatistics(LocalDate beginDate, LocalDate endDate, Integer topLimit);

    /**
     * 获取用户维度统计
     */
    Map<String, Object> getUserStatistics(LocalDate beginDate, LocalDate endDate);

    /**
     * 获取转化率统计
     */
    Map<String, Object> getConversionRateStatistics(LocalDate beginDate, LocalDate endDate);

    /**
     * 获取实时统计数据
     */
    Map<String, Object> getRealtimeStatistics();

    /**
     * 获取同比环比数据
     */
    Map<String, Object> getComparisonStatistics(LocalDate beginDate, LocalDate endDate);

    /**
     * 获取趋势统计数据
     */
    Map<String, Object> getTrendStatistics(LocalDate beginDate, LocalDate endDate, String granularity);
}
