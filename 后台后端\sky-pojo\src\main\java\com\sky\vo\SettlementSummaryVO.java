package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 回款汇总VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementSummaryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 总订单数
     */
    private Integer totalOrders;

    /**
     * 总订单金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 总回款金额
     */
    private BigDecimal totalSettlementAmount;

    /**
     * 未到期订单数
     */
    private Integer notDueOrders;

    /**
     * 未到期金额
     */
    private BigDecimal notDueAmount;

    /**
     * 待回款订单数
     */
    private Integer pendingOrders;

    /**
     * 待回款金额
     */
    private BigDecimal pendingAmount;

    /**
     * 已回款订单数
     */
    private Integer completedOrders;

    /**
     * 已回款金额
     */
    private BigDecimal completedAmount;
}
