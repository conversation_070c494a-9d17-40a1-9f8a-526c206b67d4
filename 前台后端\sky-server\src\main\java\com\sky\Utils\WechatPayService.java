package com.sky.Utils;

import com.sky.dto.WechatRefundRequestDTO;
import com.sky.dto.WechatRefundQueryDTO;
import com.sky.dto.WechatRefundListQueryDTO;
import com.sky.vo.WechatRefundResponseVO;
import com.sky.vo.WechatRefundQueryVO;
import com.sky.vo.WechatRefundListQueryVO;
import com.sky.properties.WechatPayProperties;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.*;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Signature;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 微信境外支付服务
 */
@Slf4j
@Service
public class WechatPayService {

    @Autowired
    private WechatPayProperties wechatPayProperties;
    
    // 用于缓存的成员变量
    private String merchantId;
    private String privateKeyPath;
    private String merchantSerialNumber;
    private String apiV3Key;
    private String notifyUrl;
    private String appId;
    
    // 常量
    private static final int MAX_OUT_TRADE_NO_LENGTH = 32;
    private static final String CURRENCY = "USD";
    private static final String API_URL = "https://apihk.mch.weixin.qq.com/v3/global/transactions/native";
    private static final String REFUND_API_URL = "https://apihk.mch.weixin.qq.com/v3/global/refunds";
    private static final String TRADE_TYPE = "NATIVE";
    private static final String MERCHANT_CATEGORY_CODE = "4111";
    private static final String DEFAULT_OPENID = "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"; // 默认openid，实际应从授权获取

    // 退款相关常量
    private static final int MAX_OUT_REFUND_NO_LENGTH = 64;
    private static final String DEFAULT_REFUND_SOURCE = "REFUND_SOURCE_UNSETTLED_FUNDS";

    // JSON解析器
    private final ObjectMapper objectMapper = new ObjectMapper();

    private NativePayService nativePayService;
    private Config wechatPayConfig;

    @PostConstruct
    public void init() {
        try {
            log.info("初始化微信境外支付服务...");
            
            // 从属性中获取配置
            this.merchantId = wechatPayProperties.getMchid();
            this.merchantSerialNumber = wechatPayProperties.getMchSerialNo();
            this.apiV3Key = wechatPayProperties.getApiV3Key();
            this.notifyUrl = wechatPayProperties.getNotifyUrl();
            this.appId = wechatPayProperties.getAppid();
            this.privateKeyPath = "cert/" + wechatPayProperties.getPrivateKeyPath();
            
            log.info("商户号: {}", merchantId);
            log.info("证书序列号: {}", merchantSerialNumber);
            log.info("私钥路径: {}", privateKeyPath);
            log.info("通知地址: {}", notifyUrl);
            log.info("APIv3密钥: {}", apiV3Key != null && !apiV3Key.isEmpty() ? "已设置" : "未设置");

            // 验证必要参数
            if (merchantId == null || merchantId.trim().isEmpty()) {
                throw new IllegalArgumentException("商户号不能为空");
            }
            if (merchantSerialNumber == null || merchantSerialNumber.trim().isEmpty()) {
                throw new IllegalArgumentException("证书序列号不能为空");
            }
            if (apiV3Key == null || apiV3Key.trim().isEmpty()) {
                throw new IllegalArgumentException("APIv3密钥不能为空");
            }

            // 从classpath加载私钥内容
            String privateKey;
            try {
                ClassPathResource resource = new ClassPathResource(privateKeyPath);
                privateKey = FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));
                log.info("成功加载私钥文件，长度: {} 字符", privateKey.length());

                // 验证私钥格式
                if (!privateKey.contains("-----BEGIN PRIVATE KEY-----") || !privateKey.contains("-----END PRIVATE KEY-----")) {
                    throw new IllegalArgumentException("私钥文件格式不正确，应该是PKCS#8格式的PEM文件");
                }

                // 测试私钥是否可以正常加载
                loadPrivateKeyFromString(privateKey);
                log.info("私钥验证成功");

            } catch (Exception e) {
                log.error("无法加载或验证私钥文件: {}", e.getMessage(), e);
                throw new RuntimeException("无法加载或验证私钥文件", e);
            }

            // 初始化商户配置
            wechatPayConfig = new RSAAutoCertificateConfig.Builder()
                    .merchantId(merchantId)
                    .privateKey(privateKey)
                    .merchantSerialNumber(merchantSerialNumber)
                    .apiV3Key(apiV3Key)
                    .build();

            // 初始化支付服务
            nativePayService = new NativePayService.Builder().config(wechatPayConfig).build();
            log.info("微信境外支付服务初始化成功");
        } catch (Exception e) {
            log.error("微信境外支付服务初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("微信境外支付服务初始化失败", e);
        }
    }

    /**
     * 生成订单号
     */
    private String generateOutTradeNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
        String baseNo = "GLOBAL_" + timestamp + "_" + uuid;
        return baseNo.length() <= MAX_OUT_TRADE_NO_LENGTH
                ? baseNo
                : baseNo.substring(0, MAX_OUT_TRADE_NO_LENGTH);
    }

    /**
     * 创建微信境外支付订单
     * @param outTradeNo 商户订单号
     * @param total 订单金额（单位：分）
     * @param description 商品描述
     * @param clientIp 客户端IP地址
     * @return 支付二维码链接
     */
    public String createOrder(String outTradeNo, int total, String description, String clientIp) {
        try {
            // 参数验证
            if (total <= 0) throw new IllegalArgumentException("金额必须大于0");
            
            // 处理订单号
            if (outTradeNo == null || outTradeNo.isEmpty()) {
                // 如果没有提供订单号，则生成一个
                outTradeNo = generateOutTradeNo();
                log.info("生成新的订单号: {}", outTradeNo);
            } else {
                // 直接使用前端提供的订单号，不做额外处理
                log.info("使用前端提供的订单号: {}", outTradeNo);
                
                // 根据官方文档，商户订单号长度限制为32个字符
                if (outTradeNo.length() > MAX_OUT_TRADE_NO_LENGTH) {
                    log.warn("订单号超过{}个字符，将被截断。原订单号: {}", MAX_OUT_TRADE_NO_LENGTH, outTradeNo);
                    outTradeNo = outTradeNo.substring(0, MAX_OUT_TRADE_NO_LENGTH);
                }
            }
            
            log.info("最终使用的订单号: {}, 长度: {}", outTradeNo, outTradeNo.length());
            
            // 处理商品描述
            if (description == null || description.isEmpty()) {
                description = "商品购买";
            }
            // 根据官方文档，商品描述长度限制为127个字符
            description = description.length() > 127 ? description.substring(0, 127) : description;
            
            // 验证客户端IP
            if (clientIp == null || !isValidIp(clientIp)) {
                throw new IllegalArgumentException("无效的客户端IP");
            }
            
            // 构建请求体
            JSONObject requestJson = new JSONObject();
            
            // 基础信息
            requestJson.put("mchid", merchantId);
            requestJson.put("appid", appId);
            requestJson.put("description", description);
            requestJson.put("out_trade_no", outTradeNo);
            requestJson.put("notify_url", notifyUrl);
            requestJson.put("trade_type", "NATIVE");
            
            // 添加商户分类代码（跨境支付必需）
            requestJson.put("merchant_category_code", wechatPayProperties.getCrossBorder().getMerchantCategoryCode());

            // 金额信息
            JSONObject amount = new JSONObject();
            amount.put("total", total);
            
            // 使用配置的货币类型（跨境支付必需）
            amount.put("currency", wechatPayProperties.getCrossBorder().getCurrency());
            
            requestJson.put("amount", amount);

            // 场景信息
            JSONObject sceneInfo = new JSONObject();
            sceneInfo.put("payer_client_ip", clientIp);
            requestJson.put("scene_info", sceneInfo);

            // 打印完整请求体（调试用）
            String requestBody = requestJson.toString();
            log.info("完整请求参数: {}", requestBody);

            // 使用跨境支付API URL
            String apiUrl = API_URL;
            log.info("使用跨境支付API URL: {}", apiUrl);
            
            String responseStr = httpPost(apiUrl, requestBody);
            log.info("微信支付响应: {}", responseStr);

            // 解析响应
            JSONObject responseJson = new JSONObject(responseStr);
            return responseJson.getString("code_url");
        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw new RuntimeException("创建订单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行HTTP POST请求
     */
    private String httpPost(String url, String requestBody) throws Exception {
        log.info("发送HTTP POST请求到: {}", url);
        
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");
            
            // 添加认证头
            String token = getToken(url, "POST", requestBody);
            httpPost.setHeader("Authorization", token);
            httpPost.setHeader("User-Agent", "WechatPay-Custom/1.0");
            
            httpPost.setEntity(new StringEntity(requestBody, ContentType.APPLICATION_JSON));
            
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseBody = entity != null ? EntityUtils.toString(entity) : null;
                
                log.debug("响应码: {}, 响应体: {}", statusCode, responseBody);
                
                if (statusCode >= 200 && statusCode < 300) {
                    return responseBody;
                } else {
                    log.error("HTTP请求失败: {} - {}", statusCode, responseBody);
                    throw new RuntimeException("HTTP请求失败: " + statusCode + ", 响应: " + responseBody);
                }
            }
        }
    }
    
    /**
     * 获取认证令牌
     */
    private String getToken(String url, String method, String body) {
        try {
            // 生成随机字符串和时间戳
            String nonceStr = UUID.randomUUID().toString().replaceAll("-", "");
            long timestamp = System.currentTimeMillis() / 1000;

            // 构建签名字符串 - 正确处理URL路径和查询参数
            URL urlObj = new URL(url);
            String canonicalUrl = urlObj.getPath();
            if (urlObj.getQuery() != null) {
                canonicalUrl += "?" + urlObj.getQuery();
            }

            // 处理请求体 - GET请求通常没有请求体
            String requestBody = (body == null || body.trim().isEmpty()) ? "" : body;

            String message = method + "\n" +
                             canonicalUrl + "\n" +
                             timestamp + "\n" +
                             nonceStr + "\n" +
                             requestBody + "\n";

            log.debug("签名字符串构建:");
            log.debug("HTTP方法: [{}]", method);
            log.debug("URL路径: [{}]", canonicalUrl);
            log.debug("时间戳: [{}]", timestamp);
            log.debug("随机字符串: [{}]", nonceStr);
            log.debug("请求体: [{}]", requestBody);
            log.debug("完整签名字符串: [{}]", message.replace("\n", "\\n"));

            // 从classpath加载私钥内容
            ClassPathResource resource = new ClassPathResource(privateKeyPath);
            String privateKeyContent = FileCopyUtils.copyToString(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));

            // 签名
            PrivateKey privateKey = loadPrivateKeyFromString(privateKeyContent);
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(message.getBytes(StandardCharsets.UTF_8));
            byte[] signBytes = signature.sign();
            String signatureStr = Base64.getEncoder().encodeToString(signBytes);

            log.debug("生成的签名: [{}]", signatureStr);
            log.debug("商户号: [{}]", merchantId);
            log.debug("证书序列号: [{}]", merchantSerialNumber);

            // 构建认证头
            String authHeader = "WECHATPAY2-SHA256-RSA2048 " +
                   "mchid=\"" + merchantId + "\"," +
                   "nonce_str=\"" + nonceStr + "\"," +
                   "timestamp=\"" + timestamp + "\"," +
                   "serial_no=\"" + merchantSerialNumber + "\"," +
                   "signature=\"" + signatureStr + "\"";

            log.debug("认证头: [{}]", authHeader);
            return authHeader;
        } catch (Exception e) {
            log.error("生成认证令牌失败", e);
            throw new RuntimeException("生成认证令牌失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 加载私钥
     */
    private PrivateKey loadPrivateKeyFromString(String privateKeyPEM) throws Exception {
        // 移除PEM格式的头尾和换行符
        String privateKeyContent = privateKeyPEM
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");
        
        // Base64解码
        byte[] encodedKey = Base64.getDecoder().decode(privateKeyContent);
        
        // 创建PKCS8EncodedKeySpec对象
        java.security.spec.PKCS8EncodedKeySpec keySpec = new java.security.spec.PKCS8EncodedKeySpec(encodedKey);
        
        // 获取KeyFactory对象并生成PrivateKey对象
        java.security.KeyFactory keyFactory = java.security.KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }
    
    /**
     * 验证IP地址格式
     */
    private boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        for (String part : parts) {
            try {
                int value = Integer.parseInt(part);
                if (value < 0 || value > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 申请退款
     * @param requestDTO 退款申请请求
     * @return 退款响应
     */
    public WechatRefundResponseVO applyRefund(WechatRefundRequestDTO requestDTO) {
        try {
            log.info("申请退款，请求参数: {}", requestDTO);

            // 参数验证
            validateRefundRequest(requestDTO);

            // 构建请求体
            JSONObject requestJson = buildRefundRequestJson(requestDTO);

            // 发送退款请求
            String responseStr = httpPost(REFUND_API_URL, requestJson.toString());
            log.info("微信退款响应: {}", responseStr);

            // 解析响应
            return objectMapper.readValue(responseStr, WechatRefundResponseVO.class);

        } catch (Exception e) {
            log.error("申请退款失败", e);
            throw new RuntimeException("申请退款失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询退款结果
     * @param queryDTO 退款查询请求
     * @return 退款查询响应
     */
    public WechatRefundQueryVO queryRefund(WechatRefundQueryDTO queryDTO) {
        try {
            log.info("查询退款结果，请求参数: {}", queryDTO);

            // 参数验证
            validateRefundQueryRequest(queryDTO);

            // 构建查询URL
            String queryUrl = buildRefundQueryUrl(queryDTO);

            // 发送查询请求
            String responseStr = httpGet(queryUrl);
            log.info("微信退款查询响应: {}", responseStr);

            // 解析响应
            return objectMapper.readValue(responseStr, WechatRefundQueryVO.class);

        } catch (Exception e) {
            log.error("查询退款结果失败", e);
            throw new RuntimeException("查询退款结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证退款申请请求参数
     */
    private void validateRefundRequest(WechatRefundRequestDTO requestDTO) {
        if (requestDTO == null) {
            throw new IllegalArgumentException("退款请求参数不能为空");
        }

        // 验证订单号（微信支付订单号和商户订单号二选一）
        if ((requestDTO.getTransactionId() == null || requestDTO.getTransactionId().trim().isEmpty()) &&
            (requestDTO.getOutTradeNo() == null || requestDTO.getOutTradeNo().trim().isEmpty())) {
            throw new IllegalArgumentException("微信支付订单号和商户订单号不能同时为空");
        }

        // 验证退款单号
        if (requestDTO.getOutRefundNo() == null || requestDTO.getOutRefundNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户退款单号不能为空");
        }

        if (requestDTO.getOutRefundNo().length() > MAX_OUT_REFUND_NO_LENGTH) {
            throw new IllegalArgumentException("商户退款单号长度不能超过" + MAX_OUT_REFUND_NO_LENGTH + "个字符");
        }

        // 验证金额
        if (requestDTO.getRefundAmount() == null || requestDTO.getRefundAmount() <= 0) {
            throw new IllegalArgumentException("退款金额必须大于0");
        }

        if (requestDTO.getTotalAmount() == null || requestDTO.getTotalAmount() <= 0) {
            throw new IllegalArgumentException("原订单金额必须大于0");
        }

        if (requestDTO.getRefundAmount() > requestDTO.getTotalAmount()) {
            throw new IllegalArgumentException("退款金额不能超过原订单金额");
        }
    }

    /**
     * 验证退款查询请求参数
     */
    private void validateRefundQueryRequest(WechatRefundQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new IllegalArgumentException("退款查询请求参数不能为空");
        }

        // 验证退款订单号（微信支付退款订单号和商户退款单号二选一）
        if ((queryDTO.getRefundId() == null || queryDTO.getRefundId().trim().isEmpty()) &&
            (queryDTO.getOutRefundNo() == null || queryDTO.getOutRefundNo().trim().isEmpty())) {
            throw new IllegalArgumentException("微信支付退款订单号和商户退款单号不能同时为空");
        }
    }

    /**
     * 构建退款申请请求JSON
     */
    private JSONObject buildRefundRequestJson(WechatRefundRequestDTO requestDTO) {
        JSONObject requestJson = new JSONObject();

        // 基础信息
        requestJson.put("mchid", merchantId);
        requestJson.put("appid", appId);

        // 订单号（优先使用微信支付订单号）
        if (requestDTO.getTransactionId() != null && !requestDTO.getTransactionId().trim().isEmpty()) {
            requestJson.put("transaction_id", requestDTO.getTransactionId().trim());
        } else {
            requestJson.put("out_trade_no", requestDTO.getOutTradeNo().trim());
        }

        // 退款单号
        requestJson.put("out_refund_no", requestDTO.getOutRefundNo().trim());

        // 退款原因
        if (requestDTO.getReason() != null && !requestDTO.getReason().trim().isEmpty()) {
            requestJson.put("reason", requestDTO.getReason().trim());
        }

        // 退款资金来源
        String source = requestDTO.getSource();
        if (source == null || source.trim().isEmpty()) {
            source = DEFAULT_REFUND_SOURCE;
        }
        requestJson.put("source", source);

        // 金额信息
        JSONObject amount = new JSONObject();
        amount.put("refund", requestDTO.getRefundAmount());
        amount.put("total", requestDTO.getTotalAmount());
        amount.put("currency", wechatPayProperties.getCrossBorder().getCurrency());
        requestJson.put("amount", amount);

        // 退款通知地址
        if (requestDTO.getNotifyUrl() != null && !requestDTO.getNotifyUrl().trim().isEmpty()) {
            requestJson.put("notify_url", requestDTO.getNotifyUrl().trim());
        }

        return requestJson;
    }

    /**
     * 构建退款查询URL
     */
    private String buildRefundQueryUrl(WechatRefundQueryDTO queryDTO) {
        StringBuilder urlBuilder = new StringBuilder(REFUND_API_URL);

        // 根据查询方式构建URL
        if (queryDTO.getRefundId() != null && !queryDTO.getRefundId().trim().isEmpty()) {
            // 使用微信支付退款订单号查询
            urlBuilder.append("/id/").append(queryDTO.getRefundId().trim());
        } else {
            // 使用商户退款单号查询
            urlBuilder.append("/out-refund-no/").append(queryDTO.getOutRefundNo().trim());
        }

        // 添加商户号参数
        urlBuilder.append("?mchid=").append(merchantId);

        // 如果是机构模式，添加相关参数
        if (queryDTO.getSpMchid() != null && !queryDTO.getSpMchid().trim().isEmpty()) {
            urlBuilder.append("&sp_mchid=").append(queryDTO.getSpMchid().trim());
        }

        if (queryDTO.getSubMchid() != null && !queryDTO.getSubMchid().trim().isEmpty()) {
            urlBuilder.append("&sub_mchid=").append(queryDTO.getSubMchid().trim());
        }

        return urlBuilder.toString();
    }

    /**
     * 执行HTTP GET请求
     */
    private String httpGet(String url) throws Exception {
        log.info("发送HTTP GET请求到: {}", url);

        // 验证配置
        validateConfiguration();

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Content-Type", "application/json");
            httpGet.setHeader("Accept", "application/json");

            // 添加认证头（GET请求body为空）
            String token = getToken(url, "GET", "");
            httpGet.setHeader("Authorization", token);
            httpGet.setHeader("User-Agent", "WechatPay-Custom/1.0");

            try (CloseableHttpResponse response = client.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseBody = entity != null ? EntityUtils.toString(entity) : null;

                log.debug("响应码: {}, 响应体: {}", statusCode, responseBody);

                if (statusCode >= 200 && statusCode < 300) {
                    return responseBody;
                } else {
                    log.error("HTTP请求失败: {} - {}", statusCode, responseBody);
                    throw new RuntimeException("HTTP请求失败: " + statusCode + ", 响应: " + responseBody);
                }
            }
        }
    }

    /**
     * 查询所有退款
     * @param queryDTO 查询所有退款请求
     * @return 查询所有退款响应
     */
    public WechatRefundListQueryVO queryAllRefunds(WechatRefundListQueryDTO queryDTO) {
        try {
            log.info("查询所有退款，请求参数: {}", queryDTO);

            // 参数验证
            validateRefundListQueryRequest(queryDTO);

            // 构建查询URL
            String queryUrl = buildRefundListQueryUrl(queryDTO);

            // 发送查询请求
            String responseStr = httpGet(queryUrl);
            log.info("微信查询所有退款响应: {}", responseStr);

            // 解析响应
            return objectMapper.readValue(responseStr, WechatRefundListQueryVO.class);

        } catch (Exception e) {
            log.error("查询所有退款失败", e);
            throw new RuntimeException("查询所有退款失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证查询所有退款请求参数
     */
    private void validateRefundListQueryRequest(WechatRefundListQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new IllegalArgumentException("查询所有退款请求参数不能为空");
        }

        // 验证订单号（微信支付订单号和商户订单号二选一）
        if ((queryDTO.getTransactionId() == null || queryDTO.getTransactionId().trim().isEmpty()) &&
            (queryDTO.getOutTradeNo() == null || queryDTO.getOutTradeNo().trim().isEmpty())) {
            throw new IllegalArgumentException("微信支付订单号和商户订单号不能同时为空");
        }

        // 验证分页参数
        if (queryDTO.getOffset() == null || queryDTO.getOffset() < 0) {
            queryDTO.setOffset(0); // 默认从0开始
        }

        if (queryDTO.getCount() == null || queryDTO.getCount() <= 0) {
            queryDTO.setCount(10); // 默认每页10条
        } else if (queryDTO.getCount() > 20) {
            queryDTO.setCount(20); // 最大20条
        }
    }

    /**
     * 构建查询所有退款URL
     */
    private String buildRefundListQueryUrl(WechatRefundListQueryDTO queryDTO) {
        StringBuilder urlBuilder = new StringBuilder(REFUND_API_URL);
        urlBuilder.append("?");

        // 添加订单号参数
        if (queryDTO.getTransactionId() != null && !queryDTO.getTransactionId().trim().isEmpty()) {
            urlBuilder.append("transaction_id=").append(queryDTO.getTransactionId().trim()).append("&");
        } else {
            urlBuilder.append("out_trade_no=").append(queryDTO.getOutTradeNo().trim()).append("&");
        }

        // 添加分页参数
        urlBuilder.append("offset=").append(queryDTO.getOffset()).append("&");
        urlBuilder.append("count=").append(queryDTO.getCount()).append("&");

        // 添加商户号参数
        urlBuilder.append("mchid=").append(merchantId);

        // 如果是机构模式，添加相关参数
        if (queryDTO.getSpMchid() != null && !queryDTO.getSpMchid().trim().isEmpty()) {
            urlBuilder.append("&sp_mchid=").append(queryDTO.getSpMchid().trim());
        }

        if (queryDTO.getSubMchid() != null && !queryDTO.getSubMchid().trim().isEmpty()) {
            urlBuilder.append("&sub_mchid=").append(queryDTO.getSubMchid().trim());
        }

        return urlBuilder.toString();
    }

    /**
     * 验证微信支付配置
     */
    private void validateConfiguration() {
        if (merchantId == null || merchantId.trim().isEmpty()) {
            throw new IllegalStateException("商户号未配置");
        }
        if (merchantSerialNumber == null || merchantSerialNumber.trim().isEmpty()) {
            throw new IllegalStateException("证书序列号未配置");
        }
        if (apiV3Key == null || apiV3Key.trim().isEmpty()) {
            throw new IllegalStateException("APIv3密钥未配置");
        }
        if (privateKeyPath == null || privateKeyPath.trim().isEmpty()) {
            throw new IllegalStateException("私钥路径未配置");
        }

        log.debug("微信支付配置验证通过");
    }
}