package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemProgramsDTO implements Serializable {

    //页码
    private int page;

    //每页记录数
    private int pageSize;

    private String type;

    private String status;

}
