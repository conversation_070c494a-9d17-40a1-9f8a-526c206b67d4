package com.sky.controller.Seller;

import com.sky.constant.MessageConstant;
import com.sky.context.BaseContext;
import com.sky.dto.PaymentAccountDTO;
import com.sky.dto.PaymentAccountQueryDTO;
import com.sky.result.Result;
import com.sky.service.PaymentAccountService;
import com.sky.vo.PageResult;
import com.sky.vo.PaymentAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商家端收账账户管理控制器
 */
@RestController
@RequestMapping("/merchant/payment-account")
@Api(tags = "商家端收账账户管理接口")
@Slf4j
public class PaymentAccountController {

    @Autowired
    private PaymentAccountService paymentAccountService;

    /**
     * 添加收账账户
     */
    @PostMapping
    @ApiOperation("添加收账账户")
    public Result<String> addPaymentAccount(@Valid @RequestBody PaymentAccountDTO paymentAccountDTO) {
        log.info("添加收账账户：{}", paymentAccountDTO);
        Long sellerId = BaseContext.getCurrentId();
        paymentAccountService.addPaymentAccount(paymentAccountDTO, sellerId);
        return Result.success(MessageConstant.PAYMENT_ACCOUNT_ADD_SUCCESS);
    }

    /**
     * 更新收账账户
     */
    @PutMapping
    @ApiOperation("更新收账账户")
    public Result<String> updatePaymentAccount(@Valid @RequestBody PaymentAccountDTO paymentAccountDTO) {
        log.info("更新收账账户：{}", paymentAccountDTO);
        Long sellerId = BaseContext.getCurrentId();
        paymentAccountService.updatePaymentAccount(paymentAccountDTO, sellerId);
        return Result.success(MessageConstant.PAYMENT_ACCOUNT_UPDATE_SUCCESS);
    }

    /**
     * 删除收账账户
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除收账账户")
    public Result<String> deletePaymentAccount(@PathVariable Long id) {
        log.info("删除收账账户，ID：{}", id);
        Long sellerId = BaseContext.getCurrentId();
        paymentAccountService.deletePaymentAccount(id, sellerId);
        return Result.success(MessageConstant.PAYMENT_ACCOUNT_DELETE_SUCCESS);
    }

    /**
     * 根据ID查询收账账户
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询收账账户")
    public Result<PaymentAccountVO> getPaymentAccountById(@PathVariable Long id) {
        log.info("查询收账账户，ID：{}", id);
        Long sellerId = BaseContext.getCurrentId();
        PaymentAccountVO paymentAccountVO = paymentAccountService.getPaymentAccountById(id, sellerId);
        return Result.success(paymentAccountVO);
    }

    /**
     * 分页查询收账账户
     */
    @GetMapping("/page")
    @ApiOperation("分页查询收账账户")
    public Result<PageResult> pageQuery(PaymentAccountQueryDTO queryDTO) {
        log.info("分页查询收账账户：{}", queryDTO);
        Long sellerId = BaseContext.getCurrentId();
        PageResult pageResult = paymentAccountService.pageQuery(queryDTO, sellerId);
        return Result.success(pageResult);
    }

    /**
     * 查询所有收账账户
     */
    @GetMapping("/list")
    @ApiOperation("查询所有收账账户")
    public Result<List<PaymentAccountVO>> getPaymentAccountList() {
        log.info("查询所有收账账户");
        Long sellerId = BaseContext.getCurrentId();
        List<PaymentAccountVO> paymentAccounts = paymentAccountService.getPaymentAccountsBySellerId(sellerId);
        return Result.success(paymentAccounts);
    }

    /**
     * 查询默认收账账户
     */
    @GetMapping("/default")
    @ApiOperation("查询默认收账账户")
    public Result<PaymentAccountVO> getDefaultPaymentAccount() {
        log.info("查询默认收账账户");
        Long sellerId = BaseContext.getCurrentId();
        PaymentAccountVO defaultAccount = paymentAccountService.getDefaultPaymentAccount(sellerId);
        return Result.success(defaultAccount);
    }

    /**
     * 设置默认收账账户
     */
    @PutMapping("/{id}/default")
    @ApiOperation("设置默认收账账户")
    public Result<String> setDefaultPaymentAccount(@PathVariable Long id) {
        log.info("设置默认收账账户，ID：{}", id);
        Long sellerId = BaseContext.getCurrentId();
        paymentAccountService.setDefaultPaymentAccount(id, sellerId);
        return Result.success(MessageConstant.DEFAULT_ACCOUNT_SET_SUCCESS);
    }

    /**
     * 启用收账账户
     */
    @PutMapping("/{id}/enable")
    @ApiOperation("启用收账账户")
    public Result<String> enablePaymentAccount(@PathVariable Long id) {
        log.info("启用收账账户，ID：{}", id);
        Long sellerId = BaseContext.getCurrentId();
        paymentAccountService.updateAccountStatus(id, sellerId, 1);
        return Result.success(MessageConstant.ACCOUNT_STATUS_UPDATE_SUCCESS);
    }

    /**
     * 禁用收账账户
     */
    @PutMapping("/{id}/disable")
    @ApiOperation("禁用收账账户")
    public Result<String> disablePaymentAccount(@PathVariable Long id) {
        log.info("禁用收账账户，ID：{}", id);
        Long sellerId = BaseContext.getCurrentId();
        paymentAccountService.updateAccountStatus(id, sellerId, 0);
        return Result.success(MessageConstant.ACCOUNT_STATUS_UPDATE_SUCCESS);
    }
}
