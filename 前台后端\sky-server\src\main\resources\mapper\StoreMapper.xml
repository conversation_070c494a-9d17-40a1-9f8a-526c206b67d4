<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.StoreMapper">
    <insert id="insert">
        insert into store (id,seller_id,store_name,photo,store_status,store_description,store_address,create_time,last_login_time)
        values (#{id},#{sellerId},#{storeName},#{photo},#{storeStatus},#{storeDescription},#{storeAddress},#{createTime},#{lastLoginTime})
    </insert>
    <update id="update">
    update store
        <set>
            <if test="sellerId != null">
                seller_id = #{sellerId},
            </if>
            <if test=" storeName!= null">
                store_name = #{storeName},
            </if>
            <if test="photo != null">
                photo = #{photo},
            </if>
            <if test="storeStatus != null">
                store_status = #{storeStatus},
            </if>
            <if test="storeDescription != null">
                store_description = #{storeDescription},
            </if>
            <if test="storeAddress != null">
                store_address = #{storeAddress},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime}
            </if>
            <if test="financialInfo !=null">
                financial_info = #{financialInfo}
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
