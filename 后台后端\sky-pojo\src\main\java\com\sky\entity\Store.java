package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 店铺
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Store implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long sellerId;

    private String storeName;

    private String photo;

    private String storeStatus;

    private String storeDescription;

    private String storeAddress;

    private LocalDateTime createTime;

    private LocalDateTime lastLoginTime;

}
