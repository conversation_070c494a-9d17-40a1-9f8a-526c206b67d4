package com.sky.exception;

/**
 * 退款业务异常
 */
public class RefundException extends BaseException {

    public RefundException() {
        super();
    }

    public RefundException(String msg) {
        super(msg);
    }

    public RefundException(String msg, Throwable cause) {
        super(msg, cause);
    }

    // 退款相关的具体异常类型
    public static class RefundNotAllowedException extends RefundException {
        public RefundNotAllowedException(String reason) {
            super("不允许退款：" + reason);
        }
    }

    public static class RefundAmountException extends RefundException {
        public RefundAmountException(String message) {
            super(message);
        }
    }

    public static class RefundApplicationNotExistException extends RefundException {
        public RefundApplicationNotExistException() {
            super("退款申请不存在");
        }
        
        public RefundApplicationNotExistException(String refundNo) {
            super("退款申请不存在：" + refundNo);
        }
    }

    public static class RefundPermissionException extends RefundException {
        public RefundPermissionException() {
            super("无权限操作此退款申请");
        }
        
        public RefundPermissionException(String message) {
            super(message);
        }
    }

    public static class RefundStatusException extends RefundException {
        public RefundStatusException(String message) {
            super(message);
        }
    }

    public static class RefundProcessException extends RefundException {
        public RefundProcessException(String message) {
            super(message);
        }
        
        public RefundProcessException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public static class WechatRefundException extends RefundException {
        public WechatRefundException(String message) {
            super("微信退款失败：" + message);
        }
        
        public WechatRefundException(String message, Throwable cause) {
            super("微信退款失败：" + message, cause);
        }
    }
}
