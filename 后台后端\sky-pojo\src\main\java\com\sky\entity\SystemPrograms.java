package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemPrograms implements Serializable {

    private static final long serialVersionUID = 1L;

        private Long id;

        private String programName;

        private String programStatus;

        private String testStatus;

        private String token;

        private LocalDateTime expirationDate;

        private String operation;

        private LocalDateTime createdTime;

        private LocalDateTime updatedTime;

}
