package com.sky.controller.admin;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sky.dto.CategoryCreateDTO;
import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.dto.CategoryQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import com.sky.vo.CategoryTreeVO;
import com.sky.vo.CategoryVO;
import com.sky.vo.ListCategoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.List;

@RestController
@RequestMapping("/categories")
@Api(tags = "分类管理")
@Validated
@RequiredArgsConstructor
public class CategoryController {
    private final CategoryService categoryService;

    // 创建分类（已有）
    @PostMapping("/create")
    @ApiOperation("创建分类")
    public Result<Long> createCategory(@RequestBody CategoryCreateDTO dto) {
        return Result.success(categoryService.createCategory(dto));
    }

    // 获取分类树（已有但需完善参数校验）
    @GetMapping("/{rootId}/tree")
    @ApiOperation("获取分类树")
    public Result<CategoryTreeVO> getCategoryTree(
            @PathVariable  Long rootId) { // 添加参数校验
        return Result.success(categoryService.getCategoryTree(rootId));
    }


    // 新增级联删除
    @DeleteMapping("/{id}")
    @ApiOperation("级联删除分类")
    public Result<Void> deleteCategory(
            @PathVariable  Long id) {
        categoryService.deleteCategoryWithSubtree(id);
        return Result.success();
    }


    // 新增获取全量树（可选）
    @GetMapping("/tree")
    @ApiOperation("获取全量分类树")
    public Result<List<CategoryTreeVO>> getFullTree() throws JsonProcessingException {

        return Result.success(categoryService.getFullCategoryTree());
    }
}