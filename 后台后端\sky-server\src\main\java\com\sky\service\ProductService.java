package com.sky.service;

import com.sky.dto.ProductPageQueryDTO;
import com.sky.entity.PmsProduct;
import com.sky.result.PageResult;
import com.sky.vo.ProductVO;

import java.util.List;

public interface ProductService {


    /**
     * 产品分页查询
     *
     * @param productPageQueryDTO
     * @return
     */
    PageResult pageQuery(ProductPageQueryDTO productPageQueryDTO);


    /**
     * 根据id查询产品和对应的分类数据
     *
     * @param id
     * @return
     */
    PmsProduct getByProductId(Long id);

    /**
     * 根据分类id查询产品
     *
     * @param categoryId
     * @return
     */
    List<PmsProduct> getByCategoryId(Long categoryId);

}
