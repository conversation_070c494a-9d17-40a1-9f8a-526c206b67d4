package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单项数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "订单项数据传输对象")
public class OrderItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("商品ID")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @ApiModelProperty("商品数量")
    @NotNull(message = "商品数量不能为空")
    @Min(value = 1, message = "商品数量必须大于0")
    private Integer quantity;

    @ApiModelProperty("商品单价")
    @NotNull(message = "商品单价不能为空")
    private BigDecimal unitPrice;
} 