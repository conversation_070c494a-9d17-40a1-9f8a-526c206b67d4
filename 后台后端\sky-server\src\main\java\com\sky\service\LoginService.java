package com.sky.service;

import com.sky.dto.EVPIDTO;
import com.sky.dto.PassWordDTO;
import com.sky.dto.SellerLoginDTO;
import com.sky.dto.SellerRegisterDTO;
import com.sky.entity.Buyer;
import com.sky.entity.EVPI;
import com.sky.entity.Seller;
import com.sky.vo.EVPIVO;
import org.springframework.web.multipart.MultipartFile;

public interface LoginService {
    void loginRegister(SellerRegisterDTO sellerRegisterDTO, MultipartFile licenseFile, MultipartFile warehouseFiles1, MultipartFile warehouseFiles2, MultipartFile warehouseFiles3 ,MultipartFile IDCard1,
                       MultipartFile IDCard2);

    Seller login(SellerLoginDTO sellerLoginDTO);

  //  void ImpShopData(EVPIDTO evpidto, MultipartFile licenseFile, MultipartFile[] warehouseFiles);

    EVPI getShopData(Long id);

    Seller getSellerData(Long id);

    void updata(Seller seller);

    void ChangePassWord(PassWordDTO passWordDTO);

    void loginByEmail(SellerLoginDTO sellerLoginDTO);

    Seller login1(SellerLoginDTO sellerLoginDTO);
}
