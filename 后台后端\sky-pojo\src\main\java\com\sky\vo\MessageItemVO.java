package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String msgID;           // 消息唯一标识
    private String msgType;         // 消息类型
    private String title;           // 消息标题
    private String senderName;      // 发送者名称
    private LocalDateTime sendTime; // 发送时间
    private Boolean isRead;         // 是否已读
} 