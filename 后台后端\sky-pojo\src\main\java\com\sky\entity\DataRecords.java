package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataRecords  implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String serialNumber;

    private Integer sellerId;
//1不可提现0可提现
    private String status;

    private String content;

    private BigDecimal amount;

    private String operation;


    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;
}
