package com.sky.mapper;

import com.sky.annotation.AutoFill;
import com.sky.entity.Store;
import com.sky.enumeration.OperationType;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface StoreMapper {
    /**
     * 新增店铺
     * @param store
     */
    void insert(Store store);

    /**
     * 修改店铺
     * @param store
     */
    void update(Store store);

     /**
      * 根据id查询店铺
      * @param id
      * @return
      */
     @Select("select * from store where id = #{id}")
    Store getById(Long id);

     /**
      * 根据id删除店铺
      * @param id
      */
     @Delete("delete from store where id = #{id}")
    void delete(Long id);
}
