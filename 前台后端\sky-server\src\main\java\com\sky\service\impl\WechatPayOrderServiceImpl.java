package com.sky.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sky.dto.WechatPayOrderQueryDTO;
import com.sky.exception.BusinessException;
import com.sky.properties.WechatPayProperties;
import com.sky.service.WechatPayOrderService;
import com.sky.vo.WechatPayOrderQueryVO;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.http.AbstractHttpClient;
import com.wechat.pay.java.core.http.DefaultHttpClientBuilder;
import com.wechat.pay.java.core.http.HttpClient;
import com.wechat.pay.java.core.http.HttpMethod;
import com.wechat.pay.java.core.http.HttpRequest;
import com.wechat.pay.java.core.http.HttpResponse;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByIdRequest;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByOutTradeNoRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 微信支付订单服务实现类
 */
@Service
@Slf4j
public class WechatPayOrderServiceImpl implements WechatPayOrderService {

    private final WechatPayProperties wechatPayProperties;
    private final NativePayService nativePayService;
    private final ObjectMapper objectMapper;
    private final Config wechatPayConfig;
    private HttpClient httpClient;

    @Autowired
    public WechatPayOrderServiceImpl(
            WechatPayProperties wechatPayProperties,
            NativePayService nativePayService,
            Config wechatPayConfig,
            @Qualifier("wechatPayObjectMapper") ObjectMapper objectMapper) {
        this.wechatPayProperties = wechatPayProperties;
        this.nativePayService = nativePayService;
        this.wechatPayConfig = wechatPayConfig;
        this.objectMapper = objectMapper;
        this.httpClient = new DefaultHttpClientBuilder().config(wechatPayConfig).build();
    }

    /**
     * 查询订单
     * 
     * @param queryDTO 查询参数
     * @return 订单详情
     */
    @Override
    public WechatPayOrderQueryVO queryOrder(WechatPayOrderQueryDTO queryDTO) {
        // 参数校验
        if (queryDTO == null) {
            throw new BusinessException("查询参数不能为空");
        }
        
        if (StringUtils.hasText(queryDTO.getTransactionId())) {
            // 优先使用微信支付订单号查询
            return queryOrderByTransactionId(queryDTO.getTransactionId());
        } else if (StringUtils.hasText(queryDTO.getOutTradeNo())) {
            // 使用商户订单号查询
            return queryOrderByOutTradeNo(queryDTO.getOutTradeNo());
        } else {
            throw new BusinessException("微信支付订单号和商户订单号不能同时为空");
        }
    }
    
    /**
     * 根据微信支付订单号查询订单
     * 
     * @param transactionId 微信支付订单号
     * @return 订单详情
     */
    @Override
    public WechatPayOrderQueryVO queryOrderByTransactionId(String transactionId) {
        if (!StringUtils.hasText(transactionId)) {
            throw new BusinessException("微信支付订单号不能为空");
        }
        
        try {
            log.info("根据微信支付订单号查询订单: {}", transactionId);
            
            // 强制使用跨境支付API查询订单
            log.info("使用跨境支付API查询订单");
            return queryGlobalOrderById(transactionId);
            
        } catch (Exception e) {
            log.error("查询订单失败: {}", e.getMessage(), e);
            throw new BusinessException("查询订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据商户订单号查询订单
     * 
     * @param outTradeNo 商户订单号
     * @return 订单详情
     */
    @Override
    public WechatPayOrderQueryVO queryOrderByOutTradeNo(String outTradeNo) {
        if (!StringUtils.hasText(outTradeNo)) {
            throw new BusinessException("商户订单号不能为空");
        }
        
        try {
            log.info("根据商户订单号查询订单: {}", outTradeNo);
            
            // 直接使用传入的订单号查询，不做额外处理
            log.info("使用跨境支付API查询订单");
            return queryGlobalOrderByOutTradeNo(outTradeNo);
            
        } catch (Exception e) {
            log.error("查询订单失败: {}", e.getMessage(), e);
            throw new BusinessException("查询订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为跨境支付订单
     * 
     * @param orderNo 订单号（微信支付订单号或商户订单号）
     * @return 是否为跨境支付订单
     */
    private boolean isGlobalOrder(String orderNo) {
        // 检查跨境支付是否启用
        if (!wechatPayProperties.getCrossBorder().isEnabled()) {
            return false;
        }
        
        // 检查订单号是否包含GLOBAL前缀，用于识别跨境支付订单
        boolean isGlobal = orderNo.startsWith("GLOBAL_");
        log.info("订单号: {}, 是否为跨境支付订单: {}", orderNo, isGlobal);
        return isGlobal;
    }
    
    /**
     * 使用跨境支付API根据微信支付订单号查询订单
     * 
     * @param transactionId 微信支付订单号
     * @return 订单详情
     */
    private WechatPayOrderQueryVO queryGlobalOrderById(String transactionId) throws Exception {
        log.info("使用跨境支付API查询订单，微信支付订单号: {}", transactionId);
        
        // 根据官方文档，使用正确的跨境支付API URL
        String apiBaseUrl = "https://apihk.mch.weixin.qq.com/v3/global/transactions";
        
        // 构建完整URL，注意参数格式：/id/{transaction_id}?mchid={mchid}
        String url = apiBaseUrl + "/id/" + transactionId + "?mchid=" + wechatPayProperties.getMchid();
        log.info("跨境支付API完整URL(transaction_id): {}", url);
        
        // 创建自定义HTTP请求，因为NativePayService不支持直接调用跨境支付API
        HttpRequest httpRequest = new HttpRequest.Builder()
                .httpMethod(HttpMethod.GET)
                .url(url)
                .build();
        
        log.info("发送跨境支付订单查询请求: {}", httpRequest);
        
        try {
            // 发送请求
            HttpResponse<Transaction> httpResponse = httpClient.execute(httpRequest, Transaction.class);
            Transaction transaction = httpResponse.getServiceResponse();
            log.info("跨境支付订单查询响应: {}", transaction);
            
            // 转换为VO
            return convertTransactionToVO(transaction);
        } catch (ServiceException e) {
            // 处理订单不存在的情况
            String errorCode = getErrorCode(e);
            if (errorCode != null && errorCode.equals("ORDER_NOT_EXIST")) {
                log.info("订单不存在，返回默认支付状态: {}", transactionId);
                // 创建一个默认的订单查询结果，状态为NOTPAY（未支付）
                WechatPayOrderQueryVO vo = new WechatPayOrderQueryVO();
                vo.setTransactionId(transactionId);
                vo.setTradeState("NOTPAY");
                vo.setTradeStateDesc("订单不存在或未支付");
                return vo;
            } else {
                // 其他错误，继续抛出
                throw e;
            }
        }
    }
    
    /**
     * 使用跨境支付API根据商户订单号查询订单
     * 
     * @param outTradeNo 商户订单号
     * @return 订单详情
     */
    private WechatPayOrderQueryVO queryGlobalOrderByOutTradeNo(String outTradeNo) throws Exception {
        log.info("使用跨境支付API查询订单，商户订单号: {}", outTradeNo);
        
        // 根据官方文档，使用正确的跨境支付API URL
        String apiBaseUrl = "https://apihk.mch.weixin.qq.com/v3/global/transactions";
        log.info("跨境支付API基础URL: {}", apiBaseUrl);
        
        // 构建完整URL，注意参数格式：/out-trade-no/{out_trade_no}?mchid={mchid}
        String url = apiBaseUrl + "/out-trade-no/" + outTradeNo + "?mchid=" + wechatPayProperties.getMchid();
        log.info("跨境支付API完整URL(out_trade_no): {}", url);
        
        // 创建自定义HTTP请求，因为NativePayService不支持直接调用跨境支付API
        HttpRequest httpRequest = new HttpRequest.Builder()
                .httpMethod(HttpMethod.GET)
                .url(url)
                .build();
        
        log.info("发送跨境支付订单查询请求: {}", httpRequest);
        
        try {
            // 发送请求
            HttpResponse<Transaction> httpResponse = httpClient.execute(httpRequest, Transaction.class);
            Transaction transaction = httpResponse.getServiceResponse();
            log.info("跨境支付订单查询响应: {}", transaction);
            
            // 转换为VO
            return convertTransactionToVO(transaction);
        } catch (ServiceException e) {
            // 处理订单不存在的情况
            String errorCode = getErrorCode(e);
            if (errorCode != null && errorCode.equals("ORDER_NOT_EXIST")) {
                log.info("订单不存在，返回默认支付状态: {}", outTradeNo);
                // 创建一个默认的订单查询结果，状态为NOTPAY（未支付）
                WechatPayOrderQueryVO vo = new WechatPayOrderQueryVO();
                vo.setOutTradeNo(outTradeNo);
                vo.setTradeState("NOTPAY");
                vo.setTradeStateDesc("订单不存在或未支付");
                return vo;
            } else {
                // 其他错误，继续抛出
                throw e;
            }
        }
    }
    
    /**
     * 从ServiceException中获取错误代码
     * 
     * @param e ServiceException异常
     * @return 错误代码，如果无法获取则返回null
     */
    private String getErrorCode(ServiceException e) {
        try {
            String responseBody = e.getMessage();
            if (responseBody != null && responseBody.contains("\"code\":")) {
                int codeStart = responseBody.indexOf("\"code\":\"") + 8;
                int codeEnd = responseBody.indexOf("\"", codeStart);
                if (codeStart > 8 && codeEnd > codeStart) {
                    return responseBody.substring(codeStart, codeEnd);
                }
            }
        } catch (Exception ex) {
            log.error("解析错误代码失败", ex);
        }
        return null;
    }

    /**
     * 将Transaction转换为WechatPayOrderQueryVO
     */
    private WechatPayOrderQueryVO convertTransactionToVO(Transaction transaction) {
        try {
            // 使用ObjectMapper进行对象转换
            String json = objectMapper.writeValueAsString(transaction);
            return objectMapper.readValue(json, WechatPayOrderQueryVO.class);
        } catch (Exception e) {
            log.error("转换Transaction失败: {}", e.getMessage(), e);
            throw new BusinessException("转换Transaction失败: " + e.getMessage());
        }
    }
} 