package com.sky.service;

import com.sky.dto.OrderBatchOperationDTO;
import com.sky.vo.OrderBatchOperationResultVO;

/**
 * 订单批量操作服务接口
 */
public interface OrderBatchService {

    /**
     * 批量发货
     */
    OrderBatchOperationResultVO batchShip(OrderBatchOperationDTO batchOperationDTO);

    /**
     * 批量取消
     */
    OrderBatchOperationResultVO batchCancel(OrderBatchOperationDTO batchOperationDTO);

    /**
     * 批量完成
     */
    OrderBatchOperationResultVO batchComplete(OrderBatchOperationDTO batchOperationDTO);

    /**
     * 批量导出
     */
    OrderBatchOperationResultVO batchExport(OrderBatchOperationDTO batchOperationDTO);
}
