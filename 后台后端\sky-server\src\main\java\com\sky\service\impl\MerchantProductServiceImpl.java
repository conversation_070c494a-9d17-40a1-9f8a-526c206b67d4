package com.sky.service.impl;

import com.sky.entity.MerchantProduct;
import com.sky.mapper.MerchantProductMapper;
import com.sky.service.MerchantProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MerchantProductServiceImpl implements MerchantProductService {
    @Autowired
    private MerchantProductMapper merchantProductMapper;

    @Override
    public List<MerchantProduct> findAll() {
        return merchantProductMapper.findAll();
    }

    @Override
    public List<MerchantProduct> findByProductCode(String productCode) {
        return merchantProductMapper.findByProductCode(productCode);
    }

    @Override
    public List<MerchantProduct> findByProductTitle(String productTitle) {
        return merchantProductMapper.findByProductTitle(productTitle);
    }

    @Override
    public List<MerchantProduct> findByStatus(String status) {
        return merchantProductMapper.findByStatus(status);
    }

    @Override
    public void publishProduct(MerchantProduct merchantProduct) {
        merchantProductMapper.insert(merchantProduct);
    }
}