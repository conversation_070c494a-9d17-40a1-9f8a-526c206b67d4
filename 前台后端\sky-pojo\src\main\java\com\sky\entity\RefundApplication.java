package com.sky.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款申请实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("refund_application")
public class RefundApplication implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请状态常量
     */
    public static final Integer STATUS_PENDING = 1;        // 待处理
    public static final Integer STATUS_APPROVED = 2;       // 已同意
    public static final Integer STATUS_REJECTED = 3;       // 已拒绝
    public static final Integer STATUS_CANCELLED = 4;      // 已取消
    public static final Integer STATUS_REFUNDING = 5;      // 退款中
    public static final Integer STATUS_REFUND_SUCCESS = 6; // 退款成功
    public static final Integer STATUS_REFUND_FAILED = 7;  // 退款失败

    /**
     * 退款类型常量
     */
    public static final Integer TYPE_REFUND_ONLY = 1;      // 仅退款
    public static final Integer TYPE_RETURN_REFUND = 2;    // 退货退款

    /**
     * 审核状态常量
     */
    public static final Integer APPROVAL_PENDING = 1;      // 待审核
    public static final Integer APPROVAL_APPROVED = 2;     // 审核通过
    public static final Integer APPROVAL_REJECTED = 3;     // 审核拒绝

    /**
     * 退款方式常量
     */
    public static final Integer METHOD_ORIGINAL = 1;       // 原路退回
    public static final Integer METHOD_BALANCE = 2;        // 余额退款

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 退款申请单号
     */
    private String refundNo;

    /**
     * 关联的订单ID
     */
    private Long orderId;

    /**
     * 关联的订单号
     */
    private String orderNumber;

    /**
     * 申请人ID
     */
    private Long buyerId;

    /**
     * 申请退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款理由
     */
    private String refundReason;

    /**
     * 退款类型：1-仅退款，2-退货退款
     */
    private Integer refundType;

    /**
     * 申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败
     */
    private Integer applicationStatus;

    /**
     * 是否需要审核：0-不需要，1-需要
     */
    private Integer needApproval;

    /**
     * 审核状态：1-待审核，2-审核通过，3-审核拒绝
     */
    private Integer approvalStatus;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 审核时间
     */
    private LocalDateTime approvalTime;

    /**
     * 审核备注
     */
    private String approvalRemark;

    /**
     * 退款方式：1-原路退回，2-余额退款
     */
    private Integer refundMethod;

    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;

    /**
     * 退款完成时间
     */
    private LocalDateTime refundTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 获取申请状态描述
     */
    public String getApplicationStatusDesc() {
        switch (applicationStatus) {
            case 1: return "待处理";
            case 2: return "已同意";
            case 3: return "已拒绝";
            case 4: return "已取消";
            case 5: return "退款中";
            case 6: return "退款成功";
            case 7: return "退款失败";
            default: return "未知状态";
        }
    }

    /**
     * 获取退款类型描述
     */
    public String getRefundTypeDesc() {
        switch (refundType) {
            case 1: return "仅退款";
            case 2: return "退货退款";
            default: return "未知类型";
        }
    }

    /**
     * 获取审核状态描述
     */
    public String getApprovalStatusDesc() {
        if (approvalStatus == null) {
            return "无需审核";
        }
        switch (approvalStatus) {
            case 1: return "待审核";
            case 2: return "审核通过";
            case 3: return "审核拒绝";
            default: return "未知状态";
        }
    }

    /**
     * 判断是否可以取消
     */
    @JsonIgnore
    public boolean canCancel() {
        return applicationStatus != null &&
               (applicationStatus.equals(STATUS_PENDING) ||
                applicationStatus.equals(STATUS_APPROVED));
    }

    /**
     * 判断是否需要审核
     */
    @JsonIgnore
    public boolean requiresApproval() {
        return needApproval != null && needApproval == 1;
    }

    /**
     * 判断是否已完成退款
     */
    @JsonIgnore
    public boolean isRefundCompleted() {
        return applicationStatus != null &&
               applicationStatus.equals(STATUS_REFUND_SUCCESS);
    }
}
