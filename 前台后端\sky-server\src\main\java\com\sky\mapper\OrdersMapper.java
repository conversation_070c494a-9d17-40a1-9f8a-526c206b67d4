package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.sky.entity.Orders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单Mapper接口
 */
@Mapper
public interface OrdersMapper extends BaseMapper<Orders> {

    /**
     * 创建订单
     * @param orders 订单对象
     * @return 影响行数
     */
    int createOrder(Orders orders);
    
    /**
     * 根据订单号查询订单
     * @param number 订单号
     * @return 订单对象
     */
    Orders getByNumber(@Param("number") String number);
    
    /**
     * 根据买家ID查询订单列表
     * @param buyerId 买家ID
     * @return 订单列表
     */
    List<Orders> getByBuyerId(@Param("buyerId") Long buyerId);
    
    /**
     * 更新订单状态
     * @param id 订单ID
     * @param status 订单状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 更新支付信息
     * @param id 订单ID
     * @param payMethod 支付方式
     * @param paymentTransactionId 支付交易ID
     * @return 影响行数
     */
    int updatePayment(@Param("id") Long id, @Param("payMethod") Integer payMethod, 
                      @Param("paymentTransactionId") String paymentTransactionId);
    
    /**
     * 分页查询订单
     * @param page 分页对象
     * @param buyerId 买家ID
     * @param status 订单状态
     * @return 分页结果
     */
    Page<Orders> pageQuery(Page<Orders> page, @Param("buyerId") Long buyerId,
                           @Param("status") Integer status);

    /**
     * 更新订单退款状态
     * @param id 订单ID
     * @param refundStatus 退款状态
     * @param refundAmount 退款金额
     * @param refundTime 退款时间
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateRefundStatus(@Param("id") Long id,
                          @Param("refundStatus") Integer refundStatus,
                          @Param("refundAmount") java.math.BigDecimal refundAmount,
                          @Param("refundTime") java.time.LocalDateTime refundTime,
                          @Param("updateTime") java.time.LocalDateTime updateTime);

    /**
     * 根据ID查询订单（包含退款信息）
     * @param id 订单ID
     * @return 订单对象
     */
    Orders selectById(@Param("id") Long id);
}