<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.CommissionMapper">

    <select id="getSettings" resultType="com.sky.entity.CommissionSettings">
        SELECT * FROM commission_settings LIMIT 1
    </select>

    <insert id="saveSettings" parameterType="com.sky.entity.CommissionSettings">
        INSERT INTO commission_settings (
            basic_rate, team_rate, leader_rate, min_withdraw, withdraw_cycle, 
            withdraw_days, upgrade_rules, rebate_policy, create_time, update_time
        ) VALUES (
            #{basicRate}, #{teamRate}, #{leaderRate}, #{minWithdraw}, #{withdrawCycle},
            #{withdrawDays,typeHandler=com.sky.handler.JsonTypeHandler}, #{upgradeRules,typeHandler=com.sky.handler.JsonTypeHandler}, #{rebatePolicy,typeHandler=com.sky.handler.JsonTypeHandler}, NOW(), NOW()
        )
    </insert>

    <select id="countLeaders" resultType="java.lang.Long">
        SELECT COUNT(*) FROM team_leader
        <where>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%')
                OR phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>

    <select id="getLeaders" resultType="com.sky.entity.TeamLeader">
        SELECT * FROM team_leader
        <where>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%')
                OR phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        <choose>
            <when test="sortBy != null and sortBy != ''">
                ORDER BY ${sortBy} ${sortOrder}
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getLeaderDetail" resultType="com.sky.entity.TeamLeader">
        SELECT * FROM team_leader WHERE id = #{id}
    </select>

    <insert id="addLeader" parameterType="com.sky.entity.TeamLeader">
        INSERT INTO team_leader (
            name, real_name, avatar, phone, email, id_number, bank_info, 
            address, create_time, level, status, team_size
        ) VALUES (
            #{name}, #{realName}, #{avatar}, #{phone}, #{email}, #{idNumber}, #{bankInfo,typeHandler=com.sky.handler.JsonTypeHandler},
            #{address}, NOW(), #{level}, #{status}, #{teamSize}
        )
    </insert>

    <update id="updateLeader" parameterType="com.sky.entity.TeamLeader">
        UPDATE team_leader
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="bankInfo != null">bank_info = #{bankInfo,typeHandler=com.sky.handler.JsonTypeHandler},</if>
            <if test="address != null">address = #{address},</if>
            <if test="level != null">level = #{level},</if>
            <if test="status != null">status = #{status},</if>
            <if test="teamSize != null">team_size = #{teamSize},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteLeader">
        DELETE FROM team_leader WHERE id = #{id}
    </delete>

    <update id="updateLeaderStatus">
        UPDATE team_leader SET status = #{status} WHERE id = #{id}
    </update>

    <select id="getStats" resultType="com.sky.vo.CommissionStats">
        SELECT 
            SUM(order_amount) as totalSales,
            SUM(commission_amount) as totalCommission,
            SUM(commission_amount) / SUM(order_amount) as commissionRate,
            SUM(CASE WHEN status = 'pending' THEN commission_amount ELSE 0 END) as pendingCommission,
            SUM(CASE WHEN status = 'paid' THEN commission_amount ELSE 0 END) as paidCommission,
            COUNT(DISTINCT leader_id) as leadersCount,
            (SELECT COUNT(*) FROM team_member) as teamMembersCount
        FROM commission_record
        <where>
            <if test="period != null and period != ''">
                <choose>
                    <when test="period == 'today'">
                        AND DATE(create_time) = CURDATE()
                    </when>
                    <when test="period == 'yesterday'">
                        AND DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                    </when>
                    <when test="period == 'thisWeek'">
                        AND YEARWEEK(create_time) = YEARWEEK(NOW())
                    </when>
                    <when test="period == 'lastWeek'">
                        AND YEARWEEK(create_time) = YEARWEEK(DATE_SUB(NOW(), INTERVAL 1 WEEK))
                    </when>
                    <when test="period == 'thisMonth'">
                        AND DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(NOW(), '%Y%m')
                    </when>
                    <when test="period == 'lastMonth'">
                        AND DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m')
                    </when>
                    <when test="period == 'custom'">
                        AND create_time BETWEEN #{startDate} AND #{endDate}
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="countRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM commission_record
        <where>
            <if test="leaderId != null">
                AND leader_id = #{leaderId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="orderType != null and orderType != ''">
                AND order_type = #{orderType}
            </if>
            <if test="dateRange != null and dateRange != ''">
                <choose>
                    <when test="dateRange == 'today'">
                        AND DATE(create_time) = CURDATE()
                    </when>
                    <when test="dateRange == 'yesterday'">
                        AND DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                    </when>
                    <when test="dateRange == 'thisWeek'">
                        AND YEARWEEK(create_time) = YEARWEEK(NOW())
                    </when>
                    <when test="dateRange == 'lastWeek'">
                        AND YEARWEEK(create_time) = YEARWEEK(DATE_SUB(NOW(), INTERVAL 1 WEEK))
                    </when>
                    <when test="dateRange == 'thisMonth'">
                        AND DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(NOW(), '%Y%m')
                    </when>
                    <when test="dateRange == 'lastMonth'">
                        AND DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m')
                    </when>
                    <when test="dateRange == 'custom'">
                        AND create_time BETWEEN #{startDate} AND #{endDate}
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="getRecords" resultType="com.sky.entity.CommissionRecord">
        SELECT * FROM commission_record
        <where>
            <if test="leaderId != null">
                AND leader_id = #{leaderId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="orderType != null and orderType != ''">
                AND order_type = #{orderType}
            </if>
            <if test="dateRange != null and dateRange != ''">
                <choose>
                    <when test="dateRange == 'today'">
                        AND DATE(create_time) = CURDATE()
                    </when>
                    <when test="dateRange == 'yesterday'">
                        AND DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                    </when>
                    <when test="dateRange == 'thisWeek'">
                        AND YEARWEEK(create_time) = YEARWEEK(NOW())
                    </when>
                    <when test="dateRange == 'lastWeek'">
                        AND YEARWEEK(create_time) = YEARWEEK(DATE_SUB(NOW(), INTERVAL 1 WEEK))
                    </when>
                    <when test="dateRange == 'thisMonth'">
                        AND DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(NOW(), '%Y%m')
                    </when>
                    <when test="dateRange == 'lastMonth'">
                        AND DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m')
                    </when>
                    <when test="dateRange == 'custom'">
                        AND create_time BETWEEN #{startDate} AND #{endDate}
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

</mapper> 