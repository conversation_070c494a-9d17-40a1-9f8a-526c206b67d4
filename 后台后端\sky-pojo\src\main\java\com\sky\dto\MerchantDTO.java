package com.sky.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantDTO {
    /**
     * 店铺ID（自增主键）
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 联系手机号
     */
    private String mobile;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人所在省份
     */
    private String province;

    /**
     * 联系人所在城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 账号状态：0-正常 1-禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
