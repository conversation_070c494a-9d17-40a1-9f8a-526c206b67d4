package com.sky.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sky.entity.Seller;
import com.sky.entity.SellerPermission;
import com.sky.mapper.SellerMapper;
import com.sky.mapper.SellerPermissionMapper;
import com.sky.service.SellerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Service
public class SellerServiceImpl implements SellerService {

    private static final Logger logger = LoggerFactory.getLogger(SellerServiceImpl.class);

    @Autowired
    private SellerMapper sellerMapper;
    @Autowired
    private SellerPermissionMapper sellerPermissionMapper;

    @Override
    public List<Seller> findAll() {
        try {
            return sellerMapper.findAll();
        } catch (Exception e) {
            logger.error("查询所有商家信息失败", e);
            throw new RuntimeException("查询所有商家信息失败", e);
        }
    }

    @Override
    public Seller findById(Long id) {
        try {
            return sellerMapper.findById(id);
        } catch (Exception e) {
            logger.error("根据ID查询商家信息失败, ID: {}", id, e);
            throw new RuntimeException("根据ID查询商家信息失败", e);
        }
    }

    @Override
    public Boolean update(Seller seller) {
        try {
            // 将 Long 类型转换为 Integer 类型
            Integer sellerId = seller.getId() == null ? null : seller.getId().intValue();
            Long l = Long.valueOf(sellerId);
            Seller existingSeller = sellerMapper.findById(l);
            if (existingSeller == null) {
                throw new RuntimeException("商家信息不存在");
            }

            // 更新允许修改的字段
            existingSeller.setAccountName(seller.getAccountName());
            existingSeller.setPassword(seller.getPassword());
            existingSeller.setGender(seller.getGender());
            existingSeller.setPhone(seller.getPhone());
            existingSeller.setEmail(seller.getEmail());
            existingSeller.setAccountStatus(seller.getAccountStatus());
            existingSeller.setVerificationCode(seller.getVerificationCode());
            existingSeller.setPhotoUrl(seller.getPhotoUrl());

            // 不更新 createTime 和 lastLoginTime

            sellerMapper.update(existingSeller);
            return true;
        } catch (Exception e) {
            logger.error("更新商家信息失败, ID: {}", seller.getId(), e);
            throw new RuntimeException("更新商家信息失败", e);
        }
    }

    @Override
    public Boolean deleteById(Integer id) {
        try {
//            sellerPermissionMapper.delete(new LambdaQueryWrapper<>().eq(SellerPermission::getSellerId,id));
            sellerMapper.deleteById(id);
            return true;
        } catch (Exception e) {
            logger.error("删除商家信息失败, ID: {}", id, e);
            throw new RuntimeException("删除商家信息失败", e);
        }
    }
}