package com.sky.service;

import com.sky.dto.track17.Track17RealtimeRequest;
import com.sky.dto.track17.Track17RealtimeResponse;
import com.sky.vo.Track17Response;

import java.util.List;

/**
 * 17TRACK API服务接口
 */
public interface Track17ApiService {

    /**
     * 获取实时物流信息
     * @param request 查询请求
     * @return 物流信息响应
     */
    Track17Response<Track17RealtimeResponse> getRealtimeTracking(Track17RealtimeRequest request);

    /**
     * 批量获取实时物流信息
     * @param requests 查询请求列表
     * @return 物流信息响应列表
     */
    Track17Response<List<Track17RealtimeResponse>> getBatchRealtimeTracking(List<Track17RealtimeRequest> requests);

    /**
     * 根据物流单号和运输商代码获取物流信息
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 物流信息响应
     */
    Track17Response<Track17RealtimeResponse> getTrackingByNumber(String trackingNumber, String carrierCode);

    /**
     * 检测运输商
     * @param trackingNumber 物流单号
     * @return 运输商代码列表
     */
    Track17Response<List<String>> detectCarrier(String trackingNumber);

    /**
     * 获取支持的运输商列表
     * @return 运输商列表
     */
    Track17Response<List<String>> getSupportedCarriers();
}
