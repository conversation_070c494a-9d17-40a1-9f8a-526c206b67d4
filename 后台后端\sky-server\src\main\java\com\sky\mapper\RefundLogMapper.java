package com.sky.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 退款日志Mapper
 */
@Mapper
public interface RefundLogMapper {
    
    /**
     * 插入退款操作日志
     */
    @Insert("INSERT INTO refund_log (refund_application_id, refund_no, old_status, new_status, " +
            "operation_type, operator_id, operator_name, operation_desc, create_time) " +
            "VALUES (#{refundApplicationId}, #{refundNo}, #{oldStatus}, #{newStatus}, " +
            "#{operationType}, #{operatorId}, #{operatorName}, #{operationDesc}, #{createTime})")
    void insertRefundLog(@Param("refundApplicationId") Long refundApplicationId,
                        @Param("refundNo") String refundNo,
                        @Param("oldStatus") Integer oldStatus,
                        @Param("newStatus") Integer newStatus,
                        @Param("operationType") String operationType,
                        @Param("operatorId") Long operatorId,
                        @Param("operatorName") String operatorName,
                        @Param("operationDesc") String operationDesc,
                        @Param("createTime") LocalDateTime createTime);
}
