package com.sky.controller.Seller;

import com.sky.entity.Warehouse;
import com.sky.result.Result;
import com.sky.service.WarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/warehouse")
@Api(tags = "仓库管理接口")
public class WarehouseController {
    @Autowired
    private WarehouseService warehouseService;

    @GetMapping("/list")
    @ApiOperation("获取所有仓库列表")
    public Result<List<Warehouse>> findAll() {
        List<Warehouse> warehouses = warehouseService.findAll();
        return Result.success(warehouses);
    }

    @PostMapping("/add")
    @ApiOperation("添加仓库")
    public Result<String> addWarehouse(@RequestBody Warehouse warehouse) {
        warehouseService.addWarehouse(warehouse);
        return Result.success("仓库添加成功");
    }
}