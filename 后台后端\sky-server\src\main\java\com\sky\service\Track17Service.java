package com.sky.service;

import com.sky.dto.RegisterTrackingDTO;
import com.sky.dto.TrackingQueryDTO;
import com.sky.entity.TrackingRecord;
import com.sky.vo.TrackingDetailVO;

import java.util.List;

/**
 * 17TRACK物流跟踪服务接口
 */
public interface Track17Service {

    /**
     * 注册物流单号
     * @param registerDTO 注册请求参数
     * @return 跟踪记录
     */
    TrackingRecord registerTracking(RegisterTrackingDTO registerDTO);

    /**
     * 批量注册物流单号
     * @param registerDTOs 批量注册请求参数
     * @return 注册结果列表
     */
    List<TrackingRecord> batchRegisterTracking(List<RegisterTrackingDTO> registerDTOs);

    /**
     * 查询物流跟踪详情
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 物流详情
     */
    TrackingDetailVO getTrackingDetail(String trackingNumber, Integer carrierCode);

    /**
     * 根据订单ID查询物流信息
     * @param orderId 订单ID
     * @return 物流详情列表
     */
    List<TrackingDetailVO> getTrackingByOrderId(Long orderId);

    /**
     * 获取跟踪列表
     * @param queryDTO 查询参数
     * @return 跟踪记录列表
     */
    List<TrackingRecord> getTrackingList(TrackingQueryDTO queryDTO);

    /**
     * 停止跟踪
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 是否成功
     */
    boolean stopTracking(String trackingNumber, Integer carrierCode);

    /**
     * 重启跟踪
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 是否成功
     */
    boolean restartTracking(String trackingNumber, Integer carrierCode);

    /**
     * 删除跟踪
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 是否成功
     */
    boolean deleteTracking(String trackingNumber, Integer carrierCode);

    /**
     * 修改跟踪信息
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @param tag 标签
     * @param remark 备注
     * @return 是否成功
     */
    boolean updateTrackingInfo(String trackingNumber, Integer carrierCode, String tag, String remark);

    /**
     * 获取剩余配额
     * @return 配额信息
     */
    String getQuota();

    /**
     * 同步物流状态（手动触发）
     * @param trackingNumber 物流单号
     * @param carrierCode 运输商代码
     * @return 是否成功
     */
    boolean syncTrackingStatus(String trackingNumber, Integer carrierCode);
}
