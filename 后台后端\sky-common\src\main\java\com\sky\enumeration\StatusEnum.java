package com.sky.enumeration;

/**
 * 通用状态枚举（适用于账号、角色、权限等）
 */
public enum StatusEnum {
    ENABLED("ENABLED", "启用"),
    DISABLED("DISABLED", "禁用"),
    LOCKED("LOCKED", "锁定"),
    EXPIRED("EXPIRED", "过期"),
    PENDING("PENDING", "待激活");

    private final String code;
    private final String description;

    StatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据code获取枚举实例
    public static StatusEnum getByCode(String code) {
        for (StatusEnum value : values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    // Getters
    public String getCode() { return code; }
    public String getDescription() { return description; }
}
