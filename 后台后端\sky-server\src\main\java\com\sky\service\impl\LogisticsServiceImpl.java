package com.sky.service.impl;

import com.sky.dto.LogisticsInfoDTO;
import com.sky.entity.LogisticsInfo;
import com.sky.mapper.LogisticsMapper;
import com.sky.service.LogisticsService;
import com.sky.vo.LogisticsInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class LogisticsServiceImpl implements LogisticsService {

    @Autowired
    private LogisticsMapper logisticsMapper;

    /**
     * 创建物流信息
     * @param logisticsInfoDTO
     */
    @Override
    public void createLogistics(LogisticsInfoDTO logisticsInfoDTO) {
        LogisticsInfo logisticsInfo = new LogisticsInfo();
        BeanUtils.copyProperties(logisticsInfoDTO,logisticsInfo);
        logisticsInfo.setCreateTime(LocalDateTime.now());

        logisticsMapper.insertLogistics(logisticsInfo);
    }

    /**
     * 查询物流信息
     * @param id
     * @return
     */
    @Override
    public LogisticsInfoVO getLogisticsInfo(Long id) {
        LogisticsInfo logisticsInfo = logisticsMapper.getById(id);
        LogisticsInfoVO logisticsInfoVO = new LogisticsInfoVO();
        BeanUtils.copyProperties(logisticsInfo,logisticsInfoVO);
        return logisticsInfoVO;
    }

    /**
     * 修改物流信息状态
     * @param id
     * @param logisticsStatus
     */
    @Override
    public void updateLogistics(Long id, String logisticsStatus) {
        logisticsMapper.updateLogistics(id,logisticsStatus);
    }
}
