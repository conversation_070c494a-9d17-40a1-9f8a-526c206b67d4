package com.sky.service;

import com.sky.dto.BuyerDTO;
import com.sky.dto.BuyerLoginDTO;
import com.sky.dto.BuyerRegisterDTO;
import com.sky.entity.Buyer;
import com.sky.vo.BuyerInfoVO;

public interface BuyerService {




    /**
     * 用户注册
     * @param buyerRegisterDTO
     */
    void buyerRegister(BuyerRegisterDTO buyerRegisterDTO) ;

    /**
     * 员工登录
     * @param buyerLoginDTO
     * @return
     */
    Buyer login(BuyerLoginDTO buyerLoginDTO);

    /**
     * 根据id查询用户信息
     * @param id
     * @return
     */
    BuyerInfoVO getById(Long id);

    /**
     * 编辑员工信息
     * @param buyerDTO
     */
    void update(BuyerDTO buyerDTO);

}
