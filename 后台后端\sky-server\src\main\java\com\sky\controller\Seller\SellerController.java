package com.sky.controller.Seller;

import com.sky.entity.EVPI;
import com.sky.entity.Seller;
import com.sky.entity.SellerAndShop;
import com.sky.mapper.LoginMapper;
import com.sky.result.Result;
import com.sky.service.SellerService;
import com.sky.vo.EVPIVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/sellers")
public class SellerController {

    @Autowired
    private LoginMapper loginMapper;

    @Autowired
    private SellerService sellerService;

    /**
     * 查询所有商家信息
     *
     * @return 商家列表
     */
    @GetMapping("/All")
    public Result<List<Seller>> findAll() {

        return Result.success(sellerService.findAll());
    }

    /**
     * 根据 ID 查询商家信息
     *
     * @param id 商家 ID
     * @return 商家信息
     */
    @GetMapping("/{id}")
    public Result<SellerAndShop> findById(@PathVariable Long id) {
        SellerAndShop sellerAndShop = new SellerAndShop();
        Seller byId = sellerService.findById(id);
        EVPI data = loginMapper.getData(id);
        sellerAndShop.setEvpi(data);
        sellerAndShop.setSeller(byId);
        return Result.success(sellerAndShop);
    }
    @GetMapping("/id/{id}")
    public Result<Seller> findSellerById(@PathVariable Long id) {
        Seller byId = sellerService.findById(id);

        return Result.success(byId);
    }

    /**
     * 更新商家信息
     *
     * @param seller 商家信息
     */
    @PutMapping("/update")
    public void update(@RequestBody Seller seller) {

        Result.success(sellerService.update(seller));
    }

    /**
     * 根据 ID 删除商家信息
     *
     * @param id 商家 ID
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteById(@PathVariable Integer id) {
        sellerService.deleteById(id);
        return Result.success();
    }


}