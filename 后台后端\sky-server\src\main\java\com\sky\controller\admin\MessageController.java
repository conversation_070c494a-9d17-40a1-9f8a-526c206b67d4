package com.sky.controller.admin;
import com.sky.dto.BatchMessageDTO;
import com.sky.dto.MessageDTO;
import com.sky.dto.RecipientQueryDTO;
import com.sky.result.Result;
import com.sky.service.MessageService;
import com.sky.vo.MessageDetailVO;
import com.sky.vo.MessageListVO;
import com.sky.vo.RecipientVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/message")
@Api(tags = "站内信管理相关接口")
@CrossOrigin(origins = "*")
@Slf4j
public class MessageController {

    @Autowired
    private MessageService messageService;

    /**
     * 获取消息接收者列表
     */
    @GetMapping("/recipients")
    @ApiOperation("获取消息接收者列表")
    public Result<List<RecipientVO>> getRecipients(RecipientQueryDTO queryDTO) {
        log.info("获取消息接收者列表：{}", queryDTO);
        List<RecipientVO> recipients = messageService.getRecipients(queryDTO);
        return Result.success(recipients);
    }

    /**
     * 发送站内信
     */
    @PostMapping("/send")
    @ApiOperation("发送站内信")
    public Result<String> sendMessage(@RequestBody MessageDTO messageDTO) {
        log.info("发送站内信：{}", messageDTO);
        String messageId = messageService.sendMessage(messageDTO);
        return Result.success(messageId);
    }

    /**
     * 发送系统公告
     */
    @PostMapping("/announcement")
    @ApiOperation("发送系统公告")
    public Result<String> sendAnnouncement(@RequestBody MessageDTO messageDTO) {
        log.info("发送系统公告：{}", messageDTO);
        String messageId = messageService.sendAnnouncement(messageDTO);
        return Result.success(messageId);
    }

    /**
     * 获取站内信列表
     */
    @GetMapping("/list")
    @ApiOperation("获取站内信列表")
    public Result<MessageListVO> getMessageList(
            @RequestParam String type,
            @RequestParam Long id) {
        log.info("获取站内信列表：type={}, id={}", type, id);
        MessageListVO messageList = messageService.getMessageList(type, id);
        return Result.success(messageList);
    }

    /**
     * 获取消息详情
     */
    @GetMapping("/{msgID}")
    @ApiOperation("获取消息详情")
    public Result<MessageDetailVO> getMessageDetail(
            @PathVariable String msgID,
            @RequestParam String type,
            @RequestParam Long id) {
        log.info("获取消息详情：msgID={}, type={}, id={}", msgID, type, id);
        MessageDetailVO messageDetail = messageService.getMessageDetail(msgID, type, id);
        return Result.success(messageDetail);
    }

    /**
     * 标记消息为已读
     */
    @PutMapping("/{msgID}/read")
    @ApiOperation("标记消息为已读")
    public Result<String> markAsRead(
            @PathVariable String msgID,
            @RequestParam String type,
            @RequestParam Long id) {
        log.info("标记消息为已读：msgID={}, type={}, id={}", msgID, type, id);
        messageService.markAsRead(msgID, type, id);
        return Result.success("消息已标记为已读");
    }

    /**
     * 批量标记消息为已读
     */
    @PutMapping("/batch/read")
    @ApiOperation("批量标记消息为已读")
    public Result<String> batchMarkAsRead(@RequestBody BatchMessageDTO batchDTO) {
        log.info("批量标记消息为已读：{}", batchDTO);
        messageService.batchMarkAsRead(batchDTO);
        return Result.success("批量标记成功");
    }

    /**
     * 标记全部消息为已读
     */
    @PutMapping("/all/read")
    @ApiOperation("标记全部消息为已读")
    public Result<String> markAllAsRead(
            @RequestParam String type,
            @RequestParam Long id) {
        log.info("标记全部消息为已读：type={}, id={}", type, id);
        messageService.markAllAsRead(type, id);
        return Result.success("消息全部标记成功");
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread/count")
    @ApiOperation("获取未读消息数量")
    public Result<Integer> getUnreadCount(
            @RequestParam String type,
            @RequestParam Long id) {
        log.info("获取未读消息数量：type={}, id={}", type, id);
        Integer count = messageService.getUnreadCount(type, id);
        return Result.success(count);
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{msgID}")
    @ApiOperation("删除消息")
    public Result<String> deleteMessage(
            @PathVariable String msgID,
            @RequestParam String type,
            @RequestParam Long id) {
        log.info("删除消息：msgID={}, type={}, id={}", msgID, type, id);
        messageService.deleteMessage(msgID, type, id);
        return Result.success("删除成功");
    }

    /**
     * 批量删除消息
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除消息")
    public Result<String> batchDeleteMessage(@RequestBody BatchMessageDTO batchDTO) {
        log.info("批量删除消息：{}", batchDTO);
        messageService.batchDeleteMessage(batchDTO);
        return Result.success("删除成功");
    }
} 