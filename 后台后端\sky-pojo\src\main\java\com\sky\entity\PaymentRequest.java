package com.sky.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import com.wechat.pay.java.service.payments.nativepay.model.Amount;
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer total;

    private Amount amount;

    private String title;

    private String outTradeNo;
}
