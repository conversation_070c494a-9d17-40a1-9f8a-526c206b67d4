package com.sky.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductReview {
    private Long id;             // 主键ID
    private Long buyerId;        // 买家ID
    private Long productId;      // 商品ID
    private String buyerName;    // 买家名称
    private String storeName;    // 店铺名称
    private String productName;  // 商品名称
    private Integer rating;      // 评分
    private String comment;      // 评论内容
    private Integer isRecommended; // 是否推荐
    private LocalDateTime commentTime; // 评论时间
    private Integer status;      // 状态
}
