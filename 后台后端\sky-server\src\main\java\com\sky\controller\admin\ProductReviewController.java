package com.sky.controller.admin;

import com.sky.entity.BusinessException;
import com.sky.entity.ProductReview;
import com.sky.result.Result;
import com.sky.service.ProductReviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/reviews")
@Api(tags = "C端商品评论接口")
public class ProductReviewController {

    @Autowired
    private ProductReviewService productReviewService;

    // 添加评论
    @PostMapping("/add")
    @ApiOperation("添加评论")
    public Result<Void> addReview(@RequestBody ProductReview productReview) {
        try {
            productReviewService.addReview(productReview);
            return Result.success();
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }
    }

    // 查询评论
    @GetMapping("/list/{productId}")
    @ApiOperation("查询评论")
    public Result<List<ProductReview>> getReviews(@PathVariable Long productId) {
        List<ProductReview> reviews = productReviewService.getReviewsByProductId(productId);
        return Result.success(reviews);
    }

    // 删除评论
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除评论")
    public Result<Void> deleteReview(@PathVariable Long id) {
        productReviewService.deleteReview(id);
        return Result.success();
    }
}